# 微信支付集成配置指南

本文档说明如何配置微信支付功能，使其在实际项目中正常工作。

## 1. 微信开放平台配置

### 1.1 注册微信开放平台账号
1. 访问 [微信开放平台](https://open.weixin.qq.com/)
2. 注册开发者账号并完成认证
3. 创建移动应用并获得AppID

### 1.2 配置应用信息
- **应用名称**: 健康助手
- **应用包名**: 与Flutter项目的包名一致
- **应用签名**: 需要提供正式签名的MD5值

## 2. 微信商户平台配置

### 2.1 注册微信商户号
1. 访问 [微信商户平台](https://pay.weixin.qq.com/)
2. 注册商户号并完成认证
3. 开通APP支付功能

### 2.2 配置支付参数
- **商户号**: 从微信商户平台获取
- **API密钥**: 在商户平台设置API密钥
- **证书**: 下载API证书用于退款等操作

## 3. Flutter项目配置

### 3.1 更新微信AppID
在 `lib/src/services/wechat_pay_service.dart` 文件中更新配置：

```dart
class WeChatPayConfig {
  /// 微信开放平台AppID - 需要替换为实际的AppID
  static const String appId = 'wx1234567890abcdef'; // 替换为实际AppID
  
  /// iOS Universal Link - 需要替换为实际的Universal Link
  static const String? universalLink = 'https://your-domain.com/'; // 替换为实际域名
  
  /// 是否为调试模式
  static const bool isDebug = false; // 生产环境设置为false
}
```

### 3.2 Android配置

#### 3.2.1 更新 android/app/build.gradle
```gradle
android {
    defaultConfig {
        // 确保包名与微信开放平台注册的一致
        applicationId "com.yourcompany.healthassistant"
    }
}
```

#### 3.2.2 添加微信支付Activity
在 `android/app/src/main/AndroidManifest.xml` 中添加：

```xml
<activity
    android:name=".wxapi.WXPayEntryActivity"
    android:exported="true"
    android:launchMode="singleTop">
    <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <data android:scheme="wx你的AppID" />
    </intent-filter>
</activity>
```

#### 3.2.3 创建WXPayEntryActivity
在 `android/app/src/main/java/com/yourcompany/healthassistant/wxapi/` 目录下创建 `WXPayEntryActivity.java`：

```java
package com.yourcompany.healthassistant.wxapi;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

public class WXPayEntryActivity extends Activity implements IWXAPIEventHandler {
    private IWXAPI api;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        api = WXAPIFactory.createWXAPI(this, "你的AppID");
        api.handleIntent(getIntent(), this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        api.handleIntent(intent, this);
    }

    @Override
    public void onReq(BaseReq req) {
    }

    @Override
    public void onResp(BaseResp resp) {
        finish();
    }
}
```

### 3.3 iOS配置

#### 3.3.1 更新 ios/Runner/Info.plist
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>weixin</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>wx你的AppID</string>
        </array>
    </dict>
</array>

<key>LSApplicationQueriesSchemes</key>
<array>
    <string>weixin</string>
    <string>weixinULAPI</string>
</array>
```

#### 3.3.2 配置Universal Link
1. 在你的域名服务器上配置Universal Link
2. 在微信开放平台填写Universal Link
3. 更新WeChatPayConfig中的universalLink

## 4. 后端配置

### 4.1 支付接口实现
确保后端已实现以下接口：
- `POST /applet/v1/products/orders/{order_id}/payment` - 创建支付
- `GET /applet/v1/products/orders/{order_id}/payment/status` - 查询支付状态
- `PUT /applet/v1/products/orders/{order_id}/payment/sync` - 同步支付状态

### 4.2 微信支付回调
配置微信支付回调URL，处理支付结果通知。

## 5. 测试配置

### 5.1 开发环境测试
1. 使用微信开发者工具进行调试
2. 确保测试设备安装了微信APP
3. 使用测试商户号进行支付测试

### 5.2 生产环境部署
1. 使用正式的微信AppID和商户号
2. 配置正式的签名证书
3. 设置WeChatPayConfig.isDebug = false

## 6. 常见问题

### 6.1 微信未安装
应用会自动检测微信是否安装，如果未安装会提示用户安装。

### 6.2 支付失败
1. 检查AppID是否正确
2. 检查商户号配置是否正确
3. 检查网络连接
4. 查看后端日志确认支付参数

### 6.3 iOS Universal Link问题
1. 确保域名可以正常访问
2. 检查apple-app-site-association文件配置
3. 在微信开放平台正确填写Universal Link

## 7. 安全注意事项

1. **不要在客户端存储敏感信息**：API密钥、商户号等敏感信息只能在后端使用
2. **验证支付结果**：客户端收到支付成功后，必须通过后端验证支付状态
3. **使用HTTPS**：所有支付相关的网络请求必须使用HTTPS
4. **签名验证**：后端必须验证微信支付回调的签名

## 8. 支付流程说明

1. **用户下单**：在订单确认页面点击"提交订单"
2. **创建支付**：调用后端API创建微信支付订单
3. **调用微信支付**：使用fluwx插件调用微信APP进行支付
4. **处理支付结果**：根据微信返回的结果更新订单状态
5. **验证支付状态**：通过后端API验证最终支付状态

## 9. 相关文件

- `lib/src/services/wechat_pay_service.dart` - 微信支付服务
- `lib/src/services/payment_service.dart` - 支付服务
- `lib/src/models/payment_model.dart` - 支付数据模型
- `lib/src/presentation/orders/pages/order_confirm_page.dart` - 订单确认页面
- `lib/src/presentation/orders/pages/my_orders_page.dart` - 我的订单页面

配置完成后，用户就可以在应用中正常使用微信支付功能了。
