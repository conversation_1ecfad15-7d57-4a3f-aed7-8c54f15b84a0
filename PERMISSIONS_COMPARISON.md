# Android vs iOS 权限对比

## 📱 权限配置对比表

| 功能 | Android权限 | iOS权限 | 状态 |
|------|------------|---------|------|
| **相机拍照** | `android.permission.CAMERA` | `NSCameraUsageDescription` | ✅ 已配置 |
| **录音功能** | `android.permission.RECORD_AUDIO` | `NSMicrophoneUsageDescription` | ✅ 已配置 |
| **读取相册** | `android.permission.READ_MEDIA_IMAGES` | `NSPhotoLibraryUsageDescription` | ✅ 已配置 |
| **存储写入** | `android.permission.WRITE_EXTERNAL_STORAGE` | `NSPhotoLibraryAddUsageDescription` | ✅ 已配置 |
| **精确定位** | `android.permission.ACCESS_FINE_LOCATION` | `NSLocationWhenInUseUsageDescription` | ✅ 已配置 |
| **粗略定位** | `android.permission.ACCESS_COARSE_LOCATION` | `NSLocationWhenInUseUsageDescription` | ✅ 已配置 |
| **后台定位** | `android.permission.ACCESS_BACKGROUND_LOCATION` | `NSLocationAlwaysAndWhenInUseUsageDescription` | ✅ 已配置 |

## 🔍 Android独有权限 (iOS无需配置)

| Android权限 | 说明 | iOS处理方式 |
|------------|------|------------|
| `android.permission.INTERNET` | 网络访问 | iOS默认允许 |
| `android.permission.ACCESS_NETWORK_STATE` | 网络状态 | iOS默认允许 |
| `android.permission.READ_EXTERNAL_STORAGE` | 读取存储 | 通过相册权限处理 |
| `android.permission.FLASHLIGHT` | 手电筒 | 通过相机权限包含 |
| `android.permission.ACCESS_WIFI_STATE` | WiFi状态 | iOS默认允许 |
| `android.permission.CHANGE_WIFI_STATE` | WiFi控制 | iOS不允许应用控制 |
| `android.permission.ACCESS_MOCK_LOCATION` | 模拟位置 | iOS开发者选项 |

## 📋 权限描述文本

### Android (AndroidManifest.xml)
```xml
<!-- 网络权限 -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

<!-- 录音权限 -->
<uses-permission android:name="android.permission.RECORD_AUDIO" />

<!-- 存储权限 -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

<!-- 相机权限 -->
<uses-permission android:name="android.permission.CAMERA" />

<!-- 照片访问权限 (Android 13+) -->
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

<!-- 位置权限 -->
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
```

### iOS (Info.plist)
```xml
<!-- 录音权限 -->
<key>NSMicrophoneUsageDescription</key>
<string>此应用需要使用麦克风进行健康语音记录和AI医生语音咨询功能</string>

<!-- 相机权限 -->
<key>NSCameraUsageDescription</key>
<string>此应用需要使用相机进行健康报告扫描和医疗图片拍摄功能</string>

<!-- 相册权限 -->
<key>NSPhotoLibraryUsageDescription</key>
<string>此应用需要访问相册选择健康相关图片和医疗报告</string>

<!-- 相册添加权限 -->
<key>NSPhotoLibraryAddUsageDescription</key>
<string>此应用需要保存健康报告和医疗图片到相册</string>

<!-- 定位权限 - 使用期间 -->
<key>NSLocationWhenInUseUsageDescription</key>
<string>此应用需要获取您的位置信息以提供基于位置的健康服务和医院推荐</string>

<!-- 后台定位权限 -->
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>此应用需要后台位置权限以提供紧急健康服务和位置相关的健康提醒</string>
```

## ✅ 配置验证

运行以下命令检查权限配置：

```bash
# 检查iOS权限配置
./scripts/check_ios_permissions.sh

# 检查Android权限配置
grep -n "uses-permission" android/app/src/main/AndroidManifest.xml
```

## 🔧 权限请求时机

1. **相机权限**: 用户点击拍照按钮时
2. **麦克风权限**: 用户开始录音时
3. **相册权限**: 用户选择图片时
4. **定位权限**: 用户使用位置相关功能时

## 📝 注意事项

- iOS权限描述必须清晰说明使用目的
- Android权限在安装时显示，iOS权限在使用时请求
- 权限被拒绝后要提供友好的用户引导
- 测试时建议卸载重装应用以重置权限状态

## 🎯 权限配置完成

✅ Android权限: 11个权限已配置  
✅ iOS权限: 6个权限已配置  
✅ 权限对应关系: 完全匹配  
✅ 权限描述文本: 清晰明确
