#!/bin/bash

# Release 构建脚本 - 已解决所有主要问题
# 使用方法: ./scripts/build_release.sh

echo "🚀 开始构建 Release 版本..."
echo "📱 应用包名: com.minhan.tourguide"
echo "✅ BuildConfig 警告已修复"
echo "✅ 签名配置已正确设置"
echo ""

# 设置国内镜像环境变量
echo "🌐 设置 Flutter 国内镜像..."
export FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn
export PUB_HOSTED_URL=https://pub.flutter-io.cn

echo "✅ 镜像设置完成"
echo "   FLUTTER_STORAGE_BASE_URL: $FLUTTER_STORAGE_BASE_URL"
echo "   PUB_HOSTED_URL: $PUB_HOSTED_URL"
echo ""

# 清理项目
echo "🧹 清理项目缓存..."
flutter clean

echo "📦 获取依赖..."
flutter pub get

echo "🔍 运行代码分析..."
flutter analyze
if [ $? -ne 0 ]; then
    echo "❌ 代码分析失败，请修复错误后重试"
    exit 1
fi

echo "✅ 代码分析通过"
echo ""

# 尝试构建 Release 版本
echo "🔨 构建 Release APK..."
flutter build apk --release

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Release 版本构建成功！"
    echo "📍 APK 位置: build/app/outputs/flutter-apk/app-release.apk"
    echo "📏 APK 大小: $(du -h build/app/outputs/flutter-apk/app-release.apk | cut -f1)"
    echo ""
    echo "🔒 签名信息:"
    echo "   密钥库: android/app/minhan-tourguide-keystore.jks"
    echo "   别名: minhan-tourguide"
else
    echo ""
    echo "❌ Release 版本构建失败"
    echo ""
    echo "🔄 尝试其他解决方案:"
    echo "1. 检查网络连接"
    echo "2. 配置代理设置 (如果使用代理)"
    echo "3. 尝试: flutter build apk --release --offline"
    echo "4. 查看完整日志: flutter build apk --release --verbose"
    echo ""
    exit 1
fi 