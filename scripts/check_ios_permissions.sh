#!/bin/bash

# iOS权限配置检查脚本

echo "🔍 检查iOS权限配置..."

INFO_PLIST="ios/Runner/Info.plist"

if [ ! -f "$INFO_PLIST" ]; then
    echo "❌ Info.plist文件不存在: $INFO_PLIST"
    exit 1
fi

echo "📱 检查Info.plist权限配置..."

# 检查必需的权限配置（与Android权限严格对应）
permissions=(
    "NSCameraUsageDescription"                    # 对应 android.permission.CAMERA
    "NSMicrophoneUsageDescription"                # 对应 android.permission.RECORD_AUDIO
    "NSPhotoLibraryUsageDescription"              # 对应 android.permission.READ_MEDIA_IMAGES
    "NSPhotoLibraryAddUsageDescription"           # 对应 android.permission.WRITE_EXTERNAL_STORAGE
    "NSLocationWhenInUseUsageDescription"         # 对应 android.permission.ACCESS_FINE_LOCATION
    "NSLocationAlwaysAndWhenInUseUsageDescription" # 对应 android.permission.ACCESS_BACKGROUND_LOCATION
)

missing_permissions=()

for permission in "${permissions[@]}"; do
    if grep -q "$permission" "$INFO_PLIST"; then
        echo "✅ $permission - 已配置"
    else
        echo "❌ $permission - 缺失"
        missing_permissions+=("$permission")
    fi
done

if [ ${#missing_permissions[@]} -eq 0 ]; then
    echo ""
    echo "🎉 所有必需的权限配置都已存在！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 清理项目: flutter clean"
    echo "2. 删除iOS构建文件: rm -rf ios/build"
    echo "3. 重新获取依赖: flutter pub get"
    echo "4. 在真机上测试权限功能"
    echo ""
    echo "💡 如果权限仍然无法获取，请："
    echo "- 卸载设备上的应用重新安装"
    echo "- 检查设备的隐私设置"
    echo "- 确认应用签名配置正确"
else
    echo ""
    echo "⚠️  发现缺失的权限配置:"
    for permission in "${missing_permissions[@]}"; do
        echo "   - $permission"
    done
    echo ""
    echo "请检查 $INFO_PLIST 文件并添加缺失的权限配置"
fi

echo ""
echo "🔧 iOS部署目标检查..."
if grep -q "IPHONEOS_DEPLOYMENT_TARGET = 12.0" ios/Runner.xcodeproj/project.pbxproj; then
    echo "✅ iOS部署目标: 12.0 (兼容性良好)"
else
    echo "⚠️  请检查iOS部署目标设置"
fi

echo ""
echo "📚 更多帮助请查看: IOS_PERMISSIONS_GUIDE.md"
