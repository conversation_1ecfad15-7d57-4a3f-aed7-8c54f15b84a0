#!/bin/bash

# Flutter 测试自动化脚本
# 用于运行完整的测试套件，包括代码分析、测试执行和覆盖率报告

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出带颜色的文本
print_step() {
    echo -e "${BLUE}🔧 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查Flutter是否安装
check_flutter() {
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter未安装或未添加到PATH"
        exit 1
    fi
    print_success "Flutter环境检查通过"
}

# 检查项目目录
check_project() {
    if [ ! -f "pubspec.yaml" ]; then
        print_error "未在Flutter项目根目录运行此脚本"
        exit 1
    fi
    print_success "项目结构检查通过"
}

# 清理之前的构建产物
clean_project() {
    print_step "清理项目..."
    flutter clean
    print_success "项目清理完成"
}

# 安装依赖
install_dependencies() {
    print_step "安装项目依赖..."
    flutter pub get
    print_success "依赖安装完成"
}

# 代码静态分析
analyze_code() {
    print_step "执行代码静态分析..."
    if flutter analyze; then
        print_success "代码分析通过"
    else
        print_warning "代码分析发现问题，但继续执行测试"
    fi
}

# 运行测试
run_tests() {
    print_step "运行测试套件..."
    
    # 创建测试结果目录
    mkdir -p test_results
    
    # 运行测试并生成覆盖率报告
    if flutter test --coverage --reporter expanded; then
        print_success "所有测试通过"
    else
        print_error "测试失败"
        exit 1
    fi
}

# 生成覆盖率报告
generate_coverage_report() {
    print_step "生成覆盖率报告..."
    
    # 检查是否有覆盖率数据
    if [ ! -f "coverage/lcov.info" ]; then
        print_warning "未找到覆盖率数据"
        return
    fi
    
    # 检查lcov是否安装
    if command -v lcov &> /dev/null; then
        # 生成HTML报告
        mkdir -p coverage/html
        genhtml coverage/lcov.info -o coverage/html --quiet
        print_success "HTML覆盖率报告生成完成: coverage/html/index.html"
        
        # 输出覆盖率摘要
        echo ""
        echo "📊 覆盖率摘要:"
        lcov --summary coverage/lcov.info
    else
        print_warning "lcov未安装，跳过HTML报告生成"
        print_warning "安装方法: brew install lcov (macOS) 或 sudo apt-get install lcov (Ubuntu)"
    fi
}

# 输出测试结果摘要
print_summary() {
    echo ""
    echo "======================================"
    echo "🎯 测试执行完成!"
    echo "======================================"
    echo ""
    
    if [ -f "coverage/lcov.info" ]; then
        echo "📈 覆盖率报告: coverage/html/index.html"
    fi
    
    echo "📝 查看详细测试结果，请运行:"
    echo "   flutter test --reporter expanded"
    echo ""
    echo "🔍 运行特定测试:"
    echo "   flutter test test/specific_test.dart"
    echo ""
    echo "📊 仅生成覆盖率报告:"
    echo "   flutter test --coverage"
    echo ""
}

# 主函数
main() {
    echo "🧪 Flutter 项目测试自动化脚本"
    echo "=================================="
    echo ""
    
    # 检查环境
    check_flutter
    check_project
    
    # 解析命令行参数
    CLEAN=false
    SKIP_ANALYSIS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean)
                CLEAN=true
                shift
                ;;
            --skip-analysis)
                SKIP_ANALYSIS=true
                shift
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --clean          清理项目后运行测试"
                echo "  --skip-analysis  跳过代码静态分析"
                echo "  --help           显示此帮助信息"
                echo ""
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                echo "使用 --help 查看可用选项"
                exit 1
                ;;
        esac
    done
    
    # 执行测试流程
    if [ "$CLEAN" = true ]; then
        clean_project
    fi
    
    install_dependencies
    
    if [ "$SKIP_ANALYSIS" = false ]; then
        analyze_code
    fi
    
    run_tests
    generate_coverage_report
    print_summary
    
    print_success "测试流程执行完成!"
}

# 捕获中断信号
trap 'echo ""; print_warning "测试被中断"; exit 1' INT

# 执行主函数
main "$@" 