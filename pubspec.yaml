name: health_assistant_app
description: "智能健康助手 - 您的个人健康管理专家"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.3

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  intl: ^0.20.2
  flutter_svg: ^2.0.10+1
  http: ^1.4.0
  http_parser: ^4.0.2
  audioplayers: ^6.1.0
  shared_preferences: ^2.5.3
  sqflite: ^2.4.2
  path: ^1.9.1
  flutter_secure_storage: ^10.0.0-beta.4
  provider: ^6.1.2
  flutter_phoenix: ^1.1.1
  # 音频录制相关依赖
  record: ^6.0.0
  permission_handler: ^12.0.0+1
  path_provider: ^2.1.4
  # 相机和图片处理相关依赖
  camera: ^0.11.0
  image_picker: ^1.1.2
  crop_your_image: ^2.0.0
  image: ^4.3.0
  # 分享功能依赖
  share_plus: ^11.0.0
  # 应用信息获取
  package_info_plus: ^8.0.2

  # Skeleton loading animation
  shimmer: ^3.0.0
  # Markdown支持
  flutter_markdown: ^0.7.4+1
  # 微信支付SDK
  fluwx: ^5.6.0

  # 城市选择器
  city_pickers: ^1.3.0

  # 定位服务
  geolocator: ^10.1.0

  # 地理编码服务
  geocoding: ^2.1.1

  # 图片缓存
  cached_network_image: ^3.4.1
  flutter_cache_manager: ^3.4.1

  # URL启动器 - 用于拨打电话等功能
  url_launcher: ^6.2.4



dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  flutter_launcher_icons: ^0.14.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  
  # 启用代码生成
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/svg/splash/
    - assets/fonts/
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: UKIJTor
      fonts:
        - asset: assets/fonts/UKIJTor.ttf
          weight: 400
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons配置
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/app_logo.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/images/app_logo.png"
  windows:
    generate: true
    image_path: "assets/images/app_logo.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/app_logo.png"
