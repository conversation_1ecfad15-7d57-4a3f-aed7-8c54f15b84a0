---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter 应用网络请求和数据处理规范，包括数据层结构、模型、数据源、仓库模式及API配置。
globs: ["**/lib/src/data/**/*.dart"]
alwaysApply: false
---
# 网络请求与数据处理规范

## 1. 数据层结构

1.1. **分层明确**: `lib/src/data/` 下必须清晰划分：
    * `models/`: 数据模型 (`*.model.dart`)。
    * `datasources/`: 数据源，包含 `remote/` (API 服务, `*.api_service.dart`) 和 `local/` (本地存储封装)。
    * `repositories/`: 数据仓库接口 (`*.repository.dart`) 及实现 (`*.repository_impl.dart`)。

## 2. 数据模型 (Models)

2.1. **纯粹 Dart 对象 (POCO/PODO)**: 模型类仅含数据属性和（反）序列化逻辑。
2.2. **不可变性推荐**: 属性 `final`，提供 `copyWith`。
2.3. **序列化**: 必须提供 `fromJson()` 命名构造函数和 `toJson()` 方法。**强烈推荐**使用 `json_serializable` (+ `freezed` 可选) 生成代码。

## 3. 数据源 (Data Sources)

3.1. **API 服务 (Remote)**:
    * **封装 HTTP 客户端**: 使用 `dio` (推荐) 或 `http`。封装客户端实例、BaseURL、拦截器、超时等。
    * **方法对应端点**: 每个 API 端点对应 Service 类中的异步方法。
    * **错误处理**: 捕获 HTTP 错误、网络错误、超时，并转换为自定义的、易于上层处理的异常 (如 `NetworkException`)。

3.2. **本地数据源 (Local)**: 封装 SharedPreferences, SQLite (`sqflite`), Hive 等操作，提供清晰 CRUD 接口。

## 4. 数据仓库 (Repositories)

4.1. **接口与实现分离**: 定义抽象 Repository 接口，具体实现在 `data/repositories/`，依赖数据源。
4.2. **单一数据来源抽象**: Repository 负责协调从何处获取数据 (缓存优先、网络回退等)，对上层屏蔽数据来源复杂性。
4.3. **错误处理聚合**: 聚合数据源错误，提供统一错误或结果类型 (如 `Either<Failure, SuccessType>`)。

## 5. 网络配置与安全

5.1. **Base URL**: 通过环境配置或常量管理，禁止硬编码。
5.2. **超时设置**: 设置合理的连接和读取超时。
5.3. **认证信息**: Token、API Keys 等敏感信息严禁硬编码，应安全存储并动态添加到请求。
5.4. **HTTPS**: 生产环境必须使用 HTTPS。

## 6. AI 助手指引

* 新增 API 交互时，AI 应在对应 API Service 中创建方法，并创建/更新相关数据模型 (推荐 `json_serializable`)。
* Repository 方法实现中，AI 应考虑数据缓存策略和统一错误处理。
* AI 生成的网络请求代码必须包含错误捕获和处理。