---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter 应用路由管理规范，强调命名路由、集中管理及强类型参数。
globs: ["**/lib/src/config/app_routes.dart", "**/*_screen.dart", "**/*_page.dart"]
alwaysApply: false
---
# 路由管理规范

## 1. 核心原则

1.1. **命名路由优先**: 强制使用**命名路由** (`Navigator.pushNamed()`)，禁止直接使用 `MaterialPageRoute` 进行匿名跳转。

1.2. **路由集中管理**: 所有路由名称和页面构建逻辑在**单一专属文件** (如 `lib/src/config/app_routes.dart`) 中统一定义。路由名使用常量。

1.3. **强类型路由参数**:
    * 避免使用 `ModalRoute.of(context)!.settings.arguments` 直接转换 `Object?`。
    * 为需参数的路由定义参数类，或使用路由包的类型安全参数功能。

## 2. 路由实现方案

2.1. **方案一: `MaterialApp` 的 `routes` 和 `onGenerateRoute`**:
    * `routes`: 用于无参数或固定参数的简单路由。
    * `onGenerateRoute`: **必须**用于处理带参数路由和未知路由的 fallback 逻辑。在其中进行参数类型检查和传递。

2.2. **方案二: 使用路由管理包 (如 `go_router`, `auto_route`)**:
    * **推荐**用于中大型应用，提供更强大功能（声明式、类型安全参数、嵌套、重定向等）。
    * 一旦选用，项目中所有导航应通过该包 API 进行。AI 生成代码时必须遵循。

## 3. 导航器键 (Navigator Keys)

3.1. **全局导航器键**: 在 `MaterialApp` 设置 `navigatorKey`，通过 DI 供全局访问，用于无 `BuildContext` 的导航（如 Service 层推送跳转）。谨慎使用。

3.2. **嵌套导航器键**: 用于 Tab 页内独立导航栈时，为各内部 `Navigator` 分配独立 `GlobalKey<NavigatorState>`。

## 4. AI 助手指引

* AI 生成导航代码时，必须使用项目中定义的命名路由常量。
* 路由需参数时，AI 应推荐或使用强类型参数。
* AI 创建新页面时，应提示在路由管理文件中注册。
* AI 应遵循项目选定的路由方案 (`onGenerateRoute` 或特定路由包)。