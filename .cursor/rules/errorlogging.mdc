---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter 应用中错误处理的核心原则、实践方法（try-catch, Result类型）以及结构化日志记录规范。
globs: ["**/*.dart"]
alwaysApply: false
---
# 错误处理与日志规范

## 1. 错误处理核心原则

1.1. **尽早捕获，清晰传递**: 在错误可能发生处尽早捕获，并向上传递定义清晰的错误或自定义异常。
1.2. **用户友好**: 对用户可见错误，必须友好展示，避免暴露原始错误堆栈。提供有意义反馈。
1.3. **不隐藏错误**: 严禁“吞掉”异常 (空 `catch` 或仅 `print()`)。所有捕获异常都应处理或重抛包装异常。
1.4. **自定义异常**: 为应用定义特定异常类 (如 `NetworkException`, `BusinessLogicException`)，携带上下文信息。

## 2. 错误处理实践

2.1. **`try-catch` 块**:
    * 用于可能抛出异常的代码块。
    * `catch (e, s)`: 同时捕获异常 `e` 和堆栈 `s`。
    * 捕获特定类型异常进行针对性处理，最后用通用 `catch` 兜底。
    ```dart
    try { /* ... */ }
    on DioException catch (e,s) { /* ... */ throw NetworkException.fromDioError(e); }
    catch (e,s) { /* ... */ throw GenericAppException(e.toString()); }
    ```

2.2. **`Result` 类型模式 (推荐)**:
    * 操作返回 `Either<Failure, Success>` (如 `dartz` 包) 或自定义 `Result<T, E>`。
    * 强制调用方显式处理成功与失败。

2.3. **Flutter 全局错误捕获 (在 `main.dart` 中设置)**:
    * **`FlutterError.onError`**: 捕获 Flutter 框架错误。记录并上报。
    * **`PlatformDispatcher.instance.onError`**: 捕获 Dart 层面未捕获的异步错误。记录并上报。
    * **`runZonedGuarded`**: 包裹 `runApp()`，捕获 Zone 内未处理的同步/异步错误。记录并上报。

## 3. 日志规范

3.1. **日志库选型**: **必须**使用结构化日志库 (推荐 `logger`)。**严禁**在生产代码中使用 `print()`。
3.2. **日志级别**: 清晰使用 VERBOSE/DEBUG, INFO, WARNING, ERROR, WTF/FATAL。
3.3. **日志内容**: 包含时间戳、级别、来源 (类名/模块)、清晰消息。错误日志附带错误对象和堆栈跟踪。
3.4. **日志输出与管理**:
    * 开发环境: 输出到控制台。
    * 生产环境: 关闭或减少低级别日志，WARNING 及以上日志上报到远程监控平台 (Sentry, Firebase Crashlytics 等)。

## 4. AI 助手指引

* AI 生成的任何可能失败的操作，**必须**包含 `try-catch`。
* AI 捕获异常时，应建议用项目日志库记录，而非 `print()`。
* AI 生成日志语句时，应遵循级别和内容规范。
* AI 可建议将常见错误封装为自定义异常。