---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter 项目自动化测试规范，涵盖单元测试、Widget测试、集成测试的核心原则、工具和实践。
globs: ["**/test/**", "**/integration_test/**"]
alwaysApply: false
---
# 测试规范

## 1. 测试核心原则

1.1. **质量基石**: **必须**为项目编写自动化测试，确保质量、减少回归、提升重构信心。
1.2. **测试金字塔**: 合理分配单元测试 (最多)、Widget 测试 (其次)、集成测试 (较少) 的比例。
1.3. **可测试性设计**: 编码时即考虑可测试性 (依赖注入、职责分离)。
1.4. **隔离与可重复**: 测试用例相互独立，结果可重复。

## 2. 单元测试 (Unit Tests)

2.1. **位置**: `test/<path_to_source_file>/<file_name>_test.dart` (镜像 `lib/` 结构)。
2.2. **测试对象**: 数据模型、仓库、服务/数据源、状态管理单元 (BLoC/Cubit等)、工具类。
2.3. **Mocking**: **必须**使用 Mocking 框架 (`mockito` 或 `mocktail`) 模拟外部依赖，隔离被测单元。
2.4. **断言**: 使用 `package:test/test.dart` 的 `expect()`，清晰表达期望。测试边界、正常和异常情况。
2.5. **分组与描述**: 使用 `group()` 组织相关测试，`test()` 提供清晰描述。
    ```dart
    // 示例 (UserServiceImpl 依赖 AuthApi)
    // test/data/repositories/user_service_test.dart
    import 'package:flutter_test/flutter_test.dart';
    import 'package:mockito/annotations.dart';
    import 'package:mockito/mockito.dart';
    // Assume User, UserService, AuthApi, UserNotFoundException exist
    // import 'user_service_test.mocks.dart'; // Generated by build_runner

    // @GenerateMocks([AuthApi]) // Annotation for build_runner
    void main() {
      // late UserServiceImpl userService;
      // late MockAuthApi mockAuthApi;

      // setUp(() {
      //   mockAuthApi = MockAuthApi();
      //   userService = UserServiceImpl(authApi: mockAuthApi);
      // });

      // group('UserService - getUser', () {
      //   test('returns User on successful fetch', () async {
      //     when(mockAuthApi.fetchUserData('123')).thenAnswer((_) async => {'id': '123', 'name': 'Test User'});
      //     final user = await userService.getUser('123');
      //     expect(user, isA<User>());
      //     expect(user.name, 'Test User');
      //   });
      //   test('throws UserNotFoundException on API 404', () async {
      //     when(mockAuthApi.fetchUserData('invalid_id')).thenThrow(ApiException(statusCode: 404));
      //     expect(() => userService.getUser('invalid_id'), throwsA(isA<UserNotFoundException>()));
      //   });
      // });
    }
    ```
    *注意: 上述 Mockito 示例需要运行 `flutter pub run build_runner build` 来生成 `*.mocks.dart` 文件。*

## 3. Widget 测试 (Widget Tests / Component Tests)

3.1. **位置**: 同单元测试。
3.2. **测试对象**: 单个 Widget 或一小组相关 Widgets。
3.3. **核心 API**: `flutter_test` 包的 `testWidgets()`, `WidgetTester`, `find`, `expect` 及 Matchers (`findsOneWidget`, `matchesGoldenFile`等)。
3.4. **测试内容**: Widget 是否按预期渲染、用户交互是否触发正确行为/状态变更、特定条件下外观 (黄金文件测试谨慎使用)。
3.5. **`pumpWidget()`**: 构建渲染被测 Widget。
3.6. **`pump()` / `pumpAndSettle()`**: 触发帧渲染，处理动画或异步 UI 更新。
3.7. **依赖处理**: Mock Widget 依赖的服务或状态管理单元，通过构造函数或 Provider 注入。

## 4. 集成测试 (Integration Tests)

4.1. **位置**: 项目根下 `integration_test/` 目录，文件以 `_test.dart` 结尾。
4.2. **测试对象**: 完整用户流程、跨模块交互、与外部服务 (Firebase, 原生插件) 的集成。
4.3. **核心 API**: `integration_test` 包，语法类似 Widget 测试，但在真实设备/模拟器上运行整个应用。
4.4. **测试内容**: 模拟真实用户操作场景 (登录、购买等)，验证导航、状态、数据持久化、与后端/原生功能交互。
4.5. **运行**: `flutter test integration_test`。
4.6. **耗时**: 比单元/Widget 测试慢，专注关键流程。

## 5. 测试覆盖率

5.1. **目标**: 设定合理覆盖率目标 (如单元测试关键逻辑 >70-80%)。
5.2. **工具**: `flutter test --coverage` 生成报告，用 `genhtml` (lcov) 转为 HTML。
5.3. **持续集成 (CI)**: CI/CD 流程中自动运行所有测试并检查覆盖率。

## 6. AI 助手指引

* AI 生成新业务逻辑时，应主动建议为其编写单元测试，并生成测试骨架/Mock。
* AI 生成新 Widget 时，应主动建议为其编写 Widget 测试，覆盖核心渲染和交互。
* AI 生成测试代码时，应遵循分组、清晰描述、正确 Mock/断言的原则。