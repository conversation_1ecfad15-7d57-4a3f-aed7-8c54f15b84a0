---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter 中处理异步操作（Future, Stream, Isolate）的核心原则和最佳实践，强调UI不阻塞和状态管理。
globs: ["**/*.dart"]
alwaysApply: false
---
# 异步处理规范

## 1. 核心原则

1.1. **UI 线程不阻塞**: 任何可能耗时的操作（网络、IO、复杂计算）**必须**在异步任务中执行，严禁阻塞 UI 线程。
1.2. **状态明确**: 异步操作相关的 UI **必须**清晰处理和展示加载中 (Loading)、成功/有数据 (Success/Data)、失败/错误 (Error)、空状态 (Empty)。
1.3. **资源管理**: 异步操作资源 (如 `StreamSubscription`, `Timer`) **必须**在不再需要时 (如 Widget `dispose` 时) 正确取消或释放，防内存泄漏。

## 2. `Future` 的使用

2.1. **`async / await`**: 默认使用此方式处理异步序列操作。AI 生成异步函数时应默认使用。
2.2. **`FutureBuilder`**: Widget 树中根据 `Future` 状态构建 UI 的标准方式。**必须**处理 `snapshot.connectionState` (如 `waiting`, `done`) 和 `snapshot.hasError`, `snapshot.hasData`。
    ```dart
    FutureBuilder<MyData>(
      future: _loadMyDataFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) { /* Show loading */ }
        else if (snapshot.hasError) { /* Show error */ }
        else if (snapshot.hasData) { /* Show data */ }
        else { /* Show no data or initial state */ }
      },
    )
    ```
2.3. **错误处理**: `async` 函数内用 `try-catch` 捕获 `Future` 异常。`FutureBuilder` 通过 `snapshot.hasError` 和 `snapshot.error` 提供错误。
2.4. **禁止在 `build` 中直接调用创建 `Future` 的函数**: 应将 `Future` 对象存入 `State` 成员变量，在 `initState` 或适当逻辑中初始化一次。

## 3. `Stream` 的使用

3.1. **`StreamBuilder`**: 根据 `Stream` 发射数据/错误动态构建 UI。**必须**处理 `snapshot.connectionState`, `snapshot.hasError`, `snapshot.hasData`。
3.2. **订阅管理 (`StreamSubscription`)**: 若在 `State` 中手动 `listen` Stream，**必须**在 `dispose()` 中 `cancel()` 订阅。`StreamBuilder` 自动管理。
3.3. **`StreamController`**: 创建自定义 `Stream` 时使用。**必须**在不再需要时调用 `streamController.close()`。

## 4. `Isolate` (CPU 密集型任务)

4.1. **适用场景**: 纯粹的、与平台通道无关的 CPU 密集型计算，可使用 `Isolate` 避免阻塞 UI 线程和主 Isolate。
4.2. **`compute()` 函数**: Flutter 提供的简化 `Isolate` 接口。
4.3. **谨慎使用**: 创建和管理 Isolate 有开销，仅在确认存在 CPU 瓶颈且任务适合并行时用。

## 5. AI 助手指引

* 需执行耗时操作并更新 UI 时，AI 应建议 `FutureBuilder`/`StreamBuilder` (或集成状态管理的类似组件)。
* AI 生成的 `FutureBuilder`/`StreamBuilder` 代码**必须**包含对所有连接和错误状态的完整处理。
* AI 建议手动订阅 `Stream` 时，**必须**同时提醒并在 `dispose()` 中添加取消订阅。
* AI 应指导用户将返回 `Future` 的函数调用放 `initState` 等仅执行一次的逻辑中。