---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter Widget 设计的核心原则，包括组合、单一职责、Stateless/Stateful 选择、性能优化及参数回调。
globs: ["**/lib/src/presentation/**/*.dart", "**/lib/src/features/**/*.dart", "**/lib/src/modules/**/*.dart", "**/lib/src/common/widgets/**/*.dart"]
alwaysApply: false
---
# Widget 设计原则

## 1. Widget 核心思想

1.1. **组合优于继承**: 优先通过组合小而专一的 Widgets 构建复杂 UI。
1.2. **单一职责**: 每个 Widget 应尽可能只负责 UI 的一小部分或一个特定功能。
1.3. **UI 即代码**: 通过声明式 Widget 树表达 UI 结构、布局和样式。

## 2. StatelessWidget vs StatefulWidget

2.1. **StatelessWidget 优先**: 默认使用 `StatelessWidget`。仅当 Widget 需管理自身内部、会随时间变化且影响 UI 外观的状态时，才用 `StatefulWidget`。
2.2. **StatefulWidget 状态管理**: 状态在其 `State` 对象中管理。在 `dispose()` 方法中正确释放资源 (取消订阅、销毁控制器等)。

## 3. 构建高性能 Widgets

3.1. **`build` 方法纯粹**: `build` 方法应无副作用，仅根据属性和状态返回 Widget 树。避免执行耗时操作。
3.2. **`const` 构造与实例**: **强制尽可能**为 Widget 提供 `const` 构造函数，并在构建 Widget 树时使用 `const` 关键字创建实例，以利用 Flutter 的缓存和性能优化。
3.3. **精确重建**:
    * 确保 `setState()` 仅在必要时调用且影响范围最小。
    * 对复杂页面，拆分需频繁更新的部分为独立 Widget，或使用状态管理方案提供的精确重建机制 (`Consumer`, `Selector`, `BlocBuilder`)。
3.4. **避免在 `build` 中重复创建不变对象**: 如 `EdgeInsets`, `TextStyle`，应提取为 `final` 字段或 `static const`。

## 4. Widget 拆分与复用

4.1. **提取可复用组件**: UI 的某部分在多处使用，或 Widget 变得过于复杂（`build` 方法过长），**必须**提取为独立的自定义 Widget。自定义 Widget 应职责清晰，参数接口明确。
4.2. **保持 Widget 树扁平化**: 避免不必要的 Widget 嵌套，审视是否可使用 `Container` 等的复合属性代替多层包裹。

## 5. 参数与回调

5.1. **`Key` 的使用**: 对列表元素顺序可能改变或需跨树保持状态的 Widget (尤其 `StatefulWidget`)，提供唯一 `Key` (`ValueKey`, `ObjectKey`)。公共 Widget 构造函数应接受 `Key? key` 并传递给 `super`。
5.2. **回调函数**: 子 Widget 与父 Widget 通信通过回调函数 (`VoidCallback`, `ValueChanged<T>`) 实现，签名应清晰。

## 6. AI 助手指引

* AI 生成 UI 时优先创建 `StatelessWidget`。
* 需状态管理时，AI 应建议将逻辑移出 Widget 或创建 `StatefulWidget` 并正确管理其 `State`。
* AI 生成的 Widget 代码**必须**尽可能使用 `const`。
* AI 发现 `build` 方法复杂或冗长时，应主动建议拆分。
* AI 创建自定义 Widget 时，应确保参数接口清晰并包含 `Key? key`。