---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter 开发工具链、IDE（Android Studio, VS Code）、AI编程助手（如Cursor）及版本控制（Git）的使用规范与最佳实践。
globs: ["**/.*"] # Broad, or could be manual
alwaysApply: false
---
# 工具链与 IDE (含 AI 助手) 使用规范

## 1. Flutter SDK 与版本管理

1.1. **版本一致性**: 团队开发确保所有成员使用相同主次版本 (Major.Minor) 的 Flutter SDK。项目 `pubspec.yaml` 的 Dart SDK `environment` 约束应反映此约定。
1.2. **版本管理工具 (推荐 `FVM`)**: 使用 Flutter 版本管理工具 (如 FVM) 实现项目级 SDK 版本指定与团队统一。AI 可在项目初始化时提及。
1.3. **定期升级**: 定期评估并升级 Flutter SDK 稳定版，获取新特性、性能改进和 Bug 修复 (充分测试后)。

## 2. IDE (集成开发环境)

2.1. **推荐**: Android Studio 或 Visual Studio Code (VS Code)，两者均有优秀 Flutter/Dart 插件。
2.2. **Flutter & Dart 插件**: **必须**安装并保持更新官方插件。AI 助手 (如 Cursor) 也依赖其核心功能。
2.3. **IDE 配置**:
    * **自动格式化**: 配置 IDE 保存时自动运行 `dart format`。
    * **静态分析**: 确保 IDE 正确加载并显示 `analysis_options.yaml` 定义的 Lint 规则警告/错误。
    * **快捷键**: 熟悉并利用 IDE 提供的 Flutter 相关快捷键。

## 3. AI 编程助手 (如 Cursor) 使用规范

3.1. **明确指令与上下文**: 向 AI 提问或要求代码时，提供清晰、具体需求和充足上下文 (相关代码、设计模式、功能模块)。可明确指示 AI 遵循本文档规范。
3.2. **代码审查与理解**: **严禁**盲目复制粘贴 AI 代码。**必须**阅读理解每一行，确保符合需求、规范和最佳实践。开发者负最终责任。
3.3. **逐步生成与迭代**: 对复杂功能，引导 AI 逐步生成，小步迭代和测试。
3.4. **学习与反馈**: 利用 AI 解释概念。若 AI 代码不符规范，尝试提供反馈并要求修正。
3.5. **辅助角色**: 定位 AI 为“副驾驶”或高级代码片段生成器，非完全替代开发者思考。
3.6. **隐私与安全**: 与云端 AI 交互时，注意不粘贴/上传敏感信息 (API Keys, 私有业务逻辑等)，除非信任其安全策略。

## 4. 构建与调试工具

4.1. **Flutter DevTools**: 熟练使用进行调试、性能分析 (CPU, 内存)、Widget 审查、网络监控等。
4.2. **IDE 调试器**: 充分利用断点调试、变量查看、表达式求值等功能。
4.3. **构建命令**: 了解 `flutter run` (debug), `flutter build apk/appbundle/ios --release` 等命令及不同构建模式区别。

## 5. 版本控制 (Git)

5.1. **强制使用**: 所有项目代码**必须**使用 Git 进行版本控制。
5.2. **分支策略**: 遵循团队统一策略 (如 Git Flow, GitHub Flow)。
5.3. **Commit Message**: 编写清晰、规范的 Commit Message (推荐 Conventional Commits)。
5.4. **Code Review**: 重要代码更改必须经过 Code Review。