---
description: 
globs: 
alwaysApply: false
---
---
description: pubspec.yaml 文件的核心配置、依赖管理、Flutter特定配置（资源、字体）及相关文件结构约定。
globs: ["**/pubspec.yaml", "**/assets/**", "**/fonts/**"]
alwaysApply: false
---
# pubspec.yaml 与资源管理规范

## 1. `pubspec.yaml` 核心配置

1.1. **项目元数据**:
    * `name`: 项目名称，小写蛇形命名法 (`snake_case`)。
    * `description`: 项目简短描述。
    * `version`: 项目版本号 (`主.次.修订+构建号`)。
    * `publish_to: 'none'`: 若不发布到 pub.dev，必须设置。

1.2. **环境 (Environment)**:
    * `environment: sdk: '>=3.0.0 <4.0.0'` (示例)，指定兼容的 Dart SDK 版本范围。

## 2. 依赖管理 (Dependencies)

2.1. **`dependencies:`**: 应用**运行时**必需的包。
2.2. **`dev_dependencies:`**: 仅在**开发和测试阶段**使用的包 (如 `flutter_lints`, `build_runner`)。
2.3. **版本约束**:
    * **推荐 `^版本号` (Caret Syntax)**，允许兼容的小版本更新。
    * 固定版本或范围约束用于特殊情况。
    * Git 依赖和本地路径依赖按需使用。
2.4. **`dependency_overrides:`**: 仅在绝对必要时临时使用，不应长期存在。
2.5. **`flutter pub get`**: 修改依赖后**必须**执行此命令。AI 助手应提示。

## 3. Flutter 特定配置 (`flutter:`)

3.1. **`uses-material-design: true`**: 通常默认，确保 Material Design 图标和主题可用。

3.2. **资源 (Assets)**:
    * 所有静态资源 (图片、JSON等) **必须**在此处声明。
    * **路径约定**: 资源文件放项目根下 `assets/` 目录，内按类型建子目录 (如 `assets/images/`, `assets/icons/`, `assets/data/`)。
    * **声明方式 (推荐按目录)**:
        ```yaml
        flutter:
          assets:
            - assets/images/ # 声明整个 images 目录
            - assets/data/
        # - assets/images/logo.png # 也可按文件声明
        ```
    * **分辨率适配**: 可使用 `2.0x/`, `3.0x/` 子目录存放高分图，并在 `pubspec.yaml` 中声明基础路径。
    * AI 助手在用户需使用新资源时，必须提醒在此处声明。

3.3. **字体 (Fonts)**:
    * 自定义字体文件 (`.ttf`, `.otf`) **必须**在此处声明。
    * 字体文件放项目 `assets/fonts/` 或顶层 `fonts/` 目录。
    * **声明格式**:
        ```yaml
        flutter:
          fonts:
            - family: MyCustomFont
              fonts:
                - asset: assets/fonts/MyCustomFont-Regular.ttf
                - asset: assets/fonts/MyCustomFont-Bold.ttf
                  weight: 700
                - asset: assets/fonts/MyCustomFont-Italic.ttf
                  style: italic
        ```
    * 代码中通过 `fontFamily: 'MyCustomFont'` 使用。

## 4. AI 助手指引

* 添加新包时，AI 应加入正确依赖区并提示 `flutter pub get`。
* 使用新资源/字体时，AI 必须提醒用户按规范放置文件并在 `pubspec.yaml` 中声明。