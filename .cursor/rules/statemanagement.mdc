---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter 应用状态管理的核心原则、推荐方案模式、实现指南及反模式规避。
globs: ["**/lib/src/presentation/**/*.dart", "**/lib/src/features/**/*.dart", "**/lib/src/modules/**/*.dart"]
alwaysApply: false
---
# 状态管理规范

## 1. 核心原则

1.1. **选型统一**: 项目必须选择并统一使用一种主流状态管理方案 (如 Provider, Riverpod, BLoC/Cubit)。AI 应遵循项目选型。

1.2. **职责分离 (UI vs Logic)**:
    * UI (Widgets) 仅负责展示与用户输入传递。
    * 业务逻辑、状态变更、数据获取等**严禁**在 Widget 中处理，必须由专门的状态管理单元负责。

1.3. **数据流向清晰**: 推荐单向数据流，使状态变更可追溯。UI 事件 -> 状态单元处理 -> 状态变更 -> UI 重建。

1.4. **作用域最小化**: 状态管理作用域应尽可能小，避免不必要的全局状态。

## 2. 状态管理单元实现

2.1. **不可变状态**: 状态对象推荐使用不可变性 (`immutable`)，通过创建新状态对象进行变更 (如使用 `copyWith` 或 `freezed`)。

2.2. **事件/行为封装**: 用户操作或业务事件应封装成状态管理单元的公共方法或事件 (Events in BLoC)。

2.3. **副作用管理**: 网络请求、数据库操作等副作用在状态管理单元中异步处理，并管理加载、成功、失败等UI状态。

2.4. **依赖注入**: 外部依赖（Repository, Service）应通过构造函数注入到状态管理单元。

## 3. UI (Widget) 层交互

3.1. **状态获取**: Widget 通过相应机制 (`context.watch`, `ref.watch`, `BlocBuilder`) 监听和获取状态，且仅监听必要状态片段。

3.2. **事件派发**: Widget 通过 `context.read`, `ref.read().notifier`, `bloc.add()` 等调用状态管理单元的方法或派发事件。

3.3. **UI 逻辑最小化**: Widget 的 `build` 方法保持简洁，主要负责状态到 UI 的映射。复杂业务判断移至状态管理单元。

## 4. 避免反模式

4.1. **禁止在 `build` 方法中执行副作用**。
4.2. **谨慎使用全局状态**，优先局部状态。
4.3. **Widget 禁止直接访问 Repository/Service**，应通过状态管理单元。

## 5. AI 助手指引

* 生成新功能时，AI 应遵循项目当前状态管理方案。
* 重构时，AI 应协助将业务逻辑从 Widgets 剥离。
* 添加状态/事件时，AI 应确保遵循状态不可变性和副作用管理原则。