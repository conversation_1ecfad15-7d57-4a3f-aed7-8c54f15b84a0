---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter 项目代码质量、静态分析、编码风格、命名约定及注释文档的核心规范。
globs: ["**/*.dart", "**/analysis_options.yaml"]
alwaysApply: false
---
# 代码规范与质量指南

## 1. 静态分析与 Linting

1.1. **`analysis_options.yaml` 强制配置**:
    * 项目中**必须**包含 `analysis_options.yaml` 文件。
    * **推荐 Lint 集**: 至少启用 `flutter_lints`，强烈推荐 `very_good_analysis` 或 `lints` 并按需自定义。
    ```yaml
    # 示例:
    include: package:very_good_analysis/analysis_options.yaml # 或 flutter_lints/flutter.yaml
    # linter:
    #   rules:
    #     # 在此基础上启用/禁用或配置特定规则
    #     avoid_print: true # 生产代码中避免 print
    #     public_member_api_docs: true # 公共 API 必须有文档注释
    #     lines_longer_than_80_chars: false # 考虑团队习惯调整
    #     always_specify_types: true
    ```
1.2. **零警告/错误策略**: 所有提交的代码，在 `flutter analyze` 下**不应**有任何 errors 或 warnings (除非团队明确忽略的特定情况)。AI 生成代码必须符合 Lint 规则。

## 2. 编码风格与格式化

2.1. **官方格式化**: 所有 Dart 代码**必须**使用 `dart format .` (或 IDE 保存时自动格式化) 进行格式化。
2.2. **行长度**: 推荐最大行长度 80-100 字符。
2.3. **导入顺序**: `dart:*`, `package:*` (外部包), 项目相对路径 (`import 'src/...'`)。各组内按字母排序。项目内文件导入使用相对路径。
2.4. **UI设计**: 但凡涉及到UI改动必须要匹配各种尺寸的手机,不要写静态页面,保证是响应式布局设计

## 3. 命名约定

3.1. **类/枚举/类型定义/扩展**: `UpperCamelCase`。
3.2. **方法/函数/变量/参数**: `lowerCamelCase`。
3.3. **常量**: Flutter 风格 `kLowerCamelCase` (如 `kDefaultPadding`) 或通用 Dart 风格 `UPPER_SNAKE_CASE` (团队统一)。
3.4. **文件名 (Dart)**: `snake_case.dart`。
3.5. **私有成员/类**: 以下划线 `_` 开头。

## 4. 注释与文档 (Dartdoc)

4.1. **公共 API**: 所有公共类、方法、函数、属性**必须**有 Dartdoc 注释 (`///`)，清晰说明其用途、参数、返回值和异常。
4.2. **实现注释**: 对复杂或非直观逻辑使用行内 (`//`) 或块 (`/* ... */`) 注释。避免过度注释。
4.3. **`TODO` / `FIXME`**: 使用标准格式 `// TODO(usernameOrTicket): Description`，并定期处理。

## 5. 代码可读性与简洁性

5.1. **避免深度嵌套**: 通过提取方法/Widget 或卫语句优化。
5.2. **函数/方法单一职责**: 保持简短。
5.3. **清晰的布尔逻辑**: 复杂布尔表达式提取为命名良好的变量/函数。
5.4. **善用 Dart 特性**: 集合操作, 级联运算符 (`..`), 空安全 (`?`, `!`, `late`, `required`), 扩展方法。
5.5. **避免魔法值**: 使用具名常量。

## 6. AI 助手指引

* AI 生成的代码必须严格遵守 `analysis_options.yaml` 及本规范。
* 生成公共 API 时，AI 应主动包含 Dartdoc。
* AI 不应生成含 `print()` 的生产代码，应建议使用日志框架。