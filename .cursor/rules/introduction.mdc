---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter 项目开发核心规范的整体介绍、目的、核心原则及规范结构。
globs: always
alwaysApply: false
---
# Flutter 项目开发核心规范简介

## 1. 目的

本文档旨在为基于 Flutter 的项目开发提供一套统一、高效、可维护的核心规范与最佳实践指南。这些规范适用于项目团队成员及 AI 编程助手（如 Cursor），以确保代码质量、提升开发效率、促进团队协作，并构建可扩展、高性能的应用。

## 2. 核心原则

* **模块化与高内聚低耦合**: 按功能或分层组织代码，避免单一文件承载过多职责。
* **代码清晰与可维护性**: 编写易于理解、修改和测试的代码。
* **一致性**: 在整个项目中遵循统一的编码风格、命名约定和设计模式。
* **性能意识**: 关注应用性能，避免不必要的重建和资源浪费。
* **可测试性**: 编写易于测试的代码，并确保关键路径有足够的测试覆盖。
* **拥抱自动化**: 利用 Flutter 工具链、静态分析和自动化测试提升效率和质量。

## 3. 适用范围

本规范集适用于所有新启动及正在进行中的 Flutter 项目。AI 助手在提供代码建议、重构、审查等辅助时，应优先遵循本规范。

## 4. 规范结构

本规范集将按主题划分为多个规则文件，每个文件聚焦于特定的开发领域（例如：项目结构、状态管理、代码风格等）。AI 助手在提供建议时，应明确指出所依据的规范条款（如果适用）。