---
description: 
globs: 
alwaysApply: false
---
---
description: Flutter 项目的推荐目录结构、文件组织方式及核心文件职责。强调模块化和代码分离。
globs: ["**/lib/**/*.dart", "**/test/**", "**/pubspec.yaml", "**/analysis_options.yaml"]
alwaysApply: false
---
# 项目结构与代码组织规范

## 1. `lib/` 目录核心规范

1.1. **强制结构化**: `lib/` 目录必须进行结构化分层或按功能模块组织。严禁将所有业务逻辑和 UI 代码堆积在少数几个文件中或直接在 `lib/` 根下。

1.2. **推荐的 `lib/` 顶级结构**:
    * `main.dart`: **仅限**应用初始化 (服务、DI)、`runApp()` 调用。
    * `app.dart` (或 `my_app.dart`): 存放应用的根 Widget (通常是 `MyApp`)。
    * `src/` (或 `core/` + `features/`): 核心代码区。
        * `config/`: 应用配置 (路由 `app_routes.dart`, 主题 `app_theme.dart`, 常量 `app_constants.dart`, 颜色 `app_colors.dart`)。
        * `common/` (或 `shared/`, `utils/`): 应用全局共享组件 (通用 Widgets `widgets/`, 工具类 `utils/`, 自定义异常 `exceptions/`)。
        * `data/`: 数据处理层 (模型 `models/`, 仓库 `repositories/`, 数据源 `datasources/` (含 `remote/` API 服务和 `local/` 本地存储))。
        * `domain/` (可选，大型项目): 领域实体 `entities/`, 用例 `usecases/`。
        * `presentation/` (或 `features/`, `modules/`): 表现层，按功能模块划分，每个模块包含 `screens/` (或 `pages/`), `widgets/`, 及状态管理逻辑 (如 `bloc/`, `controller/`)。
        * `di/` (或 `injection/`): 依赖注入配置。

1.3. **文件命名**: Dart 文件名使用 `snake_case.dart`。类名使用 `UpperCamelCase`。

1.4. **导出文件 (Barrel Files)**:
    * 在每个主要功能目录或层级下，使用导出文件 (如 `feature_a.dart`) 统一导出公共组件/类，简化导入路径。

## 2. 其他顶级目录规范

2.1. **平台特定目录 (`android/`, `ios/`等)**: 仅用于平台特定配置和原生代码集成。谨慎修改。

2.2. **`test/` 目录**: 结构应镜像 `lib/`，测试文件名以 `_test.dart` 结尾。

2.3. **`.gitignore`**: 必须包含 `.dart_tool/`, `build/`, IDE 构建产物。

2.4. **`analysis_options.yaml`**: 必须存在，配置严格的 Lint 规则 (推荐 `flutter_lints` 或 `very_good_analysis`)。AI 应参考此文件。

2.5. **`README.md`**: 必须包含项目简介、搭建、运行命令等。