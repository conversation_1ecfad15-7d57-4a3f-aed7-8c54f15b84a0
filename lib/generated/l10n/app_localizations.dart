import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ug.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ug'),
    Locale('zh'),
  ];

  /// 设置页面标题
  ///
  /// In zh, this message translates to:
  /// **'设置'**
  String get settingsTitle;

  /// 语言设置选项
  ///
  /// In zh, this message translates to:
  /// **'语言设置'**
  String get languageSettings;

  /// 选择语言
  ///
  /// In zh, this message translates to:
  /// **'选择语言'**
  String get selectLanguage;

  /// 选择源语言标题
  ///
  /// In zh, this message translates to:
  /// **'选择源语言'**
  String get selectSourceLanguage;

  /// 选择目标语言标题
  ///
  /// In zh, this message translates to:
  /// **'选择目标语言'**
  String get selectTargetLanguage;

  /// 中文语言名称
  ///
  /// In zh, this message translates to:
  /// **'中文'**
  String get languageChinese;

  /// 英文语言名称
  ///
  /// In zh, this message translates to:
  /// **'English'**
  String get languageEnglish;

  /// 维吾尔语语言名称
  ///
  /// In zh, this message translates to:
  /// **'ئۇيغۇرچە'**
  String get languageUyghur;

  /// 取消按钮
  ///
  /// In zh, this message translates to:
  /// **'取消'**
  String get cancel;

  /// 确认按钮
  ///
  /// In zh, this message translates to:
  /// **'确认'**
  String get confirm;

  /// 显示设置分组标题
  ///
  /// In zh, this message translates to:
  /// **'显示设置'**
  String get displaySettings;

  /// 暗色模式设置
  ///
  /// In zh, this message translates to:
  /// **'暗色模式'**
  String get darkMode;

  /// 暗色模式设置描述
  ///
  /// In zh, this message translates to:
  /// **'在浅色和暗色主题之间切换'**
  String get darkModeDescription;

  /// 跟随系统主题设置
  ///
  /// In zh, this message translates to:
  /// **'跟随系统主题'**
  String get followSystemTheme;

  /// 跟随系统主题设置描述
  ///
  /// In zh, this message translates to:
  /// **'自动适应系统的浅色/暗色设置'**
  String get followSystemThemeDescription;

  /// 字体大小设置
  ///
  /// In zh, this message translates to:
  /// **'字体大小'**
  String get fontSize;

  /// 字体大小设置描述
  ///
  /// In zh, this message translates to:
  /// **'调整应用内字体大小'**
  String get fontSizeDescription;

  /// 语言设置描述
  ///
  /// In zh, this message translates to:
  /// **'选择应用显示语言'**
  String get languageSettingsDescription;

  /// 其他设置分组
  ///
  /// In zh, this message translates to:
  /// **'其他'**
  String get other;

  /// 帮助与反馈
  ///
  /// In zh, this message translates to:
  /// **'帮助与反馈'**
  String get helpAndFeedback;

  /// 帮助与反馈描述
  ///
  /// In zh, this message translates to:
  /// **'常见问题和意见反馈'**
  String get helpAndFeedbackDescription;

  /// 关于我们
  ///
  /// In zh, this message translates to:
  /// **'关于我们'**
  String get aboutUs;

  /// 关于我们描述
  ///
  /// In zh, this message translates to:
  /// **'版本信息和公司介绍'**
  String get aboutUsDescription;

  /// 退出登录
  ///
  /// In zh, this message translates to:
  /// **'退出登录'**
  String get logout;

  /// 退出登录描述
  ///
  /// In zh, this message translates to:
  /// **'清除登录状态并返回登录页'**
  String get logoutDescription;

  /// 关于对话框标题
  ///
  /// In zh, this message translates to:
  /// **'关于我们'**
  String get aboutDialogTitle;

  /// 关于对话框内容
  ///
  /// In zh, this message translates to:
  /// **'健康助手 v1.0.0\n\n一款专业的AI健康导游应用'**
  String get aboutDialogContent;

  /// 健康助手页面标题
  ///
  /// In zh, this message translates to:
  /// **'健康助手'**
  String get homeTitle;

  /// 历史页面标题
  ///
  /// In zh, this message translates to:
  /// **'历史'**
  String get historyTitle;

  /// 健康助手页面标题
  ///
  /// In zh, this message translates to:
  /// **'健康助手'**
  String get aiTourGuideTitle;

  /// 智能导游标签
  ///
  /// In zh, this message translates to:
  /// **'智能导游'**
  String get smartGuide;

  /// 欢迎来到前缀
  ///
  /// In zh, this message translates to:
  /// **'欢迎您来到'**
  String get welcomeTo;

  /// 分享应用菜单项
  ///
  /// In zh, this message translates to:
  /// **'分享应用'**
  String get shareApp;

  /// 分销管理菜单项
  ///
  /// In zh, this message translates to:
  /// **'分销管理'**
  String get distributionManagement;

  /// 我方语言
  ///
  /// In zh, this message translates to:
  /// **'我方语言'**
  String get myLanguage;

  /// 对方语言
  ///
  /// In zh, this message translates to:
  /// **'对方语言'**
  String get theirLanguage;

  /// 源语言标签
  ///
  /// In zh, this message translates to:
  /// **'源'**
  String get sourceLanguage;

  /// 目标语言标签
  ///
  /// In zh, this message translates to:
  /// **'目标'**
  String get targetLanguage;

  /// 底部导航健康助手
  ///
  /// In zh, this message translates to:
  /// **'健康助手'**
  String get bottomNavHome;

  /// 底部导航历史
  ///
  /// In zh, this message translates to:
  /// **'历史'**
  String get bottomNavHistory;

  /// 底部导航健康助手
  ///
  /// In zh, this message translates to:
  /// **'健康助手'**
  String get bottomNavAiGuide;

  /// 底部导航列表
  ///
  /// In zh, this message translates to:
  /// **'列表'**
  String get bottomNavSearch;

  /// 底部导航我的
  ///
  /// In zh, this message translates to:
  /// **'我的'**
  String get bottomNavProfile;

  /// 底部导航设置
  ///
  /// In zh, this message translates to:
  /// **'设置'**
  String get bottomNavSettings;

  /// 列表页面标题
  ///
  /// In zh, this message translates to:
  /// **'列表'**
  String get searchPageTitle;

  /// 搜索框提示文本
  ///
  /// In zh, this message translates to:
  /// **'搜索医生或产品...'**
  String get searchHint;

  /// 医生标签页
  ///
  /// In zh, this message translates to:
  /// **'医生'**
  String get doctorTab;

  /// 产品标签页
  ///
  /// In zh, this message translates to:
  /// **'产品'**
  String get productTab;

  /// 无医生信息提示
  ///
  /// In zh, this message translates to:
  /// **'暂无医生信息'**
  String get noDoctorsAvailable;

  /// 无产品信息提示
  ///
  /// In zh, this message translates to:
  /// **'暂无产品信息'**
  String get noProductsAvailable;

  /// 无搜索结果提示
  ///
  /// In zh, this message translates to:
  /// **'未找到相关结果'**
  String get noSearchResults;

  /// 输入框提示文本
  ///
  /// In zh, this message translates to:
  /// **'请输入您的问题...'**
  String get inputHint;

  /// 语音输入提示
  ///
  /// In zh, this message translates to:
  /// **'点击说话'**
  String get tapToSpeak;

  /// 语音识别中
  ///
  /// In zh, this message translates to:
  /// **'正在听...'**
  String get listening;

  /// 处理中状态文字
  ///
  /// In zh, this message translates to:
  /// **'处理中...'**
  String get processing;

  /// 清空按钮
  ///
  /// In zh, this message translates to:
  /// **'清空'**
  String get clearHistory;

  /// 复制按钮
  ///
  /// In zh, this message translates to:
  /// **'复制'**
  String get copy;

  /// 分享按钮
  ///
  /// In zh, this message translates to:
  /// **'分享'**
  String get share;

  /// 播放按钮
  ///
  /// In zh, this message translates to:
  /// **'播放'**
  String get play;

  /// 暂停按钮
  ///
  /// In zh, this message translates to:
  /// **'暂停'**
  String get pause;

  /// 重试按钮
  ///
  /// In zh, this message translates to:
  /// **'重试'**
  String get retry;

  /// 错误对话框标题
  ///
  /// In zh, this message translates to:
  /// **'错误'**
  String get error;

  /// 网络错误提示
  ///
  /// In zh, this message translates to:
  /// **'网络连接失败，请检查网络设置'**
  String get networkError;

  /// 权限被拒绝提示
  ///
  /// In zh, this message translates to:
  /// **'权限被拒绝'**
  String get permissionDenied;

  /// 相机权限提示
  ///
  /// In zh, this message translates to:
  /// **'需要相机权限'**
  String get cameraPermissionRequired;

  /// 需要麦克风权限才能录音
  ///
  /// In zh, this message translates to:
  /// **'需要麦克风权限才能录音'**
  String get microphonePermissionRequired;

  /// 应用标题
  ///
  /// In zh, this message translates to:
  /// **'健康助手'**
  String get appTitle;

  /// 欢迎信息
  ///
  /// In zh, this message translates to:
  /// **'欢迎使用健康助手'**
  String get welcomeMessage;

  /// 欢迎页面描述
  ///
  /// In zh, this message translates to:
  /// **'您的专属AI健康导游，随时为您提供健康咨询和导游服务'**
  String get welcomeDescription;

  /// 退出应用确认提示
  ///
  /// In zh, this message translates to:
  /// **'再次返回即可退出软件'**
  String get exitAppConfirm;

  /// 主题切换对话框标题
  ///
  /// In zh, this message translates to:
  /// **'主题切换'**
  String get themeSwitch;

  /// 主题切换确认消息
  ///
  /// In zh, this message translates to:
  /// **'切换主题模式需要重启应用才能完全生效，是否立即重启？'**
  String get themeSwitchMessage;

  /// 语言切换确认消息
  ///
  /// In zh, this message translates to:
  /// **'切换语言需要重启应用才能完全生效，是否立即重启？'**
  String get languageSwitchMessage;

  /// 重启按钮
  ///
  /// In zh, this message translates to:
  /// **'重启'**
  String get restart;

  /// 用户管理标签页
  ///
  /// In zh, this message translates to:
  /// **'用户管理'**
  String get userManagementTab;

  /// 用户管理
  ///
  /// In zh, this message translates to:
  /// **'用户管理'**
  String get userManagement;

  /// 用户列表
  ///
  /// In zh, this message translates to:
  /// **'用户列表'**
  String get userList;

  /// 用户统计
  ///
  /// In zh, this message translates to:
  /// **'用户统计'**
  String get userStatistics;

  /// 总用户数
  ///
  /// In zh, this message translates to:
  /// **'总用户数'**
  String get totalUsers;

  /// 活跃用户
  ///
  /// In zh, this message translates to:
  /// **'活跃用户'**
  String get activeUsers;

  /// 管理员用户
  ///
  /// In zh, this message translates to:
  /// **'管理员'**
  String get adminUsers;

  /// 医生用户
  ///
  /// In zh, this message translates to:
  /// **'医生'**
  String get doctorUsers;

  /// 搜索用户
  ///
  /// In zh, this message translates to:
  /// **'搜索用户'**
  String get searchUsers;

  /// 搜索用户提示文本
  ///
  /// In zh, this message translates to:
  /// **'按昵称、电话或ID搜索'**
  String get searchByNicknamePhoneId;

  /// 用户状态
  ///
  /// In zh, this message translates to:
  /// **'用户状态'**
  String get userStatus;

  /// 全部状态筛选
  ///
  /// In zh, this message translates to:
  /// **'全部'**
  String get allStatus;

  /// 已启用状态
  ///
  /// In zh, this message translates to:
  /// **'已启用'**
  String get enabledStatus;

  /// 已禁用状态
  ///
  /// In zh, this message translates to:
  /// **'已禁用'**
  String get disabledStatus;

  /// 用户角色
  ///
  /// In zh, this message translates to:
  /// **'用户角色'**
  String get userRole;

  /// 全部角色
  ///
  /// In zh, this message translates to:
  /// **'全部角色'**
  String get allRoles;

  /// 普通用户状态
  ///
  /// In zh, this message translates to:
  /// **'普通用户'**
  String get normalUser;

  /// 管理员用户
  ///
  /// In zh, this message translates to:
  /// **'管理员'**
  String get adminUser;

  /// 医生用户
  ///
  /// In zh, this message translates to:
  /// **'医生'**
  String get doctorUser;

  /// 用户性别
  ///
  /// In zh, this message translates to:
  /// **'性别'**
  String get userGender;

  /// 男性别选项
  ///
  /// In zh, this message translates to:
  /// **'男'**
  String get male;

  /// 女性别选项
  ///
  /// In zh, this message translates to:
  /// **'女'**
  String get female;

  /// 未知标签
  ///
  /// In zh, this message translates to:
  /// **'未知'**
  String get unknown;

  /// 注册来源
  ///
  /// In zh, this message translates to:
  /// **'注册来源'**
  String get registerSource;

  /// APP注册来源
  ///
  /// In zh, this message translates to:
  /// **'APP'**
  String get appSource;

  /// 小程序注册来源
  ///
  /// In zh, this message translates to:
  /// **'小程序'**
  String get miniProgramSource;

  /// 用户余额
  ///
  /// In zh, this message translates to:
  /// **'用户余额'**
  String get userBalance;

  /// 用户积分
  ///
  /// In zh, this message translates to:
  /// **'用户积分'**
  String get userIntegral;

  /// 调整余额
  ///
  /// In zh, this message translates to:
  /// **'调整余额'**
  String get adjustBalance;

  /// 调整积分
  ///
  /// In zh, this message translates to:
  /// **'调整积分'**
  String get adjustIntegral;

  /// 调整金额
  ///
  /// In zh, this message translates to:
  /// **'调整金额'**
  String get adjustAmount;

  /// 调整原因
  ///
  /// In zh, this message translates to:
  /// **'调整原因'**
  String get adjustReason;

  /// 请输入调整金额
  ///
  /// In zh, this message translates to:
  /// **'请输入调整金额'**
  String get pleaseEnterAmount;

  /// 请输入调整原因
  ///
  /// In zh, this message translates to:
  /// **'请输入调整原因'**
  String get pleaseEnterReason;

  /// 正数为增加，负数为减少
  ///
  /// In zh, this message translates to:
  /// **'正数为增加，负数为减少'**
  String get positiveForIncrease;

  /// 用户详情
  ///
  /// In zh, this message translates to:
  /// **'用户详情'**
  String get userDetail;

  /// 编辑用户
  ///
  /// In zh, this message translates to:
  /// **'编辑用户'**
  String get editUser;

  /// 启用用户
  ///
  /// In zh, this message translates to:
  /// **'启用用户'**
  String get enableUser;

  /// 禁用用户
  ///
  /// In zh, this message translates to:
  /// **'禁用用户'**
  String get disableUser;

  /// 重置密码
  ///
  /// In zh, this message translates to:
  /// **'重置密码'**
  String get resetPassword;

  /// 新密码字段标签
  ///
  /// In zh, this message translates to:
  /// **'新密码'**
  String get newPassword;

  /// 请输入新密码
  ///
  /// In zh, this message translates to:
  /// **'请输入新密码'**
  String get pleaseEnterNewPassword;

  /// 密码长度6-20位
  ///
  /// In zh, this message translates to:
  /// **'密码长度6-20位'**
  String get passwordLength;

  /// 用户昵称
  ///
  /// In zh, this message translates to:
  /// **'用户昵称'**
  String get userNickname;

  /// 用户电话
  ///
  /// In zh, this message translates to:
  /// **'用户电话'**
  String get userPhone;

  /// 用户生日
  ///
  /// In zh, this message translates to:
  /// **'用户生日'**
  String get userBirthday;

  /// 注册时间
  ///
  /// In zh, this message translates to:
  /// **'注册时间'**
  String get registrationTime;

  /// 最后登录时间
  ///
  /// In zh, this message translates to:
  /// **'最后登录'**
  String get lastLoginTime;

  /// 用户登录令牌
  ///
  /// In zh, this message translates to:
  /// **'登录令牌'**
  String get userTokens;

  /// 清除所有令牌
  ///
  /// In zh, this message translates to:
  /// **'清除所有令牌'**
  String get clearAllTokens;

  /// 确认清除所有登录令牌？
  ///
  /// In zh, this message translates to:
  /// **'确认清除所有登录令牌？'**
  String get confirmClearTokens;

  /// 清除令牌警告
  ///
  /// In zh, this message translates to:
  /// **'清除后用户将在所有设备上被强制下线'**
  String get clearTokensWarning;

  /// 设备类型
  ///
  /// In zh, this message translates to:
  /// **'设备类型'**
  String get deviceType;

  /// 过期时间
  ///
  /// In zh, this message translates to:
  /// **'过期时间'**
  String get expiryTime;

  /// 创建时间
  ///
  /// In zh, this message translates to:
  /// **'创建时间'**
  String get createTime;

  /// 已过期
  ///
  /// In zh, this message translates to:
  /// **'已过期'**
  String get expired;

  /// 有效
  ///
  /// In zh, this message translates to:
  /// **'有效'**
  String get valid;

  /// 加载用户列表失败
  ///
  /// In zh, this message translates to:
  /// **'加载用户列表失败'**
  String get loadUserListFailed;

  /// 加载用户详情失败
  ///
  /// In zh, this message translates to:
  /// **'加载用户详情失败'**
  String get loadUserDetailFailed;

  /// 更新用户信息成功
  ///
  /// In zh, this message translates to:
  /// **'更新用户信息成功'**
  String get updateUserSuccess;

  /// 更新用户信息失败
  ///
  /// In zh, this message translates to:
  /// **'更新用户信息失败'**
  String get updateUserFailed;

  /// 调整余额成功
  ///
  /// In zh, this message translates to:
  /// **'调整余额成功'**
  String get adjustBalanceSuccess;

  /// 调整余额失败
  ///
  /// In zh, this message translates to:
  /// **'调整余额失败'**
  String get adjustBalanceFailed;

  /// 调整积分成功
  ///
  /// In zh, this message translates to:
  /// **'调整积分成功'**
  String get adjustIntegralSuccess;

  /// 调整积分失败
  ///
  /// In zh, this message translates to:
  /// **'调整积分失败'**
  String get adjustIntegralFailed;

  /// 重置密码成功
  ///
  /// In zh, this message translates to:
  /// **'重置密码成功'**
  String get resetPasswordSuccess;

  /// 重置密码失败
  ///
  /// In zh, this message translates to:
  /// **'重置密码失败'**
  String get resetPasswordFailed;

  /// 清除令牌成功
  ///
  /// In zh, this message translates to:
  /// **'清除令牌成功'**
  String get clearTokensSuccess;

  /// 清除令牌失败
  ///
  /// In zh, this message translates to:
  /// **'清除令牌失败'**
  String get clearTokensFailed;

  /// 暂无用户
  ///
  /// In zh, this message translates to:
  /// **'暂无用户'**
  String get noUsersFound;

  /// 启用用户成功
  ///
  /// In zh, this message translates to:
  /// **'启用用户成功'**
  String get enableUserSuccess;

  /// 禁用用户成功
  ///
  /// In zh, this message translates to:
  /// **'禁用用户成功'**
  String get disableUserSuccess;

  /// 重置
  ///
  /// In zh, this message translates to:
  /// **'重置'**
  String get reset;

  /// 应用
  ///
  /// In zh, this message translates to:
  /// **'应用'**
  String get apply;

  /// 今日新增用户
  ///
  /// In zh, this message translates to:
  /// **'今日新增'**
  String get todayNewUsers;

  /// 本周新增用户
  ///
  /// In zh, this message translates to:
  /// **'本周新增'**
  String get thisWeekNewUsers;

  /// 总余额
  ///
  /// In zh, this message translates to:
  /// **'总余额'**
  String get totalBalance;

  /// 总积分
  ///
  /// In zh, this message translates to:
  /// **'总积分'**
  String get totalIntegral;

  /// 展开筛选
  ///
  /// In zh, this message translates to:
  /// **'展开筛选'**
  String get expandFilters;

  /// 收起筛选
  ///
  /// In zh, this message translates to:
  /// **'收起筛选'**
  String get collapseFilters;

  /// 用户详情页面开发中
  ///
  /// In zh, this message translates to:
  /// **'用户详情页面开发中...'**
  String get userDevelopmentInProgress;

  /// 管理员角色
  ///
  /// In zh, this message translates to:
  /// **'管理员'**
  String get roleAdmin;

  /// 医生角色
  ///
  /// In zh, this message translates to:
  /// **'医生'**
  String get roleDoctor;

  /// 分销员角色
  ///
  /// In zh, this message translates to:
  /// **'分销员'**
  String get roleReferrer;

  /// 普通用户角色
  ///
  /// In zh, this message translates to:
  /// **'普通用户'**
  String get roleNormalUser;

  /// 编辑用户信息
  ///
  /// In zh, this message translates to:
  /// **'编辑用户信息'**
  String get editUserInfo;

  /// 编辑用户角色
  ///
  /// In zh, this message translates to:
  /// **'编辑用户角色'**
  String get editUserRole;

  /// 用户昵称标签
  ///
  /// In zh, this message translates to:
  /// **'用户昵称'**
  String get userNicknameLabel;

  /// 用户电话标签
  ///
  /// In zh, this message translates to:
  /// **'用户电话'**
  String get userPhoneLabel;

  /// 用户生日标签
  ///
  /// In zh, this message translates to:
  /// **'用户生日'**
  String get userBirthdayLabel;

  /// 用户性别标签
  ///
  /// In zh, this message translates to:
  /// **'用户性别'**
  String get userGenderLabel;

  /// 请输入用户昵称
  ///
  /// In zh, this message translates to:
  /// **'请输入用户昵称'**
  String get pleaseEnterNickname;

  /// 请输入用户电话
  ///
  /// In zh, this message translates to:
  /// **'请输入用户电话'**
  String get pleaseEnterPhone;

  /// 选择生日
  ///
  /// In zh, this message translates to:
  /// **'选择生日'**
  String get selectBirthday;

  /// 选择性别
  ///
  /// In zh, this message translates to:
  /// **'选择性别'**
  String get selectGender;

  /// 关联医生
  ///
  /// In zh, this message translates to:
  /// **'关联医生'**
  String get associatedDoctor;

  /// 选择医生
  ///
  /// In zh, this message translates to:
  /// **'选择医生'**
  String get selectDoctor;

  /// 分销员等级
  ///
  /// In zh, this message translates to:
  /// **'分销员等级'**
  String get referrerLevel;

  /// 等级
  ///
  /// In zh, this message translates to:
  /// **'等级'**
  String get level;

  /// IP地址
  ///
  /// In zh, this message translates to:
  /// **'IP地址'**
  String get ipAddress;

  /// 角色权限管理
  ///
  /// In zh, this message translates to:
  /// **'角色权限管理'**
  String get roleManagement;

  /// 当前角色
  ///
  /// In zh, this message translates to:
  /// **'当前角色'**
  String get currentRoles;

  /// 角色详情
  ///
  /// In zh, this message translates to:
  /// **'角色详情'**
  String get roleDetails;

  /// 编辑角色
  ///
  /// In zh, this message translates to:
  /// **'编辑角色'**
  String get editRole;

  /// 余额积分管理
  ///
  /// In zh, this message translates to:
  /// **'余额积分管理'**
  String get balanceIntegralManagement;

  /// 令牌管理
  ///
  /// In zh, this message translates to:
  /// **'令牌管理'**
  String get tokenManagement;

  /// 总令牌数
  ///
  /// In zh, this message translates to:
  /// **'总令牌数'**
  String get totalTokens;

  /// 暂无登录令牌
  ///
  /// In zh, this message translates to:
  /// **'暂无登录令牌'**
  String get noTokensFound;

  /// 令牌清除成功
  ///
  /// In zh, this message translates to:
  /// **'令牌清除成功'**
  String get tokenClearSuccess;

  /// 令牌清除失败
  ///
  /// In zh, this message translates to:
  /// **'令牌清除失败'**
  String get tokenClearFailed;

  /// 加载令牌列表失败
  ///
  /// In zh, this message translates to:
  /// **'加载令牌列表失败'**
  String get loadTokensFailed;

  /// 请填写完整信息
  ///
  /// In zh, this message translates to:
  /// **'请填写完整信息'**
  String get pleaseCompleteInfo;

  /// 请输入有效的金额
  ///
  /// In zh, this message translates to:
  /// **'请输入有效的金额'**
  String get pleaseEnterValidAmount;

  /// 请输入有效的积分
  ///
  /// In zh, this message translates to:
  /// **'请输入有效的积分'**
  String get pleaseEnterValidPoints;

  /// 用户编辑功能开发中
  ///
  /// In zh, this message translates to:
  /// **'用户编辑功能开发中...'**
  String get userEditInProgress;

  /// 角色编辑功能开发中
  ///
  /// In zh, this message translates to:
  /// **'角色编辑功能开发中...'**
  String get roleEditInProgress;

  /// 男性
  ///
  /// In zh, this message translates to:
  /// **'男'**
  String get genderMale;

  /// 女性
  ///
  /// In zh, this message translates to:
  /// **'女'**
  String get genderFemale;

  /// 性别未知
  ///
  /// In zh, this message translates to:
  /// **'未知'**
  String get genderUnknown;

  /// 分销员文本
  ///
  /// In zh, this message translates to:
  /// **'分销员'**
  String get referrerText;

  /// 角色更新成功
  ///
  /// In zh, this message translates to:
  /// **'角色更新成功'**
  String get updateRoleSuccess;

  /// 角色更新失败
  ///
  /// In zh, this message translates to:
  /// **'角色更新失败'**
  String get updateRoleFailed;

  /// 管理员角色描述
  ///
  /// In zh, this message translates to:
  /// **'拥有系统管理权限'**
  String get adminRoleDescription;

  /// 医生角色描述
  ///
  /// In zh, this message translates to:
  /// **'可以管理产品和订单'**
  String get doctorRoleDescription;

  /// 分销员角色描述
  ///
  /// In zh, this message translates to:
  /// **'可以推广获得佣金'**
  String get referrerRoleDescription;

  /// 请输入分销员等级
  ///
  /// In zh, this message translates to:
  /// **'请输入分销员等级'**
  String get pleaseEnterReferrerLevel;

  /// APP注册来源
  ///
  /// In zh, this message translates to:
  /// **'APP'**
  String get registerSourceApp;

  /// 小程序注册来源
  ///
  /// In zh, this message translates to:
  /// **'小程序'**
  String get registerSourceMiniProgram;

  /// 启用状态
  ///
  /// In zh, this message translates to:
  /// **'启用'**
  String get statusEnabled;

  /// 禁用状态
  ///
  /// In zh, this message translates to:
  /// **'禁用'**
  String get statusDisabled;

  /// 健康档案
  ///
  /// In zh, this message translates to:
  /// **'健康档案'**
  String get healthProfile;

  /// 查看完整健康档案
  ///
  /// In zh, this message translates to:
  /// **'查看完整档案'**
  String get viewFullHealthProfile;

  /// 心率
  ///
  /// In zh, this message translates to:
  /// **'心率'**
  String get heartRate;

  /// 体温
  ///
  /// In zh, this message translates to:
  /// **'体温'**
  String get bodyTemperature;

  /// 体重标签
  ///
  /// In zh, this message translates to:
  /// **'体重'**
  String get weight;

  /// 身高标签
  ///
  /// In zh, this message translates to:
  /// **'身高'**
  String get height;

  /// 健康档案功能即将上线
  ///
  /// In zh, this message translates to:
  /// **'健康档案功能即将上线'**
  String get healthProfileFeatureComingSoon;

  /// 血型标签
  ///
  /// In zh, this message translates to:
  /// **'血型'**
  String get bloodType;

  /// 运动频率标签
  ///
  /// In zh, this message translates to:
  /// **'运动频率'**
  String get exerciseFrequency;

  /// 久坐不动
  ///
  /// In zh, this message translates to:
  /// **'久坐'**
  String get sedentary;

  /// 轻度运动
  ///
  /// In zh, this message translates to:
  /// **'轻度运动'**
  String get lightExercise;

  /// 中度运动
  ///
  /// In zh, this message translates to:
  /// **'中度运动'**
  String get moderateExercise;

  /// 高强度运动
  ///
  /// In zh, this message translates to:
  /// **'高强度运动'**
  String get activeExercise;

  /// 暂无健康档案
  ///
  /// In zh, this message translates to:
  /// **'暂无健康档案'**
  String get noHealthProfileYet;

  /// 创建健康档案
  ///
  /// In zh, this message translates to:
  /// **'创建健康档案'**
  String get createHealthProfile;

  /// 健康档案编辑功能即将上线
  ///
  /// In zh, this message translates to:
  /// **'健康档案编辑功能即将上线'**
  String get healthProfileEditFeatureComingSoon;

  /// 基本信息部分
  ///
  /// In zh, this message translates to:
  /// **'基本信息'**
  String get basicInfo;

  /// 过敏史标题
  ///
  /// In zh, this message translates to:
  /// **'过敏史'**
  String get allergyHistory;

  /// 慢性病史标题
  ///
  /// In zh, this message translates to:
  /// **'慢性病史'**
  String get chronicDiseaseHistory;

  /// 当前用药标题
  ///
  /// In zh, this message translates to:
  /// **'当前用药'**
  String get currentMedication;

  /// 生活方式标题
  ///
  /// In zh, this message translates to:
  /// **'生活方式'**
  String get lifestyle;

  /// 是否有过敏询问
  ///
  /// In zh, this message translates to:
  /// **'是否有药物、食物或其它物质过敏'**
  String get hasAllergies;

  /// 药物过敏
  ///
  /// In zh, this message translates to:
  /// **'药物过敏'**
  String get drugAllergies;

  /// 食物过敏
  ///
  /// In zh, this message translates to:
  /// **'食物过敏'**
  String get foodAllergies;

  /// 其他过敏
  ///
  /// In zh, this message translates to:
  /// **'其他过敏'**
  String get otherAllergies;

  /// 是否有慢性病
  ///
  /// In zh, this message translates to:
  /// **'是否有慢性病'**
  String get hasChronicDiseases;

  /// 慢性病列表
  ///
  /// In zh, this message translates to:
  /// **'慢性病列表'**
  String get chronicDiseasesList;

  /// 血压范围标签
  ///
  /// In zh, this message translates to:
  /// **'平常血压范围'**
  String get bloodPressureRange;

  /// 血糖范围标签
  ///
  /// In zh, this message translates to:
  /// **'平常空腹血糖范围'**
  String get bloodSugarRange;

  /// 是否用药
  ///
  /// In zh, this message translates to:
  /// **'是否用药'**
  String get hasCurrentMedication;

  /// 用药详情
  ///
  /// In zh, this message translates to:
  /// **'用药详情'**
  String get medicationDetails;

  /// 吸烟情况标签
  ///
  /// In zh, this message translates to:
  /// **'吸烟情况'**
  String get smokingStatus;

  /// 饮酒情况标签
  ///
  /// In zh, this message translates to:
  /// **'饮酒情况'**
  String get drinkingStatus;

  /// 睡眠时长标签
  ///
  /// In zh, this message translates to:
  /// **'平均每晚睡眠时长'**
  String get sleepDuration;

  /// 睡眠质量标签
  ///
  /// In zh, this message translates to:
  /// **'睡眠质量'**
  String get sleepQuality;

  /// 压力水平标签
  ///
  /// In zh, this message translates to:
  /// **'近期压力水平'**
  String get stressLevel;

  /// 是选项
  ///
  /// In zh, this message translates to:
  /// **'是'**
  String get yes;

  /// 否选项
  ///
  /// In zh, this message translates to:
  /// **'否'**
  String get no;

  /// 从不
  ///
  /// In zh, this message translates to:
  /// **'从不'**
  String get never;

  /// 已戒
  ///
  /// In zh, this message translates to:
  /// **'已戒'**
  String get quit;

  /// 偶尔
  ///
  /// In zh, this message translates to:
  /// **'偶尔'**
  String get occasional;

  /// 每天
  ///
  /// In zh, this message translates to:
  /// **'每天'**
  String get daily;

  /// 社交
  ///
  /// In zh, this message translates to:
  /// **'社交'**
  String get social;

  /// 每周
  ///
  /// In zh, this message translates to:
  /// **'每周'**
  String get weekly;

  /// 少于6小时
  ///
  /// In zh, this message translates to:
  /// **'少于6小时'**
  String get lessThan6Hours;

  /// 6-7小时
  ///
  /// In zh, this message translates to:
  /// **'6-7小时'**
  String get sixToSevenHours;

  /// 7-8小时
  ///
  /// In zh, this message translates to:
  /// **'7-8小时'**
  String get sevenToEightHours;

  /// 超过8小时
  ///
  /// In zh, this message translates to:
  /// **'超过8小时'**
  String get moreThan8Hours;

  /// 良好
  ///
  /// In zh, this message translates to:
  /// **'良好'**
  String get good;

  /// 一般
  ///
  /// In zh, this message translates to:
  /// **'一般'**
  String get fair;

  /// 较差
  ///
  /// In zh, this message translates to:
  /// **'较差'**
  String get poor;

  /// 很低
  ///
  /// In zh, this message translates to:
  /// **'很低'**
  String get veryLow;

  /// 低
  ///
  /// In zh, this message translates to:
  /// **'低'**
  String get low;

  /// 中等
  ///
  /// In zh, this message translates to:
  /// **'中等'**
  String get moderate;

  /// 高
  ///
  /// In zh, this message translates to:
  /// **'高'**
  String get high;

  /// 很高
  ///
  /// In zh, this message translates to:
  /// **'很高'**
  String get veryHigh;

  /// 请输入手机号验证提示
  ///
  /// In zh, this message translates to:
  /// **'请输入手机号'**
  String get pleaseEnterPhoneNumber;

  /// 请输入正确的手机号提示
  ///
  /// In zh, this message translates to:
  /// **'请输入正确的手机号'**
  String get pleaseEnterCorrectPhoneNumber;

  /// 请输入密码提示
  ///
  /// In zh, this message translates to:
  /// **'请输入密码'**
  String get pleaseEnterPassword;

  /// 密码最小长度提示
  ///
  /// In zh, this message translates to:
  /// **'密码长度至少6位'**
  String get passwordMinLength;

  /// 正在登录提示
  ///
  /// In zh, this message translates to:
  /// **'正在登录...'**
  String get loggingIn;

  /// 登录成功提示
  ///
  /// In zh, this message translates to:
  /// **'登录成功'**
  String get loginSuccessful;

  /// 登录成功但数据为空
  ///
  /// In zh, this message translates to:
  /// **'登录成功但用户数据为空'**
  String get loginSuccessButNoData;

  /// 数据处理错误
  ///
  /// In zh, this message translates to:
  /// **'处理用户数据时出错: {error}'**
  String dataProcessingError(String error);

  /// 登录过程错误
  ///
  /// In zh, this message translates to:
  /// **'登录过程中出错: {error}'**
  String loginProcessError(String error);

  /// 密码错误提示
  ///
  /// In zh, this message translates to:
  /// **'密码错误'**
  String get passwordIncorrect;

  /// 手机号未注册提示
  ///
  /// In zh, this message translates to:
  /// **'该手机号未注册，请先注册'**
  String get phoneNotRegistered;

  /// 密码登录标题
  ///
  /// In zh, this message translates to:
  /// **'密码登录'**
  String get passwordLogin;

  /// 密码登录副标题
  ///
  /// In zh, this message translates to:
  /// **'请使用您的手机号和密码登录'**
  String get passwordLoginSubtitle;

  /// 手机号输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入手机号'**
  String get phoneNumberHint;

  /// 密码输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入密码'**
  String get passwordHint;

  /// 忘记密码按钮
  ///
  /// In zh, this message translates to:
  /// **'忘记密码?'**
  String get forgotPassword;

  /// 验证码登录按钮
  ///
  /// In zh, this message translates to:
  /// **'验证码登录'**
  String get smsLogin;

  /// 注册账号按钮
  ///
  /// In zh, this message translates to:
  /// **'注册账号'**
  String get registerAccount;

  /// 其他登录方式提示
  ///
  /// In zh, this message translates to:
  /// **'或选其他登录方式'**
  String get orOtherLoginMethods;

  /// 登录协议文本
  ///
  /// In zh, this message translates to:
  /// **'登录即表示您同意《用户协议》和《隐私政策》'**
  String get loginAgreement;

  /// 验证码已发送
  ///
  /// In zh, this message translates to:
  /// **'验证码已发送'**
  String get verificationCodeSent;

  /// 发送失败
  ///
  /// In zh, this message translates to:
  /// **'发送失败'**
  String get sendFailed;

  /// 输入验证码提示
  ///
  /// In zh, this message translates to:
  /// **'请输入验证码'**
  String get pleaseEnterVerificationCode;

  /// 验证码位数提示
  ///
  /// In zh, this message translates to:
  /// **'验证码应为6位数字'**
  String get verificationCodeShouldBe6Digits;

  /// 登录按钮
  ///
  /// In zh, this message translates to:
  /// **'登录'**
  String get login;

  /// 欢迎回来
  ///
  /// In zh, this message translates to:
  /// **'欢迎回来'**
  String get welcomeBack;

  /// 请使用手机号登录
  ///
  /// In zh, this message translates to:
  /// **'请使用您的手机号登录账户'**
  String get pleaseLoginWithPhoneNumber;

  /// 密码登录描述
  ///
  /// In zh, this message translates to:
  /// **'请使用您的手机号和密码登录'**
  String get passwordLoginDesc;

  /// 同意条款
  ///
  /// In zh, this message translates to:
  /// **'登录即表示您同意《用户协议》和《隐私政策》'**
  String get agreeToTerms;

  /// 验证码输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入验证码'**
  String get verificationCodeHint;

  /// 新密码输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入新密码'**
  String get newPasswordHint;

  /// 确认密码输入提示
  ///
  /// In zh, this message translates to:
  /// **'请再次输入新密码'**
  String get confirmPasswordHint;

  /// 获取验证码
  ///
  /// In zh, this message translates to:
  /// **'获取验证码'**
  String get getVerificationCode;

  /// 重新发送验证码
  ///
  /// In zh, this message translates to:
  /// **'重新发送'**
  String get resendVerificationCode;

  /// 重置密码描述
  ///
  /// In zh, this message translates to:
  /// **'请输入手机号获取验证码，然后设置新密码'**
  String get resetPasswordDescription;

  /// 确认重置
  ///
  /// In zh, this message translates to:
  /// **'确认重置'**
  String get confirmReset;

  /// 密码不一致验证消息
  ///
  /// In zh, this message translates to:
  /// **'两次输入的密码不一致'**
  String get passwordsDoNotMatch;

  /// 密码重置成功
  ///
  /// In zh, this message translates to:
  /// **'密码重置成功，请使用新密码登录'**
  String get passwordResetSuccess;

  /// 联系客服
  ///
  /// In zh, this message translates to:
  /// **'如有问题，请联系客服处理'**
  String get contactCustomerService;

  /// 重置密码页面标题
  ///
  /// In zh, this message translates to:
  /// **'重置密码'**
  String get resetPasswordTitle;

  /// 输入正确手机号
  ///
  /// In zh, this message translates to:
  /// **'请输入正确的手机号'**
  String get enterCorrectPhoneNumber;

  /// 输入验证码
  ///
  /// In zh, this message translates to:
  /// **'请输入验证码'**
  String get enterVerificationCode;

  /// 验证码6位数字提示
  ///
  /// In zh, this message translates to:
  /// **'验证码应为6位数字'**
  String get verificationCodeSixDigits;

  /// 输入新密码
  ///
  /// In zh, this message translates to:
  /// **'请输入新密码'**
  String get enterNewPassword;

  /// 密码最少6位提示
  ///
  /// In zh, this message translates to:
  /// **'密码长度至少6位'**
  String get passwordMinSixCharacters;

  /// 再次输入新密码提示
  ///
  /// In zh, this message translates to:
  /// **'请再次输入新密码'**
  String get enterNewPasswordAgain;

  /// 获取验证码按钮
  ///
  /// In zh, this message translates to:
  /// **'获取验证码'**
  String get getVerificationCodeButton;

  /// 重新发送倒计时
  ///
  /// In zh, this message translates to:
  /// **'重新发送({seconds}s)'**
  String resendCountdown(int seconds);

  /// 发送验证码失败
  ///
  /// In zh, this message translates to:
  /// **'发送验证码失败'**
  String get sendVerificationCodeFailed;

  /// 正在重置密码加载提示
  ///
  /// In zh, this message translates to:
  /// **'正在重置密码...'**
  String get resettingPasswordLoading;

  /// 密码重置失败
  ///
  /// In zh, this message translates to:
  /// **'密码重置失败'**
  String get passwordResetFailed;

  /// 网络连接失败重试提示
  ///
  /// In zh, this message translates to:
  /// **'网络连接失败，请稍后重试'**
  String get networkConnectionFailedRetry;

  /// 清空聊天历史标题
  ///
  /// In zh, this message translates to:
  /// **'清空聊天历史'**
  String get clearHistoryTitle;

  /// 历史记录已清空提示
  ///
  /// In zh, this message translates to:
  /// **'历史记录已清空'**
  String get historyCleared;

  /// 清空历史记录失败
  ///
  /// In zh, this message translates to:
  /// **'清空历史记录失败'**
  String get clearHistoryFailed;

  /// 清空所有历史
  ///
  /// In zh, this message translates to:
  /// **'清空所有历史'**
  String get clearAllHistory;

  /// 天前
  ///
  /// In zh, this message translates to:
  /// **'天前'**
  String get daysAgo;

  /// 小时前时间指示器
  ///
  /// In zh, this message translates to:
  /// **'{hours}小时前'**
  String hoursAgo(int hours);

  /// 分钟前时间指示器
  ///
  /// In zh, this message translates to:
  /// **'{minutes}分钟前'**
  String minutesAgo(int minutes);

  /// 刚刚时间指示器
  ///
  /// In zh, this message translates to:
  /// **'刚刚'**
  String get justNow;

  /// 重新拍照
  ///
  /// In zh, this message translates to:
  /// **'重新拍照'**
  String get retakePhoto;

  /// 图片处理错误
  ///
  /// In zh, this message translates to:
  /// **'处理图片时出错'**
  String get imageProcessingError;

  /// 退出应用提示
  ///
  /// In zh, this message translates to:
  /// **'再次返回即可退出软件'**
  String get exitAppHint;

  /// 中文语言
  ///
  /// In zh, this message translates to:
  /// **'中文'**
  String get chinese;

  /// 维吾尔语
  ///
  /// In zh, this message translates to:
  /// **'维吾尔语'**
  String get uyghur;

  /// 哈萨克语
  ///
  /// In zh, this message translates to:
  /// **'哈萨克语'**
  String get kazakh;

  /// 俄语
  ///
  /// In zh, this message translates to:
  /// **'俄语'**
  String get russian;

  /// 法语
  ///
  /// In zh, this message translates to:
  /// **'法语'**
  String get french;

  /// 西班牙语
  ///
  /// In zh, this message translates to:
  /// **'西班牙语'**
  String get spanish;

  /// 粤语
  ///
  /// In zh, this message translates to:
  /// **'粤语'**
  String get cantonese;

  /// 请先选择源语言
  ///
  /// In zh, this message translates to:
  /// **'请先选择源语言'**
  String get selectSourceLanguageFirst;

  /// 目标语言将自动更新
  ///
  /// In zh, this message translates to:
  /// **'目标语言将根据源语言自动更新'**
  String get targetLanguageWillUpdate;

  /// 面对面交流
  ///
  /// In zh, this message translates to:
  /// **'面对面交流'**
  String get faceToFaceConversation;

  /// 对话按钮文本
  ///
  /// In zh, this message translates to:
  /// **'对话'**
  String get conversation;

  /// 新建聊天按钮文本
  ///
  /// In zh, this message translates to:
  /// **'新建'**
  String get newChat;

  /// AI聊天历史标题
  ///
  /// In zh, this message translates to:
  /// **'聊天历史'**
  String get aiChatHistory;

  /// 无聊天历史提示
  ///
  /// In zh, this message translates to:
  /// **'暂无聊天记录'**
  String get noChatHistory;

  /// 开始新聊天提示
  ///
  /// In zh, this message translates to:
  /// **'点击右上角新建按钮开始聊天'**
  String get startNewChat;

  /// 开始聊天提示
  ///
  /// In zh, this message translates to:
  /// **'开始聊天'**
  String get startChatting;

  /// 发送第一条消息提示
  ///
  /// In zh, this message translates to:
  /// **'发送第一条消息开始对话'**
  String get sendFirstMessage;

  /// AI思考中提示
  ///
  /// In zh, this message translates to:
  /// **'正在思考中...'**
  String get thinking;

  /// 消息发送中提示
  ///
  /// In zh, this message translates to:
  /// **'发送中...'**
  String get sending;

  /// 语音消息标签
  ///
  /// In zh, this message translates to:
  /// **'语音消息'**
  String get audioMessage;

  /// 输入框提示文本
  ///
  /// In zh, this message translates to:
  /// **'输入消息...'**
  String get typeMessage;

  /// 编辑标题按钮
  ///
  /// In zh, this message translates to:
  /// **'编辑标题'**
  String get editTitle;

  /// 输入标题提示
  ///
  /// In zh, this message translates to:
  /// **'请输入标题'**
  String get enterTitle;

  /// 删除对话
  ///
  /// In zh, this message translates to:
  /// **'删除对话'**
  String get deleteConversation;

  /// 删除对话确认
  ///
  /// In zh, this message translates to:
  /// **'确定要删除这个对话吗？此操作无法撤销。'**
  String get deleteConversationConfirm;

  /// 编辑按钮
  ///
  /// In zh, this message translates to:
  /// **'编辑'**
  String get edit;

  /// 删除按钮
  ///
  /// In zh, this message translates to:
  /// **'删除'**
  String get delete;

  /// 保存按钮
  ///
  /// In zh, this message translates to:
  /// **'保存'**
  String get save;

  /// 加载失败
  ///
  /// In zh, this message translates to:
  /// **'加载数据失败'**
  String get loadFailed;

  /// 麦克风权限被拒绝
  ///
  /// In zh, this message translates to:
  /// **'麦克风权限被拒绝'**
  String get microphonePermissionDenied;

  /// 录音时间太短消息
  ///
  /// In zh, this message translates to:
  /// **'录音时间太短'**
  String get recordingTooShort;

  /// 录音已取消
  ///
  /// In zh, this message translates to:
  /// **'录音已取消'**
  String get recordingCancelled;

  /// AI聊天历史副标题
  ///
  /// In zh, this message translates to:
  /// **'查看您与健康助手的对话记录'**
  String get aiChatHistorySubtitle;

  /// 清空历史按钮文本
  ///
  /// In zh, this message translates to:
  /// **'清空历史'**
  String get clearHistoryButton;

  /// 清空聊天历史确认
  ///
  /// In zh, this message translates to:
  /// **'确定要清空所有聊天历史吗？此操作无法撤销。'**
  String get clearHistoryConfirm;

  /// 清空对话
  ///
  /// In zh, this message translates to:
  /// **'清空对话'**
  String get clearConversation;

  /// 按住说话按钮文字
  ///
  /// In zh, this message translates to:
  /// **'按住说话'**
  String get holdToSpeak;

  /// 等待对方说话
  ///
  /// In zh, this message translates to:
  /// **'等待对方说话'**
  String get waitingForOther;

  /// 需要麦克风权限
  ///
  /// In zh, this message translates to:
  /// **'需要麦克风权限才能录音'**
  String get microphonePermissionNeeded;

  /// 录音失败
  ///
  /// In zh, this message translates to:
  /// **'录音失败: {error}'**
  String recordingFailed(String error);

  /// 录音文件无效
  ///
  /// In zh, this message translates to:
  /// **'录音文件无效'**
  String get invalidAudioFile;

  /// 音频处理失败
  ///
  /// In zh, this message translates to:
  /// **'音频处理失败，请重试'**
  String get audioProcessingFailed;

  /// 无法识别语音
  ///
  /// In zh, this message translates to:
  /// **'未能识别语音内容'**
  String get cannotRecognizeVoice;

  /// 确认清空对话
  ///
  /// In zh, this message translates to:
  /// **'确定要清空所有聊天记录吗？此操作无法撤销。'**
  String get confirmClearConversation;

  /// 对话已清空
  ///
  /// In zh, this message translates to:
  /// **'对话已清空'**
  String get conversationCleared;

  /// 用户
  ///
  /// In zh, this message translates to:
  /// **'用户'**
  String get user;

  /// 点击登录
  ///
  /// In zh, this message translates to:
  /// **'点击登录'**
  String get clickToLogin;

  /// 登录享受更多功能
  ///
  /// In zh, this message translates to:
  /// **'登录后享受更多功能'**
  String get loginToEnjoyMoreFeatures;

  /// 编辑资料页面标题
  ///
  /// In zh, this message translates to:
  /// **'编辑资料'**
  String get editProfile;

  /// VIP会员状态
  ///
  /// In zh, this message translates to:
  /// **'VIP会员'**
  String get vipMember;

  /// 分销员
  ///
  /// In zh, this message translates to:
  /// **'分销员'**
  String get distributorLevel;

  /// 应用名称
  ///
  /// In zh, this message translates to:
  /// **'健康助手'**
  String get appName;

  /// 分享成功
  ///
  /// In zh, this message translates to:
  /// **'分享成功！好友通过您的链接下载可获得奖励'**
  String get shareSuccess;

  /// 分享功能不可用
  ///
  /// In zh, this message translates to:
  /// **'分享功能暂时不可用，请稍后重试'**
  String get shareNotAvailable;

  /// 分享主题
  ///
  /// In zh, this message translates to:
  /// **'推荐App'**
  String get shareSubject;

  /// 带推荐的分享内容
  ///
  /// In zh, this message translates to:
  /// **'我发现了一款超棒的健康助手App：{appName}！专业的AI健康导游，随时为您提供健康咨询和导游服务。使用我的专属推荐链接下载，还有额外福利哦！🎁\n\n立即下载：{url}'**
  String shareContentWithReferral(String appName, String url);

  /// 普通分享内容
  ///
  /// In zh, this message translates to:
  /// **'我发现了一款超棒的健康助手App：{appName}！专业的AI健康导游，随时为您提供健康咨询和导游服务。你也来试试吧！\n\n立即下载：{url}'**
  String shareContentNormal(String appName, String url);

  /// 退出登录确认
  ///
  /// In zh, this message translates to:
  /// **'确定要退出登录吗？退出后需要重新登录才能使用完整功能。'**
  String get logoutConfirmation;

  /// 退出登录成功
  ///
  /// In zh, this message translates to:
  /// **'已退出登录'**
  String get logoutSuccess;

  /// 退出登录失败
  ///
  /// In zh, this message translates to:
  /// **'退出登录失败'**
  String get logoutFailed;

  /// 即将推出功能标识
  ///
  /// In zh, this message translates to:
  /// **'(即将推出)'**
  String get comingSoon;

  /// 医生工作经验
  ///
  /// In zh, this message translates to:
  /// **'{years}年经验'**
  String yearsExperience(int years);

  /// 医生评分
  ///
  /// In zh, this message translates to:
  /// **'{rating}分'**
  String ratingScore(String rating);

  /// AI助手标识
  ///
  /// In zh, this message translates to:
  /// **'AI'**
  String get aiAssistant;

  /// 医生专业介绍标题
  ///
  /// In zh, this message translates to:
  /// **'专业介绍'**
  String get professionalIntroduction;

  /// 医生AI助手选择成功提示
  ///
  /// In zh, this message translates to:
  /// **'已选择{doctorName}的AI助手'**
  String doctorAiAssistantSelected(String doctorName);

  /// 加载中文字
  ///
  /// In zh, this message translates to:
  /// **'加载中...'**
  String get loading;

  /// 暂无医生信息消息
  ///
  /// In zh, this message translates to:
  /// **'暂无医生信息'**
  String get noDoctorInfo;

  /// 医师职称
  ///
  /// In zh, this message translates to:
  /// **'医师'**
  String get doctorTitle;

  /// 擅长领域字段
  ///
  /// In zh, this message translates to:
  /// **'擅长领域'**
  String get specialtyField;

  /// 对话历史空状态提示
  ///
  /// In zh, this message translates to:
  /// **'开始与AI导游对话吧'**
  String get startChatWithAiGuide;

  /// 更新对话标题失败提示
  ///
  /// In zh, this message translates to:
  /// **'更新标题失败'**
  String get updateTitleFailed;

  /// 选择地址按钮
  ///
  /// In zh, this message translates to:
  /// **'选择地址'**
  String get selectAddress;

  /// 地址管理按钮
  ///
  /// In zh, this message translates to:
  /// **'地址管理'**
  String get addressManagement;

  /// 无地址提示
  ///
  /// In zh, this message translates to:
  /// **'暂无收货地址'**
  String get noAddressesYet;

  /// 添加地址提示
  ///
  /// In zh, this message translates to:
  /// **'点击右下角添加地址'**
  String get clickToAddAddress;

  /// 设为默认按钮
  ///
  /// In zh, this message translates to:
  /// **'设为默认'**
  String get setAsDefault;

  /// 已选择商品数量
  ///
  /// In zh, this message translates to:
  /// **'已选择 {count} 件商品'**
  String selectedItemsCount(int count);

  /// 总金额标签
  ///
  /// In zh, this message translates to:
  /// **'合计: '**
  String get totalAmount;

  /// 我的点赞页面标题
  ///
  /// In zh, this message translates to:
  /// **'我的点赞'**
  String get myLikesTitle;

  /// 我的收藏页面标题
  ///
  /// In zh, this message translates to:
  /// **'我的收藏'**
  String get myFavoritesTitle;

  /// 无点赞医生空状态提示
  ///
  /// In zh, this message translates to:
  /// **'暂无点赞的医生'**
  String get noLikedDoctors;

  /// 无收藏医生空状态提示
  ///
  /// In zh, this message translates to:
  /// **'暂无收藏的医生'**
  String get noFavoriteDoctors;

  /// 去点赞医生提示
  ///
  /// In zh, this message translates to:
  /// **'去医生详情页面为您喜欢的医生点赞吧'**
  String get goLikeDoctors;

  /// 去收藏医生提示
  ///
  /// In zh, this message translates to:
  /// **'去医生详情页面收藏您喜欢的医生吧'**
  String get goFavoriteDoctors;

  /// 全选按钮
  ///
  /// In zh, this message translates to:
  /// **'全选'**
  String get selectAll;

  /// 删除选中按钮
  ///
  /// In zh, this message translates to:
  /// **'删除选中'**
  String get deleteSelected;

  /// 结算按钮
  ///
  /// In zh, this message translates to:
  /// **'结算'**
  String get checkout;

  /// 删除按钮带数量
  ///
  /// In zh, this message translates to:
  /// **'删除 ({count})'**
  String deleteWithCount(int count);

  /// 结算按钮带数量
  ///
  /// In zh, this message translates to:
  /// **'结算 ({count})'**
  String checkoutWithCount(int count);

  /// 医生信息标题
  ///
  /// In zh, this message translates to:
  /// **'医生信息'**
  String get doctorInfo;

  /// 医生姓名标签
  ///
  /// In zh, this message translates to:
  /// **'医生姓名'**
  String get doctorName;

  /// 联系电话标签
  ///
  /// In zh, this message translates to:
  /// **'联系电话'**
  String get contactPhone;

  /// 工作地址标签
  ///
  /// In zh, this message translates to:
  /// **'工作地址'**
  String get workAddress;

  /// 拨打电话按钮
  ///
  /// In zh, this message translates to:
  /// **'拨打'**
  String get call;

  /// 暂无产品提示
  ///
  /// In zh, this message translates to:
  /// **'暂无产品'**
  String get noProducts;

  /// 产品数量
  ///
  /// In zh, this message translates to:
  /// **'{count}个产品'**
  String productsCount(int count);

  /// 立即购买按钮
  ///
  /// In zh, this message translates to:
  /// **'立即购买'**
  String get buyNow;

  /// 加入购物车按钮
  ///
  /// In zh, this message translates to:
  /// **'加入购物车'**
  String get addToCart;

  /// 医生标签
  ///
  /// In zh, this message translates to:
  /// **'医生'**
  String get doctor;

  /// 商品数量标签
  ///
  /// In zh, this message translates to:
  /// **'商品数量'**
  String get productQuantity;

  /// 商品总价标签
  ///
  /// In zh, this message translates to:
  /// **'商品总价'**
  String get productTotalPrice;

  /// 运费标签
  ///
  /// In zh, this message translates to:
  /// **'运费'**
  String get shippingFee;

  /// 免费标签
  ///
  /// In zh, this message translates to:
  /// **'免费'**
  String get free;

  /// 实付款标签
  ///
  /// In zh, this message translates to:
  /// **'实付款'**
  String get actualPayment;

  /// 确认订单按钮
  ///
  /// In zh, this message translates to:
  /// **'确认订单'**
  String get confirmOrder;

  /// 订单创建成功提示
  ///
  /// In zh, this message translates to:
  /// **'订单创建成功'**
  String get orderCreatedSuccess;

  /// 创建订单失败提示
  ///
  /// In zh, this message translates to:
  /// **'创建订单失败：{error}'**
  String createOrderFailed(String error);

  /// 结算失败提示
  ///
  /// In zh, this message translates to:
  /// **'结算失败: {error}'**
  String checkoutFailed(String error);

  /// 数量范围提示
  ///
  /// In zh, this message translates to:
  /// **'请输入1-99之间的有效数量'**
  String get quantityRange;

  /// 件数单位
  ///
  /// In zh, this message translates to:
  /// **'件'**
  String get items;

  /// 咨询按钮
  ///
  /// In zh, this message translates to:
  /// **'咨询'**
  String get consultation;

  /// 预约按钮
  ///
  /// In zh, this message translates to:
  /// **'预约'**
  String get appointment;

  /// 年份单位
  ///
  /// In zh, this message translates to:
  /// **'年'**
  String get years;

  /// 从业年限标签
  ///
  /// In zh, this message translates to:
  /// **'从业年限'**
  String get yearsOfExperience;

  /// 产品详情页面标题
  ///
  /// In zh, this message translates to:
  /// **'产品详情'**
  String get productDetail;

  /// 价格标签
  ///
  /// In zh, this message translates to:
  /// **'价格'**
  String get price;

  /// 预约时间标签
  ///
  /// In zh, this message translates to:
  /// **'预约时间'**
  String get appointmentTime;

  /// 详细说明标题
  ///
  /// In zh, this message translates to:
  /// **'详细说明'**
  String get detailedDescription;

  /// 选择数量标题
  ///
  /// In zh, this message translates to:
  /// **'选择数量'**
  String get selectQuantity;

  /// 数量标签
  ///
  /// In zh, this message translates to:
  /// **'数量'**
  String get quantity;

  /// 购物车空状态提示
  ///
  /// In zh, this message translates to:
  /// **'购物车空空如也'**
  String get cartEmpty;

  /// 购物车空状态描述
  ///
  /// In zh, this message translates to:
  /// **'快去挑选心仪的商品吧'**
  String get cartEmptyDescription;

  /// 去购物按钮
  ///
  /// In zh, this message translates to:
  /// **'去购物'**
  String get goShopping;

  /// 购买确认弹窗标题
  ///
  /// In zh, this message translates to:
  /// **'购买确认'**
  String get purchaseConfirmation;

  /// 制造商字段
  ///
  /// In zh, this message translates to:
  /// **'制造商'**
  String get manufacturer;

  /// 星期三
  ///
  /// In zh, this message translates to:
  /// **'周三'**
  String get wednesday;

  /// 上午时间
  ///
  /// In zh, this message translates to:
  /// **'上午'**
  String get morning;

  /// 下午时间
  ///
  /// In zh, this message translates to:
  /// **'下午'**
  String get afternoon;

  /// 晚上时间
  ///
  /// In zh, this message translates to:
  /// **'晚上'**
  String get evening;

  /// 确认购买按钮
  ///
  /// In zh, this message translates to:
  /// **'确认购买'**
  String get confirmPurchase;

  /// 产品名称字段
  ///
  /// In zh, this message translates to:
  /// **'产品名称'**
  String get productName;

  /// 单价标签
  ///
  /// In zh, this message translates to:
  /// **'单价'**
  String get unitPrice;

  /// 购买确认提示信息
  ///
  /// In zh, this message translates to:
  /// **'请确认您的购买信息，点击确认后将跳转到订单确认页面。'**
  String get purchaseConfirmationMessage;

  /// 确认订单页面标题
  ///
  /// In zh, this message translates to:
  /// **'确认订单'**
  String get orderConfirmation;

  /// 商品信息标题
  ///
  /// In zh, this message translates to:
  /// **'商品信息'**
  String get productInfo;

  /// 物流信息标题
  ///
  /// In zh, this message translates to:
  /// **'物流信息'**
  String get shippingInfo;

  /// 订单金额标题
  ///
  /// In zh, this message translates to:
  /// **'订单金额'**
  String get orderAmount;

  /// 收货人姓名标签
  ///
  /// In zh, this message translates to:
  /// **'收货人姓名'**
  String get recipientName;

  /// 收货人电话标签
  ///
  /// In zh, this message translates to:
  /// **'收货人电话'**
  String get recipientPhone;

  /// 收货地址标签
  ///
  /// In zh, this message translates to:
  /// **'收货地址'**
  String get shippingAddress;

  /// 获取当前位置按钮
  ///
  /// In zh, this message translates to:
  /// **'获取当前位置'**
  String get getCurrentLocation;

  /// 获取位置中提示
  ///
  /// In zh, this message translates to:
  /// **'正在获取位置...'**
  String get gettingLocation;

  /// 小计标签
  ///
  /// In zh, this message translates to:
  /// **'小计'**
  String get subtotal;

  /// 提交订单按钮
  ///
  /// In zh, this message translates to:
  /// **'提交订单'**
  String get submitOrder;

  /// 提交订单中提示
  ///
  /// In zh, this message translates to:
  /// **'正在提交订单...'**
  String get submittingOrder;

  /// 输入收货人姓名提示
  ///
  /// In zh, this message translates to:
  /// **'请输入收货人姓名'**
  String get enterRecipientName;

  /// 输入收货人电话提示
  ///
  /// In zh, this message translates to:
  /// **'请输入收货人电话'**
  String get enterRecipientPhone;

  /// 输入收货地址提示
  ///
  /// In zh, this message translates to:
  /// **'请输入收货地址'**
  String get enterShippingAddress;

  /// 商品总数显示
  ///
  /// In zh, this message translates to:
  /// **'共{count}件商品'**
  String totalItems(int count);

  /// 完成按钮
  ///
  /// In zh, this message translates to:
  /// **'完成'**
  String get done;

  /// 所在地区标签
  ///
  /// In zh, this message translates to:
  /// **'所在地区'**
  String get region;

  /// 选择地区提示
  ///
  /// In zh, this message translates to:
  /// **'请选择省市区'**
  String get selectRegion;

  /// 地区选择验证提示
  ///
  /// In zh, this message translates to:
  /// **'请选择所在地区'**
  String get pleaseSelectRegion;

  /// 商品金额标签
  ///
  /// In zh, this message translates to:
  /// **'商品金额'**
  String get productAmount;

  /// 免运费标签
  ///
  /// In zh, this message translates to:
  /// **'免运费'**
  String get freeShipping;

  /// 默认地址标签
  ///
  /// In zh, this message translates to:
  /// **'默认'**
  String get defaultAddress;

  /// 编辑地址按钮
  ///
  /// In zh, this message translates to:
  /// **'编辑'**
  String get editAddress;

  /// 删除地址按钮
  ///
  /// In zh, this message translates to:
  /// **'删除'**
  String get deleteAddress;

  /// 加载地址失败提示
  ///
  /// In zh, this message translates to:
  /// **'加载地址失败'**
  String get loadAddressFailed;

  /// 设置默认地址成功提示
  ///
  /// In zh, this message translates to:
  /// **'设置成功'**
  String get setDefaultSuccess;

  /// 设置默认地址失败提示
  ///
  /// In zh, this message translates to:
  /// **'设置失败'**
  String get setDefaultFailed;

  /// 新增地址标题
  ///
  /// In zh, this message translates to:
  /// **'新增地址'**
  String get addAddress;

  /// 收货人姓名标签
  ///
  /// In zh, this message translates to:
  /// **'收货人姓名'**
  String get receiverName;

  /// 收货人姓名输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入收货人姓名'**
  String get enterReceiverName;

  /// 联系电话输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入联系电话'**
  String get enterContactPhone;

  /// 详细地址标签
  ///
  /// In zh, this message translates to:
  /// **'详细地址'**
  String get detailedAddress;

  /// 详细地址输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入详细的收货地址（街道、门牌号等）'**
  String get enterDetailedAddress;

  /// 邮政编码标签
  ///
  /// In zh, this message translates to:
  /// **'邮政编码（可选）'**
  String get postalCodeOptional;

  /// 邮政编码输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入邮政编码'**
  String get enterPostalCode;

  /// 地址标签标签
  ///
  /// In zh, this message translates to:
  /// **'地址标签（可选）'**
  String get addressLabelOptional;

  /// 地址标签输入提示
  ///
  /// In zh, this message translates to:
  /// **'如：家、公司、学校等'**
  String get enterAddressLabel;

  /// 地址长度验证提示
  ///
  /// In zh, this message translates to:
  /// **'详细地址至少需要5个字符'**
  String get addressTooShort;

  /// 地址长度验证提示
  ///
  /// In zh, this message translates to:
  /// **'详细地址不能超过200个字符'**
  String get addressTooLong;

  /// 保存修改按钮
  ///
  /// In zh, this message translates to:
  /// **'保存修改'**
  String get saveChanges;

  /// 保存地址按钮
  ///
  /// In zh, this message translates to:
  /// **'保存地址'**
  String get saveAddress;

  /// 设为默认地址开关
  ///
  /// In zh, this message translates to:
  /// **'设为默认地址'**
  String get setAsDefaultAddress;

  /// 点赞成功提示
  ///
  /// In zh, this message translates to:
  /// **'点赞成功'**
  String get likeSuccess;

  /// 取消点赞成功提示
  ///
  /// In zh, this message translates to:
  /// **'取消点赞成功'**
  String get unlikeSuccess;

  /// 收藏成功提示
  ///
  /// In zh, this message translates to:
  /// **'收藏成功'**
  String get favoriteSuccess;

  /// 取消收藏成功提示
  ///
  /// In zh, this message translates to:
  /// **'取消收藏成功'**
  String get unfavoriteSuccess;

  /// 操作失败提示
  ///
  /// In zh, this message translates to:
  /// **'操作失败'**
  String get operationFailed;

  /// 咨询医生按钮
  ///
  /// In zh, this message translates to:
  /// **'咨询医生'**
  String get consultDoctor;

  /// 医生详情标题
  ///
  /// In zh, this message translates to:
  /// **'医生详情'**
  String get doctorDetails;

  /// 擅长领域标签
  ///
  /// In zh, this message translates to:
  /// **'擅长领域'**
  String get specialties;

  /// 医生推荐标题
  ///
  /// In zh, this message translates to:
  /// **'医生推荐'**
  String get doctorRecommendations;

  /// 工作时间
  ///
  /// In zh, this message translates to:
  /// **'周一至周五 9:00-17:00'**
  String get workingHours;

  /// 无法打开电话应用提示
  ///
  /// In zh, this message translates to:
  /// **'无法打开电话应用，号码已复制到剪贴板'**
  String get cannotOpenPhoneApp;

  /// 操作失败手动拨打提示
  ///
  /// In zh, this message translates to:
  /// **'操作失败，请手动拨打'**
  String get operationFailedManualDial;

  /// 医师职称
  ///
  /// In zh, this message translates to:
  /// **'医师'**
  String get physician;

  /// 暂无描述提示
  ///
  /// In zh, this message translates to:
  /// **'暂无描述'**
  String get noDescription;

  /// 查看详情按钮
  ///
  /// In zh, this message translates to:
  /// **'查看详情'**
  String get viewDetails;

  /// 复制成功提示
  ///
  /// In zh, this message translates to:
  /// **'{content}已复制到剪贴板'**
  String copiedToClipboard(String content);

  /// 复制失败提示
  ///
  /// In zh, this message translates to:
  /// **'复制失败'**
  String get copyFailed;

  /// 地图准备中请等待
  ///
  /// In zh, this message translates to:
  /// **'地图正在准备中，请稍后再试'**
  String get mapPreparingPleaseWait;

  /// 地图标题
  ///
  /// In zh, this message translates to:
  /// **'地图'**
  String get mapTitle;

  /// 健康助手语音识别失败
  ///
  /// In zh, this message translates to:
  /// **'健康助手语音识别失败'**
  String get aiGuideVoiceRecognitionFailure;

  /// 正在搜索特定分类
  ///
  /// In zh, this message translates to:
  /// **'正在搜索{category}...'**
  String searchingCategory(String category);

  /// 尝试其他分类或检查网络
  ///
  /// In zh, this message translates to:
  /// **'请尝试选择其他分类或检查网络连接'**
  String get tryOtherCategoriesOrCheckNetwork;

  /// 在城市中未找到分类结果
  ///
  /// In zh, this message translates to:
  /// **'未找到{city}{category}'**
  String noResultsFoundFor(String category, String city);

  /// 未找到相关分类
  ///
  /// In zh, this message translates to:
  /// **'未找到相关{category}'**
  String noRelatedCategoryFound(String category);

  /// 地址标签
  ///
  /// In zh, this message translates to:
  /// **'地址'**
  String get address;

  /// 地址标签
  ///
  /// In zh, this message translates to:
  /// **'地址：{address}'**
  String addressLabel(String address);

  /// 导航
  ///
  /// In zh, this message translates to:
  /// **'导航'**
  String get navigation;

  /// 打开导航失败
  ///
  /// In zh, this message translates to:
  /// **'打开导航失败: {error}'**
  String openNavigationFailed(String error);

  /// 公园广场分类
  ///
  /// In zh, this message translates to:
  /// **'公园广场'**
  String get parksAndSquares;

  /// 公园分类
  ///
  /// In zh, this message translates to:
  /// **'公园'**
  String get parks;

  /// 动物园分类
  ///
  /// In zh, this message translates to:
  /// **'动物园'**
  String get zoos;

  /// 植物园分类
  ///
  /// In zh, this message translates to:
  /// **'植物园'**
  String get botanicalGardens;

  /// 水族馆分类
  ///
  /// In zh, this message translates to:
  /// **'水族馆'**
  String get aquariums;

  /// 城市广场分类
  ///
  /// In zh, this message translates to:
  /// **'城市广场'**
  String get citySquares;

  /// 纪念馆分类
  ///
  /// In zh, this message translates to:
  /// **'纪念馆'**
  String get memorialHalls;

  /// 寺庙道观分类
  ///
  /// In zh, this message translates to:
  /// **'寺庙道观'**
  String get templesAndTaoistTemples;

  /// 教堂分类
  ///
  /// In zh, this message translates to:
  /// **'教堂'**
  String get churches;

  /// 海滩分类
  ///
  /// In zh, this message translates to:
  /// **'海滩'**
  String get beaches;

  /// 加载详情失败
  ///
  /// In zh, this message translates to:
  /// **'加载详情失败: {error}'**
  String loadDetailsFailed(String error);

  /// 特色美食
  ///
  /// In zh, this message translates to:
  /// **'特色美食'**
  String get specialtyFood;

  /// 联系信息
  ///
  /// In zh, this message translates to:
  /// **'联系方式'**
  String get contactInfo;

  /// 网站
  ///
  /// In zh, this message translates to:
  /// **'网站'**
  String get website;

  /// 正在生成详情信息
  ///
  /// In zh, this message translates to:
  /// **'正在生成详情信息...'**
  String get generatingDetailInfo;

  /// 查看AI生成的详细介绍
  ///
  /// In zh, this message translates to:
  /// **'查看AI生成的详细介绍'**
  String get viewAiGeneratedDetailedIntroduction;

  /// 点击获取AI生成的详细介绍
  ///
  /// In zh, this message translates to:
  /// **'点击获取AI生成的详细介绍'**
  String get clickToGetAiGeneratedDetailedIntroduction;

  /// AI正在生成
  ///
  /// In zh, this message translates to:
  /// **'AI正在生成中...'**
  String get aiGenerating;

  /// 生成详情失败
  ///
  /// In zh, this message translates to:
  /// **'生成详情失败，请稍后重试'**
  String get generateDetailsFailed;

  /// 打开导航失败错误
  ///
  /// In zh, this message translates to:
  /// **'打开导航失败: {error}'**
  String openNavigationFailedError(String error);

  /// 地址已复制到剪贴板
  ///
  /// In zh, this message translates to:
  /// **'地址已复制到剪贴板'**
  String get addressCopiedToClipboard;

  /// 景区类型
  ///
  /// In zh, this message translates to:
  /// **'风景名胜'**
  String get scenicSpotType;

  /// 打开导航失败错误
  ///
  /// In zh, this message translates to:
  /// **'打开导航失败: {error}'**
  String openNavigationFailedWithError(String error);

  /// 地图加载失败
  ///
  /// In zh, this message translates to:
  /// **'地图加载失败'**
  String get mapLoadFailed;

  /// 无法加载地图，请返回并重试
  ///
  /// In zh, this message translates to:
  /// **'无法加载地图，请返回并重试'**
  String get unableToLoadMapPleaseRetry;

  /// 返回
  ///
  /// In zh, this message translates to:
  /// **'返回'**
  String get back;

  /// 定位到当前位置
  ///
  /// In zh, this message translates to:
  /// **'定位到当前位置'**
  String get locateToCurrentPosition;

  /// 搜索地点...
  ///
  /// In zh, this message translates to:
  /// **'搜索地点...'**
  String get searchLocation;

  /// 已找到地点
  ///
  /// In zh, this message translates to:
  /// **'已找到：{name}'**
  String foundLocation(String name);

  /// 地图控制器未初始化
  ///
  /// In zh, this message translates to:
  /// **'地图控制器未初始化，请返回并重试'**
  String get mapControllerNotInitialized;

  /// 定位服务异常
  ///
  /// In zh, this message translates to:
  /// **'定位服务异常：{error}'**
  String locationServiceException(String error);

  /// 地图未完全加载
  ///
  /// In zh, this message translates to:
  /// **'地图未完全加载，请稍后重试'**
  String get mapNotFullyLoaded;

  /// 定位失败
  ///
  /// In zh, this message translates to:
  /// **'定位失败：{error}'**
  String locationFailed(String error);

  /// 相机权限需要
  ///
  /// In zh, this message translates to:
  /// **'无法打开相机，请检查权限设置'**
  String get cameraPermissionNeeded;

  /// 健康助手识别
  ///
  /// In zh, this message translates to:
  /// **'健康助手识别'**
  String get aiTourGuideRecognition;

  /// 健康助手语音识别
  ///
  /// In zh, this message translates to:
  /// **'健康助手语音识别'**
  String get aiTourGuideVoiceRecognition;

  /// 用户头像功能开发中
  ///
  /// In zh, this message translates to:
  /// **'用户头像功能开发中'**
  String get userAvatarFeatureInDevelopment;

  /// 功能开发中
  ///
  /// In zh, this message translates to:
  /// **'功能开发中'**
  String get inDevelopment;

  /// 关闭提示
  ///
  /// In zh, this message translates to:
  /// **'关闭'**
  String get close;

  /// 概览标签
  ///
  /// In zh, this message translates to:
  /// **'概览'**
  String get overview;

  /// 记录标签
  ///
  /// In zh, this message translates to:
  /// **'记录'**
  String get records;

  /// 团队标签
  ///
  /// In zh, this message translates to:
  /// **'团队'**
  String get team;

  /// 推广标签
  ///
  /// In zh, this message translates to:
  /// **'推广'**
  String get promotion;

  /// 加载更多失败
  ///
  /// In zh, this message translates to:
  /// **'加载更多失败'**
  String get loadMoreFailed;

  /// 获取用户列表失败
  ///
  /// In zh, this message translates to:
  /// **'获取用户列表失败'**
  String get getUserListFailed;

  /// 等级修改成功
  ///
  /// In zh, this message translates to:
  /// **'用户等级修改成功'**
  String get levelUpdateSuccess;

  /// 修改等级失败
  ///
  /// In zh, this message translates to:
  /// **'修改等级失败'**
  String get levelUpdateFailed;

  /// 已认证分销员
  ///
  /// In zh, this message translates to:
  /// **'已认证分销员'**
  String get certifiedDistributor;

  /// 资金详情
  ///
  /// In zh, this message translates to:
  /// **'资金详情'**
  String get fundsDetail;

  /// 提现中
  ///
  /// In zh, this message translates to:
  /// **'提现中'**
  String get withdrawing;

  /// 已提现
  ///
  /// In zh, this message translates to:
  /// **'已提现'**
  String get withdrawn;

  /// 分销佣金总收入
  ///
  /// In zh, this message translates to:
  /// **'分销佣金总收入'**
  String get totalCommissionIncome;

  /// 暂无推广海报
  ///
  /// In zh, this message translates to:
  /// **'暂无推广海报'**
  String get noPromotionPosters;

  /// 测试说明
  ///
  /// In zh, this message translates to:
  /// **'测试说明：'**
  String get testDescription;

  /// 长按选择说明
  ///
  /// In zh, this message translates to:
  /// **'长按下方的文本进行选择，复制的内容只包含汉字。'**
  String get longPressToSelect;

  /// 智能复制版本
  ///
  /// In zh, this message translates to:
  /// **'智能复制版本：'**
  String get smartCopyVersion;

  /// 纯文本版本
  ///
  /// In zh, this message translates to:
  /// **'纯文本版本：'**
  String get plainTextVersion;

  /// 智能复制功能测试
  ///
  /// In zh, this message translates to:
  /// **'智能复制功能测试'**
  String get smartCopyTest;

  /// 加载历史记录失败
  ///
  /// In zh, this message translates to:
  /// **'加载历史记录失败: {error}'**
  String loadHistoryFailed(String error);

  /// 清空历史记录失败
  ///
  /// In zh, this message translates to:
  /// **'清空历史记录失败: {error}'**
  String clearHistoryFailure(String error);

  /// 相机访问失败
  ///
  /// In zh, this message translates to:
  /// **'无法打开相机，请检查权限设置'**
  String get cameraAccessFailure;

  /// 使用说明
  ///
  /// In zh, this message translates to:
  /// **'您的专属AI健康导游，随时为您提供健康咨询和导游服务'**
  String get instructions;

  /// 输入手机号提示
  ///
  /// In zh, this message translates to:
  /// **'请输入11位手机号'**
  String get enterPhoneNumber;

  /// 输入正确验证码
  ///
  /// In zh, this message translates to:
  /// **'请输入正确的验证码'**
  String get enterCorrectVerificationCode;

  /// 正在发送验证码
  ///
  /// In zh, this message translates to:
  /// **'正在发送验证码...'**
  String get sendingVerificationCode;

  /// 验证码已发送至手机
  ///
  /// In zh, this message translates to:
  /// **'验证码已发送至您的手机'**
  String get verificationCodeSentToPhone;

  /// 网络连接失败
  ///
  /// In zh, this message translates to:
  /// **'网络连接失败，请稍后重试'**
  String get networkConnectionFailed;

  /// 网络请求失败
  ///
  /// In zh, this message translates to:
  /// **'网络请求失败，状态码: {statusCode}'**
  String networkRequestFailed(String statusCode);

  /// 发送验证码出错
  ///
  /// In zh, this message translates to:
  /// **'发送验证码出错: {error}'**
  String sendVerificationCodeError(String error);

  /// 重置密码中
  ///
  /// In zh, this message translates to:
  /// **'正在重置密码...'**
  String get resettingPassword;

  /// 处理用户数据出错
  ///
  /// In zh, this message translates to:
  /// **'处理用户数据时出错: {error}'**
  String processingUserDataError(String error);

  /// 登录成功但无用户数据
  ///
  /// In zh, this message translates to:
  /// **'登录成功但用户数据为空'**
  String get loginSuccessButNoUserData;

  /// 用户未注册跳转注册
  ///
  /// In zh, this message translates to:
  /// **'用户未注册，即将跳转到注册页面'**
  String get userNotRegisteredRedirecting;

  /// 历史记录已删除
  ///
  /// In zh, this message translates to:
  /// **'历史记录已删除'**
  String get historyDeleted;

  /// 删除历史记录失败
  ///
  /// In zh, this message translates to:
  /// **'删除历史记录失败: {error}'**
  String deleteHistoryFailed(String error);

  /// 未检测到相机
  ///
  /// In zh, this message translates to:
  /// **'未检测到可用相机'**
  String get noCameraDetected;

  /// 相机初始化失败
  ///
  /// In zh, this message translates to:
  /// **'相机初始化失败: {error}'**
  String cameraInitializationFailed(String error);

  /// 相机未准备就绪
  ///
  /// In zh, this message translates to:
  /// **'相机未准备就绪'**
  String get cameraNotReady;

  /// 拍照失败
  ///
  /// In zh, this message translates to:
  /// **'拍照失败: {error}'**
  String capturePhotoFailed(String error);

  /// 需要相册权限
  ///
  /// In zh, this message translates to:
  /// **'需要相册权限才能选择图片'**
  String get galleryPermissionRequired;

  /// 选择图片失败消息
  ///
  /// In zh, this message translates to:
  /// **'选择图片失败: {error}'**
  String selectImageFailed(String error);

  /// 手电筒操作失败
  ///
  /// In zh, this message translates to:
  /// **'手电筒操作失败'**
  String get flashlightOperationFailed;

  /// 切换摄像头失败
  ///
  /// In zh, this message translates to:
  /// **'切换摄像头失败'**
  String get switchCameraFailed;

  /// 语言不支持作为源语言
  ///
  /// In zh, this message translates to:
  /// **'{language}不支持作为源语言，无法切换'**
  String languageNotSupportedAsSource(String language);

  /// 输入用户名
  ///
  /// In zh, this message translates to:
  /// **'请输入用户名'**
  String get enterUsername;

  /// 密码最小长度6位
  ///
  /// In zh, this message translates to:
  /// **'密码长度至少为6位'**
  String get passwordMinLength6;

  /// 密码不一致
  ///
  /// In zh, this message translates to:
  /// **'两次输入的密码不一致'**
  String get passwordsNotMatch;

  /// 同意用户协议
  ///
  /// In zh, this message translates to:
  /// **'请同意用户协议和隐私政策'**
  String get agreeToUserAgreement;

  /// 注册中
  ///
  /// In zh, this message translates to:
  /// **'正在注册...'**
  String get registering;

  /// 注册成功
  ///
  /// In zh, this message translates to:
  /// **'注册成功'**
  String get registerSuccess;

  /// 手机号格式不正确
  ///
  /// In zh, this message translates to:
  /// **'手机号格式不正确'**
  String get phoneFormatIncorrect;

  /// 验证码过期
  ///
  /// In zh, this message translates to:
  /// **'验证码错误或已过期'**
  String get verificationCodeExpired;

  /// 用户名已注册
  ///
  /// In zh, this message translates to:
  /// **'用户名已被注册，请更换用户名'**
  String get usernameAlreadyRegistered;

  /// 手机号已注册
  ///
  /// In zh, this message translates to:
  /// **'该手机号已注册，可直接登录'**
  String get phoneAlreadyRegistered;

  /// 注册失败
  ///
  /// In zh, this message translates to:
  /// **'注册失败: {message}'**
  String registerFailed(String message);

  /// 注册过程出错
  ///
  /// In zh, this message translates to:
  /// **'注册过程中出错: {error}'**
  String registerProcessError(String error);

  /// 打开用户协议
  ///
  /// In zh, this message translates to:
  /// **'打开用户协议'**
  String get openUserAgreement;

  /// 打开隐私政策
  ///
  /// In zh, this message translates to:
  /// **'打开隐私政策'**
  String get openPrivacyPolicy;

  /// 价格信息加载中
  ///
  /// In zh, this message translates to:
  /// **'价格信息加载中，请稍候...'**
  String get priceInfoLoadingWait;

  /// 价格信息加载失败
  ///
  /// In zh, this message translates to:
  /// **'获取价格信息失败，显示默认价格'**
  String get priceInfoLoadFailed;

  /// 录音启动失败
  ///
  /// In zh, this message translates to:
  /// **'录音启动失败，请检查麦克风权限'**
  String get recordingStartFailed;

  /// 录音启动出错
  ///
  /// In zh, this message translates to:
  /// **'录音启动出错，请重试'**
  String get recordingStartError;

  /// 录音失败重试
  ///
  /// In zh, this message translates to:
  /// **'录音失败，请重试'**
  String get recordingFailedRetry;

  /// 音频处理失败重试
  ///
  /// In zh, this message translates to:
  /// **'音频处理失败，请重试'**
  String get audioProcessingFailedRetry;

  /// 语音处理出错
  ///
  /// In zh, this message translates to:
  /// **'处理语音时出错：{error}'**
  String voiceProcessingError(String error);

  /// 播放失败
  ///
  /// In zh, this message translates to:
  /// **'播放失败: {error}'**
  String playbackFailed(String error);

  /// 录音需要麦克风权限
  ///
  /// In zh, this message translates to:
  /// **'无法录音：需要麦克风权限'**
  String get microphoneRecordingPermissionRequired;

  /// 权限已授予重试录音
  ///
  /// In zh, this message translates to:
  /// **'权限已授予，请重新长按录音按钮开始录音'**
  String get permissionGrantedRetryRecording;

  /// 退出登录失败
  ///
  /// In zh, this message translates to:
  /// **'退出登录失败: {error}'**
  String logoutFailedError(String error);

  /// 健康助手识别结果
  ///
  /// In zh, this message translates to:
  /// **'健康助手识别: {text}'**
  String aiTourGuideRecognitionResult(String text);

  /// 健康助手语音识别结果
  ///
  /// In zh, this message translates to:
  /// **'健康助手语音识别: {text}'**
  String aiTourGuideVoiceRecognitionResult(String text);

  /// 个人资料页面标题
  ///
  /// In zh, this message translates to:
  /// **'我的'**
  String get profileTitle;

  /// 编辑个人资料页面标题
  ///
  /// In zh, this message translates to:
  /// **'编辑个人资料'**
  String get editProfileTitle;

  /// 健康信息标题
  ///
  /// In zh, this message translates to:
  /// **'健康信息'**
  String get healthInfo;

  /// 身高输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入身高 (cm)'**
  String get heightHint;

  /// 身高验证提示
  ///
  /// In zh, this message translates to:
  /// **'请输入有效的身高 (50-250cm)'**
  String get heightValidation;

  /// 体重输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入体重 (kg)'**
  String get weightHint;

  /// 体重验证提示
  ///
  /// In zh, this message translates to:
  /// **'请输入有效的体重 (20-300kg)'**
  String get weightValidation;

  /// 血型选择提示
  ///
  /// In zh, this message translates to:
  /// **'请选择血型'**
  String get selectBloodType;

  /// A型血
  ///
  /// In zh, this message translates to:
  /// **'A型'**
  String get bloodTypeA;

  /// B型血
  ///
  /// In zh, this message translates to:
  /// **'B型'**
  String get bloodTypeB;

  /// AB型血
  ///
  /// In zh, this message translates to:
  /// **'AB型'**
  String get bloodTypeAB;

  /// O型血
  ///
  /// In zh, this message translates to:
  /// **'O型'**
  String get bloodTypeO;

  /// 血型不清楚
  ///
  /// In zh, this message translates to:
  /// **'不清楚'**
  String get bloodTypeUnknown;

  /// 常住地址标签
  ///
  /// In zh, this message translates to:
  /// **'常住地址'**
  String get residentialAddress;

  /// 定位按钮
  ///
  /// In zh, this message translates to:
  /// **'定位'**
  String get locate;

  /// 选择常住地址提示
  ///
  /// In zh, this message translates to:
  /// **'请选择常住地址'**
  String get selectResidentialAddress;

  /// 地区选择失败提示
  ///
  /// In zh, this message translates to:
  /// **'地区选择失败，请重试'**
  String get regionSelectionFailed;

  /// 常见过敏源标题
  ///
  /// In zh, this message translates to:
  /// **'常见过敏源'**
  String get commonAllergens;

  /// 青霉素过敏
  ///
  /// In zh, this message translates to:
  /// **'青霉素类药物'**
  String get penicillinAllergy;

  /// 头孢过敏
  ///
  /// In zh, this message translates to:
  /// **'头孢类药物'**
  String get cephalosporinAllergy;

  /// 阿司匹林过敏
  ///
  /// In zh, this message translates to:
  /// **'阿司匹林'**
  String get aspirinAllergy;

  /// 花生过敏
  ///
  /// In zh, this message translates to:
  /// **'花生'**
  String get peanutAllergy;

  /// 海鲜过敏
  ///
  /// In zh, this message translates to:
  /// **'海鲜'**
  String get seafoodAllergy;

  /// 牛奶过敏
  ///
  /// In zh, this message translates to:
  /// **'牛奶'**
  String get milkAllergy;

  /// 鸡蛋过敏
  ///
  /// In zh, this message translates to:
  /// **'鸡蛋'**
  String get eggAllergy;

  /// 花粉尘螨过敏
  ///
  /// In zh, this message translates to:
  /// **'花粉/尘螨'**
  String get pollenDustMiteAllergy;

  /// 其他过敏物质标签
  ///
  /// In zh, this message translates to:
  /// **'其他过敏物质'**
  String get otherAllergens;

  /// 其他过敏源输入提示
  ///
  /// In zh, this message translates to:
  /// **'请补充其他过敏源'**
  String get otherAllergensHint;

  /// 是否在服用药物询问
  ///
  /// In zh, this message translates to:
  /// **'目前是否在服用任何药物'**
  String get takingMedication;

  /// 药物清单标签
  ///
  /// In zh, this message translates to:
  /// **'药物清单'**
  String get medicationList;

  /// 药物清单输入提示
  ///
  /// In zh, this message translates to:
  /// **'请列出正在服用的药物名称、剂量和频率'**
  String get medicationListHint;

  /// 是否患有慢性疾病询问
  ///
  /// In zh, this message translates to:
  /// **'是否患有慢性疾病'**
  String get hasChronicDisease;

  /// 具体病症标题
  ///
  /// In zh, this message translates to:
  /// **'具体病症'**
  String get specificSymptoms;

  /// 高血压
  ///
  /// In zh, this message translates to:
  /// **'高血压'**
  String get hypertension;

  /// 血压输入提示
  ///
  /// In zh, this message translates to:
  /// **'例如 130/85 mmHg'**
  String get bloodPressureHint;

  /// 糖尿病
  ///
  /// In zh, this message translates to:
  /// **'糖尿病'**
  String get diabetes;

  /// 血糖输入提示
  ///
  /// In zh, this message translates to:
  /// **'例如 5.8 mmol/L'**
  String get bloodSugarHint;

  /// 其他慢性疾病标签
  ///
  /// In zh, this message translates to:
  /// **'其他慢性疾病'**
  String get otherChronicDiseases;

  /// 其他慢性疾病输入提示
  ///
  /// In zh, this message translates to:
  /// **'请补充其他慢性疾病'**
  String get otherChronicDiseasesHint;

  /// 手术与住院史标题
  ///
  /// In zh, this message translates to:
  /// **'手术与住院史'**
  String get surgeryHistory;

  /// 是否有手术住院史询问
  ///
  /// In zh, this message translates to:
  /// **'过去是否有过手术或住院经历'**
  String get hasSurgeryHistory;

  /// 手术详情标签
  ///
  /// In zh, this message translates to:
  /// **'详情说明'**
  String get surgeryDetails;

  /// 手术详情输入提示
  ///
  /// In zh, this message translates to:
  /// **'请描述手术或住院的详细情况'**
  String get surgeryDetailsHint;

  /// 家族病史标题
  ///
  /// In zh, this message translates to:
  /// **'家族病史'**
  String get familyHistory;

  /// 家族疾病史询问
  ///
  /// In zh, this message translates to:
  /// **'直系亲属（父母、兄弟姐妹、子女）是否有以下疾病'**
  String get familyDiseaseHistory;

  /// 家族高血压史
  ///
  /// In zh, this message translates to:
  /// **'高血压'**
  String get familyHypertension;

  /// 家族糖尿病史
  ///
  /// In zh, this message translates to:
  /// **'糖尿病'**
  String get familyDiabetes;

  /// 家族心脏病史
  ///
  /// In zh, this message translates to:
  /// **'心脏病'**
  String get familyHeartDisease;

  /// 家族中风史
  ///
  /// In zh, this message translates to:
  /// **'中风'**
  String get familyStroke;

  /// 家族癌症史
  ///
  /// In zh, this message translates to:
  /// **'癌症'**
  String get familyCancer;

  /// 家族精神健康疾病史
  ///
  /// In zh, this message translates to:
  /// **'精神健康疾病'**
  String get familyMentalHealth;

  /// 其他家族病史标签
  ///
  /// In zh, this message translates to:
  /// **'其他家族病史补充'**
  String get otherFamilyHistory;

  /// 其他家族病史输入提示
  ///
  /// In zh, this message translates to:
  /// **'请补充其他家族病史'**
  String get otherFamilyHistoryHint;

  /// 久坐运动频率
  ///
  /// In zh, this message translates to:
  /// **'久坐 (基本不运动)'**
  String get exerciseSedentary;

  /// 轻度运动频率
  ///
  /// In zh, this message translates to:
  /// **'轻度活跃 (每周运动1-2次)'**
  String get exerciseLight;

  /// 中度运动频率
  ///
  /// In zh, this message translates to:
  /// **'中度活跃 (每周运动3-5次)'**
  String get exerciseModerate;

  /// 高度运动频率
  ///
  /// In zh, this message translates to:
  /// **'非常活跃 (每周运动6次及以上)'**
  String get exerciseActive;

  /// 饮食偏好标签
  ///
  /// In zh, this message translates to:
  /// **'日常饮食偏好'**
  String get dietaryPreferences;

  /// 饮食均衡
  ///
  /// In zh, this message translates to:
  /// **'饮食均衡'**
  String get balancedDiet;

  /// 偏素食
  ///
  /// In zh, this message translates to:
  /// **'偏素食'**
  String get vegetarianDiet;

  /// 偏肉食
  ///
  /// In zh, this message translates to:
  /// **'偏肉食'**
  String get meatDiet;

  /// 偏好油腻食物
  ///
  /// In zh, this message translates to:
  /// **'偏好油腻食物'**
  String get oilyFood;

  /// 偏好咸味食物
  ///
  /// In zh, this message translates to:
  /// **'偏好咸味食物'**
  String get saltyFood;

  /// 偏好甜食
  ///
  /// In zh, this message translates to:
  /// **'偏好甜食'**
  String get sweetFood;

  /// 从不吸烟
  ///
  /// In zh, this message translates to:
  /// **'从不吸烟'**
  String get neverSmoke;

  /// 已戒烟
  ///
  /// In zh, this message translates to:
  /// **'已戒烟'**
  String get quitSmoking;

  /// 偶尔吸烟
  ///
  /// In zh, this message translates to:
  /// **'偶尔吸烟 (非每日)'**
  String get occasionalSmoking;

  /// 经常吸烟
  ///
  /// In zh, this message translates to:
  /// **'经常吸烟 (每日)'**
  String get dailySmoking;

  /// 从不饮酒
  ///
  /// In zh, this message translates to:
  /// **'从不饮酒'**
  String get neverDrink;

  /// 已戒酒
  ///
  /// In zh, this message translates to:
  /// **'已戒酒'**
  String get quitDrinking;

  /// 偶尔社交性饮酒
  ///
  /// In zh, this message translates to:
  /// **'偶尔社交性饮酒'**
  String get socialDrinking;

  /// 每周1-3次饮酒
  ///
  /// In zh, this message translates to:
  /// **'每周1-3次'**
  String get weeklyDrinking;

  /// 几乎每天饮酒
  ///
  /// In zh, this message translates to:
  /// **'几乎每天'**
  String get dailyDrinking;

  /// 少于6小时睡眠
  ///
  /// In zh, this message translates to:
  /// **'少于6小时'**
  String get sleepLessThan6;

  /// 6-7小时睡眠
  ///
  /// In zh, this message translates to:
  /// **'6-7小时'**
  String get sleep6To7;

  /// 7-8小时睡眠
  ///
  /// In zh, this message translates to:
  /// **'7-8小时'**
  String get sleep7To8;

  /// 8小时以上睡眠
  ///
  /// In zh, this message translates to:
  /// **'8小时以上'**
  String get sleepMoreThan8;

  /// 睡眠质量良好
  ///
  /// In zh, this message translates to:
  /// **'良好 (容易入睡，很少惊醒)'**
  String get sleepGood;

  /// 睡眠质量一般
  ///
  /// In zh, this message translates to:
  /// **'一般 (偶有入睡困难或早醒)'**
  String get sleepFair;

  /// 睡眠质量较差
  ///
  /// In zh, this message translates to:
  /// **'较差 (长期入睡困难、多梦、早醒)'**
  String get sleepPoor;

  /// 压力很小
  ///
  /// In zh, this message translates to:
  /// **'很小'**
  String get stressLow;

  /// 稍有压力
  ///
  /// In zh, this message translates to:
  /// **'稍有压力'**
  String get stressMild;

  /// 压力适中
  ///
  /// In zh, this message translates to:
  /// **'压力适中'**
  String get stressModerate;

  /// 压力较大
  ///
  /// In zh, this message translates to:
  /// **'压力较大'**
  String get stressHigh;

  /// 压力极大
  ///
  /// In zh, this message translates to:
  /// **'压力极大'**
  String get stressExtreme;

  /// 女性健康标题
  ///
  /// In zh, this message translates to:
  /// **'女性健康'**
  String get womenHealth;

  /// 是否已绝经询问
  ///
  /// In zh, this message translates to:
  /// **'是否已绝经'**
  String get isMenopause;

  /// 月经周期是否规律询问
  ///
  /// In zh, this message translates to:
  /// **'月经周期是否规律'**
  String get menstrualCycleRegular;

  /// 月经规律
  ///
  /// In zh, this message translates to:
  /// **'规律'**
  String get menstrualRegular;

  /// 月经不规律
  ///
  /// In zh, this message translates to:
  /// **'不规律'**
  String get menstrualIrregular;

  /// 月经周期不确定
  ///
  /// In zh, this message translates to:
  /// **'不确定'**
  String get menstrualUncertain;

  /// 是否曾怀孕询问
  ///
  /// In zh, this message translates to:
  /// **'是否曾怀孕'**
  String get hasPregnancy;

  /// 生育次数标签
  ///
  /// In zh, this message translates to:
  /// **'生育次数'**
  String get birthCount;

  /// 生育0次
  ///
  /// In zh, this message translates to:
  /// **'0次'**
  String get birthCount0;

  /// 生育1次
  ///
  /// In zh, this message translates to:
  /// **'1次'**
  String get birthCount1;

  /// 生育2次
  ///
  /// In zh, this message translates to:
  /// **'2次'**
  String get birthCount2;

  /// 生育3次
  ///
  /// In zh, this message translates to:
  /// **'3次'**
  String get birthCount3;

  /// 生育4次
  ///
  /// In zh, this message translates to:
  /// **'4次'**
  String get birthCount4;

  /// 生育5次及以上
  ///
  /// In zh, this message translates to:
  /// **'5次及以上'**
  String get birthCount5Plus;

  /// 无法解析图片文件错误
  ///
  /// In zh, this message translates to:
  /// **'无法解析图片文件'**
  String get cannotParseImage;

  /// VIP会员徽章
  ///
  /// In zh, this message translates to:
  /// **'VIP会员'**
  String get vipMemberBadge;

  /// 普通用户徽章
  ///
  /// In zh, this message translates to:
  /// **'普通用户'**
  String get normalUserBadge;

  /// 分销员等级徽章
  ///
  /// In zh, this message translates to:
  /// **'分销员 Lv.{level}'**
  String distributorLevelBadge(String level);

  /// 分销员徽章
  ///
  /// In zh, this message translates to:
  /// **'分销员'**
  String get distributorBadge;

  /// 阿拉伯语
  ///
  /// In zh, this message translates to:
  /// **'阿拉伯语'**
  String get arabic;

  /// 英语
  ///
  /// In zh, this message translates to:
  /// **'英语'**
  String get english;

  /// 语言切换不支持提示
  ///
  /// In zh, this message translates to:
  /// **'{language}不支持作为源语言，无法切换'**
  String languageSwitchNotSupported(String language);

  /// 源语言标签
  ///
  /// In zh, this message translates to:
  /// **'源'**
  String get sourceLanguageLabel;

  /// 目标语言标签
  ///
  /// In zh, this message translates to:
  /// **'目标'**
  String get targetLanguageLabel;

  /// 当前余额
  ///
  /// In zh, this message translates to:
  /// **'当前余额'**
  String get currentBalance;

  /// 今日收入
  ///
  /// In zh, this message translates to:
  /// **'今日收入'**
  String get todayIncome;

  /// 总收入
  ///
  /// In zh, this message translates to:
  /// **'总收入'**
  String get totalIncome;

  /// 按住麦克风开始说话
  ///
  /// In zh, this message translates to:
  /// **'按住麦克风开始说话'**
  String get holdMicrophoneToSpeak;

  /// 等待对方说话
  ///
  /// In zh, this message translates to:
  /// **'等待对方说话'**
  String get waitingForOtherParty;

  /// 确认清空对话标题
  ///
  /// In zh, this message translates to:
  /// **'确认清空'**
  String get confirmClear;

  /// 确认清空所有聊天记录
  ///
  /// In zh, this message translates to:
  /// **'确定要清空所有聊天记录吗？此操作无法撤销。'**
  String get confirmClearAllChatRecords;

  /// 确定清空
  ///
  /// In zh, this message translates to:
  /// **'确定清空'**
  String get confirmClearAction;

  /// 注册页面标题
  ///
  /// In zh, this message translates to:
  /// **'注册用户'**
  String get registerTitle;

  /// 用户名输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入用户名'**
  String get usernameHint;

  /// 设置密码提示
  ///
  /// In zh, this message translates to:
  /// **'设置密码'**
  String get setPassword;

  /// 确认密码提示
  ///
  /// In zh, this message translates to:
  /// **'重输密码'**
  String get confirmPassword;

  /// 获取验证码按钮
  ///
  /// In zh, this message translates to:
  /// **'获取验证码'**
  String get getCodeButton;

  /// 倒计时秒数
  ///
  /// In zh, this message translates to:
  /// **'{count}秒'**
  String countdownSeconds(int count);

  /// 注册按钮
  ///
  /// In zh, this message translates to:
  /// **'注册'**
  String get register;

  /// 手机号登录按钮
  ///
  /// In zh, this message translates to:
  /// **'手机号登录'**
  String get phoneNumberLogin;

  /// 用户协议和隐私政策
  ///
  /// In zh, this message translates to:
  /// **'《用户协议》和《隐私政策》'**
  String get userAgreementAndPrivacyPolicy;

  /// 我已阅读并同意前缀
  ///
  /// In zh, this message translates to:
  /// **'我已阅读并同意'**
  String get iAgreeToThe;

  /// 帮助与反馈页面标题
  ///
  /// In zh, this message translates to:
  /// **'帮助与反馈'**
  String get helpFeedbackTitle;

  /// 姓名输入框标签
  ///
  /// In zh, this message translates to:
  /// **'您的称呼 (可选)'**
  String get yourNameOptional;

  /// 手机号输入框标签
  ///
  /// In zh, this message translates to:
  /// **'您的手机号'**
  String get yourPhoneNumber;

  /// 问题描述输入框标签
  ///
  /// In zh, this message translates to:
  /// **'请详细描述您的问题或建议'**
  String get describeProblemDetail;

  /// 提交反馈按钮
  ///
  /// In zh, this message translates to:
  /// **'提交反馈'**
  String get submitFeedback;

  /// 手机号格式错误提示
  ///
  /// In zh, this message translates to:
  /// **'请输入正确的手机号格式'**
  String get pleaseEnterCorrectPhoneFormat;

  /// 请描述问题验证提示
  ///
  /// In zh, this message translates to:
  /// **'请描述您遇到的问题'**
  String get pleaseDescribeProblem;

  /// 描述最小长度提示
  ///
  /// In zh, this message translates to:
  /// **'问题描述至少需要10个字符'**
  String get descriptionMinLength;

  /// 描述最大长度提示
  ///
  /// In zh, this message translates to:
  /// **'问题描述不能超过1000个字符'**
  String get descriptionMaxLength;

  /// 提交反馈中提示
  ///
  /// In zh, this message translates to:
  /// **'正在提交反馈...'**
  String get submittingFeedback;

  /// 反馈提交成功提示
  ///
  /// In zh, this message translates to:
  /// **'反馈提交成功，感谢您的建议！'**
  String get feedbackSubmittedSuccess;

  /// 反馈提交失败提示
  ///
  /// In zh, this message translates to:
  /// **'反馈提交失败: {error}'**
  String feedbackSubmissionFailed(String error);

  /// 反馈说明标题
  ///
  /// In zh, this message translates to:
  /// **'反馈说明'**
  String get feedbackInstructions;

  /// 反馈说明文本
  ///
  /// In zh, this message translates to:
  /// **'我们会收集您的问题描述和相关应用数据（不包含敏感信息）以便更好地解决您的问题。提交后我们将通过您提供的手机号与您联系。'**
  String get feedbackInstructionsText;

  /// 输入姓名提示
  ///
  /// In zh, this message translates to:
  /// **'请输入您的称呼'**
  String get enterYourName;

  /// 问题描述提示文本
  ///
  /// In zh, this message translates to:
  /// **'请详细描述您遇到的问题或建议\n包括：\n• 具体的操作步骤\n• 期望的结果\n• 实际发生的情况\n• 其他相关信息'**
  String get problemDescriptionHint;

  /// 提交中状态文本
  ///
  /// In zh, this message translates to:
  /// **'提交中...'**
  String get submitting;

  /// 测试日志生成按钮
  ///
  /// In zh, this message translates to:
  /// **'测试日志生成'**
  String get testLogGeneration;

  /// 查看错误日志按钮
  ///
  /// In zh, this message translates to:
  /// **'查看错误日志'**
  String get viewErrorLogs;

  /// 生成测试错误按钮
  ///
  /// In zh, this message translates to:
  /// **'生成测试错误'**
  String get generateTestErrors;

  /// 隐私说明文本
  ///
  /// In zh, this message translates to:
  /// **'提示：您的隐私对我们很重要，我们不会收集密码等敏感信息，仅收集必要的应用配置和日志数据以帮助解决问题。'**
  String get privacyNotice;

  /// 日志生成成功标题
  ///
  /// In zh, this message translates to:
  /// **'日志生成成功'**
  String get logGenerationSuccess;

  /// 日志大小文本
  ///
  /// In zh, this message translates to:
  /// **'日志大小: {size}KB'**
  String logSize(String size);

  /// 测试错误日志生成消息
  ///
  /// In zh, this message translates to:
  /// **'已生成测试错误日志，可以在错误日志查看器中查看'**
  String get testErrorLogsGenerated;

  /// 反馈提交成功消息
  ///
  /// In zh, this message translates to:
  /// **'反馈提交成功，我们会尽快处理您的问题'**
  String get feedbackSubmittedSuccessfully;

  /// 提交失败消息
  ///
  /// In zh, this message translates to:
  /// **'提交失败'**
  String get submissionFailed;

  /// 提交失败检查网络消息
  ///
  /// In zh, this message translates to:
  /// **'提交失败，请检查网络连接'**
  String get submissionFailedCheckNetwork;

  /// 日志生成失败消息
  ///
  /// In zh, this message translates to:
  /// **'日志生成失败: {error}'**
  String logGenerationFailed(String error);

  /// 手机号联系提示
  ///
  /// In zh, this message translates to:
  /// **'(用于问题回访)'**
  String get phoneNumberForContact;

  /// 昵称字段标签
  ///
  /// In zh, this message translates to:
  /// **'昵称'**
  String get nickname;

  /// 昵称必填验证消息
  ///
  /// In zh, this message translates to:
  /// **'昵称不能为空'**
  String get nicknameRequired;

  /// 昵称最小长度验证消息
  ///
  /// In zh, this message translates to:
  /// **'昵称至少需要2个字符'**
  String get nicknameMinLength;

  /// 修改密码分组标题
  ///
  /// In zh, this message translates to:
  /// **'修改密码'**
  String get changePassword;

  /// 修改密码页面说明文字
  ///
  /// In zh, this message translates to:
  /// **'请设置您的新密码，确保密码安全性。'**
  String get changePasswordDescription;

  /// 密码要求标题
  ///
  /// In zh, this message translates to:
  /// **'密码要求'**
  String get passwordRequirements;

  /// 密码长度要求说明
  ///
  /// In zh, this message translates to:
  /// **'密码长度为6-20位字符'**
  String get passwordLengthRequirement;

  /// 账户设置分组标题
  ///
  /// In zh, this message translates to:
  /// **'账户设置'**
  String get accountSettings;

  /// 修改密码选项副标题
  ///
  /// In zh, this message translates to:
  /// **'修改您的登录密码'**
  String get changePasswordSubtitle;

  /// 新密码必填验证消息
  ///
  /// In zh, this message translates to:
  /// **'请输入新密码'**
  String get newPasswordRequired;

  /// 确认新密码字段标签
  ///
  /// In zh, this message translates to:
  /// **'确认新密码'**
  String get confirmNewPassword;

  /// 确认新密码必填验证消息
  ///
  /// In zh, this message translates to:
  /// **'请确认新密码'**
  String get confirmNewPasswordRequired;

  /// 更换头像按钮文本
  ///
  /// In zh, this message translates to:
  /// **'更换头像'**
  String get changeAvatar;

  /// 上传中状态
  ///
  /// In zh, this message translates to:
  /// **'上传中...'**
  String get uploading;

  /// 选择头像对话框标题
  ///
  /// In zh, this message translates to:
  /// **'选择头像'**
  String get selectAvatar;

  /// 拍照选项
  ///
  /// In zh, this message translates to:
  /// **'拍照'**
  String get takePhoto;

  /// 从相册选择选项
  ///
  /// In zh, this message translates to:
  /// **'从相册选择'**
  String get selectFromGallery;

  /// 头像上传成功消息
  ///
  /// In zh, this message translates to:
  /// **'头像上传成功'**
  String get avatarUploadSuccess;

  /// 头像上传失败消息
  ///
  /// In zh, this message translates to:
  /// **'头像上传失败'**
  String get avatarUploadFailed;

  /// 性别字段标签
  ///
  /// In zh, this message translates to:
  /// **'性别'**
  String get gender;

  /// 未设置选项
  ///
  /// In zh, this message translates to:
  /// **'未设置'**
  String get notSet;

  /// 生日字段标签
  ///
  /// In zh, this message translates to:
  /// **'生日'**
  String get birthday;

  /// 资料保存成功消息
  ///
  /// In zh, this message translates to:
  /// **'资料保存成功'**
  String get profileSaveSuccess;

  /// 保存失败消息
  ///
  /// In zh, this message translates to:
  /// **'保存失败'**
  String get saveFailed;

  /// 密码修改成功消息
  ///
  /// In zh, this message translates to:
  /// **'密码修改成功'**
  String get passwordChangeSuccess;

  /// 裁剪头像对话框标题
  ///
  /// In zh, this message translates to:
  /// **'裁剪头像'**
  String get cropAvatar;

  /// 重新选择图片按钮
  ///
  /// In zh, this message translates to:
  /// **'重新选择'**
  String get reselectImage;

  /// 确认裁剪按钮
  ///
  /// In zh, this message translates to:
  /// **'确认'**
  String get confirmCrop;

  /// 无法解析图片文件错误消息
  ///
  /// In zh, this message translates to:
  /// **'无法解析图片文件'**
  String get cannotParseImageFile;

  /// 加载用户资料失败消息
  ///
  /// In zh, this message translates to:
  /// **'加载用户资料失败'**
  String get loadUserProfileFailed;

  /// 拍照失败消息
  ///
  /// In zh, this message translates to:
  /// **'拍照失败: {error}'**
  String takePhotoFailed(String error);

  /// 头像上传失败但资料将保存消息
  ///
  /// In zh, this message translates to:
  /// **'头像上传失败，但其他资料将继续保存'**
  String get avatarUploadFailedButProfileWillSave;

  /// 登录过期消息
  ///
  /// In zh, this message translates to:
  /// **'登录已过期，请重新登录'**
  String get loginExpiredPleaseRelogin;

  /// 处理图片失败消息
  ///
  /// In zh, this message translates to:
  /// **'处理图片失败: {error}'**
  String processImageFailed(String error);

  /// 新密码最大长度验证消息
  ///
  /// In zh, this message translates to:
  /// **'新密码不能超过20位'**
  String get newPasswordMaxLength;

  /// 用户卡片中的会员状态标签
  ///
  /// In zh, this message translates to:
  /// **'会员状态'**
  String get userCardMemberStatusLabel;

  /// 用户卡片中的到期日期标签
  ///
  /// In zh, this message translates to:
  /// **'到期时间'**
  String get userCardExpiryDateLabel;

  /// 用户卡片中的UID标签
  ///
  /// In zh, this message translates to:
  /// **'UID:'**
  String get userCardUidLabel;

  /// 简体中文语言选项
  ///
  /// In zh, this message translates to:
  /// **'中文'**
  String get languageOptionChineseSimplified;

  /// 维吾尔语语言选项
  ///
  /// In zh, this message translates to:
  /// **'维吾尔语'**
  String get languageOptionUyghur;

  /// 英语语言选项
  ///
  /// In zh, this message translates to:
  /// **'英语'**
  String get languageOptionEnglish;

  /// 哈萨克语语言选项
  ///
  /// In zh, this message translates to:
  /// **'哈萨克语'**
  String get languageOptionKazakh;

  /// 俄语语言选项
  ///
  /// In zh, this message translates to:
  /// **'俄语'**
  String get languageOptionRussian;

  /// 法语语言选项
  ///
  /// In zh, this message translates to:
  /// **'法语'**
  String get languageOptionFrench;

  /// 西班牙语语言选项
  ///
  /// In zh, this message translates to:
  /// **'西班牙语'**
  String get languageOptionSpanish;

  /// 粤语语言选项
  ///
  /// In zh, this message translates to:
  /// **'粤语'**
  String get languageOptionCantonese;

  /// 阿拉伯语语言选项
  ///
  /// In zh, this message translates to:
  /// **'阿拉伯语'**
  String get languageOptionArabic;

  /// 历史记录为空提示
  ///
  /// In zh, this message translates to:
  /// **'暂无历史记录'**
  String get historyListEmpty;

  /// 健康助手语音提问按钮
  ///
  /// In zh, this message translates to:
  /// **'语音提问'**
  String get aiGuideVoiceQueryButton;

  /// 健康助手拍照提问按钮
  ///
  /// In zh, this message translates to:
  /// **'拍照提问'**
  String get aiGuidePhotoQueryButton;

  /// 健康助手提问提示文本
  ///
  /// In zh, this message translates to:
  /// **'欢迎使用健康助手，请按住下方按钮拍照提问'**
  String get aiGuideQueryHint;

  /// 面对面交流语言选择提示
  ///
  /// In zh, this message translates to:
  /// **'请选择双方语言'**
  String get faceToFaceSelectLanguagesHint;

  /// 重新发送验证码倒计时标签
  ///
  /// In zh, this message translates to:
  /// **'重新发送({seconds}秒)'**
  String resendCodeTimerLabel(String seconds);

  /// 游客用户标签
  ///
  /// In zh, this message translates to:
  /// **'游客用户'**
  String get guestUser;

  /// 请先登录提示
  ///
  /// In zh, this message translates to:
  /// **'请先登录'**
  String get pleaseLogin;

  /// 会员状态标签
  ///
  /// In zh, this message translates to:
  /// **'会员状态'**
  String get membershipStatus;

  /// 到期时间标签
  ///
  /// In zh, this message translates to:
  /// **'到期时间'**
  String get expiresOn;

  /// 编辑资料按钮文本
  ///
  /// In zh, this message translates to:
  /// **'编辑资料'**
  String get editProfileButton;

  /// 未登录用户状态
  ///
  /// In zh, this message translates to:
  /// **'未登录用户'**
  String get notLoggedInUser;

  /// 验证码登录页面标题
  ///
  /// In zh, this message translates to:
  /// **'验证码登录'**
  String get verificationCodeLoginTitle;

  /// 手机号输入框标签
  ///
  /// In zh, this message translates to:
  /// **'手机号'**
  String get phoneInputLabel;

  /// 手机号输入框提示
  ///
  /// In zh, this message translates to:
  /// **'请输入手机号'**
  String get phoneInputHint;

  /// 验证码输入框标签
  ///
  /// In zh, this message translates to:
  /// **'验证码'**
  String get codeInputLabel;

  /// 验证码输入框提示
  ///
  /// In zh, this message translates to:
  /// **'请输入验证码'**
  String get codeInputHint;

  /// 重新发送验证码倒计时按钮
  ///
  /// In zh, this message translates to:
  /// **'重新发送({seconds}秒)'**
  String resendCodeTimerButton(String seconds);

  /// 登录按钮
  ///
  /// In zh, this message translates to:
  /// **'登录'**
  String get loginButton;

  /// 自动注册提示
  ///
  /// In zh, this message translates to:
  /// **'未注册手机号将自动注册'**
  String get autoRegisterHint;

  /// 提醒弹窗标题
  ///
  /// In zh, this message translates to:
  /// **'提醒'**
  String get reminderTitle;

  /// 分销管理需要登录提示
  ///
  /// In zh, this message translates to:
  /// **'请先登录再使用分销管理功能'**
  String get loginRequiredForDistributionMessage;

  /// 分销管理权限不足提示
  ///
  /// In zh, this message translates to:
  /// **'账户没有分销员权限，你可以申请成为分销员'**
  String get distributionAccessDeniedMessage;

  /// 去登录按钮
  ///
  /// In zh, this message translates to:
  /// **'去登录'**
  String get goToLoginButton;

  /// 申请按钮
  ///
  /// In zh, this message translates to:
  /// **'申请'**
  String get applyButton;

  /// 健康助手页面输入框提示
  ///
  /// In zh, this message translates to:
  /// **'请输入消息'**
  String get typeMessageHint;

  /// 验证码倒计时显示
  ///
  /// In zh, this message translates to:
  /// **'{seconds}秒'**
  String verificationCodeSentSeconds(String seconds);

  /// 欢迎回来标题
  ///
  /// In zh, this message translates to:
  /// **'欢迎回来'**
  String get welcomeBackTitle;

  /// 手机号登录副标题
  ///
  /// In zh, this message translates to:
  /// **'请使用您的手机号登录账户'**
  String get loginWithPhoneSubtitle;

  /// 注册账号按钮
  ///
  /// In zh, this message translates to:
  /// **'注册账号'**
  String get registerAccountButton;

  /// 密码登录按钮
  ///
  /// In zh, this message translates to:
  /// **'密码登录'**
  String get passwordLoginButton;

  /// 登录协议文本
  ///
  /// In zh, this message translates to:
  /// **'登录即表示您同意《用户协议》和《隐私政策》'**
  String get loginAgreementText;

  /// 登录失败提示
  ///
  /// In zh, this message translates to:
  /// **'登录失败'**
  String get loginFailed;

  /// 功能需要登录提示
  ///
  /// In zh, this message translates to:
  /// **'请先登录再使用{featureName}功能'**
  String loginRequiredForFeature(String featureName);

  /// 通用登录提示
  ///
  /// In zh, this message translates to:
  /// **'此功能需要登录后才能使用，请先登录'**
  String get loginRequiredGeneral;

  /// 登录按钮文本
  ///
  /// In zh, this message translates to:
  /// **'登录'**
  String get loginButtonText;

  /// 申请已提交提示
  ///
  /// In zh, this message translates to:
  /// **'申请已提交，请等待审核'**
  String get applicationSubmitted;

  /// 申请失败提示
  ///
  /// In zh, this message translates to:
  /// **'申请失败'**
  String get applicationFailed;

  /// 分销员收益描述
  ///
  /// In zh, this message translates to:
  /// **'成为分销员后，您可以推广产品并获得佣金收益'**
  String get distributorBenefitDescription;

  /// 健康助手拍照提问功能名称
  ///
  /// In zh, this message translates to:
  /// **'健康助手拍照提问'**
  String get aiGuidePhotoQuestion;

  /// 健康助手语音提问功能名称
  ///
  /// In zh, this message translates to:
  /// **'健康助手语音提问'**
  String get aiGuideVoiceQuestion;

  /// 录音启动失败权限提示
  ///
  /// In zh, this message translates to:
  /// **'录音启动失败，请检查麦克风权限'**
  String get recordingStartFailedCheckPermission;

  /// 调整字体大小对话框标题
  ///
  /// In zh, this message translates to:
  /// **'调整字体大小'**
  String get adjustFontSize;

  /// 字体预览文本
  ///
  /// In zh, this message translates to:
  /// **'字体预览 Font Preview'**
  String get fontPreviewText;

  /// 小字体标签
  ///
  /// In zh, this message translates to:
  /// **'小'**
  String get smallSize;

  /// 大字体标签
  ///
  /// In zh, this message translates to:
  /// **'大'**
  String get largeSize;

  /// 当前字体大小标签
  ///
  /// In zh, this message translates to:
  /// **'当前大小: {size}'**
  String currentSizeLabel(String size);

  /// 小字体预设标签
  ///
  /// In zh, this message translates to:
  /// **'小'**
  String get smallSizeLabel;

  /// 中字体预设标签
  ///
  /// In zh, this message translates to:
  /// **'中'**
  String get mediumSizeLabel;

  /// 大字体预设标签
  ///
  /// In zh, this message translates to:
  /// **'大'**
  String get largeSizeLabel;

  /// VIP会员特权对话框标题
  ///
  /// In zh, this message translates to:
  /// **'VIP会员特权'**
  String get vipMembershipTitle;

  /// 更高准确度功能
  ///
  /// In zh, this message translates to:
  /// **'准确度更高'**
  String get higherAccuracy;

  /// 无广告功能
  ///
  /// In zh, this message translates to:
  /// **'无广告干扰'**
  String get adFree;

  /// 无限使用功能
  ///
  /// In zh, this message translates to:
  /// **'无限次使用'**
  String get unlimitedUsage;

  /// 月度会员选项
  ///
  /// In zh, this message translates to:
  /// **'月度会员'**
  String get monthlyMembership;

  /// 年度会员选项
  ///
  /// In zh, this message translates to:
  /// **'年度会员'**
  String get annualMembership;

  /// 节省百分比
  ///
  /// In zh, this message translates to:
  /// **'省{percentage}%'**
  String savePercentage(String percentage);

  /// 获取价格信息中
  ///
  /// In zh, this message translates to:
  /// **'正在获取价格信息...'**
  String get gettingPriceInfo;

  /// 原价字段
  ///
  /// In zh, this message translates to:
  /// **'原价'**
  String get originalPrice;

  /// 大约、约
  ///
  /// In zh, this message translates to:
  /// **'约'**
  String get approximately;

  /// 月单位
  ///
  /// In zh, this message translates to:
  /// **'月'**
  String get monthlyUnit;

  /// 年单位
  ///
  /// In zh, this message translates to:
  /// **'年'**
  String get yearlyUnit;

  /// 每月
  ///
  /// In zh, this message translates to:
  /// **'/月'**
  String get perMonth;

  /// 每年
  ///
  /// In zh, this message translates to:
  /// **'/年'**
  String get perYear;

  /// 立即开通VIP按钮
  ///
  /// In zh, this message translates to:
  /// **'立即开通VIP会员'**
  String get activateVipNow;

  /// 服务条款同意文本
  ///
  /// In zh, this message translates to:
  /// **'开通即表示同意《用户服务协议》和《隐私政策》'**
  String get serviceTermsAgreement;

  /// 包月会员套餐名称
  ///
  /// In zh, this message translates to:
  /// **'包月会员'**
  String get monthlyMemberPackage;

  /// 包年会员套餐名称
  ///
  /// In zh, this message translates to:
  /// **'包年会员'**
  String get annualMemberPackage;

  /// 一个月VIP特权描述
  ///
  /// In zh, this message translates to:
  /// **'一个月VIP特权'**
  String get oneMonthVipPrivileges;

  /// 一年VIP特权描述
  ///
  /// In zh, this message translates to:
  /// **'一年VIP特权，支持自动续费'**
  String get oneYearVipPrivileges;

  /// 价格加载失败提示
  ///
  /// In zh, this message translates to:
  /// **'获取价格信息失败，显示默认价格'**
  String get priceLoadFailed;

  /// 价格信息加载中提示
  ///
  /// In zh, this message translates to:
  /// **'价格信息加载中，请稍候...'**
  String get priceInfoLoading;

  /// 即将开通套餐提示
  ///
  /// In zh, this message translates to:
  /// **'即将开通{packageName}，请稍候...'**
  String aboutToActivate(String packageName);

  /// 年度VIP会员徽章
  ///
  /// In zh, this message translates to:
  /// **'年度VIP会员'**
  String get annualVipMember;

  /// 月度VIP会员徽章
  ///
  /// In zh, this message translates to:
  /// **'月度VIP会员'**
  String get monthlyVipMember;

  /// 终身VIP会员徽章
  ///
  /// In zh, this message translates to:
  /// **'终身VIP会员'**
  String get lifetimeVipMember;

  /// 小字体档位
  ///
  /// In zh, this message translates to:
  /// **'小'**
  String get fontSizeSmall;

  /// 中字体档位
  ///
  /// In zh, this message translates to:
  /// **'中'**
  String get fontSizeMedium;

  /// 大字体档位
  ///
  /// In zh, this message translates to:
  /// **'大'**
  String get fontSizeLarge;

  /// 我的订单菜单项
  ///
  /// In zh, this message translates to:
  /// **'我的订单'**
  String get myOrders;

  /// 聊天历史提示
  ///
  /// In zh, this message translates to:
  /// **'聊天历史'**
  String get chatHistory;

  /// 医生管理菜单项
  ///
  /// In zh, this message translates to:
  /// **'医生管理'**
  String get doctorManagement;

  /// 管理员菜单项
  ///
  /// In zh, this message translates to:
  /// **'管理员'**
  String get adminManagement;

  /// 我的点赞按钮
  ///
  /// In zh, this message translates to:
  /// **'我的点赞'**
  String get myLikes;

  /// 我的收藏按钮
  ///
  /// In zh, this message translates to:
  /// **'我的收藏'**
  String get myFavorites;

  /// 购物车按钮
  ///
  /// In zh, this message translates to:
  /// **'购物车'**
  String get shoppingCart;

  /// 分销管理功能名称
  ///
  /// In zh, this message translates to:
  /// **'分销管理'**
  String get distributionManagementFeature;

  /// 管理员管理功能名称
  ///
  /// In zh, this message translates to:
  /// **'管理员管理'**
  String get adminManagementFeature;

  /// 医生管理功能名称
  ///
  /// In zh, this message translates to:
  /// **'医生管理'**
  String get doctorManagementFeature;

  /// 医生访问限制消息
  ///
  /// In zh, this message translates to:
  /// **'只有医生用户才能访问医生管理功能'**
  String get onlyDoctorUsersCanAccess;

  /// 查看购物车功能名称
  ///
  /// In zh, this message translates to:
  /// **'查看购物车'**
  String get viewShoppingCartFeature;

  /// 选择要删除的商品提示
  ///
  /// In zh, this message translates to:
  /// **'请选择要删除的商品'**
  String get pleaseSelectItemsToDelete;

  /// 确认删除对话框标题
  ///
  /// In zh, this message translates to:
  /// **'确认删除'**
  String get confirmDelete;

  /// 确认删除商品消息
  ///
  /// In zh, this message translates to:
  /// **'确定要删除选中的 {count} 个商品吗？'**
  String confirmDeleteItems(int count);

  /// 删除成功消息
  ///
  /// In zh, this message translates to:
  /// **'删除成功'**
  String get deleteSuccess;

  /// 删除失败消息
  ///
  /// In zh, this message translates to:
  /// **'删除失败: {error}'**
  String deleteFailed(String error);

  /// 选择要结算的商品提示
  ///
  /// In zh, this message translates to:
  /// **'请选择要结算的商品'**
  String get pleaseSelectItemsToCheckout;

  /// 购物车页面标题
  ///
  /// In zh, this message translates to:
  /// **'购物车'**
  String get cartTitle;

  /// 我的订单页面标题
  ///
  /// In zh, this message translates to:
  /// **'我的订单'**
  String get myOrdersTitle;

  /// 我的订单功能名称
  ///
  /// In zh, this message translates to:
  /// **'我的订单'**
  String get myOrdersFeature;

  /// 全部订单状态标签
  ///
  /// In zh, this message translates to:
  /// **'全部'**
  String get orderStatusAll;

  /// 待支付状态
  ///
  /// In zh, this message translates to:
  /// **'待支付'**
  String get orderStatusPending;

  /// 待发货订单状态
  ///
  /// In zh, this message translates to:
  /// **'待发货'**
  String get orderStatusPendingShipment;

  /// 已发货订单状态
  ///
  /// In zh, this message translates to:
  /// **'已发货'**
  String get orderStatusShipped;

  /// 已完成订单状态
  ///
  /// In zh, this message translates to:
  /// **'已完成'**
  String get orderStatusCompleted;

  /// 已取消订单状态
  ///
  /// In zh, this message translates to:
  /// **'已取消'**
  String get orderStatusCancelled;

  /// 未知订单状态
  ///
  /// In zh, this message translates to:
  /// **'未知状态'**
  String get orderStatusUnknown;

  /// 未支付状态
  ///
  /// In zh, this message translates to:
  /// **'未支付'**
  String get payStatusUnpaid;

  /// 已支付状态
  ///
  /// In zh, this message translates to:
  /// **'已支付'**
  String get payStatusPaid;

  /// 已退款状态
  ///
  /// In zh, this message translates to:
  /// **'已退款'**
  String get payStatusRefunded;

  /// 商品标签
  ///
  /// In zh, this message translates to:
  /// **'商品'**
  String get product;

  /// 暂无订单消息
  ///
  /// In zh, this message translates to:
  /// **'暂无订单'**
  String get noOrders;

  /// 管理员管理页面标题
  ///
  /// In zh, this message translates to:
  /// **'管理员管理'**
  String get adminManagementTitle;

  /// 医生管理标签页
  ///
  /// In zh, this message translates to:
  /// **'医生管理'**
  String get doctorManagementTab;

  /// 产品审核标签页
  ///
  /// In zh, this message translates to:
  /// **'产品审核'**
  String get productReviewTab;

  /// 订单管理标签页
  ///
  /// In zh, this message translates to:
  /// **'订单管理'**
  String get orderManagementTab;

  /// 暂无医生数据消息
  ///
  /// In zh, this message translates to:
  /// **'暂无医生数据'**
  String get noDoctorData;

  /// 加载医生列表失败消息
  ///
  /// In zh, this message translates to:
  /// **'加载医生列表失败'**
  String get loadDoctorListFailed;

  /// 添加医生按钮
  ///
  /// In zh, this message translates to:
  /// **'添加医生'**
  String get addDoctor;

  /// 编辑医生按钮
  ///
  /// In zh, this message translates to:
  /// **'编辑医生'**
  String get editDoctor;

  /// 删除医生按钮
  ///
  /// In zh, this message translates to:
  /// **'删除医生'**
  String get deleteDoctor;

  /// 确认删除医生对话框标题
  ///
  /// In zh, this message translates to:
  /// **'确认删除医生'**
  String get confirmDeleteDoctor;

  /// 删除医生确认消息
  ///
  /// In zh, this message translates to:
  /// **'确定要删除医生 {doctorName} 吗？此操作不可撤销。'**
  String deleteDoctorConfirmMessage(String doctorName);

  /// 删除医生成功消息
  ///
  /// In zh, this message translates to:
  /// **'删除医生成功'**
  String get deleteDoctorSuccess;

  /// 删除医生失败消息
  ///
  /// In zh, this message translates to:
  /// **'删除医生失败'**
  String get deleteDoctorFailed;

  /// 详细信息部分
  ///
  /// In zh, this message translates to:
  /// **'详细信息'**
  String get detailedInfo;

  /// AI设置
  ///
  /// In zh, this message translates to:
  /// **'AI设置'**
  String get aiSettings;

  /// 状态设置
  ///
  /// In zh, this message translates to:
  /// **'状态设置'**
  String get statusSettings;

  /// 输入医生姓名提示
  ///
  /// In zh, this message translates to:
  /// **'请输入医生姓名'**
  String get enterDoctorName;

  /// 专科标签
  ///
  /// In zh, this message translates to:
  /// **'专科'**
  String get specialty;

  /// 输入专科提示
  ///
  /// In zh, this message translates to:
  /// **'请输入专科名称，如：心血管内科'**
  String get enterSpecialty;

  /// 简介标签
  ///
  /// In zh, this message translates to:
  /// **'简介'**
  String get description;

  /// 输入医生简介提示
  ///
  /// In zh, this message translates to:
  /// **'请输入医生简介'**
  String get enterDescription;

  /// 详细描述输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入产品的详细描述，包括成分、功效、使用方法等'**
  String get enterDetailedDescription;

  /// 添加擅长领域按钮
  ///
  /// In zh, this message translates to:
  /// **'添加擅长领域'**
  String get addSpecialty;

  /// 输入擅长领域提示
  ///
  /// In zh, this message translates to:
  /// **'请输入擅长领域，如：冠心病'**
  String get enterSpecialtyField;

  /// 删除擅长领域提示
  ///
  /// In zh, this message translates to:
  /// **'删除此擅长领域'**
  String get deleteSpecialty;

  /// 系统提示词
  ///
  /// In zh, this message translates to:
  /// **'系统提示词'**
  String get systemPrompt;

  /// 输入系统提示词提示
  ///
  /// In zh, this message translates to:
  /// **'请输入AI系统提示词，定义AI医生的行为和回答风格'**
  String get enterSystemPrompt;

  /// 头像URL
  ///
  /// In zh, this message translates to:
  /// **'头像URL'**
  String get avatarUrl;

  /// 输入头像URL提示
  ///
  /// In zh, this message translates to:
  /// **'请输入头像图片URL或点击上方上传头像'**
  String get enterAvatarUrl;

  /// LLM模型名称
  ///
  /// In zh, this message translates to:
  /// **'模型名称'**
  String get llmModelName;

  /// 输入LLM模型名称提示
  ///
  /// In zh, this message translates to:
  /// **'请输入LLM模型名称，如：qwen-max-latest、claude-3-sonnet等'**
  String get enterLlmModelName;

  /// 输入工作年限提示
  ///
  /// In zh, this message translates to:
  /// **'请输入工作年限（年）'**
  String get enterYearsOfExperience;

  /// 评分标签
  ///
  /// In zh, this message translates to:
  /// **'评分'**
  String get rating;

  /// 输入评分提示
  ///
  /// In zh, this message translates to:
  /// **'请输入评分（0.0-5.0）'**
  String get enterRating;

  /// 数字人URL
  ///
  /// In zh, this message translates to:
  /// **'数字人URL'**
  String get digitalHumanUrl;

  /// 输入数字人URL提示
  ///
  /// In zh, this message translates to:
  /// **'请输入数字人URL（可选）'**
  String get enterDigitalHumanUrl;

  /// 联系电话标签
  ///
  /// In zh, this message translates to:
  /// **'联系电话'**
  String get phone;

  /// 输入联系电话提示
  ///
  /// In zh, this message translates to:
  /// **'请输入医生联系电话'**
  String get enterPhone;

  /// 输入工作地址提示
  ///
  /// In zh, this message translates to:
  /// **'请输入医生工作地址'**
  String get enterAddress;

  /// 启用状态标签
  ///
  /// In zh, this message translates to:
  /// **'启用状态'**
  String get isActive;

  /// 保存医生按钮
  ///
  /// In zh, this message translates to:
  /// **'保存医生'**
  String get saveDoctor;

  /// 保存中状态
  ///
  /// In zh, this message translates to:
  /// **'保存中...'**
  String get saving;

  /// 创建医生成功消息
  ///
  /// In zh, this message translates to:
  /// **'创建医生成功'**
  String get createDoctorSuccess;

  /// 更新医生成功消息
  ///
  /// In zh, this message translates to:
  /// **'更新医生成功'**
  String get updateDoctorSuccess;

  /// 创建医生失败消息
  ///
  /// In zh, this message translates to:
  /// **'创建医生失败'**
  String get createDoctorFailed;

  /// 更新医生失败消息
  ///
  /// In zh, this message translates to:
  /// **'更新医生失败'**
  String get updateDoctorFailed;

  /// 启用状态
  ///
  /// In zh, this message translates to:
  /// **'启用'**
  String get enabled;

  /// 禁用状态
  ///
  /// In zh, this message translates to:
  /// **'禁用'**
  String get disabled;

  /// 输入有效手机号码提示
  ///
  /// In zh, this message translates to:
  /// **'请输入有效的手机号码'**
  String get enterValidPhone;

  /// 医生头像
  ///
  /// In zh, this message translates to:
  /// **'医生头像'**
  String get doctorAvatar;

  /// 上传头像按钮
  ///
  /// In zh, this message translates to:
  /// **'上传头像'**
  String get uploadAvatar;

  /// 启用状态
  ///
  /// In zh, this message translates to:
  /// **'启用状态'**
  String get enableStatus;

  /// 医生启用状态描述
  ///
  /// In zh, this message translates to:
  /// **'医生当前处于启用状态，用户可以与其对话'**
  String get doctorEnabledDescription;

  /// 医生禁用状态描述
  ///
  /// In zh, this message translates to:
  /// **'医生当前处于禁用状态，用户无法与其对话'**
  String get doctorDisabledDescription;

  /// 擅长领域输入提示
  ///
  /// In zh, this message translates to:
  /// **'提示：每个输入框填写一个擅长领域，保存时将自动合并'**
  String get specialtyInputHint;

  /// 确认退出对话框标题
  ///
  /// In zh, this message translates to:
  /// **'确认退出'**
  String get confirmExit;

  /// 未保存更改警告
  ///
  /// In zh, this message translates to:
  /// **'您有未保存的更改，确定要退出吗？'**
  String get unsavedChangesWarning;

  /// 退出按钮
  ///
  /// In zh, this message translates to:
  /// **'退出'**
  String get exit;

  /// 上传失败消息
  ///
  /// In zh, this message translates to:
  /// **'上传失败'**
  String get uploadFailed;

  /// 支持的图片格式说明
  ///
  /// In zh, this message translates to:
  /// **'支持 JPG、PNG 格式，大小不超过 5MB'**
  String get supportedImageFormats;

  /// 收起按钮
  ///
  /// In zh, this message translates to:
  /// **'收起'**
  String get collapse;

  /// 多语言按钮
  ///
  /// In zh, this message translates to:
  /// **'多语言'**
  String get multilingual;

  /// 全部选项
  ///
  /// In zh, this message translates to:
  /// **'全部'**
  String get all;

  /// 待审核状态
  ///
  /// In zh, this message translates to:
  /// **'待审核'**
  String get pending;

  /// 已通过状态
  ///
  /// In zh, this message translates to:
  /// **'已通过'**
  String get approved;

  /// 已拒绝状态
  ///
  /// In zh, this message translates to:
  /// **'已拒绝'**
  String get rejected;

  /// 已下架状态
  ///
  /// In zh, this message translates to:
  /// **'已下架'**
  String get offline;

  /// 加载数据失败消息
  ///
  /// In zh, this message translates to:
  /// **'加载数据失败: {error}'**
  String loadDataFailed(String error);

  /// 加载医生多语言数据失败消息
  ///
  /// In zh, this message translates to:
  /// **'加载医生多语言数据失败'**
  String get loadDoctorMultilingualDataFailed;

  /// 医生创建完成回调消息
  ///
  /// In zh, this message translates to:
  /// **'医生创建完成回调'**
  String get doctorCreationCompleteCallback;

  /// 输入模型名称验证消息
  ///
  /// In zh, this message translates to:
  /// **'请输入模型名称'**
  String get enterModelName;

  /// 图片大小超出限制消息
  ///
  /// In zh, this message translates to:
  /// **'图片大小不能超过5MB'**
  String get imageSizeExceedsLimit;

  /// 医生管理页面标题
  ///
  /// In zh, this message translates to:
  /// **'医生管理'**
  String get doctorManagementTitle;

  /// 产品管理标签页
  ///
  /// In zh, this message translates to:
  /// **'产品管理'**
  String get productManagementTab;

  /// 数据概览标题
  ///
  /// In zh, this message translates to:
  /// **'数据概览'**
  String get dataOverview;

  /// 产品标签
  ///
  /// In zh, this message translates to:
  /// **'产品'**
  String get products;

  /// 待审核状态
  ///
  /// In zh, this message translates to:
  /// **'待审核'**
  String get pendingReview;

  /// 总产品数量
  ///
  /// In zh, this message translates to:
  /// **'总产品'**
  String get totalProducts;

  /// 总销售额
  ///
  /// In zh, this message translates to:
  /// **'总销售额'**
  String get totalSales;

  /// 总订单数量
  ///
  /// In zh, this message translates to:
  /// **'总订单'**
  String get totalOrders;

  /// 已通过产品数量
  ///
  /// In zh, this message translates to:
  /// **'已通过'**
  String get approvedProducts;

  /// 已拒绝产品数量
  ///
  /// In zh, this message translates to:
  /// **'已拒绝'**
  String get rejectedProducts;

  /// 审核状态标签
  ///
  /// In zh, this message translates to:
  /// **'审核状态'**
  String get reviewStatus;

  /// 库存标签
  ///
  /// In zh, this message translates to:
  /// **'库存'**
  String get inventory;

  /// 销量标签
  ///
  /// In zh, this message translates to:
  /// **'销量'**
  String get salesVolume;

  /// 订单概览标题
  ///
  /// In zh, this message translates to:
  /// **'订单概览'**
  String get orderOverview;

  /// 物流状态标签
  ///
  /// In zh, this message translates to:
  /// **'物流状态'**
  String get shippingStatus;

  /// 支付状态标签
  ///
  /// In zh, this message translates to:
  /// **'支付状态'**
  String get paymentStatus;

  /// 总计订单号标签
  ///
  /// In zh, this message translates to:
  /// **'总计订单号'**
  String get totalOrderNumber;

  /// 客户标签
  ///
  /// In zh, this message translates to:
  /// **'客户'**
  String get customer;

  /// 待支付状态
  ///
  /// In zh, this message translates to:
  /// **'待支付'**
  String get pendingPayment;

  /// 待发货状态
  ///
  /// In zh, this message translates to:
  /// **'待发货'**
  String get pendingShipment;

  /// 已发货状态
  ///
  /// In zh, this message translates to:
  /// **'已发货'**
  String get shipped;

  /// 已完成状态
  ///
  /// In zh, this message translates to:
  /// **'已完成'**
  String get completed;

  /// 已取消状态
  ///
  /// In zh, this message translates to:
  /// **'已取消'**
  String get cancelled;

  /// 审核通过备注
  ///
  /// In zh, this message translates to:
  /// **'审核通过'**
  String get reviewApproved;

  /// 审核拒绝状态
  ///
  /// In zh, this message translates to:
  /// **'审核拒绝'**
  String get reviewRejected;

  /// 下架状态
  ///
  /// In zh, this message translates to:
  /// **'下架'**
  String get offShelf;

  /// 添加产品标题
  ///
  /// In zh, this message translates to:
  /// **'添加产品'**
  String get addProduct;

  /// 编辑产品标题
  ///
  /// In zh, this message translates to:
  /// **'编辑产品'**
  String get editProduct;

  /// 产品名称输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入产品名称'**
  String get enterProductName;

  /// 产品描述标题
  ///
  /// In zh, this message translates to:
  /// **'产品描述'**
  String get productDescription;

  /// 产品简介输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入产品简介'**
  String get enterProductDescription;

  /// 产品分类字段
  ///
  /// In zh, this message translates to:
  /// **'产品分类'**
  String get productCategory;

  /// 产品分类输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入产品分类，如：保健品、药物等'**
  String get enterProductCategory;

  /// 制造商输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入制造商名称'**
  String get enterManufacturer;

  /// 产品主图字段
  ///
  /// In zh, this message translates to:
  /// **'产品主图'**
  String get productMainImage;

  /// 价格信息部分
  ///
  /// In zh, this message translates to:
  /// **'价格信息'**
  String get priceInfo;

  /// 现价字段
  ///
  /// In zh, this message translates to:
  /// **'现价'**
  String get currentPrice;

  /// 现价输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入现价'**
  String get enterCurrentPrice;

  /// 原价输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入原价（可选）'**
  String get enterOriginalPrice;

  /// 库存信息部分
  ///
  /// In zh, this message translates to:
  /// **'库存信息'**
  String get inventoryInfo;

  /// 库存数量字段
  ///
  /// In zh, this message translates to:
  /// **'库存数量'**
  String get inventoryCount;

  /// 库存数量输入提示
  ///
  /// In zh, this message translates to:
  /// **'请输入库存数量'**
  String get enterInventoryCount;

  /// 产品详情图标签
  ///
  /// In zh, this message translates to:
  /// **'产品详情图'**
  String get productDetailImages;

  /// 产品创建成功消息
  ///
  /// In zh, this message translates to:
  /// **'产品创建成功'**
  String get productCreatedSuccess;

  /// 产品更新成功消息
  ///
  /// In zh, this message translates to:
  /// **'产品更新成功'**
  String get productUpdatedSuccess;

  /// 加载产品多语言数据失败消息
  ///
  /// In zh, this message translates to:
  /// **'加载产品多语言数据失败'**
  String get loadProductMultilingualDataFailed;

  /// 订单号标签
  ///
  /// In zh, this message translates to:
  /// **'订单号'**
  String get orderNumber;

  /// 订单总计标签
  ///
  /// In zh, this message translates to:
  /// **'总计'**
  String get orderTotal;

  /// 订单项中的客户标签
  ///
  /// In zh, this message translates to:
  /// **'客户: {customer}'**
  String customerLabel(String customer);

  /// 简短订单号标签
  ///
  /// In zh, this message translates to:
  /// **'订单号'**
  String get orderNumberShort;

  /// 已支付状态
  ///
  /// In zh, this message translates to:
  /// **'已支付'**
  String get paidStatus;

  /// 未支付状态
  ///
  /// In zh, this message translates to:
  /// **'未支付'**
  String get unpaidStatus;

  /// 快递单号标签
  ///
  /// In zh, this message translates to:
  /// **'快递单号'**
  String get trackingNumber;

  /// 网络图片标签
  ///
  /// In zh, this message translates to:
  /// **'网络图片'**
  String get networkImage;

  /// 最多6张图片提示
  ///
  /// In zh, this message translates to:
  /// **'最多6张'**
  String get maxSixImages;

  /// 添加图片按钮
  ///
  /// In zh, this message translates to:
  /// **'添加图片'**
  String get addImage;

  /// 快递信息标题
  ///
  /// In zh, this message translates to:
  /// **'快递信息'**
  String get expressInfo;

  /// 快递单号标签
  ///
  /// In zh, this message translates to:
  /// **'快递单号'**
  String get expressNumber;

  /// 快递公司标签
  ///
  /// In zh, this message translates to:
  /// **'快递公司'**
  String get expressCompany;

  /// 发货备注标签
  ///
  /// In zh, this message translates to:
  /// **'发货备注'**
  String get shippingNote;

  /// 暂无物流信息消息
  ///
  /// In zh, this message translates to:
  /// **'暂无物流信息'**
  String get noShippingInfo;

  /// 订单数量带数字
  ///
  /// In zh, this message translates to:
  /// **'{count}单'**
  String orderCount(int count);

  /// 已选择图片状态
  ///
  /// In zh, this message translates to:
  /// **'已选择图片'**
  String get imageSelected;

  /// 未保存更改确认消息
  ///
  /// In zh, this message translates to:
  /// **'您有未保存的更改，确定要退出吗？'**
  String get unsavedChangesMessage;

  /// 确定操作按钮
  ///
  /// In zh, this message translates to:
  /// **'确定'**
  String get confirmAction;

  /// 暂无产品空状态消息
  ///
  /// In zh, this message translates to:
  /// **'暂无产品'**
  String get noProductsMessage;

  /// 添加第一个产品提示消息
  ///
  /// In zh, this message translates to:
  /// **'点击右下角按钮添加您的第一个产品'**
  String get addFirstProductHint;

  /// 暂无订单消息
  ///
  /// In zh, this message translates to:
  /// **'订单数据会显示在这里'**
  String get noOrdersMessage;

  /// 订单将显示在这里提示消息
  ///
  /// In zh, this message translates to:
  /// **'客户购买您的产品后订单会显示在这里'**
  String get ordersWillShowHere;

  /// 审核成功消息
  ///
  /// In zh, this message translates to:
  /// **'审核成功'**
  String get reviewSuccess;

  /// 审核失败消息
  ///
  /// In zh, this message translates to:
  /// **'审核失败'**
  String get reviewFailed;

  /// 批量审核成功消息
  ///
  /// In zh, this message translates to:
  /// **'批量审核成功'**
  String get batchReviewSuccess;

  /// 批量审核失败消息
  ///
  /// In zh, this message translates to:
  /// **'批量审核失败'**
  String get batchReviewFailed;

  /// 全部医生选项
  ///
  /// In zh, this message translates to:
  /// **'全部医生'**
  String get allDoctors;

  /// 请选择产品提示
  ///
  /// In zh, this message translates to:
  /// **'请选择要审核的产品'**
  String get pleaseSelectProducts;

  /// 批量审核通过备注
  ///
  /// In zh, this message translates to:
  /// **'批量审核通过'**
  String get batchApproved;

  /// 批量审核拒绝备注
  ///
  /// In zh, this message translates to:
  /// **'批量审核拒绝'**
  String get batchRejected;

  /// 批量通过按钮
  ///
  /// In zh, this message translates to:
  /// **'批量通过'**
  String get batchApprove;

  /// 批量拒绝按钮
  ///
  /// In zh, this message translates to:
  /// **'批量拒绝'**
  String get batchReject;

  /// 取消全选按钮
  ///
  /// In zh, this message translates to:
  /// **'取消全选'**
  String get deselectAll;

  /// 退出选择按钮
  ///
  /// In zh, this message translates to:
  /// **'退出选择'**
  String get exitSelection;

  /// 批量选择按钮
  ///
  /// In zh, this message translates to:
  /// **'批量选择'**
  String get batchSelection;

  /// 审核统计标题
  ///
  /// In zh, this message translates to:
  /// **'审核统计'**
  String get reviewStatistics;

  /// 总数标签
  ///
  /// In zh, this message translates to:
  /// **'总数'**
  String get total;

  /// 销量标签
  ///
  /// In zh, this message translates to:
  /// **'销量'**
  String get sales;

  /// 通过按钮
  ///
  /// In zh, this message translates to:
  /// **'通过'**
  String get approve;

  /// 拒绝按钮
  ///
  /// In zh, this message translates to:
  /// **'拒绝'**
  String get reject;

  /// 拒绝审核标题
  ///
  /// In zh, this message translates to:
  /// **'拒绝审核'**
  String get rejectReview;

  /// 确认拒绝产品提示
  ///
  /// In zh, this message translates to:
  /// **'确定要拒绝产品 \"{productName}\" 吗？'**
  String confirmRejectProduct(String productName);

  /// 拒绝原因输入提示
  ///
  /// In zh, this message translates to:
  /// **'拒绝原因（可选）'**
  String get rejectReason;

  /// 确定拒绝按钮
  ///
  /// In zh, this message translates to:
  /// **'确定拒绝'**
  String get confirmReject;

  /// 筛选医生标签
  ///
  /// In zh, this message translates to:
  /// **'筛选医生'**
  String get filterDoctors;

  /// 加载产品详情失败消息
  ///
  /// In zh, this message translates to:
  /// **'加载产品详情失败'**
  String get loadProductDetailFailed;

  /// 未知医生姓名
  ///
  /// In zh, this message translates to:
  /// **'未知医生'**
  String get unknownDoctor;

  /// 创建时间标签
  ///
  /// In zh, this message translates to:
  /// **'创建时间'**
  String get createdAt;

  /// 更新时间标签
  ///
  /// In zh, this message translates to:
  /// **'更新时间'**
  String get updatedAt;

  /// 产品图片标题
  ///
  /// In zh, this message translates to:
  /// **'产品图片'**
  String get productImages;

  /// 产品规格标题
  ///
  /// In zh, this message translates to:
  /// **'产品规格'**
  String get productSpecifications;

  /// 审核信息标题
  ///
  /// In zh, this message translates to:
  /// **'审核信息'**
  String get reviewInfo;

  /// 产品ID标签
  ///
  /// In zh, this message translates to:
  /// **'产品ID'**
  String get productId;

  /// 查看物流按钮
  ///
  /// In zh, this message translates to:
  /// **'查看物流'**
  String get viewShipping;

  /// 取消订单按钮
  ///
  /// In zh, this message translates to:
  /// **'取消订单'**
  String get cancelOrder;

  /// 立即支付按钮
  ///
  /// In zh, this message translates to:
  /// **'立即支付'**
  String get payNow;

  /// 确认取消对话框标题
  ///
  /// In zh, this message translates to:
  /// **'确认取消'**
  String get confirmCancel;

  /// 确认取消订单消息
  ///
  /// In zh, this message translates to:
  /// **'确定要取消这个订单吗？'**
  String get confirmCancelOrder;

  /// 订单已取消消息
  ///
  /// In zh, this message translates to:
  /// **'订单已取消'**
  String get orderCancelled;

  /// 取消订单失败消息
  ///
  /// In zh, this message translates to:
  /// **'取消订单失败：{error}'**
  String cancelOrderFailed(String error);

  /// 获取支付参数失败消息
  ///
  /// In zh, this message translates to:
  /// **'获取支付参数失败'**
  String get getPaymentParamsFailed;

  /// 支付已取消消息
  ///
  /// In zh, this message translates to:
  /// **'支付已取消'**
  String get paymentCancelled;

  /// 确认支付对话框标题
  ///
  /// In zh, this message translates to:
  /// **'确认支付'**
  String get confirmPayment;

  /// 支付金额标签
  ///
  /// In zh, this message translates to:
  /// **'支付金额'**
  String get paymentAmount;

  /// 确认支付按钮
  ///
  /// In zh, this message translates to:
  /// **'确认支付'**
  String get confirmPaymentButton;

  /// 订单支付成功消息
  ///
  /// In zh, this message translates to:
  /// **'您的订单已支付成功'**
  String get orderPaidSuccessfully;

  /// 当前对话已删除消息
  ///
  /// In zh, this message translates to:
  /// **'当前对话已被删除'**
  String get currentConversationDeleted;

  /// 新对话标题
  ///
  /// In zh, this message translates to:
  /// **'新对话'**
  String get newConversation;

  /// 刷新成功消息
  ///
  /// In zh, this message translates to:
  /// **'刷新成功'**
  String get refreshSuccess;

  /// 刷新失败消息
  ///
  /// In zh, this message translates to:
  /// **'刷新失败: {error}'**
  String refreshFailed(String error);

  /// 标题更新成功提示
  ///
  /// In zh, this message translates to:
  /// **'标题更新成功'**
  String get titleUpdateSuccess;

  /// 标题更新失败提示
  ///
  /// In zh, this message translates to:
  /// **'标题更新失败: {error}'**
  String titleUpdateFailed(String error);

  /// 对话不存在错误
  ///
  /// In zh, this message translates to:
  /// **'对话不存在'**
  String get conversationNotFound;

  /// 聊天历史页面标题
  ///
  /// In zh, this message translates to:
  /// **'聊天历史'**
  String get chatHistoryPageTitle;

  /// 该日期无聊天记录提示
  ///
  /// In zh, this message translates to:
  /// **'该日期暂无聊天记录'**
  String get noChatRecordsForDate;

  /// 输入新标题提示
  ///
  /// In zh, this message translates to:
  /// **'请输入新的标题'**
  String get enterNewTitle;

  /// 年份单位
  ///
  /// In zh, this message translates to:
  /// **'年'**
  String get year;

  /// 月份单位
  ///
  /// In zh, this message translates to:
  /// **'月'**
  String get month;

  /// 月份标签
  ///
  /// In zh, this message translates to:
  /// **'月份：'**
  String get monthLabel;

  /// 年份标签
  ///
  /// In zh, this message translates to:
  /// **'年份：'**
  String get yearLabel;

  /// 星期一
  ///
  /// In zh, this message translates to:
  /// **'一'**
  String get weekdayMon;

  /// 星期二
  ///
  /// In zh, this message translates to:
  /// **'二'**
  String get weekdayTue;

  /// 星期三
  ///
  /// In zh, this message translates to:
  /// **'三'**
  String get weekdayWed;

  /// 星期四
  ///
  /// In zh, this message translates to:
  /// **'四'**
  String get weekdayThu;

  /// 星期五
  ///
  /// In zh, this message translates to:
  /// **'五'**
  String get weekdayFri;

  /// 星期六
  ///
  /// In zh, this message translates to:
  /// **'六'**
  String get weekdaySat;

  /// 星期日
  ///
  /// In zh, this message translates to:
  /// **'日'**
  String get weekdaySun;

  /// 选择年月对话框标题
  ///
  /// In zh, this message translates to:
  /// **'选择年月'**
  String get selectYearMonth;

  /// 确定按钮
  ///
  /// In zh, this message translates to:
  /// **'确定'**
  String get ok;

  /// 数字人聊天开发中消息
  ///
  /// In zh, this message translates to:
  /// **'数字人AI聊天页面开发中...'**
  String get digitalHumanChatInDevelopment;

  /// 语音翻译功能名称
  ///
  /// In zh, this message translates to:
  /// **'语音翻译'**
  String get voiceTranslationFeature;

  /// 聊天功能名称
  ///
  /// In zh, this message translates to:
  /// **'聊天功能'**
  String get chatFeature;

  /// 语音功能名称
  ///
  /// In zh, this message translates to:
  /// **'语音功能'**
  String get voiceFeature;

  /// 聊天历史功能名称
  ///
  /// In zh, this message translates to:
  /// **'聊天历史'**
  String get chatHistoryFeature;

  /// 语音识别成功消息
  ///
  /// In zh, this message translates to:
  /// **'语音识别成功'**
  String get voiceRecognitionSuccess;

  /// 语音识别失败消息
  ///
  /// In zh, this message translates to:
  /// **'语音识别失败，请重试'**
  String get voiceRecognitionFailed;

  /// 图片预览提示文字
  ///
  /// In zh, this message translates to:
  /// **'添加文字描述或直接发送图片'**
  String get addTextDescriptionOrSendImage;

  /// 刷新提示
  ///
  /// In zh, this message translates to:
  /// **'刷新'**
  String get refresh;

  /// 暂无聊天记录消息
  ///
  /// In zh, this message translates to:
  /// **'暂无聊天记录'**
  String get noChatRecords;

  /// 筛选医生下拉框标签
  ///
  /// In zh, this message translates to:
  /// **'筛选医生'**
  String get filterDoctorsLabel;

  /// 全部医生筛选选项
  ///
  /// In zh, this message translates to:
  /// **'全部医生'**
  String get allDoctorsOption;

  /// 订单列表中的未知医生标签
  ///
  /// In zh, this message translates to:
  /// **'未知医生'**
  String get unknownDoctorLabel;

  /// 订单项中的数量标签
  ///
  /// In zh, this message translates to:
  /// **'数量: {quantity}'**
  String quantityLabel(int quantity);

  /// 订单项中的医生标签
  ///
  /// In zh, this message translates to:
  /// **'医生: {doctor}'**
  String doctorLabel(String doctor);

  /// 订单项中的单价标签
  ///
  /// In zh, this message translates to:
  /// **'单价: ¥{price}'**
  String unitPriceLabel(String price);

  /// 订单项中的总金额标签
  ///
  /// In zh, this message translates to:
  /// **'总计: ¥{amount}'**
  String totalAmountLabel(String amount);

  /// 订单项中的订单号标签
  ///
  /// In zh, this message translates to:
  /// **'订单号: {orderNumber}'**
  String orderNumberLabel(String orderNumber);

  /// 管理员发货操作
  ///
  /// In zh, this message translates to:
  /// **'管理员发货'**
  String get adminShipAction;

  /// 订单菜单中的标记完成操作
  ///
  /// In zh, this message translates to:
  /// **'标记完成'**
  String get markCompleteAction;

  /// 订单菜单中的标记取消操作
  ///
  /// In zh, this message translates to:
  /// **'标记取消'**
  String get markCancelAction;

  /// 删除订单操作
  ///
  /// In zh, this message translates to:
  /// **'删除订单'**
  String get deleteOrderAction;

  /// 统计信息中的总订单标签
  ///
  /// In zh, this message translates to:
  /// **'总订单'**
  String get totalOrdersLabel;

  /// 统计信息中的总销售额标签
  ///
  /// In zh, this message translates to:
  /// **'总销售额'**
  String get totalSalesLabel;

  /// 统计信息中的已完成订单标签
  ///
  /// In zh, this message translates to:
  /// **'已完成'**
  String get completedOrdersLabel;

  /// 统计信息中的待支付标签
  ///
  /// In zh, this message translates to:
  /// **'待支付'**
  String get pendingPaymentLabel;

  /// 统计信息中的待发货标签
  ///
  /// In zh, this message translates to:
  /// **'待发货'**
  String get pendingShipmentLabel;

  /// 统计信息中的已取消订单标签
  ///
  /// In zh, this message translates to:
  /// **'已取消'**
  String get cancelledOrdersLabel;

  /// 订单单位后缀
  ///
  /// In zh, this message translates to:
  /// **'单'**
  String get ordersUnit;

  /// 订单状态更新成功消息
  ///
  /// In zh, this message translates to:
  /// **'订单状态更新成功'**
  String get orderStatusUpdateSuccess;

  /// 更新失败消息
  ///
  /// In zh, this message translates to:
  /// **'更新失败: {error}'**
  String updateFailed(String error);

  /// 暂无订单标题
  ///
  /// In zh, this message translates to:
  /// **'暂无订单'**
  String get noOrdersTitle;

  /// 批量操作成功消息
  ///
  /// In zh, this message translates to:
  /// **'批量操作成功'**
  String get batchOperationSuccess;

  /// 批量操作失败消息
  ///
  /// In zh, this message translates to:
  /// **'批量操作失败: {error}'**
  String batchOperationFailed(String error);

  /// 确认删除对话框标题
  ///
  /// In zh, this message translates to:
  /// **'确认删除'**
  String get confirmDeleteTitle;

  /// 确认删除对话框消息
  ///
  /// In zh, this message translates to:
  /// **'确定要删除这个订单吗？此操作不可撤销。'**
  String get confirmDeleteMessage;

  /// 取消操作按钮
  ///
  /// In zh, this message translates to:
  /// **'取消'**
  String get cancelAction;

  /// 删除操作按钮
  ///
  /// In zh, this message translates to:
  /// **'删除'**
  String get deleteAction;

  /// 批量操作对话框标题
  ///
  /// In zh, this message translates to:
  /// **'批量操作 ({count}个订单)'**
  String batchOperationsTitle(int count);

  /// 标记为已完成操作
  ///
  /// In zh, this message translates to:
  /// **'标记为已完成'**
  String get markAsCompletedAction;

  /// 标记为已取消操作
  ///
  /// In zh, this message translates to:
  /// **'标记为已取消'**
  String get markAsCancelledAction;

  /// 标记为已发货操作
  ///
  /// In zh, this message translates to:
  /// **'标记为已发货'**
  String get markAsShippedAction;

  /// 统计信息中的订单单位后缀
  ///
  /// In zh, this message translates to:
  /// **'单'**
  String get ordersUnitSuffix;

  /// 订单状态更新成功消息
  ///
  /// In zh, this message translates to:
  /// **'订单状态更新成功'**
  String get orderStatusUpdateSuccessMessage;

  /// 支付状态更新成功消息
  ///
  /// In zh, this message translates to:
  /// **'支付状态更新成功'**
  String get paymentStatusUpdateSuccessMessage;

  /// 订单删除成功消息
  ///
  /// In zh, this message translates to:
  /// **'订单删除成功'**
  String get orderDeleteSuccessMessage;

  /// 请先选择订单消息
  ///
  /// In zh, this message translates to:
  /// **'请先选择要操作的订单'**
  String get pleaseSelectOrdersMessage;

  /// 标记已支付操作
  ///
  /// In zh, this message translates to:
  /// **'标记已支付'**
  String get markAsPaidAction;

  /// 待支付订单状态
  ///
  /// In zh, this message translates to:
  /// **'待支付'**
  String get orderStatusPendingPayment;

  /// 已支付订单状态
  ///
  /// In zh, this message translates to:
  /// **'已支付'**
  String get orderStatusPaid;

  /// 订单详情页面标题
  ///
  /// In zh, this message translates to:
  /// **'订单详情'**
  String get orderDetailTitle;

  /// 标记退款操作
  ///
  /// In zh, this message translates to:
  /// **'标记退款'**
  String get markAsRefundAction;

  /// 标记完成操作
  ///
  /// In zh, this message translates to:
  /// **'标记完成'**
  String get markAsCompleteAction;

  /// 标记取消操作
  ///
  /// In zh, this message translates to:
  /// **'标记取消'**
  String get markAsCancelAction;

  /// 等待支付描述
  ///
  /// In zh, this message translates to:
  /// **'等待客户完成支付'**
  String get waitingForPaymentDescription;

  /// 等待发货描述
  ///
  /// In zh, this message translates to:
  /// **'等待医生发货'**
  String get waitingForShipmentDescription;

  /// 已发货描述
  ///
  /// In zh, this message translates to:
  /// **'商品已发货，等待客户收货'**
  String get shippedDescription;

  /// 订单完成描述
  ///
  /// In zh, this message translates to:
  /// **'订单已完成'**
  String get orderCompletedDescription;

  /// 订单取消描述
  ///
  /// In zh, this message translates to:
  /// **'订单已取消'**
  String get orderCancelledDescription;

  /// 订单状态异常描述
  ///
  /// In zh, this message translates to:
  /// **'订单状态异常'**
  String get orderStatusAbnormalDescription;

  /// 待支付订单状态描述
  ///
  /// In zh, this message translates to:
  /// **'请尽快完成支付，超时订单将自动取消'**
  String get orderStatusPendingDescription;

  /// 订单准备中状态描述
  ///
  /// In zh, this message translates to:
  /// **'您的订单正在准备中，请耐心等待'**
  String get orderStatusPreparingDescription;

  /// 用户端已发货状态描述
  ///
  /// In zh, this message translates to:
  /// **'商品已发货，请注意查收'**
  String get orderStatusShippedUserDescription;

  /// 用户端订单完成状态描述
  ///
  /// In zh, this message translates to:
  /// **'订单已完成，感谢您的购买'**
  String get orderStatusCompletedUserDescription;

  /// 用户端订单取消状态描述
  ///
  /// In zh, this message translates to:
  /// **'订单已取消'**
  String get orderStatusCancelledUserDescription;

  /// 等待收货状态
  ///
  /// In zh, this message translates to:
  /// **'已发货，等待收货'**
  String get shippingStatusWaitingReceive;

  /// 物流完成状态
  ///
  /// In zh, this message translates to:
  /// **'已完成'**
  String get shippingStatusCompleted;

  /// 已发货状态
  ///
  /// In zh, this message translates to:
  /// **'已发货'**
  String get shippingStatusShipped;

  /// 物流待支付状态
  ///
  /// In zh, this message translates to:
  /// **'待支付'**
  String get shippingStatusPending;

  /// 物流待发货状态
  ///
  /// In zh, this message translates to:
  /// **'待发货'**
  String get shippingStatusWaitingShip;

  /// 物流已取消状态
  ///
  /// In zh, this message translates to:
  /// **'已取消'**
  String get shippingStatusCancelled;

  /// 物流未知状态
  ///
  /// In zh, this message translates to:
  /// **'未知状态'**
  String get shippingStatusUnknown;

  /// 权限不足需要医生权限错误消息
  ///
  /// In zh, this message translates to:
  /// **'权限不足，需要医生权限'**
  String get insufficientPermissionDoctorRequired;

  /// 获取待发货订单失败错误消息
  ///
  /// In zh, this message translates to:
  /// **'获取待发货订单失败'**
  String get getPendingShipmentOrdersFailed;

  /// 快递单号不能为空错误消息
  ///
  /// In zh, this message translates to:
  /// **'快递单号不能为空'**
  String get trackingNumberCannotBeEmpty;

  /// 发货失败错误消息
  ///
  /// In zh, this message translates to:
  /// **'发货失败'**
  String get shipmentFailed;

  /// 订单不存在或无权访问错误消息
  ///
  /// In zh, this message translates to:
  /// **'订单不存在或无权访问'**
  String get orderNotExistOrNoAccess;

  /// 发货失败请检查订单状态错误消息
  ///
  /// In zh, this message translates to:
  /// **'发货失败，请检查订单状态'**
  String get shipmentFailedCheckOrderStatus;

  /// 获取物流状态失败错误消息
  ///
  /// In zh, this message translates to:
  /// **'获取物流状态失败'**
  String get getShippingStatusFailed;

  /// 获取已发货订单失败错误消息
  ///
  /// In zh, this message translates to:
  /// **'获取已发货订单失败'**
  String get getShippedOrdersFailed;

  /// 商品信息标题
  ///
  /// In zh, this message translates to:
  /// **'商品信息'**
  String get productInfoTitle;

  /// 订单信息标题
  ///
  /// In zh, this message translates to:
  /// **'订单信息'**
  String get orderInfoTitle;

  /// 订单号字段标签
  ///
  /// In zh, this message translates to:
  /// **'订单号'**
  String get orderNumberFieldLabel;

  /// 下单时间标签
  ///
  /// In zh, this message translates to:
  /// **'下单时间'**
  String get orderTimeLabel;

  /// 支付时间标签
  ///
  /// In zh, this message translates to:
  /// **'支付时间'**
  String get paymentTimeLabel;

  /// 发货时间标签
  ///
  /// In zh, this message translates to:
  /// **'发货时间'**
  String get shipmentTimeLabel;

  /// 完成时间标签
  ///
  /// In zh, this message translates to:
  /// **'完成时间'**
  String get completionTimeLabel;

  /// 客户信息标题
  ///
  /// In zh, this message translates to:
  /// **'客户信息'**
  String get customerInfoTitle;

  /// 客户昵称标签
  ///
  /// In zh, this message translates to:
  /// **'客户昵称'**
  String get customerNicknameLabel;

  /// 用户ID标签
  ///
  /// In zh, this message translates to:
  /// **'用户ID'**
  String get userIdLabel;

  /// 收货信息标题
  ///
  /// In zh, this message translates to:
  /// **'收货信息'**
  String get shippingInfoTitle;

  /// 收货人标签
  ///
  /// In zh, this message translates to:
  /// **'收货人'**
  String get recipientLabel;

  /// 联系电话标签
  ///
  /// In zh, this message translates to:
  /// **'联系电话'**
  String get contactPhoneLabel;

  /// 收货地址标签
  ///
  /// In zh, this message translates to:
  /// **'收货地址'**
  String get shippingAddressLabel;

  /// 物流信息标题
  ///
  /// In zh, this message translates to:
  /// **'物流信息'**
  String get trackingInfoTitle;

  /// 查看详情操作
  ///
  /// In zh, this message translates to:
  /// **'查看详情'**
  String get viewDetailsAction;

  /// 快递单号标签
  ///
  /// In zh, this message translates to:
  /// **'快递单号'**
  String get trackingNumberLabel;

  /// 快递公司标签
  ///
  /// In zh, this message translates to:
  /// **'快递公司'**
  String get shippingCompanyLabel;

  /// 发货备注标签
  ///
  /// In zh, this message translates to:
  /// **'发货备注'**
  String get shippingNoteLabel;

  /// 费用明细标题
  ///
  /// In zh, this message translates to:
  /// **'费用明细'**
  String get priceDetailsTitle;

  /// 商品金额标签
  ///
  /// In zh, this message translates to:
  /// **'商品金额'**
  String get productAmountLabel;

  /// 运费标签
  ///
  /// In zh, this message translates to:
  /// **'运费'**
  String get shippingFeeLabel;

  /// 实付金额标签
  ///
  /// In zh, this message translates to:
  /// **'实付金额'**
  String get totalPaidLabel;

  /// 取消订单操作
  ///
  /// In zh, this message translates to:
  /// **'取消订单'**
  String get cancelOrderAction;

  /// 查看物流操作
  ///
  /// In zh, this message translates to:
  /// **'查看物流'**
  String get viewTrackingAction;

  /// 已复制到剪贴板消息
  ///
  /// In zh, this message translates to:
  /// **'已复制到剪贴板'**
  String get copiedToClipboardMessage;

  /// 确认删除订单标题
  ///
  /// In zh, this message translates to:
  /// **'确认删除'**
  String get confirmDeleteOrderTitle;

  /// 确认删除订单消息
  ///
  /// In zh, this message translates to:
  /// **'确定要删除这个订单吗？此操作不可撤销。'**
  String get confirmDeleteOrderMessage;

  /// 订单详情加载失败消息
  ///
  /// In zh, this message translates to:
  /// **'加载订单详情失败'**
  String get orderDetailLoadFailedMessage;

  /// 订单信息加载失败消息
  ///
  /// In zh, this message translates to:
  /// **'订单信息加载失败'**
  String get orderInfoLoadFailedMessage;

  /// 更新失败消息
  ///
  /// In zh, this message translates to:
  /// **'更新失败: {error}'**
  String updateFailedMessage(String error);

  /// 删除失败消息
  ///
  /// In zh, this message translates to:
  /// **'删除失败: {error}'**
  String deleteFailedMessage(String error);

  /// 编辑对话标题对话框标题
  ///
  /// In zh, this message translates to:
  /// **'编辑对话标题'**
  String get editConversationTitle;

  /// 回复中状态文字
  ///
  /// In zh, this message translates to:
  /// **'回复中...'**
  String get replying;

  /// 图片加载失败消息
  ///
  /// In zh, this message translates to:
  /// **'图片加载失败'**
  String get imageLoadFailed;

  /// 图片不可用消息
  ///
  /// In zh, this message translates to:
  /// **'图片不可用'**
  String get imageNotAvailable;

  /// 松开取消录音文字
  ///
  /// In zh, this message translates to:
  /// **'松开取消录音'**
  String get releaseToCancel;

  /// 录音状态文字
  ///
  /// In zh, this message translates to:
  /// **'正在录音'**
  String get recording;

  /// 上滑取消提示
  ///
  /// In zh, this message translates to:
  /// **'上滑取消'**
  String get slideUpToCancel;

  /// 继续上滑取消提示
  ///
  /// In zh, this message translates to:
  /// **'继续向上滑动取消录音'**
  String get continueSlideUpToCancel;

  /// 拍照发送页面标题
  ///
  /// In zh, this message translates to:
  /// **'拍照发送'**
  String get takePhotoAndSend;

  /// 选择发送区域页面标题
  ///
  /// In zh, this message translates to:
  /// **'选择发送区域'**
  String get selectSendArea;

  /// 发送按钮
  ///
  /// In zh, this message translates to:
  /// **'发送'**
  String get send;

  /// 订单时间轴标题
  ///
  /// In zh, this message translates to:
  /// **'订单时间轴'**
  String get orderTimeline;

  /// 订单创建状态
  ///
  /// In zh, this message translates to:
  /// **'订单创建'**
  String get orderCreated;

  /// 支付完成状态
  ///
  /// In zh, this message translates to:
  /// **'支付完成'**
  String get paymentCompleted;

  /// 商品发货状态
  ///
  /// In zh, this message translates to:
  /// **'商品发货'**
  String get goodsShipped;

  /// 订单完成状态
  ///
  /// In zh, this message translates to:
  /// **'订单完成'**
  String get orderCompleted;

  /// 订单发货对话框标题
  ///
  /// In zh, this message translates to:
  /// **'订单发货'**
  String get shipOrder;

  /// 确认发货按钮
  ///
  /// In zh, this message translates to:
  /// **'确认发货'**
  String get confirmShip;

  /// 发货按钮
  ///
  /// In zh, this message translates to:
  /// **'发货'**
  String get ship;

  /// 输入快递单号提示
  ///
  /// In zh, this message translates to:
  /// **'请输入快递单号'**
  String get enterTrackingNumber;

  /// 快递单号必填验证
  ///
  /// In zh, this message translates to:
  /// **'请输入快递单号'**
  String get trackingNumberRequired;

  /// 选择快递公司提示
  ///
  /// In zh, this message translates to:
  /// **'请选择快递公司（可选）'**
  String get selectShippingCompany;

  /// 输入发货备注提示
  ///
  /// In zh, this message translates to:
  /// **'请输入发货备注（可选）'**
  String get enterShippingNote;

  /// 发货成功消息
  ///
  /// In zh, this message translates to:
  /// **'发货成功'**
  String get shipSuccess;

  /// 发货失败消息
  ///
  /// In zh, this message translates to:
  /// **'发货失败: {error}'**
  String shipFailed(String error);

  /// 产品标签
  ///
  /// In zh, this message translates to:
  /// **'产品'**
  String get productLabel;

  /// 管理员发货标题
  ///
  /// In zh, this message translates to:
  /// **'管理员发货'**
  String get adminShipment;

  /// 快递单号必填字段标签
  ///
  /// In zh, this message translates to:
  /// **'快递单号 *'**
  String get trackingNumberRequiredField;

  /// 快递公司输入提示
  ///
  /// In zh, this message translates to:
  /// **'如：顺丰速运、圆通快递等'**
  String get shippingCompanyHint;

  /// 发货备注输入提示
  ///
  /// In zh, this message translates to:
  /// **'可填写发货说明或注意事项'**
  String get shippingNoteHint;

  /// 发货成功消息
  ///
  /// In zh, this message translates to:
  /// **'发货成功'**
  String get shipmentSuccess;

  /// 发货失败错误消息
  ///
  /// In zh, this message translates to:
  /// **'发货失败: {error}'**
  String shipmentFailedWithError(String error);

  /// 带标题的复制成功提示
  ///
  /// In zh, this message translates to:
  /// **'{title}已复制到剪贴板'**
  String copiedToClipboardWithTitle(String title);

  /// 语音识别中提示
  ///
  /// In zh, this message translates to:
  /// **'正在识别语音...'**
  String get voiceRecognizing;

  /// 语音识别失败重试提示
  ///
  /// In zh, this message translates to:
  /// **'语音识别失败，请重试'**
  String get voiceRecognitionRetry;

  /// 无法打开电话应用时的提示
  ///
  /// In zh, this message translates to:
  /// **'无法打开电话应用，号码已复制到剪贴板'**
  String get cannotOpenPhoneAppCopied;

  /// 操作失败手动拨打提示
  ///
  /// In zh, this message translates to:
  /// **'操作失败，请手动拨打：{phoneNumber}'**
  String operationFailedManualDialWithNumber(String phoneNumber);

  /// 健康助手语音识别结果
  ///
  /// In zh, this message translates to:
  /// **'健康助手语音识别: {text}'**
  String healthAssistantVoiceRecognition(String text);

  /// 健康助手语音处理失败
  ///
  /// In zh, this message translates to:
  /// **'健康助手语音处理失败: {error}'**
  String healthAssistantVoiceProcessingFailed(String error);

  /// 登录失败消息
  ///
  /// In zh, this message translates to:
  /// **'登录失败: {message}'**
  String loginFailedWithMessage(String message);

  /// 验证码错误或已过期
  ///
  /// In zh, this message translates to:
  /// **'验证码错误或已过期'**
  String get verificationCodeIncorrectOrExpired;

  /// 用户名已存在
  ///
  /// In zh, this message translates to:
  /// **'用户名已被注册，请更换用户名'**
  String get usernameAlreadyExists;

  /// DPI适配设置页面标题
  ///
  /// In zh, this message translates to:
  /// **'DPI适配设置'**
  String get dpiAdaptationSettings;

  /// DPI适配设置页面描述
  ///
  /// In zh, this message translates to:
  /// **'调整应用的显示缩放比例以适应不同的屏幕密度'**
  String get dpiAdaptationDescription;

  /// 当前DPI缩放比例
  ///
  /// In zh, this message translates to:
  /// **'当前缩放比例'**
  String get currentDpiScale;

  /// 系统默认选项
  ///
  /// In zh, this message translates to:
  /// **'系统默认'**
  String get systemDefault;

  /// 小尺寸选项
  ///
  /// In zh, this message translates to:
  /// **'小'**
  String get small;

  /// 正常尺寸选项
  ///
  /// In zh, this message translates to:
  /// **'正常'**
  String get normal;

  /// 大尺寸选项
  ///
  /// In zh, this message translates to:
  /// **'大'**
  String get large;

  /// 超大尺寸选项
  ///
  /// In zh, this message translates to:
  /// **'超大'**
  String get extraLarge;

  /// 预览文本标签
  ///
  /// In zh, this message translates to:
  /// **'预览文本'**
  String get previewText;

  /// 示例预览文本
  ///
  /// In zh, this message translates to:
  /// **'这是一段示例文本，用于预览当前的缩放效果。'**
  String get sampleText;

  /// 应用更改按钮
  ///
  /// In zh, this message translates to:
  /// **'应用更改'**
  String get applyChanges;

  /// 重置为默认按钮
  ///
  /// In zh, this message translates to:
  /// **'重置为默认'**
  String get resetToDefault;

  /// DPI设置应用成功提示
  ///
  /// In zh, this message translates to:
  /// **'DPI设置已应用'**
  String get dpiSettingsApplied;

  /// DPI设置重置成功提示
  ///
  /// In zh, this message translates to:
  /// **'DPI设置已重置为默认'**
  String get dpiSettingsReset;

  /// DPI自动适配模式
  ///
  /// In zh, this message translates to:
  /// **'自动适配'**
  String get dpiModeAuto;

  /// DPI自动适配模式描述
  ///
  /// In zh, this message translates to:
  /// **'根据设备DPI自动调整界面大小（推荐）'**
  String get dpiModeAutoDesc;

  /// DPI紧凑模式
  ///
  /// In zh, this message translates to:
  /// **'紧凑模式'**
  String get dpiModeSmall;

  /// DPI紧凑模式描述
  ///
  /// In zh, this message translates to:
  /// **'较小的界面元素，适合高DPI设备'**
  String get dpiModeSmallDesc;

  /// DPI标准模式
  ///
  /// In zh, this message translates to:
  /// **'标准模式'**
  String get dpiModeStandard;

  /// DPI标准模式描述
  ///
  /// In zh, this message translates to:
  /// **'默认大小的界面元素'**
  String get dpiModeStandardDesc;

  /// DPI宽松模式
  ///
  /// In zh, this message translates to:
  /// **'宽松模式'**
  String get dpiModeLarge;

  /// DPI宽松模式描述
  ///
  /// In zh, this message translates to:
  /// **'较大的界面元素，适合低DPI设备'**
  String get dpiModeLargeDesc;

  /// 当前状态标题
  ///
  /// In zh, this message translates to:
  /// **'当前状态'**
  String get currentStatus;

  /// 适配模式标签
  ///
  /// In zh, this message translates to:
  /// **'适配模式'**
  String get adaptationMode;

  /// 缩放因子标签
  ///
  /// In zh, this message translates to:
  /// **'缩放因子'**
  String get scaleFactor;

  /// 设备信息标题
  ///
  /// In zh, this message translates to:
  /// **'设备信息'**
  String get deviceInfo;

  /// 屏幕尺寸标签
  ///
  /// In zh, this message translates to:
  /// **'屏幕尺寸'**
  String get screenSize;

  /// 设备像素比标签
  ///
  /// In zh, this message translates to:
  /// **'设备像素比'**
  String get devicePixelRatio;

  /// 屏幕对角线标签
  ///
  /// In zh, this message translates to:
  /// **'屏幕对角线'**
  String get screenDiagonal;

  /// 自动缩放因子标签
  ///
  /// In zh, this message translates to:
  /// **'自动缩放因子'**
  String get autoScaleFactor;

  /// 效果预览标题
  ///
  /// In zh, this message translates to:
  /// **'效果预览'**
  String get effectPreview;

  /// 示例按钮文本
  ///
  /// In zh, this message translates to:
  /// **'示例按钮'**
  String get sampleButton;

  /// 标题文本示例
  ///
  /// In zh, this message translates to:
  /// **'标题文本'**
  String get titleText;

  /// 示例描述文本
  ///
  /// In zh, this message translates to:
  /// **'这是一段示例文本，用于预览当前DPI适配设置的效果。'**
  String get sampleDescription;

  /// 英寸单位
  ///
  /// In zh, this message translates to:
  /// **'英寸'**
  String get inches;

  /// DPI适配设置选项标题
  ///
  /// In zh, this message translates to:
  /// **'DPI适配'**
  String get dpiAdaptation;

  /// DPI适配设置选项描述
  ///
  /// In zh, this message translates to:
  /// **'调整界面元素大小以适应不同DPI的设备'**
  String get dpiAdaptationSubtitle;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'ug', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'ug':
      return AppLocalizationsUg();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
