// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get settingsTitle => '设置';

  @override
  String get languageSettings => '语言设置';

  @override
  String get selectLanguage => '选择语言';

  @override
  String get selectSourceLanguage => '选择源语言';

  @override
  String get selectTargetLanguage => '选择目标语言';

  @override
  String get languageChinese => '中文';

  @override
  String get languageEnglish => 'English';

  @override
  String get languageUyghur => 'ئۇيغۇرچە';

  @override
  String get cancel => '取消';

  @override
  String get confirm => '确认';

  @override
  String get displaySettings => '显示设置';

  @override
  String get darkMode => '暗色模式';

  @override
  String get darkModeDescription => '在浅色和暗色主题之间切换';

  @override
  String get followSystemTheme => '跟随系统主题';

  @override
  String get followSystemThemeDescription => '自动适应系统的浅色/暗色设置';

  @override
  String get fontSize => '字体大小';

  @override
  String get fontSizeDescription => '调整应用内字体大小';

  @override
  String get languageSettingsDescription => '选择应用显示语言';

  @override
  String get other => '其他';

  @override
  String get helpAndFeedback => '帮助与反馈';

  @override
  String get helpAndFeedbackDescription => '常见问题和意见反馈';

  @override
  String get aboutUs => '关于我们';

  @override
  String get aboutUsDescription => '版本信息和公司介绍';

  @override
  String get logout => '退出登录';

  @override
  String get logoutDescription => '清除登录状态并返回登录页';

  @override
  String get aboutDialogTitle => '关于我们';

  @override
  String get aboutDialogContent => '健康助手 v1.0.0\n\n一款专业的AI健康导游应用';

  @override
  String get homeTitle => '健康助手';

  @override
  String get historyTitle => '历史';

  @override
  String get aiTourGuideTitle => '健康助手';

  @override
  String get smartGuide => '智能导游';

  @override
  String get welcomeTo => '欢迎您来到';

  @override
  String get shareApp => '分享应用';

  @override
  String get distributionManagement => '分销管理';

  @override
  String get myLanguage => '我方语言';

  @override
  String get theirLanguage => '对方语言';

  @override
  String get sourceLanguage => '源';

  @override
  String get targetLanguage => '目标';

  @override
  String get bottomNavHome => '健康助手';

  @override
  String get bottomNavHistory => '历史';

  @override
  String get bottomNavAiGuide => '健康助手';

  @override
  String get bottomNavSearch => '列表';

  @override
  String get bottomNavProfile => '我的';

  @override
  String get bottomNavSettings => '设置';

  @override
  String get searchPageTitle => '列表';

  @override
  String get searchHint => '搜索医生或产品...';

  @override
  String get doctorTab => '医生';

  @override
  String get productTab => '产品';

  @override
  String get noDoctorsAvailable => '暂无医生信息';

  @override
  String get noProductsAvailable => '暂无产品信息';

  @override
  String get noSearchResults => '未找到相关结果';

  @override
  String get inputHint => '请输入您的问题...';

  @override
  String get tapToSpeak => '点击说话';

  @override
  String get listening => '正在听...';

  @override
  String get processing => '处理中...';

  @override
  String get clearHistory => '清空';

  @override
  String get copy => '复制';

  @override
  String get share => '分享';

  @override
  String get play => '播放';

  @override
  String get pause => '暂停';

  @override
  String get retry => '重试';

  @override
  String get error => '错误';

  @override
  String get networkError => '网络连接失败，请检查网络设置';

  @override
  String get permissionDenied => '权限被拒绝';

  @override
  String get cameraPermissionRequired => '需要相机权限';

  @override
  String get microphonePermissionRequired => '需要麦克风权限才能录音';

  @override
  String get appTitle => '健康助手';

  @override
  String get welcomeMessage => '欢迎使用健康助手';

  @override
  String get welcomeDescription => '您的专属AI健康导游，随时为您提供健康咨询和导游服务';

  @override
  String get exitAppConfirm => '再次返回即可退出软件';

  @override
  String get themeSwitch => '主题切换';

  @override
  String get themeSwitchMessage => '切换主题模式需要重启应用才能完全生效，是否立即重启？';

  @override
  String get languageSwitchMessage => '切换语言需要重启应用才能完全生效，是否立即重启？';

  @override
  String get restart => '重启';

  @override
  String get userManagementTab => '用户管理';

  @override
  String get userManagement => '用户管理';

  @override
  String get userList => '用户列表';

  @override
  String get userStatistics => '用户统计';

  @override
  String get totalUsers => '总用户数';

  @override
  String get activeUsers => '活跃用户';

  @override
  String get adminUsers => '管理员';

  @override
  String get doctorUsers => '医生';

  @override
  String get searchUsers => '搜索用户';

  @override
  String get searchByNicknamePhoneId => '按昵称、电话或ID搜索';

  @override
  String get userStatus => '用户状态';

  @override
  String get allStatus => '全部';

  @override
  String get enabledStatus => '已启用';

  @override
  String get disabledStatus => '已禁用';

  @override
  String get userRole => '用户角色';

  @override
  String get allRoles => '全部角色';

  @override
  String get normalUser => '普通用户';

  @override
  String get adminUser => '管理员';

  @override
  String get doctorUser => '医生';

  @override
  String get userGender => '性别';

  @override
  String get male => '男';

  @override
  String get female => '女';

  @override
  String get unknown => '未知';

  @override
  String get registerSource => '注册来源';

  @override
  String get appSource => 'APP';

  @override
  String get miniProgramSource => '小程序';

  @override
  String get userBalance => '用户余额';

  @override
  String get userIntegral => '用户积分';

  @override
  String get adjustBalance => '调整余额';

  @override
  String get adjustIntegral => '调整积分';

  @override
  String get adjustAmount => '调整金额';

  @override
  String get adjustReason => '调整原因';

  @override
  String get pleaseEnterAmount => '请输入调整金额';

  @override
  String get pleaseEnterReason => '请输入调整原因';

  @override
  String get positiveForIncrease => '正数为增加，负数为减少';

  @override
  String get userDetail => '用户详情';

  @override
  String get editUser => '编辑用户';

  @override
  String get enableUser => '启用用户';

  @override
  String get disableUser => '禁用用户';

  @override
  String get resetPassword => '重置密码';

  @override
  String get newPassword => '新密码';

  @override
  String get pleaseEnterNewPassword => '请输入新密码';

  @override
  String get passwordLength => '密码长度6-20位';

  @override
  String get userNickname => '用户昵称';

  @override
  String get userPhone => '用户电话';

  @override
  String get userBirthday => '用户生日';

  @override
  String get registrationTime => '注册时间';

  @override
  String get lastLoginTime => '最后登录';

  @override
  String get userTokens => '登录令牌';

  @override
  String get clearAllTokens => '清除所有令牌';

  @override
  String get confirmClearTokens => '确认清除所有登录令牌？';

  @override
  String get clearTokensWarning => '清除后用户将在所有设备上被强制下线';

  @override
  String get deviceType => '设备类型';

  @override
  String get expiryTime => '过期时间';

  @override
  String get createTime => '创建时间';

  @override
  String get expired => '已过期';

  @override
  String get valid => '有效';

  @override
  String get loadUserListFailed => '加载用户列表失败';

  @override
  String get loadUserDetailFailed => '加载用户详情失败';

  @override
  String get updateUserSuccess => '更新用户信息成功';

  @override
  String get updateUserFailed => '更新用户信息失败';

  @override
  String get adjustBalanceSuccess => '调整余额成功';

  @override
  String get adjustBalanceFailed => '调整余额失败';

  @override
  String get adjustIntegralSuccess => '调整积分成功';

  @override
  String get adjustIntegralFailed => '调整积分失败';

  @override
  String get resetPasswordSuccess => '重置密码成功';

  @override
  String get resetPasswordFailed => '重置密码失败';

  @override
  String get clearTokensSuccess => '清除令牌成功';

  @override
  String get clearTokensFailed => '清除令牌失败';

  @override
  String get noUsersFound => '暂无用户';

  @override
  String get enableUserSuccess => '启用用户成功';

  @override
  String get disableUserSuccess => '禁用用户成功';

  @override
  String get reset => '重置';

  @override
  String get apply => '应用';

  @override
  String get todayNewUsers => '今日新增';

  @override
  String get thisWeekNewUsers => '本周新增';

  @override
  String get totalBalance => '总余额';

  @override
  String get totalIntegral => '总积分';

  @override
  String get expandFilters => '展开筛选';

  @override
  String get collapseFilters => '收起筛选';

  @override
  String get userDevelopmentInProgress => '用户详情页面开发中...';

  @override
  String get roleAdmin => '管理员';

  @override
  String get roleDoctor => '医生';

  @override
  String get roleReferrer => '分销员';

  @override
  String get roleNormalUser => '普通用户';

  @override
  String get editUserInfo => '编辑用户信息';

  @override
  String get editUserRole => '编辑用户角色';

  @override
  String get userNicknameLabel => '用户昵称';

  @override
  String get userPhoneLabel => '用户电话';

  @override
  String get userBirthdayLabel => '用户生日';

  @override
  String get userGenderLabel => '用户性别';

  @override
  String get pleaseEnterNickname => '请输入用户昵称';

  @override
  String get pleaseEnterPhone => '请输入用户电话';

  @override
  String get selectBirthday => '选择生日';

  @override
  String get selectGender => '选择性别';

  @override
  String get associatedDoctor => '关联医生';

  @override
  String get selectDoctor => '选择医生';

  @override
  String get referrerLevel => '分销员等级';

  @override
  String get level => '等级';

  @override
  String get ipAddress => 'IP地址';

  @override
  String get roleManagement => '角色权限管理';

  @override
  String get currentRoles => '当前角色';

  @override
  String get roleDetails => '角色详情';

  @override
  String get editRole => '编辑角色';

  @override
  String get balanceIntegralManagement => '余额积分管理';

  @override
  String get tokenManagement => '令牌管理';

  @override
  String get totalTokens => '总令牌数';

  @override
  String get noTokensFound => '暂无登录令牌';

  @override
  String get tokenClearSuccess => '令牌清除成功';

  @override
  String get tokenClearFailed => '令牌清除失败';

  @override
  String get loadTokensFailed => '加载令牌列表失败';

  @override
  String get pleaseCompleteInfo => '请填写完整信息';

  @override
  String get pleaseEnterValidAmount => '请输入有效的金额';

  @override
  String get pleaseEnterValidPoints => '请输入有效的积分';

  @override
  String get userEditInProgress => '用户编辑功能开发中...';

  @override
  String get roleEditInProgress => '角色编辑功能开发中...';

  @override
  String get genderMale => '男';

  @override
  String get genderFemale => '女';

  @override
  String get genderUnknown => '未知';

  @override
  String get referrerText => '分销员';

  @override
  String get updateRoleSuccess => '角色更新成功';

  @override
  String get updateRoleFailed => '角色更新失败';

  @override
  String get adminRoleDescription => '拥有系统管理权限';

  @override
  String get doctorRoleDescription => '可以管理产品和订单';

  @override
  String get referrerRoleDescription => '可以推广获得佣金';

  @override
  String get pleaseEnterReferrerLevel => '请输入分销员等级';

  @override
  String get registerSourceApp => 'APP';

  @override
  String get registerSourceMiniProgram => '小程序';

  @override
  String get statusEnabled => '启用';

  @override
  String get statusDisabled => '禁用';

  @override
  String get healthProfile => '健康档案';

  @override
  String get viewFullHealthProfile => '查看完整档案';

  @override
  String get heartRate => '心率';

  @override
  String get bodyTemperature => '体温';

  @override
  String get weight => '体重';

  @override
  String get height => '身高';

  @override
  String get healthProfileFeatureComingSoon => '健康档案功能即将上线';

  @override
  String get bloodType => '血型';

  @override
  String get exerciseFrequency => '运动频率';

  @override
  String get sedentary => '久坐';

  @override
  String get lightExercise => '轻度运动';

  @override
  String get moderateExercise => '中度运动';

  @override
  String get activeExercise => '高强度运动';

  @override
  String get noHealthProfileYet => '暂无健康档案';

  @override
  String get createHealthProfile => '创建健康档案';

  @override
  String get healthProfileEditFeatureComingSoon => '健康档案编辑功能即将上线';

  @override
  String get basicInfo => '基本信息';

  @override
  String get allergyHistory => '过敏史';

  @override
  String get chronicDiseaseHistory => '慢性病史';

  @override
  String get currentMedication => '当前用药';

  @override
  String get lifestyle => '生活方式';

  @override
  String get hasAllergies => '是否有药物、食物或其它物质过敏';

  @override
  String get drugAllergies => '药物过敏';

  @override
  String get foodAllergies => '食物过敏';

  @override
  String get otherAllergies => '其他过敏';

  @override
  String get hasChronicDiseases => '是否有慢性病';

  @override
  String get chronicDiseasesList => '慢性病列表';

  @override
  String get bloodPressureRange => '平常血压范围';

  @override
  String get bloodSugarRange => '平常空腹血糖范围';

  @override
  String get hasCurrentMedication => '是否用药';

  @override
  String get medicationDetails => '用药详情';

  @override
  String get smokingStatus => '吸烟情况';

  @override
  String get drinkingStatus => '饮酒情况';

  @override
  String get sleepDuration => '平均每晚睡眠时长';

  @override
  String get sleepQuality => '睡眠质量';

  @override
  String get stressLevel => '近期压力水平';

  @override
  String get yes => '是';

  @override
  String get no => '否';

  @override
  String get never => '从不';

  @override
  String get quit => '已戒';

  @override
  String get occasional => '偶尔';

  @override
  String get daily => '每天';

  @override
  String get social => '社交';

  @override
  String get weekly => '每周';

  @override
  String get lessThan6Hours => '少于6小时';

  @override
  String get sixToSevenHours => '6-7小时';

  @override
  String get sevenToEightHours => '7-8小时';

  @override
  String get moreThan8Hours => '超过8小时';

  @override
  String get good => '良好';

  @override
  String get fair => '一般';

  @override
  String get poor => '较差';

  @override
  String get veryLow => '很低';

  @override
  String get low => '低';

  @override
  String get moderate => '中等';

  @override
  String get high => '高';

  @override
  String get veryHigh => '很高';

  @override
  String get pleaseEnterPhoneNumber => '请输入手机号';

  @override
  String get pleaseEnterCorrectPhoneNumber => '请输入正确的手机号';

  @override
  String get pleaseEnterPassword => '请输入密码';

  @override
  String get passwordMinLength => '密码长度至少6位';

  @override
  String get loggingIn => '正在登录...';

  @override
  String get loginSuccessful => '登录成功';

  @override
  String get loginSuccessButNoData => '登录成功但用户数据为空';

  @override
  String dataProcessingError(String error) {
    return '处理用户数据时出错: $error';
  }

  @override
  String loginProcessError(String error) {
    return '登录过程中出错: $error';
  }

  @override
  String get passwordIncorrect => '密码错误';

  @override
  String get phoneNotRegistered => '该手机号未注册，请先注册';

  @override
  String get passwordLogin => '密码登录';

  @override
  String get passwordLoginSubtitle => '请使用您的手机号和密码登录';

  @override
  String get phoneNumberHint => '请输入手机号';

  @override
  String get passwordHint => '请输入密码';

  @override
  String get forgotPassword => '忘记密码?';

  @override
  String get smsLogin => '验证码登录';

  @override
  String get registerAccount => '注册账号';

  @override
  String get orOtherLoginMethods => '或选其他登录方式';

  @override
  String get loginAgreement => '登录即表示您同意《用户协议》和《隐私政策》';

  @override
  String get verificationCodeSent => '验证码已发送';

  @override
  String get sendFailed => '发送失败';

  @override
  String get pleaseEnterVerificationCode => '请输入验证码';

  @override
  String get verificationCodeShouldBe6Digits => '验证码应为6位数字';

  @override
  String get login => '登录';

  @override
  String get welcomeBack => '欢迎回来';

  @override
  String get pleaseLoginWithPhoneNumber => '请使用您的手机号登录账户';

  @override
  String get passwordLoginDesc => '请使用您的手机号和密码登录';

  @override
  String get agreeToTerms => '登录即表示您同意《用户协议》和《隐私政策》';

  @override
  String get verificationCodeHint => '请输入验证码';

  @override
  String get newPasswordHint => '请输入新密码';

  @override
  String get confirmPasswordHint => '请再次输入新密码';

  @override
  String get getVerificationCode => '获取验证码';

  @override
  String get resendVerificationCode => '重新发送';

  @override
  String get resetPasswordDescription => '请输入手机号获取验证码，然后设置新密码';

  @override
  String get confirmReset => '确认重置';

  @override
  String get passwordsDoNotMatch => '两次输入的密码不一致';

  @override
  String get passwordResetSuccess => '密码重置成功，请使用新密码登录';

  @override
  String get contactCustomerService => '如有问题，请联系客服处理';

  @override
  String get resetPasswordTitle => '重置密码';

  @override
  String get enterCorrectPhoneNumber => '请输入正确的手机号';

  @override
  String get enterVerificationCode => '请输入验证码';

  @override
  String get verificationCodeSixDigits => '验证码应为6位数字';

  @override
  String get enterNewPassword => '请输入新密码';

  @override
  String get passwordMinSixCharacters => '密码长度至少6位';

  @override
  String get enterNewPasswordAgain => '请再次输入新密码';

  @override
  String get getVerificationCodeButton => '获取验证码';

  @override
  String resendCountdown(int seconds) {
    return '重新发送(${seconds}s)';
  }

  @override
  String get sendVerificationCodeFailed => '发送验证码失败';

  @override
  String get resettingPasswordLoading => '正在重置密码...';

  @override
  String get passwordResetFailed => '密码重置失败';

  @override
  String get networkConnectionFailedRetry => '网络连接失败，请稍后重试';

  @override
  String get clearHistoryTitle => '清空聊天历史';

  @override
  String get historyCleared => '历史记录已清空';

  @override
  String get clearHistoryFailed => '清空历史记录失败';

  @override
  String get clearAllHistory => '清空所有历史';

  @override
  String get daysAgo => '天前';

  @override
  String hoursAgo(int hours) {
    return '$hours小时前';
  }

  @override
  String minutesAgo(int minutes) {
    return '$minutes分钟前';
  }

  @override
  String get justNow => '刚刚';

  @override
  String get retakePhoto => '重新拍照';

  @override
  String get imageProcessingError => '处理图片时出错';

  @override
  String get exitAppHint => '再次返回即可退出软件';

  @override
  String get chinese => '中文';

  @override
  String get uyghur => '维吾尔语';

  @override
  String get kazakh => '哈萨克语';

  @override
  String get russian => '俄语';

  @override
  String get french => '法语';

  @override
  String get spanish => '西班牙语';

  @override
  String get cantonese => '粤语';

  @override
  String get selectSourceLanguageFirst => '请先选择源语言';

  @override
  String get targetLanguageWillUpdate => '目标语言将根据源语言自动更新';

  @override
  String get faceToFaceConversation => '面对面交流';

  @override
  String get conversation => '对话';

  @override
  String get newChat => '新建';

  @override
  String get aiChatHistory => '聊天历史';

  @override
  String get noChatHistory => '暂无聊天记录';

  @override
  String get startNewChat => '点击右上角新建按钮开始聊天';

  @override
  String get startChatting => '开始聊天';

  @override
  String get sendFirstMessage => '发送第一条消息开始对话';

  @override
  String get thinking => '正在思考中...';

  @override
  String get sending => '发送中...';

  @override
  String get audioMessage => '语音消息';

  @override
  String get typeMessage => '输入消息...';

  @override
  String get editTitle => '编辑标题';

  @override
  String get enterTitle => '请输入标题';

  @override
  String get deleteConversation => '删除对话';

  @override
  String get deleteConversationConfirm => '确定要删除这个对话吗？此操作无法撤销。';

  @override
  String get edit => '编辑';

  @override
  String get delete => '删除';

  @override
  String get save => '保存';

  @override
  String get loadFailed => '加载数据失败';

  @override
  String get microphonePermissionDenied => '麦克风权限被拒绝';

  @override
  String get recordingTooShort => '录音时间太短';

  @override
  String get recordingCancelled => '录音已取消';

  @override
  String get aiChatHistorySubtitle => '查看您与健康助手的对话记录';

  @override
  String get clearHistoryButton => '清空历史';

  @override
  String get clearHistoryConfirm => '确定要清空所有聊天历史吗？此操作无法撤销。';

  @override
  String get clearConversation => '清空对话';

  @override
  String get holdToSpeak => '按住说话';

  @override
  String get waitingForOther => '等待对方说话';

  @override
  String get microphonePermissionNeeded => '需要麦克风权限才能录音';

  @override
  String recordingFailed(String error) {
    return '录音失败: $error';
  }

  @override
  String get invalidAudioFile => '录音文件无效';

  @override
  String get audioProcessingFailed => '音频处理失败，请重试';

  @override
  String get cannotRecognizeVoice => '未能识别语音内容';

  @override
  String get confirmClearConversation => '确定要清空所有聊天记录吗？此操作无法撤销。';

  @override
  String get conversationCleared => '对话已清空';

  @override
  String get user => '用户';

  @override
  String get clickToLogin => '点击登录';

  @override
  String get loginToEnjoyMoreFeatures => '登录后享受更多功能';

  @override
  String get editProfile => '编辑资料';

  @override
  String get vipMember => 'VIP会员';

  @override
  String get distributorLevel => '分销员';

  @override
  String get appName => '健康助手';

  @override
  String get shareSuccess => '分享成功！好友通过您的链接下载可获得奖励';

  @override
  String get shareNotAvailable => '分享功能暂时不可用，请稍后重试';

  @override
  String get shareSubject => '推荐App';

  @override
  String shareContentWithReferral(String appName, String url) {
    return '我发现了一款超棒的健康助手App：$appName！专业的AI健康导游，随时为您提供健康咨询和导游服务。使用我的专属推荐链接下载，还有额外福利哦！🎁\n\n立即下载：$url';
  }

  @override
  String shareContentNormal(String appName, String url) {
    return '我发现了一款超棒的健康助手App：$appName！专业的AI健康导游，随时为您提供健康咨询和导游服务。你也来试试吧！\n\n立即下载：$url';
  }

  @override
  String get logoutConfirmation => '确定要退出登录吗？退出后需要重新登录才能使用完整功能。';

  @override
  String get logoutSuccess => '已退出登录';

  @override
  String get logoutFailed => '退出登录失败';

  @override
  String get comingSoon => '(即将推出)';

  @override
  String yearsExperience(int years) {
    return '$years年经验';
  }

  @override
  String ratingScore(String rating) {
    return '$rating分';
  }

  @override
  String get aiAssistant => 'AI';

  @override
  String get professionalIntroduction => '专业介绍';

  @override
  String doctorAiAssistantSelected(String doctorName) {
    return '已选择$doctorName的AI助手';
  }

  @override
  String get loading => '加载中...';

  @override
  String get noDoctorInfo => '暂无医生信息';

  @override
  String get doctorTitle => '医师';

  @override
  String get specialtyField => '擅长领域';

  @override
  String get startChatWithAiGuide => '开始与AI导游对话吧';

  @override
  String get updateTitleFailed => '更新标题失败';

  @override
  String get selectAddress => '选择地址';

  @override
  String get addressManagement => '地址管理';

  @override
  String get noAddressesYet => '暂无收货地址';

  @override
  String get clickToAddAddress => '点击右下角添加地址';

  @override
  String get setAsDefault => '设为默认';

  @override
  String selectedItemsCount(int count) {
    return '已选择 $count 件商品';
  }

  @override
  String get totalAmount => '合计: ';

  @override
  String get myLikesTitle => '我的点赞';

  @override
  String get myFavoritesTitle => '我的收藏';

  @override
  String get noLikedDoctors => '暂无点赞的医生';

  @override
  String get noFavoriteDoctors => '暂无收藏的医生';

  @override
  String get goLikeDoctors => '去医生详情页面为您喜欢的医生点赞吧';

  @override
  String get goFavoriteDoctors => '去医生详情页面收藏您喜欢的医生吧';

  @override
  String get selectAll => '全选';

  @override
  String get deleteSelected => '删除选中';

  @override
  String get checkout => '结算';

  @override
  String deleteWithCount(int count) {
    return '删除 ($count)';
  }

  @override
  String checkoutWithCount(int count) {
    return '结算 ($count)';
  }

  @override
  String get doctorInfo => '医生信息';

  @override
  String get doctorName => '医生姓名';

  @override
  String get contactPhone => '联系电话';

  @override
  String get workAddress => '工作地址';

  @override
  String get call => '拨打';

  @override
  String get noProducts => '暂无产品';

  @override
  String productsCount(int count) {
    return '$count个产品';
  }

  @override
  String get buyNow => '立即购买';

  @override
  String get addToCart => '加入购物车';

  @override
  String get doctor => '医生';

  @override
  String get productQuantity => '商品数量';

  @override
  String get productTotalPrice => '商品总价';

  @override
  String get shippingFee => '运费';

  @override
  String get free => '免费';

  @override
  String get actualPayment => '实付款';

  @override
  String get confirmOrder => '确认订单';

  @override
  String get orderCreatedSuccess => '订单创建成功';

  @override
  String createOrderFailed(String error) {
    return '创建订单失败：$error';
  }

  @override
  String checkoutFailed(String error) {
    return '结算失败: $error';
  }

  @override
  String get quantityRange => '请输入1-99之间的有效数量';

  @override
  String get items => '件';

  @override
  String get consultation => '咨询';

  @override
  String get appointment => '预约';

  @override
  String get years => '年';

  @override
  String get yearsOfExperience => '从业年限';

  @override
  String get productDetail => '产品详情';

  @override
  String get price => '价格';

  @override
  String get appointmentTime => '预约时间';

  @override
  String get detailedDescription => '详细说明';

  @override
  String get selectQuantity => '选择数量';

  @override
  String get quantity => '数量';

  @override
  String get cartEmpty => '购物车空空如也';

  @override
  String get cartEmptyDescription => '快去挑选心仪的商品吧';

  @override
  String get goShopping => '去购物';

  @override
  String get purchaseConfirmation => '购买确认';

  @override
  String get manufacturer => '制造商';

  @override
  String get wednesday => '周三';

  @override
  String get morning => '上午';

  @override
  String get afternoon => '下午';

  @override
  String get evening => '晚上';

  @override
  String get confirmPurchase => '确认购买';

  @override
  String get productName => '产品名称';

  @override
  String get unitPrice => '单价';

  @override
  String get purchaseConfirmationMessage => '请确认您的购买信息，点击确认后将跳转到订单确认页面。';

  @override
  String get orderConfirmation => '确认订单';

  @override
  String get productInfo => '商品信息';

  @override
  String get shippingInfo => '物流信息';

  @override
  String get orderAmount => '订单金额';

  @override
  String get recipientName => '收货人姓名';

  @override
  String get recipientPhone => '收货人电话';

  @override
  String get shippingAddress => '收货地址';

  @override
  String get getCurrentLocation => '获取当前位置';

  @override
  String get gettingLocation => '正在获取位置...';

  @override
  String get subtotal => '小计';

  @override
  String get submitOrder => '提交订单';

  @override
  String get submittingOrder => '正在提交订单...';

  @override
  String get enterRecipientName => '请输入收货人姓名';

  @override
  String get enterRecipientPhone => '请输入收货人电话';

  @override
  String get enterShippingAddress => '请输入收货地址';

  @override
  String totalItems(int count) {
    return '共$count件商品';
  }

  @override
  String get done => '完成';

  @override
  String get region => '所在地区';

  @override
  String get selectRegion => '请选择省市区';

  @override
  String get pleaseSelectRegion => '请选择所在地区';

  @override
  String get productAmount => '商品金额';

  @override
  String get freeShipping => '免运费';

  @override
  String get defaultAddress => '默认';

  @override
  String get editAddress => '编辑';

  @override
  String get deleteAddress => '删除';

  @override
  String get loadAddressFailed => '加载地址失败';

  @override
  String get setDefaultSuccess => '设置成功';

  @override
  String get setDefaultFailed => '设置失败';

  @override
  String get addAddress => '新增地址';

  @override
  String get receiverName => '收货人姓名';

  @override
  String get enterReceiverName => '请输入收货人姓名';

  @override
  String get enterContactPhone => '请输入联系电话';

  @override
  String get detailedAddress => '详细地址';

  @override
  String get enterDetailedAddress => '请输入详细的收货地址（街道、门牌号等）';

  @override
  String get postalCodeOptional => '邮政编码（可选）';

  @override
  String get enterPostalCode => '请输入邮政编码';

  @override
  String get addressLabelOptional => '地址标签（可选）';

  @override
  String get enterAddressLabel => '如：家、公司、学校等';

  @override
  String get addressTooShort => '详细地址至少需要5个字符';

  @override
  String get addressTooLong => '详细地址不能超过200个字符';

  @override
  String get saveChanges => '保存修改';

  @override
  String get saveAddress => '保存地址';

  @override
  String get setAsDefaultAddress => '设为默认地址';

  @override
  String get likeSuccess => '点赞成功';

  @override
  String get unlikeSuccess => '取消点赞成功';

  @override
  String get favoriteSuccess => '收藏成功';

  @override
  String get unfavoriteSuccess => '取消收藏成功';

  @override
  String get operationFailed => '操作失败';

  @override
  String get consultDoctor => '咨询医生';

  @override
  String get doctorDetails => '医生详情';

  @override
  String get specialties => '擅长领域';

  @override
  String get doctorRecommendations => '医生推荐';

  @override
  String get workingHours => '周一至周五 9:00-17:00';

  @override
  String get cannotOpenPhoneApp => '无法打开电话应用，号码已复制到剪贴板';

  @override
  String get operationFailedManualDial => '操作失败，请手动拨打';

  @override
  String get physician => '医师';

  @override
  String get noDescription => '暂无描述';

  @override
  String get viewDetails => '查看详情';

  @override
  String copiedToClipboard(String content) {
    return '$content已复制到剪贴板';
  }

  @override
  String get copyFailed => '复制失败';

  @override
  String get mapPreparingPleaseWait => '地图正在准备中，请稍后再试';

  @override
  String get mapTitle => '地图';

  @override
  String get aiGuideVoiceRecognitionFailure => '健康助手语音识别失败';

  @override
  String searchingCategory(String category) {
    return '正在搜索$category...';
  }

  @override
  String get tryOtherCategoriesOrCheckNetwork => '请尝试选择其他分类或检查网络连接';

  @override
  String noResultsFoundFor(String category, String city) {
    return '未找到$city$category';
  }

  @override
  String noRelatedCategoryFound(String category) {
    return '未找到相关$category';
  }

  @override
  String get address => '地址';

  @override
  String addressLabel(String address) {
    return '地址：$address';
  }

  @override
  String get navigation => '导航';

  @override
  String openNavigationFailed(String error) {
    return '打开导航失败: $error';
  }

  @override
  String get parksAndSquares => '公园广场';

  @override
  String get parks => '公园';

  @override
  String get zoos => '动物园';

  @override
  String get botanicalGardens => '植物园';

  @override
  String get aquariums => '水族馆';

  @override
  String get citySquares => '城市广场';

  @override
  String get memorialHalls => '纪念馆';

  @override
  String get templesAndTaoistTemples => '寺庙道观';

  @override
  String get churches => '教堂';

  @override
  String get beaches => '海滩';

  @override
  String loadDetailsFailed(String error) {
    return '加载详情失败: $error';
  }

  @override
  String get specialtyFood => '特色美食';

  @override
  String get contactInfo => '联系方式';

  @override
  String get website => '网站';

  @override
  String get generatingDetailInfo => '正在生成详情信息...';

  @override
  String get viewAiGeneratedDetailedIntroduction => '查看AI生成的详细介绍';

  @override
  String get clickToGetAiGeneratedDetailedIntroduction => '点击获取AI生成的详细介绍';

  @override
  String get aiGenerating => 'AI正在生成中...';

  @override
  String get generateDetailsFailed => '生成详情失败，请稍后重试';

  @override
  String openNavigationFailedError(String error) {
    return '打开导航失败: $error';
  }

  @override
  String get addressCopiedToClipboard => '地址已复制到剪贴板';

  @override
  String get scenicSpotType => '风景名胜';

  @override
  String openNavigationFailedWithError(String error) {
    return '打开导航失败: $error';
  }

  @override
  String get mapLoadFailed => '地图加载失败';

  @override
  String get unableToLoadMapPleaseRetry => '无法加载地图，请返回并重试';

  @override
  String get back => '返回';

  @override
  String get locateToCurrentPosition => '定位到当前位置';

  @override
  String get searchLocation => '搜索地点...';

  @override
  String foundLocation(String name) {
    return '已找到：$name';
  }

  @override
  String get mapControllerNotInitialized => '地图控制器未初始化，请返回并重试';

  @override
  String locationServiceException(String error) {
    return '定位服务异常：$error';
  }

  @override
  String get mapNotFullyLoaded => '地图未完全加载，请稍后重试';

  @override
  String locationFailed(String error) {
    return '定位失败：$error';
  }

  @override
  String get cameraPermissionNeeded => '无法打开相机，请检查权限设置';

  @override
  String get aiTourGuideRecognition => '健康助手识别';

  @override
  String get aiTourGuideVoiceRecognition => '健康助手语音识别';

  @override
  String get userAvatarFeatureInDevelopment => '用户头像功能开发中';

  @override
  String get inDevelopment => '功能开发中';

  @override
  String get close => '关闭';

  @override
  String get overview => '概览';

  @override
  String get records => '记录';

  @override
  String get team => '团队';

  @override
  String get promotion => '推广';

  @override
  String get loadMoreFailed => '加载更多失败';

  @override
  String get getUserListFailed => '获取用户列表失败';

  @override
  String get levelUpdateSuccess => '用户等级修改成功';

  @override
  String get levelUpdateFailed => '修改等级失败';

  @override
  String get certifiedDistributor => '已认证分销员';

  @override
  String get fundsDetail => '资金详情';

  @override
  String get withdrawing => '提现中';

  @override
  String get withdrawn => '已提现';

  @override
  String get totalCommissionIncome => '分销佣金总收入';

  @override
  String get noPromotionPosters => '暂无推广海报';

  @override
  String get testDescription => '测试说明：';

  @override
  String get longPressToSelect => '长按下方的文本进行选择，复制的内容只包含汉字。';

  @override
  String get smartCopyVersion => '智能复制版本：';

  @override
  String get plainTextVersion => '纯文本版本：';

  @override
  String get smartCopyTest => '智能复制功能测试';

  @override
  String loadHistoryFailed(String error) {
    return '加载历史记录失败: $error';
  }

  @override
  String clearHistoryFailure(String error) {
    return '清空历史记录失败: $error';
  }

  @override
  String get cameraAccessFailure => '无法打开相机，请检查权限设置';

  @override
  String get instructions => '您的专属AI健康导游，随时为您提供健康咨询和导游服务';

  @override
  String get enterPhoneNumber => '请输入11位手机号';

  @override
  String get enterCorrectVerificationCode => '请输入正确的验证码';

  @override
  String get sendingVerificationCode => '正在发送验证码...';

  @override
  String get verificationCodeSentToPhone => '验证码已发送至您的手机';

  @override
  String get networkConnectionFailed => '网络连接失败，请稍后重试';

  @override
  String networkRequestFailed(String statusCode) {
    return '网络请求失败，状态码: $statusCode';
  }

  @override
  String sendVerificationCodeError(String error) {
    return '发送验证码出错: $error';
  }

  @override
  String get resettingPassword => '正在重置密码...';

  @override
  String processingUserDataError(String error) {
    return '处理用户数据时出错: $error';
  }

  @override
  String get loginSuccessButNoUserData => '登录成功但用户数据为空';

  @override
  String get userNotRegisteredRedirecting => '用户未注册，即将跳转到注册页面';

  @override
  String get historyDeleted => '历史记录已删除';

  @override
  String deleteHistoryFailed(String error) {
    return '删除历史记录失败: $error';
  }

  @override
  String get noCameraDetected => '未检测到可用相机';

  @override
  String cameraInitializationFailed(String error) {
    return '相机初始化失败: $error';
  }

  @override
  String get cameraNotReady => '相机未准备就绪';

  @override
  String capturePhotoFailed(String error) {
    return '拍照失败: $error';
  }

  @override
  String get galleryPermissionRequired => '需要相册权限才能选择图片';

  @override
  String selectImageFailed(String error) {
    return '选择图片失败: $error';
  }

  @override
  String get flashlightOperationFailed => '手电筒操作失败';

  @override
  String get switchCameraFailed => '切换摄像头失败';

  @override
  String languageNotSupportedAsSource(String language) {
    return '$language不支持作为源语言，无法切换';
  }

  @override
  String get enterUsername => '请输入用户名';

  @override
  String get passwordMinLength6 => '密码长度至少为6位';

  @override
  String get passwordsNotMatch => '两次输入的密码不一致';

  @override
  String get agreeToUserAgreement => '请同意用户协议和隐私政策';

  @override
  String get registering => '正在注册...';

  @override
  String get registerSuccess => '注册成功';

  @override
  String get phoneFormatIncorrect => '手机号格式不正确';

  @override
  String get verificationCodeExpired => '验证码错误或已过期';

  @override
  String get usernameAlreadyRegistered => '用户名已被注册，请更换用户名';

  @override
  String get phoneAlreadyRegistered => '该手机号已注册，可直接登录';

  @override
  String registerFailed(String message) {
    return '注册失败: $message';
  }

  @override
  String registerProcessError(String error) {
    return '注册过程中出错: $error';
  }

  @override
  String get openUserAgreement => '打开用户协议';

  @override
  String get openPrivacyPolicy => '打开隐私政策';

  @override
  String get priceInfoLoadingWait => '价格信息加载中，请稍候...';

  @override
  String get priceInfoLoadFailed => '获取价格信息失败，显示默认价格';

  @override
  String get recordingStartFailed => '录音启动失败，请检查麦克风权限';

  @override
  String get recordingStartError => '录音启动出错，请重试';

  @override
  String get recordingFailedRetry => '录音失败，请重试';

  @override
  String get audioProcessingFailedRetry => '音频处理失败，请重试';

  @override
  String voiceProcessingError(String error) {
    return '处理语音时出错：$error';
  }

  @override
  String playbackFailed(String error) {
    return '播放失败: $error';
  }

  @override
  String get microphoneRecordingPermissionRequired => '无法录音：需要麦克风权限';

  @override
  String get permissionGrantedRetryRecording => '权限已授予，请重新长按录音按钮开始录音';

  @override
  String logoutFailedError(String error) {
    return '退出登录失败: $error';
  }

  @override
  String aiTourGuideRecognitionResult(String text) {
    return '健康助手识别: $text';
  }

  @override
  String aiTourGuideVoiceRecognitionResult(String text) {
    return '健康助手语音识别: $text';
  }

  @override
  String get profileTitle => '我的';

  @override
  String get editProfileTitle => '编辑个人资料';

  @override
  String get healthInfo => '健康信息';

  @override
  String get heightHint => '请输入身高 (cm)';

  @override
  String get heightValidation => '请输入有效的身高 (50-250cm)';

  @override
  String get weightHint => '请输入体重 (kg)';

  @override
  String get weightValidation => '请输入有效的体重 (20-300kg)';

  @override
  String get selectBloodType => '请选择血型';

  @override
  String get bloodTypeA => 'A型';

  @override
  String get bloodTypeB => 'B型';

  @override
  String get bloodTypeAB => 'AB型';

  @override
  String get bloodTypeO => 'O型';

  @override
  String get bloodTypeUnknown => '不清楚';

  @override
  String get residentialAddress => '常住地址';

  @override
  String get locate => '定位';

  @override
  String get selectResidentialAddress => '请选择常住地址';

  @override
  String get regionSelectionFailed => '地区选择失败，请重试';

  @override
  String get commonAllergens => '常见过敏源';

  @override
  String get penicillinAllergy => '青霉素类药物';

  @override
  String get cephalosporinAllergy => '头孢类药物';

  @override
  String get aspirinAllergy => '阿司匹林';

  @override
  String get peanutAllergy => '花生';

  @override
  String get seafoodAllergy => '海鲜';

  @override
  String get milkAllergy => '牛奶';

  @override
  String get eggAllergy => '鸡蛋';

  @override
  String get pollenDustMiteAllergy => '花粉/尘螨';

  @override
  String get otherAllergens => '其他过敏物质';

  @override
  String get otherAllergensHint => '请补充其他过敏源';

  @override
  String get takingMedication => '目前是否在服用任何药物';

  @override
  String get medicationList => '药物清单';

  @override
  String get medicationListHint => '请列出正在服用的药物名称、剂量和频率';

  @override
  String get hasChronicDisease => '是否患有慢性疾病';

  @override
  String get specificSymptoms => '具体病症';

  @override
  String get hypertension => '高血压';

  @override
  String get bloodPressureHint => '例如 130/85 mmHg';

  @override
  String get diabetes => '糖尿病';

  @override
  String get bloodSugarHint => '例如 5.8 mmol/L';

  @override
  String get otherChronicDiseases => '其他慢性疾病';

  @override
  String get otherChronicDiseasesHint => '请补充其他慢性疾病';

  @override
  String get surgeryHistory => '手术与住院史';

  @override
  String get hasSurgeryHistory => '过去是否有过手术或住院经历';

  @override
  String get surgeryDetails => '详情说明';

  @override
  String get surgeryDetailsHint => '请描述手术或住院的详细情况';

  @override
  String get familyHistory => '家族病史';

  @override
  String get familyDiseaseHistory => '直系亲属（父母、兄弟姐妹、子女）是否有以下疾病';

  @override
  String get familyHypertension => '高血压';

  @override
  String get familyDiabetes => '糖尿病';

  @override
  String get familyHeartDisease => '心脏病';

  @override
  String get familyStroke => '中风';

  @override
  String get familyCancer => '癌症';

  @override
  String get familyMentalHealth => '精神健康疾病';

  @override
  String get otherFamilyHistory => '其他家族病史补充';

  @override
  String get otherFamilyHistoryHint => '请补充其他家族病史';

  @override
  String get exerciseSedentary => '久坐 (基本不运动)';

  @override
  String get exerciseLight => '轻度活跃 (每周运动1-2次)';

  @override
  String get exerciseModerate => '中度活跃 (每周运动3-5次)';

  @override
  String get exerciseActive => '非常活跃 (每周运动6次及以上)';

  @override
  String get dietaryPreferences => '日常饮食偏好';

  @override
  String get balancedDiet => '饮食均衡';

  @override
  String get vegetarianDiet => '偏素食';

  @override
  String get meatDiet => '偏肉食';

  @override
  String get oilyFood => '偏好油腻食物';

  @override
  String get saltyFood => '偏好咸味食物';

  @override
  String get sweetFood => '偏好甜食';

  @override
  String get neverSmoke => '从不吸烟';

  @override
  String get quitSmoking => '已戒烟';

  @override
  String get occasionalSmoking => '偶尔吸烟 (非每日)';

  @override
  String get dailySmoking => '经常吸烟 (每日)';

  @override
  String get neverDrink => '从不饮酒';

  @override
  String get quitDrinking => '已戒酒';

  @override
  String get socialDrinking => '偶尔社交性饮酒';

  @override
  String get weeklyDrinking => '每周1-3次';

  @override
  String get dailyDrinking => '几乎每天';

  @override
  String get sleepLessThan6 => '少于6小时';

  @override
  String get sleep6To7 => '6-7小时';

  @override
  String get sleep7To8 => '7-8小时';

  @override
  String get sleepMoreThan8 => '8小时以上';

  @override
  String get sleepGood => '良好 (容易入睡，很少惊醒)';

  @override
  String get sleepFair => '一般 (偶有入睡困难或早醒)';

  @override
  String get sleepPoor => '较差 (长期入睡困难、多梦、早醒)';

  @override
  String get stressLow => '很小';

  @override
  String get stressMild => '稍有压力';

  @override
  String get stressModerate => '压力适中';

  @override
  String get stressHigh => '压力较大';

  @override
  String get stressExtreme => '压力极大';

  @override
  String get womenHealth => '女性健康';

  @override
  String get isMenopause => '是否已绝经';

  @override
  String get menstrualCycleRegular => '月经周期是否规律';

  @override
  String get menstrualRegular => '规律';

  @override
  String get menstrualIrregular => '不规律';

  @override
  String get menstrualUncertain => '不确定';

  @override
  String get hasPregnancy => '是否曾怀孕';

  @override
  String get birthCount => '生育次数';

  @override
  String get birthCount0 => '0次';

  @override
  String get birthCount1 => '1次';

  @override
  String get birthCount2 => '2次';

  @override
  String get birthCount3 => '3次';

  @override
  String get birthCount4 => '4次';

  @override
  String get birthCount5Plus => '5次及以上';

  @override
  String get cannotParseImage => '无法解析图片文件';

  @override
  String get vipMemberBadge => 'VIP会员';

  @override
  String get normalUserBadge => '普通用户';

  @override
  String distributorLevelBadge(String level) {
    return '分销员 Lv.$level';
  }

  @override
  String get distributorBadge => '分销员';

  @override
  String get arabic => '阿拉伯语';

  @override
  String get english => '英语';

  @override
  String languageSwitchNotSupported(String language) {
    return '$language不支持作为源语言，无法切换';
  }

  @override
  String get sourceLanguageLabel => '源';

  @override
  String get targetLanguageLabel => '目标';

  @override
  String get currentBalance => '当前余额';

  @override
  String get todayIncome => '今日收入';

  @override
  String get totalIncome => '总收入';

  @override
  String get holdMicrophoneToSpeak => '按住麦克风开始说话';

  @override
  String get waitingForOtherParty => '等待对方说话';

  @override
  String get confirmClear => '确认清空';

  @override
  String get confirmClearAllChatRecords => '确定要清空所有聊天记录吗？此操作无法撤销。';

  @override
  String get confirmClearAction => '确定清空';

  @override
  String get registerTitle => '注册用户';

  @override
  String get usernameHint => '请输入用户名';

  @override
  String get setPassword => '设置密码';

  @override
  String get confirmPassword => '重输密码';

  @override
  String get getCodeButton => '获取验证码';

  @override
  String countdownSeconds(int count) {
    return '$count秒';
  }

  @override
  String get register => '注册';

  @override
  String get phoneNumberLogin => '手机号登录';

  @override
  String get userAgreementAndPrivacyPolicy => '《用户协议》和《隐私政策》';

  @override
  String get iAgreeToThe => '我已阅读并同意';

  @override
  String get helpFeedbackTitle => '帮助与反馈';

  @override
  String get yourNameOptional => '您的称呼 (可选)';

  @override
  String get yourPhoneNumber => '您的手机号';

  @override
  String get describeProblemDetail => '请详细描述您的问题或建议';

  @override
  String get submitFeedback => '提交反馈';

  @override
  String get pleaseEnterCorrectPhoneFormat => '请输入正确的手机号格式';

  @override
  String get pleaseDescribeProblem => '请描述您遇到的问题';

  @override
  String get descriptionMinLength => '问题描述至少需要10个字符';

  @override
  String get descriptionMaxLength => '问题描述不能超过1000个字符';

  @override
  String get submittingFeedback => '正在提交反馈...';

  @override
  String get feedbackSubmittedSuccess => '反馈提交成功，感谢您的建议！';

  @override
  String feedbackSubmissionFailed(String error) {
    return '反馈提交失败: $error';
  }

  @override
  String get feedbackInstructions => '反馈说明';

  @override
  String get feedbackInstructionsText =>
      '我们会收集您的问题描述和相关应用数据（不包含敏感信息）以便更好地解决您的问题。提交后我们将通过您提供的手机号与您联系。';

  @override
  String get enterYourName => '请输入您的称呼';

  @override
  String get problemDescriptionHint =>
      '请详细描述您遇到的问题或建议\n包括：\n• 具体的操作步骤\n• 期望的结果\n• 实际发生的情况\n• 其他相关信息';

  @override
  String get submitting => '提交中...';

  @override
  String get testLogGeneration => '测试日志生成';

  @override
  String get viewErrorLogs => '查看错误日志';

  @override
  String get generateTestErrors => '生成测试错误';

  @override
  String get privacyNotice =>
      '提示：您的隐私对我们很重要，我们不会收集密码等敏感信息，仅收集必要的应用配置和日志数据以帮助解决问题。';

  @override
  String get logGenerationSuccess => '日志生成成功';

  @override
  String logSize(String size) {
    return '日志大小: ${size}KB';
  }

  @override
  String get testErrorLogsGenerated => '已生成测试错误日志，可以在错误日志查看器中查看';

  @override
  String get feedbackSubmittedSuccessfully => '反馈提交成功，我们会尽快处理您的问题';

  @override
  String get submissionFailed => '提交失败';

  @override
  String get submissionFailedCheckNetwork => '提交失败，请检查网络连接';

  @override
  String logGenerationFailed(String error) {
    return '日志生成失败: $error';
  }

  @override
  String get phoneNumberForContact => '(用于问题回访)';

  @override
  String get nickname => '昵称';

  @override
  String get nicknameRequired => '昵称不能为空';

  @override
  String get nicknameMinLength => '昵称至少需要2个字符';

  @override
  String get changePassword => '修改密码';

  @override
  String get changePasswordDescription => '请设置您的新密码，确保密码安全性。';

  @override
  String get passwordRequirements => '密码要求';

  @override
  String get passwordLengthRequirement => '密码长度为6-20位字符';

  @override
  String get accountSettings => '账户设置';

  @override
  String get changePasswordSubtitle => '修改您的登录密码';

  @override
  String get newPasswordRequired => '请输入新密码';

  @override
  String get confirmNewPassword => '确认新密码';

  @override
  String get confirmNewPasswordRequired => '请确认新密码';

  @override
  String get changeAvatar => '更换头像';

  @override
  String get uploading => '上传中...';

  @override
  String get selectAvatar => '选择头像';

  @override
  String get takePhoto => '拍照';

  @override
  String get selectFromGallery => '从相册选择';

  @override
  String get avatarUploadSuccess => '头像上传成功';

  @override
  String get avatarUploadFailed => '头像上传失败';

  @override
  String get gender => '性别';

  @override
  String get notSet => '未设置';

  @override
  String get birthday => '生日';

  @override
  String get profileSaveSuccess => '资料保存成功';

  @override
  String get saveFailed => '保存失败';

  @override
  String get passwordChangeSuccess => '密码修改成功';

  @override
  String get cropAvatar => '裁剪头像';

  @override
  String get reselectImage => '重新选择';

  @override
  String get confirmCrop => '确认';

  @override
  String get cannotParseImageFile => '无法解析图片文件';

  @override
  String get loadUserProfileFailed => '加载用户资料失败';

  @override
  String takePhotoFailed(String error) {
    return '拍照失败: $error';
  }

  @override
  String get avatarUploadFailedButProfileWillSave => '头像上传失败，但其他资料将继续保存';

  @override
  String get loginExpiredPleaseRelogin => '登录已过期，请重新登录';

  @override
  String processImageFailed(String error) {
    return '处理图片失败: $error';
  }

  @override
  String get newPasswordMaxLength => '新密码不能超过20位';

  @override
  String get userCardMemberStatusLabel => '会员状态';

  @override
  String get userCardExpiryDateLabel => '到期时间';

  @override
  String get userCardUidLabel => 'UID:';

  @override
  String get languageOptionChineseSimplified => '中文';

  @override
  String get languageOptionUyghur => '维吾尔语';

  @override
  String get languageOptionEnglish => '英语';

  @override
  String get languageOptionKazakh => '哈萨克语';

  @override
  String get languageOptionRussian => '俄语';

  @override
  String get languageOptionFrench => '法语';

  @override
  String get languageOptionSpanish => '西班牙语';

  @override
  String get languageOptionCantonese => '粤语';

  @override
  String get languageOptionArabic => '阿拉伯语';

  @override
  String get historyListEmpty => '暂无历史记录';

  @override
  String get aiGuideVoiceQueryButton => '语音提问';

  @override
  String get aiGuidePhotoQueryButton => '拍照提问';

  @override
  String get aiGuideQueryHint => '欢迎使用健康助手，请按住下方按钮拍照提问';

  @override
  String get faceToFaceSelectLanguagesHint => '请选择双方语言';

  @override
  String resendCodeTimerLabel(String seconds) {
    return '重新发送($seconds秒)';
  }

  @override
  String get guestUser => '游客用户';

  @override
  String get pleaseLogin => '请先登录';

  @override
  String get membershipStatus => '会员状态';

  @override
  String get expiresOn => '到期时间';

  @override
  String get editProfileButton => '编辑资料';

  @override
  String get notLoggedInUser => '未登录用户';

  @override
  String get verificationCodeLoginTitle => '验证码登录';

  @override
  String get phoneInputLabel => '手机号';

  @override
  String get phoneInputHint => '请输入手机号';

  @override
  String get codeInputLabel => '验证码';

  @override
  String get codeInputHint => '请输入验证码';

  @override
  String resendCodeTimerButton(String seconds) {
    return '重新发送($seconds秒)';
  }

  @override
  String get loginButton => '登录';

  @override
  String get autoRegisterHint => '未注册手机号将自动注册';

  @override
  String get reminderTitle => '提醒';

  @override
  String get loginRequiredForDistributionMessage => '请先登录再使用分销管理功能';

  @override
  String get distributionAccessDeniedMessage => '账户没有分销员权限，你可以申请成为分销员';

  @override
  String get goToLoginButton => '去登录';

  @override
  String get applyButton => '申请';

  @override
  String get typeMessageHint => '请输入消息';

  @override
  String verificationCodeSentSeconds(String seconds) {
    return '$seconds秒';
  }

  @override
  String get welcomeBackTitle => '欢迎回来';

  @override
  String get loginWithPhoneSubtitle => '请使用您的手机号登录账户';

  @override
  String get registerAccountButton => '注册账号';

  @override
  String get passwordLoginButton => '密码登录';

  @override
  String get loginAgreementText => '登录即表示您同意《用户协议》和《隐私政策》';

  @override
  String get loginFailed => '登录失败';

  @override
  String loginRequiredForFeature(String featureName) {
    return '请先登录再使用$featureName功能';
  }

  @override
  String get loginRequiredGeneral => '此功能需要登录后才能使用，请先登录';

  @override
  String get loginButtonText => '登录';

  @override
  String get applicationSubmitted => '申请已提交，请等待审核';

  @override
  String get applicationFailed => '申请失败';

  @override
  String get distributorBenefitDescription => '成为分销员后，您可以推广产品并获得佣金收益';

  @override
  String get aiGuidePhotoQuestion => '健康助手拍照提问';

  @override
  String get aiGuideVoiceQuestion => '健康助手语音提问';

  @override
  String get recordingStartFailedCheckPermission => '录音启动失败，请检查麦克风权限';

  @override
  String get adjustFontSize => '调整字体大小';

  @override
  String get fontPreviewText => '字体预览 Font Preview';

  @override
  String get smallSize => '小';

  @override
  String get largeSize => '大';

  @override
  String currentSizeLabel(String size) {
    return '当前大小: $size';
  }

  @override
  String get smallSizeLabel => '小';

  @override
  String get mediumSizeLabel => '中';

  @override
  String get largeSizeLabel => '大';

  @override
  String get vipMembershipTitle => 'VIP会员特权';

  @override
  String get higherAccuracy => '准确度更高';

  @override
  String get adFree => '无广告干扰';

  @override
  String get unlimitedUsage => '无限次使用';

  @override
  String get monthlyMembership => '月度会员';

  @override
  String get annualMembership => '年度会员';

  @override
  String savePercentage(String percentage) {
    return '省$percentage%';
  }

  @override
  String get gettingPriceInfo => '正在获取价格信息...';

  @override
  String get originalPrice => '原价';

  @override
  String get approximately => '约';

  @override
  String get monthlyUnit => '月';

  @override
  String get yearlyUnit => '年';

  @override
  String get perMonth => '/月';

  @override
  String get perYear => '/年';

  @override
  String get activateVipNow => '立即开通VIP会员';

  @override
  String get serviceTermsAgreement => '开通即表示同意《用户服务协议》和《隐私政策》';

  @override
  String get monthlyMemberPackage => '包月会员';

  @override
  String get annualMemberPackage => '包年会员';

  @override
  String get oneMonthVipPrivileges => '一个月VIP特权';

  @override
  String get oneYearVipPrivileges => '一年VIP特权，支持自动续费';

  @override
  String get priceLoadFailed => '获取价格信息失败，显示默认价格';

  @override
  String get priceInfoLoading => '价格信息加载中，请稍候...';

  @override
  String aboutToActivate(String packageName) {
    return '即将开通$packageName，请稍候...';
  }

  @override
  String get annualVipMember => '年度VIP会员';

  @override
  String get monthlyVipMember => '月度VIP会员';

  @override
  String get lifetimeVipMember => '终身VIP会员';

  @override
  String get fontSizeSmall => '小';

  @override
  String get fontSizeMedium => '中';

  @override
  String get fontSizeLarge => '大';

  @override
  String get myOrders => '我的订单';

  @override
  String get chatHistory => '聊天历史';

  @override
  String get doctorManagement => '医生管理';

  @override
  String get adminManagement => '管理员';

  @override
  String get myLikes => '我的点赞';

  @override
  String get myFavorites => '我的收藏';

  @override
  String get shoppingCart => '购物车';

  @override
  String get distributionManagementFeature => '分销管理';

  @override
  String get adminManagementFeature => '管理员管理';

  @override
  String get doctorManagementFeature => '医生管理';

  @override
  String get onlyDoctorUsersCanAccess => '只有医生用户才能访问医生管理功能';

  @override
  String get viewShoppingCartFeature => '查看购物车';

  @override
  String get pleaseSelectItemsToDelete => '请选择要删除的商品';

  @override
  String get confirmDelete => '确认删除';

  @override
  String confirmDeleteItems(int count) {
    return '确定要删除选中的 $count 个商品吗？';
  }

  @override
  String get deleteSuccess => '删除成功';

  @override
  String deleteFailed(String error) {
    return '删除失败: $error';
  }

  @override
  String get pleaseSelectItemsToCheckout => '请选择要结算的商品';

  @override
  String get cartTitle => '购物车';

  @override
  String get myOrdersTitle => '我的订单';

  @override
  String get myOrdersFeature => '我的订单';

  @override
  String get orderStatusAll => '全部';

  @override
  String get orderStatusPending => '待支付';

  @override
  String get orderStatusPendingShipment => '待发货';

  @override
  String get orderStatusShipped => '已发货';

  @override
  String get orderStatusCompleted => '已完成';

  @override
  String get orderStatusCancelled => '已取消';

  @override
  String get orderStatusUnknown => '未知状态';

  @override
  String get payStatusUnpaid => '未支付';

  @override
  String get payStatusPaid => '已支付';

  @override
  String get payStatusRefunded => '已退款';

  @override
  String get product => '商品';

  @override
  String get noOrders => '暂无订单';

  @override
  String get adminManagementTitle => '管理员管理';

  @override
  String get doctorManagementTab => '医生管理';

  @override
  String get productReviewTab => '产品审核';

  @override
  String get orderManagementTab => '订单管理';

  @override
  String get noDoctorData => '暂无医生数据';

  @override
  String get loadDoctorListFailed => '加载医生列表失败';

  @override
  String get addDoctor => '添加医生';

  @override
  String get editDoctor => '编辑医生';

  @override
  String get deleteDoctor => '删除医生';

  @override
  String get confirmDeleteDoctor => '确认删除医生';

  @override
  String deleteDoctorConfirmMessage(String doctorName) {
    return '确定要删除医生 $doctorName 吗？此操作不可撤销。';
  }

  @override
  String get deleteDoctorSuccess => '删除医生成功';

  @override
  String get deleteDoctorFailed => '删除医生失败';

  @override
  String get detailedInfo => '详细信息';

  @override
  String get aiSettings => 'AI设置';

  @override
  String get statusSettings => '状态设置';

  @override
  String get enterDoctorName => '请输入医生姓名';

  @override
  String get specialty => '专科';

  @override
  String get enterSpecialty => '请输入专科名称，如：心血管内科';

  @override
  String get description => '简介';

  @override
  String get enterDescription => '请输入医生简介';

  @override
  String get enterDetailedDescription => '请输入产品的详细描述，包括成分、功效、使用方法等';

  @override
  String get addSpecialty => '添加擅长领域';

  @override
  String get enterSpecialtyField => '请输入擅长领域，如：冠心病';

  @override
  String get deleteSpecialty => '删除此擅长领域';

  @override
  String get systemPrompt => '系统提示词';

  @override
  String get enterSystemPrompt => '请输入AI系统提示词，定义AI医生的行为和回答风格';

  @override
  String get avatarUrl => '头像URL';

  @override
  String get enterAvatarUrl => '请输入头像图片URL或点击上方上传头像';

  @override
  String get llmModelName => '模型名称';

  @override
  String get enterLlmModelName =>
      '请输入LLM模型名称，如：qwen-max-latest、claude-3-sonnet等';

  @override
  String get enterYearsOfExperience => '请输入工作年限（年）';

  @override
  String get rating => '评分';

  @override
  String get enterRating => '请输入评分（0.0-5.0）';

  @override
  String get digitalHumanUrl => '数字人URL';

  @override
  String get enterDigitalHumanUrl => '请输入数字人URL（可选）';

  @override
  String get phone => '联系电话';

  @override
  String get enterPhone => '请输入医生联系电话';

  @override
  String get enterAddress => '请输入医生工作地址';

  @override
  String get isActive => '启用状态';

  @override
  String get saveDoctor => '保存医生';

  @override
  String get saving => '保存中...';

  @override
  String get createDoctorSuccess => '创建医生成功';

  @override
  String get updateDoctorSuccess => '更新医生成功';

  @override
  String get createDoctorFailed => '创建医生失败';

  @override
  String get updateDoctorFailed => '更新医生失败';

  @override
  String get enabled => '启用';

  @override
  String get disabled => '禁用';

  @override
  String get enterValidPhone => '请输入有效的手机号码';

  @override
  String get doctorAvatar => '医生头像';

  @override
  String get uploadAvatar => '上传头像';

  @override
  String get enableStatus => '启用状态';

  @override
  String get doctorEnabledDescription => '医生当前处于启用状态，用户可以与其对话';

  @override
  String get doctorDisabledDescription => '医生当前处于禁用状态，用户无法与其对话';

  @override
  String get specialtyInputHint => '提示：每个输入框填写一个擅长领域，保存时将自动合并';

  @override
  String get confirmExit => '确认退出';

  @override
  String get unsavedChangesWarning => '您有未保存的更改，确定要退出吗？';

  @override
  String get exit => '退出';

  @override
  String get uploadFailed => '上传失败';

  @override
  String get supportedImageFormats => '支持 JPG、PNG 格式，大小不超过 5MB';

  @override
  String get collapse => '收起';

  @override
  String get multilingual => '多语言';

  @override
  String get all => '全部';

  @override
  String get pending => '待审核';

  @override
  String get approved => '已通过';

  @override
  String get rejected => '已拒绝';

  @override
  String get offline => '已下架';

  @override
  String loadDataFailed(String error) {
    return '加载数据失败: $error';
  }

  @override
  String get loadDoctorMultilingualDataFailed => '加载医生多语言数据失败';

  @override
  String get doctorCreationCompleteCallback => '医生创建完成回调';

  @override
  String get enterModelName => '请输入模型名称';

  @override
  String get imageSizeExceedsLimit => '图片大小不能超过5MB';

  @override
  String get doctorManagementTitle => '医生管理';

  @override
  String get productManagementTab => '产品管理';

  @override
  String get dataOverview => '数据概览';

  @override
  String get products => '产品';

  @override
  String get pendingReview => '待审核';

  @override
  String get totalProducts => '总产品';

  @override
  String get totalSales => '总销售额';

  @override
  String get totalOrders => '总订单';

  @override
  String get approvedProducts => '已通过';

  @override
  String get rejectedProducts => '已拒绝';

  @override
  String get reviewStatus => '审核状态';

  @override
  String get inventory => '库存';

  @override
  String get salesVolume => '销量';

  @override
  String get orderOverview => '订单概览';

  @override
  String get shippingStatus => '物流状态';

  @override
  String get paymentStatus => '支付状态';

  @override
  String get totalOrderNumber => '总计订单号';

  @override
  String get customer => '客户';

  @override
  String get pendingPayment => '待支付';

  @override
  String get pendingShipment => '待发货';

  @override
  String get shipped => '已发货';

  @override
  String get completed => '已完成';

  @override
  String get cancelled => '已取消';

  @override
  String get reviewApproved => '审核通过';

  @override
  String get reviewRejected => '审核拒绝';

  @override
  String get offShelf => '下架';

  @override
  String get addProduct => '添加产品';

  @override
  String get editProduct => '编辑产品';

  @override
  String get enterProductName => '请输入产品名称';

  @override
  String get productDescription => '产品描述';

  @override
  String get enterProductDescription => '请输入产品简介';

  @override
  String get productCategory => '产品分类';

  @override
  String get enterProductCategory => '请输入产品分类，如：保健品、药物等';

  @override
  String get enterManufacturer => '请输入制造商名称';

  @override
  String get productMainImage => '产品主图';

  @override
  String get priceInfo => '价格信息';

  @override
  String get currentPrice => '现价';

  @override
  String get enterCurrentPrice => '请输入现价';

  @override
  String get enterOriginalPrice => '请输入原价（可选）';

  @override
  String get inventoryInfo => '库存信息';

  @override
  String get inventoryCount => '库存数量';

  @override
  String get enterInventoryCount => '请输入库存数量';

  @override
  String get productDetailImages => '产品详情图';

  @override
  String get productCreatedSuccess => '产品创建成功';

  @override
  String get productUpdatedSuccess => '产品更新成功';

  @override
  String get loadProductMultilingualDataFailed => '加载产品多语言数据失败';

  @override
  String get orderNumber => '订单号';

  @override
  String get orderTotal => '总计';

  @override
  String customerLabel(String customer) {
    return '客户: $customer';
  }

  @override
  String get orderNumberShort => '订单号';

  @override
  String get paidStatus => '已支付';

  @override
  String get unpaidStatus => '未支付';

  @override
  String get trackingNumber => '快递单号';

  @override
  String get networkImage => '网络图片';

  @override
  String get maxSixImages => '最多6张';

  @override
  String get addImage => '添加图片';

  @override
  String get expressInfo => '快递信息';

  @override
  String get expressNumber => '快递单号';

  @override
  String get expressCompany => '快递公司';

  @override
  String get shippingNote => '发货备注';

  @override
  String get noShippingInfo => '暂无物流信息';

  @override
  String orderCount(int count) {
    return '$count单';
  }

  @override
  String get imageSelected => '已选择图片';

  @override
  String get unsavedChangesMessage => '您有未保存的更改，确定要退出吗？';

  @override
  String get confirmAction => '确定';

  @override
  String get noProductsMessage => '暂无产品';

  @override
  String get addFirstProductHint => '点击右下角按钮添加您的第一个产品';

  @override
  String get noOrdersMessage => '订单数据会显示在这里';

  @override
  String get ordersWillShowHere => '客户购买您的产品后订单会显示在这里';

  @override
  String get reviewSuccess => '审核成功';

  @override
  String get reviewFailed => '审核失败';

  @override
  String get batchReviewSuccess => '批量审核成功';

  @override
  String get batchReviewFailed => '批量审核失败';

  @override
  String get allDoctors => '全部医生';

  @override
  String get pleaseSelectProducts => '请选择要审核的产品';

  @override
  String get batchApproved => '批量审核通过';

  @override
  String get batchRejected => '批量审核拒绝';

  @override
  String get batchApprove => '批量通过';

  @override
  String get batchReject => '批量拒绝';

  @override
  String get deselectAll => '取消全选';

  @override
  String get exitSelection => '退出选择';

  @override
  String get batchSelection => '批量选择';

  @override
  String get reviewStatistics => '审核统计';

  @override
  String get total => '总数';

  @override
  String get sales => '销量';

  @override
  String get approve => '通过';

  @override
  String get reject => '拒绝';

  @override
  String get rejectReview => '拒绝审核';

  @override
  String confirmRejectProduct(String productName) {
    return '确定要拒绝产品 \"$productName\" 吗？';
  }

  @override
  String get rejectReason => '拒绝原因（可选）';

  @override
  String get confirmReject => '确定拒绝';

  @override
  String get filterDoctors => '筛选医生';

  @override
  String get loadProductDetailFailed => '加载产品详情失败';

  @override
  String get unknownDoctor => '未知医生';

  @override
  String get createdAt => '创建时间';

  @override
  String get updatedAt => '更新时间';

  @override
  String get productImages => '产品图片';

  @override
  String get productSpecifications => '产品规格';

  @override
  String get reviewInfo => '审核信息';

  @override
  String get productId => '产品ID';

  @override
  String get viewShipping => '查看物流';

  @override
  String get cancelOrder => '取消订单';

  @override
  String get payNow => '立即支付';

  @override
  String get confirmCancel => '确认取消';

  @override
  String get confirmCancelOrder => '确定要取消这个订单吗？';

  @override
  String get orderCancelled => '订单已取消';

  @override
  String cancelOrderFailed(String error) {
    return '取消订单失败：$error';
  }

  @override
  String get getPaymentParamsFailed => '获取支付参数失败';

  @override
  String get paymentCancelled => '支付已取消';

  @override
  String get confirmPayment => '确认支付';

  @override
  String get paymentAmount => '支付金额';

  @override
  String get confirmPaymentButton => '确认支付';

  @override
  String get orderPaidSuccessfully => '您的订单已支付成功';

  @override
  String get currentConversationDeleted => '当前对话已被删除';

  @override
  String get newConversation => '新对话';

  @override
  String get refreshSuccess => '刷新成功';

  @override
  String refreshFailed(String error) {
    return '刷新失败: $error';
  }

  @override
  String get titleUpdateSuccess => '标题更新成功';

  @override
  String titleUpdateFailed(String error) {
    return '标题更新失败: $error';
  }

  @override
  String get conversationNotFound => '对话不存在';

  @override
  String get chatHistoryPageTitle => '聊天历史';

  @override
  String get noChatRecordsForDate => '该日期暂无聊天记录';

  @override
  String get enterNewTitle => '请输入新的标题';

  @override
  String get year => '年';

  @override
  String get month => '月';

  @override
  String get monthLabel => '月份：';

  @override
  String get yearLabel => '年份：';

  @override
  String get weekdayMon => '一';

  @override
  String get weekdayTue => '二';

  @override
  String get weekdayWed => '三';

  @override
  String get weekdayThu => '四';

  @override
  String get weekdayFri => '五';

  @override
  String get weekdaySat => '六';

  @override
  String get weekdaySun => '日';

  @override
  String get selectYearMonth => '选择年月';

  @override
  String get ok => '确定';

  @override
  String get digitalHumanChatInDevelopment => '数字人AI聊天页面开发中...';

  @override
  String get voiceTranslationFeature => '语音翻译';

  @override
  String get chatFeature => '聊天功能';

  @override
  String get voiceFeature => '语音功能';

  @override
  String get chatHistoryFeature => '聊天历史';

  @override
  String get voiceRecognitionSuccess => '语音识别成功';

  @override
  String get voiceRecognitionFailed => '语音识别失败，请重试';

  @override
  String get addTextDescriptionOrSendImage => '添加文字描述或直接发送图片';

  @override
  String get refresh => '刷新';

  @override
  String get noChatRecords => '暂无聊天记录';

  @override
  String get filterDoctorsLabel => '筛选医生';

  @override
  String get allDoctorsOption => '全部医生';

  @override
  String get unknownDoctorLabel => '未知医生';

  @override
  String quantityLabel(int quantity) {
    return '数量: $quantity';
  }

  @override
  String doctorLabel(String doctor) {
    return '医生: $doctor';
  }

  @override
  String unitPriceLabel(String price) {
    return '单价: ¥$price';
  }

  @override
  String totalAmountLabel(String amount) {
    return '总计: ¥$amount';
  }

  @override
  String orderNumberLabel(String orderNumber) {
    return '订单号: $orderNumber';
  }

  @override
  String get adminShipAction => '管理员发货';

  @override
  String get markCompleteAction => '标记完成';

  @override
  String get markCancelAction => '标记取消';

  @override
  String get deleteOrderAction => '删除订单';

  @override
  String get totalOrdersLabel => '总订单';

  @override
  String get totalSalesLabel => '总销售额';

  @override
  String get completedOrdersLabel => '已完成';

  @override
  String get pendingPaymentLabel => '待支付';

  @override
  String get pendingShipmentLabel => '待发货';

  @override
  String get cancelledOrdersLabel => '已取消';

  @override
  String get ordersUnit => '单';

  @override
  String get orderStatusUpdateSuccess => '订单状态更新成功';

  @override
  String updateFailed(String error) {
    return '更新失败: $error';
  }

  @override
  String get noOrdersTitle => '暂无订单';

  @override
  String get batchOperationSuccess => '批量操作成功';

  @override
  String batchOperationFailed(String error) {
    return '批量操作失败: $error';
  }

  @override
  String get confirmDeleteTitle => '确认删除';

  @override
  String get confirmDeleteMessage => '确定要删除这个订单吗？此操作不可撤销。';

  @override
  String get cancelAction => '取消';

  @override
  String get deleteAction => '删除';

  @override
  String batchOperationsTitle(int count) {
    return '批量操作 ($count个订单)';
  }

  @override
  String get markAsCompletedAction => '标记为已完成';

  @override
  String get markAsCancelledAction => '标记为已取消';

  @override
  String get markAsShippedAction => '标记为已发货';

  @override
  String get ordersUnitSuffix => '单';

  @override
  String get orderStatusUpdateSuccessMessage => '订单状态更新成功';

  @override
  String get paymentStatusUpdateSuccessMessage => '支付状态更新成功';

  @override
  String get orderDeleteSuccessMessage => '订单删除成功';

  @override
  String get pleaseSelectOrdersMessage => '请先选择要操作的订单';

  @override
  String get markAsPaidAction => '标记已支付';

  @override
  String get orderStatusPendingPayment => '待支付';

  @override
  String get orderStatusPaid => '已支付';

  @override
  String get orderDetailTitle => '订单详情';

  @override
  String get markAsRefundAction => '标记退款';

  @override
  String get markAsCompleteAction => '标记完成';

  @override
  String get markAsCancelAction => '标记取消';

  @override
  String get waitingForPaymentDescription => '等待客户完成支付';

  @override
  String get waitingForShipmentDescription => '等待医生发货';

  @override
  String get shippedDescription => '商品已发货，等待客户收货';

  @override
  String get orderCompletedDescription => '订单已完成';

  @override
  String get orderCancelledDescription => '订单已取消';

  @override
  String get orderStatusAbnormalDescription => '订单状态异常';

  @override
  String get orderStatusPendingDescription => '请尽快完成支付，超时订单将自动取消';

  @override
  String get orderStatusPreparingDescription => '您的订单正在准备中，请耐心等待';

  @override
  String get orderStatusShippedUserDescription => '商品已发货，请注意查收';

  @override
  String get orderStatusCompletedUserDescription => '订单已完成，感谢您的购买';

  @override
  String get orderStatusCancelledUserDescription => '订单已取消';

  @override
  String get shippingStatusWaitingReceive => '已发货，等待收货';

  @override
  String get shippingStatusCompleted => '已完成';

  @override
  String get shippingStatusShipped => '已发货';

  @override
  String get shippingStatusPending => '待支付';

  @override
  String get shippingStatusWaitingShip => '待发货';

  @override
  String get shippingStatusCancelled => '已取消';

  @override
  String get shippingStatusUnknown => '未知状态';

  @override
  String get insufficientPermissionDoctorRequired => '权限不足，需要医生权限';

  @override
  String get getPendingShipmentOrdersFailed => '获取待发货订单失败';

  @override
  String get trackingNumberCannotBeEmpty => '快递单号不能为空';

  @override
  String get shipmentFailed => '发货失败';

  @override
  String get orderNotExistOrNoAccess => '订单不存在或无权访问';

  @override
  String get shipmentFailedCheckOrderStatus => '发货失败，请检查订单状态';

  @override
  String get getShippingStatusFailed => '获取物流状态失败';

  @override
  String get getShippedOrdersFailed => '获取已发货订单失败';

  @override
  String get productInfoTitle => '商品信息';

  @override
  String get orderInfoTitle => '订单信息';

  @override
  String get orderNumberFieldLabel => '订单号';

  @override
  String get orderTimeLabel => '下单时间';

  @override
  String get paymentTimeLabel => '支付时间';

  @override
  String get shipmentTimeLabel => '发货时间';

  @override
  String get completionTimeLabel => '完成时间';

  @override
  String get customerInfoTitle => '客户信息';

  @override
  String get customerNicknameLabel => '客户昵称';

  @override
  String get userIdLabel => '用户ID';

  @override
  String get shippingInfoTitle => '收货信息';

  @override
  String get recipientLabel => '收货人';

  @override
  String get contactPhoneLabel => '联系电话';

  @override
  String get shippingAddressLabel => '收货地址';

  @override
  String get trackingInfoTitle => '物流信息';

  @override
  String get viewDetailsAction => '查看详情';

  @override
  String get trackingNumberLabel => '快递单号';

  @override
  String get shippingCompanyLabel => '快递公司';

  @override
  String get shippingNoteLabel => '发货备注';

  @override
  String get priceDetailsTitle => '费用明细';

  @override
  String get productAmountLabel => '商品金额';

  @override
  String get shippingFeeLabel => '运费';

  @override
  String get totalPaidLabel => '实付金额';

  @override
  String get cancelOrderAction => '取消订单';

  @override
  String get viewTrackingAction => '查看物流';

  @override
  String get copiedToClipboardMessage => '已复制到剪贴板';

  @override
  String get confirmDeleteOrderTitle => '确认删除';

  @override
  String get confirmDeleteOrderMessage => '确定要删除这个订单吗？此操作不可撤销。';

  @override
  String get orderDetailLoadFailedMessage => '加载订单详情失败';

  @override
  String get orderInfoLoadFailedMessage => '订单信息加载失败';

  @override
  String updateFailedMessage(String error) {
    return '更新失败: $error';
  }

  @override
  String deleteFailedMessage(String error) {
    return '删除失败: $error';
  }

  @override
  String get editConversationTitle => '编辑对话标题';

  @override
  String get replying => '回复中...';

  @override
  String get imageLoadFailed => '图片加载失败';

  @override
  String get imageNotAvailable => '图片不可用';

  @override
  String get releaseToCancel => '松开取消录音';

  @override
  String get recording => '正在录音';

  @override
  String get slideUpToCancel => '上滑取消';

  @override
  String get continueSlideUpToCancel => '继续向上滑动取消录音';

  @override
  String get takePhotoAndSend => '拍照发送';

  @override
  String get selectSendArea => '选择发送区域';

  @override
  String get send => '发送';

  @override
  String get orderTimeline => '订单时间轴';

  @override
  String get orderCreated => '订单创建';

  @override
  String get paymentCompleted => '支付完成';

  @override
  String get goodsShipped => '商品发货';

  @override
  String get orderCompleted => '订单完成';

  @override
  String get shipOrder => '订单发货';

  @override
  String get confirmShip => '确认发货';

  @override
  String get ship => '发货';

  @override
  String get enterTrackingNumber => '请输入快递单号';

  @override
  String get trackingNumberRequired => '请输入快递单号';

  @override
  String get selectShippingCompany => '请选择快递公司（可选）';

  @override
  String get enterShippingNote => '请输入发货备注（可选）';

  @override
  String get shipSuccess => '发货成功';

  @override
  String shipFailed(String error) {
    return '发货失败: $error';
  }

  @override
  String get productLabel => '产品';

  @override
  String get adminShipment => '管理员发货';

  @override
  String get trackingNumberRequiredField => '快递单号 *';

  @override
  String get shippingCompanyHint => '如：顺丰速运、圆通快递等';

  @override
  String get shippingNoteHint => '可填写发货说明或注意事项';

  @override
  String get shipmentSuccess => '发货成功';

  @override
  String shipmentFailedWithError(String error) {
    return '发货失败: $error';
  }

  @override
  String copiedToClipboardWithTitle(String title) {
    return '$title已复制到剪贴板';
  }

  @override
  String get voiceRecognizing => '正在识别语音...';

  @override
  String get voiceRecognitionRetry => '语音识别失败，请重试';

  @override
  String get cannotOpenPhoneAppCopied => '无法打开电话应用，号码已复制到剪贴板';

  @override
  String operationFailedManualDialWithNumber(String phoneNumber) {
    return '操作失败，请手动拨打：$phoneNumber';
  }

  @override
  String healthAssistantVoiceRecognition(String text) {
    return '健康助手语音识别: $text';
  }

  @override
  String healthAssistantVoiceProcessingFailed(String error) {
    return '健康助手语音处理失败: $error';
  }

  @override
  String loginFailedWithMessage(String message) {
    return '登录失败: $message';
  }

  @override
  String get verificationCodeIncorrectOrExpired => '验证码错误或已过期';

  @override
  String get usernameAlreadyExists => '用户名已被注册，请更换用户名';

  @override
  String get dpiAdaptationSettings => 'DPI适配设置';

  @override
  String get dpiAdaptationDescription => '调整应用的显示缩放比例以适应不同的屏幕密度';

  @override
  String get currentDpiScale => '当前缩放比例';

  @override
  String get systemDefault => '系统默认';

  @override
  String get small => '小';

  @override
  String get normal => '正常';

  @override
  String get large => '大';

  @override
  String get extraLarge => '超大';

  @override
  String get previewText => '预览文本';

  @override
  String get sampleText => '这是一段示例文本，用于预览当前的缩放效果。';

  @override
  String get applyChanges => '应用更改';

  @override
  String get resetToDefault => '重置为默认';

  @override
  String get dpiSettingsApplied => 'DPI设置已应用';

  @override
  String get dpiSettingsReset => 'DPI设置已重置为默认';

  @override
  String get dpiModeAuto => '自动适配';

  @override
  String get dpiModeAutoDesc => '根据设备DPI自动调整界面大小（推荐）';

  @override
  String get dpiModeSmall => '紧凑模式';

  @override
  String get dpiModeSmallDesc => '较小的界面元素，适合高DPI设备';

  @override
  String get dpiModeStandard => '标准模式';

  @override
  String get dpiModeStandardDesc => '默认大小的界面元素';

  @override
  String get dpiModeLarge => '宽松模式';

  @override
  String get dpiModeLargeDesc => '较大的界面元素，适合低DPI设备';

  @override
  String get currentStatus => '当前状态';

  @override
  String get adaptationMode => '适配模式';

  @override
  String get scaleFactor => '缩放因子';

  @override
  String get deviceInfo => '设备信息';

  @override
  String get screenSize => '屏幕尺寸';

  @override
  String get devicePixelRatio => '设备像素比';

  @override
  String get screenDiagonal => '屏幕对角线';

  @override
  String get autoScaleFactor => '自动缩放因子';

  @override
  String get effectPreview => '效果预览';

  @override
  String get sampleButton => '示例按钮';

  @override
  String get titleText => '标题文本';

  @override
  String get sampleDescription => '这是一段示例文本，用于预览当前DPI适配设置的效果。';

  @override
  String get inches => '英寸';

  @override
  String get dpiAdaptation => 'DPI适配';

  @override
  String get dpiAdaptationSubtitle => '调整界面元素大小以适应不同DPI的设备';
}
