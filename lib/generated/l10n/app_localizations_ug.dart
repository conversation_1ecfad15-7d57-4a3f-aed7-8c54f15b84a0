// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Uighur Uyghur (`ug`).
class AppLocalizationsUg extends AppLocalizations {
  AppLocalizationsUg([String locale = 'ug']) : super(locale);

  @override
  String get settingsTitle => 'تەڭشەكلەر';

  @override
  String get languageSettings => 'تىل تەڭشىكى';

  @override
  String get selectLanguage => 'تىل تاللاڭ';

  @override
  String get selectSourceLanguage => 'مەنبە تىلىنى تاللاڭ';

  @override
  String get selectTargetLanguage => 'نىشان تىلىنى تاللاڭ';

  @override
  String get languageChinese => 'خەنزۇچە';

  @override
  String get languageEnglish => 'ئىنگلىزچە';

  @override
  String get languageUyghur => 'ئۇيغۇرچە';

  @override
  String get cancel => 'بىكار قىلىش';

  @override
  String get confirm => 'جەزملەشتۈرۈش';

  @override
  String get displaySettings => 'كۆرسىتىش تەڭشىكى';

  @override
  String get darkMode => 'قاراڭغۇ ھالەت';

  @override
  String get darkModeDescription =>
      'ئوچۇق ۋە قاراڭغۇ تېما ئارىسىدا ئالماشتۇرۇش';

  @override
  String get followSystemTheme => 'سىستېما تېمىسىغا ئەگىشىش';

  @override
  String get followSystemThemeDescription =>
      'سىستېمىنىڭ ئوچۇق/قاراڭغۇ تەڭشىكىگە ئاپتوماتىك ماسلىشىش';

  @override
  String get fontSize => 'خەت چوڭلۇقى';

  @override
  String get fontSizeDescription => 'ئەپ ئىچىدىكى خەت چوڭلۇقىنى تەڭشەش';

  @override
  String get languageSettingsDescription => 'ئەپ كۆرسىتىش تىلىنى تاللاش';

  @override
  String get other => 'باشقا';

  @override
  String get helpAndFeedback => 'ياردەم ۋە پىكىر';

  @override
  String get helpAndFeedbackDescription =>
      'دائىملىق سوئاللار ۋە پىكىر قايتۇرۇش';

  @override
  String get aboutUs => 'بىز ھەققىدە';

  @override
  String get aboutUsDescription => 'نەشر ئۇچۇرى ۋە شىركەت تونۇشتۇرۇشى';

  @override
  String get logout => 'چىقىش';

  @override
  String get logoutDescription => 'كىرىش ھالىتىنى تازىلاپ كىرىش بېتىگە قايتىش';

  @override
  String get aboutDialogTitle => 'بىز ھەققىدە';

  @override
  String get aboutDialogContent =>
      'ساغلاملىق ياردەمچىسى v1.0.0\n\nكەسپىي AI ساغلاملىق يېتەكچىسى ئەپى';

  @override
  String get homeTitle => 'ساغلاملىق ياردەمچىسى';

  @override
  String get historyTitle => 'تارىخ';

  @override
  String get aiTourGuideTitle => 'ساغلاملىق ياردەمچىسى';

  @override
  String get smartGuide => 'ئەقلىي يېتەكچى';

  @override
  String get welcomeTo => 'سىزنى قارشى ئالىمىز';

  @override
  String get shareApp => 'ئەپنى ھەمبەھىرلەش';

  @override
  String get distributionManagement => 'تارقىتىش باشقۇرۇش';

  @override
  String get myLanguage => 'مېنىڭ تىلىم';

  @override
  String get theirLanguage => 'ئۇلارنىڭ تىلى';

  @override
  String get sourceLanguage => 'مەنبە';

  @override
  String get targetLanguage => 'نىشان';

  @override
  String get bottomNavHome => 'ساغلاملىق ياردەمچىسى';

  @override
  String get bottomNavHistory => 'تارىخ';

  @override
  String get bottomNavAiGuide => 'ساغلاملىق ياردەمچىسى';

  @override
  String get bottomNavSearch => 'تىزىملىك';

  @override
  String get bottomNavProfile => 'مېنىڭ';

  @override
  String get bottomNavSettings => 'تەڭشەكلەر';

  @override
  String get searchPageTitle => 'تىزىملىك';

  @override
  String get searchHint => 'رەقەملىك تەن ياكى مەھسۇلات ئىزدەڭ...';

  @override
  String get doctorTab => 'رەقەملىك تەن';

  @override
  String get productTab => 'مەھسۇلات';

  @override
  String get noDoctorsAvailable => 'ھازىرچە رەقەملىك تەننىڭ ئۇچۇرى يوق';

  @override
  String get noProductsAvailable => 'ھازىرچە مەھسۇلات ئۇچۇرى يوق';

  @override
  String get noSearchResults => 'مۇناسىپ نەتىجە تېپىلمىدى';

  @override
  String get inputHint => 'سوئالىڭىزنى كىرگۈزۈڭ...';

  @override
  String get tapToSpeak => 'سۆزلەش ئۈچۈن چېكىڭ';

  @override
  String get listening => 'ئاڭلاۋاتىدۇ...';

  @override
  String get processing => 'بىر تەرەپ قىلىۋاتىدۇ...';

  @override
  String get clearHistory => 'تارىخنى تازىلاش';

  @override
  String get copy => 'كۆچۈرۈش';

  @override
  String get share => 'ھەمبەھىرلەش';

  @override
  String get play => 'قويۇش';

  @override
  String get pause => 'توختىتىش';

  @override
  String get retry => 'قايتا سىناش';

  @override
  String get error => 'خاتالىق';

  @override
  String get networkError =>
      'تور ئۇلىنىشى مەغلۇپ بولدى، تور تەڭشىكىنى تەكشۈرۈڭ';

  @override
  String get permissionDenied => 'ھوقۇق رەت قىلىندى';

  @override
  String get cameraPermissionRequired => 'كامېرا ھوقۇقى كېرەك';

  @override
  String get microphonePermissionRequired => 'مىكروفون ھوقۇقى كېرەك';

  @override
  String get appTitle => 'ساغلاملىق ياردەمچىسى';

  @override
  String get welcomeMessage =>
      'ساغلاملىق ياردەمچىسىنى ئىشلىتىشىڭىزنى قارشى ئالىمىز';

  @override
  String get welcomeDescription =>
      'سىزنىڭ ئالاھىدە AI ساغلاملىق يېتەكچىڭىز، ھەر ۋاقىت سىزگە ساغلاملىق مەسلىھەت ۋە يېتەكچىلىك مۇلازىمىتى تەمىنلەيدۇ';

  @override
  String get exitAppConfirm =>
      'يەنە بىر قېتىم قايتىش كۇنۇپكىسىنى بېسىپ پروگراممىدىن چىقىڭ';

  @override
  String get themeSwitch => 'تېما ئالماشتۇرۇش';

  @override
  String get themeSwitchMessage =>
      'تېما ھالىتىنى ئالماشتۇرۇش پروگراممىنى قايتا قوزغىتىشنى تەلەپ قىلىدۇ، دەرھال قايتا قوزغىتامسىز؟';

  @override
  String get languageSwitchMessage =>
      'تىل ئالماشتۇرۇش پروگراممىنى قايتا قوزغىتىشنى تەلەپ قىلىدۇ، دەرھال قايتا قوزغىتامسىز؟';

  @override
  String get restart => 'قايتا قوزغىتىش';

  @override
  String get userManagementTab => 'ئىشلەتكۈچى باشقۇرۇش';

  @override
  String get userManagement => 'ئىشلەتكۈچى باشقۇرۇش';

  @override
  String get userList => 'ئىشلەتكۈچى تىزىملىكى';

  @override
  String get userStatistics => 'ئىشلەتكۈچى ستاتىستىكىسى';

  @override
  String get totalUsers => 'جەمئىي ئىشلەتكۈچى سانى';

  @override
  String get activeUsers => 'ئاكتىپ ئىشلەتكۈچىلەر';

  @override
  String get adminUsers => 'باشقۇرغۇچىلار';

  @override
  String get doctorUsers => 'رەقەملىك تەنلەر';

  @override
  String get searchUsers => 'ئىشلەتكۈچى ئىزدەش';

  @override
  String get searchByNicknamePhoneId =>
      'تەخەللۇس، تېلېفون ياكى ID بويىچە ئىزدەڭ';

  @override
  String get userStatus => 'ئىشلەتكۈچى ھالىتى';

  @override
  String get allStatus => 'ھەممىسى';

  @override
  String get enabledStatus => 'قوزغىتىلغان';

  @override
  String get disabledStatus => 'چەكلەنگەن';

  @override
  String get userRole => 'ئىشلەتكۈچى رولى';

  @override
  String get allRoles => 'بارلىق رول';

  @override
  String get normalUser => 'ئادەتتىكى ئىشلەتكۈچى';

  @override
  String get adminUser => 'باشقۇرغۇچى';

  @override
  String get doctorUser => 'رەقەملىك تەن';

  @override
  String get userGender => 'جىنسى';

  @override
  String get male => 'ئەر';

  @override
  String get female => 'ئايال';

  @override
  String get unknown => 'نامەلۇم';

  @override
  String get registerSource => 'تىزىملىتىش مەنبەسى';

  @override
  String get appSource => 'ئەپ';

  @override
  String get miniProgramSource => 'كىچىك پروگرامما';

  @override
  String get userBalance => 'ئىشلەتكۈچى بالانسى';

  @override
  String get userIntegral => 'ئىشلەتكۈچى ئىنتېگرالى';

  @override
  String get adjustBalance => 'بالانس تەڭشەش';

  @override
  String get adjustIntegral => 'ئىنتېگرال تەڭشەش';

  @override
  String get adjustAmount => 'تەڭشەش مىقدارى';

  @override
  String get adjustReason => 'تەڭشەش سەۋەبى';

  @override
  String get pleaseEnterAmount => 'تەڭشەش مىقدارىنى كىرگۈزۈڭ';

  @override
  String get pleaseEnterReason => 'تەڭشەش سەۋەبىنى كىرگۈزۈڭ';

  @override
  String get positiveForIncrease => 'مۇسبەت سان ئاشۇرۇش، مەنپىي سان ئازايتىش';

  @override
  String get userDetail => 'ئىشلەتكۈچى تەپسىلاتى';

  @override
  String get editUser => 'ئىشلەتكۈچى تەھرىرلەش';

  @override
  String get enableUser => 'ئىشلەتكۈچى قوزغىتىش';

  @override
  String get disableUser => 'ئىشلەتكۈچى چەكلەش';

  @override
  String get resetPassword => 'پارولنى ئەسلىگە كەلتۈرۈش';

  @override
  String get newPassword => 'يېڭى پارول';

  @override
  String get pleaseEnterNewPassword => 'يېڭى پارولنى كىرگۈزۈڭ';

  @override
  String get passwordLength => 'پارول ئۇزۇنلۇقى 6-20 خانە';

  @override
  String get userNickname => 'ئىشلەتكۈچى تەخەللۇسى';

  @override
  String get userPhone => 'ئىشلەتكۈچى تېلېفونى';

  @override
  String get userBirthday => 'ئىشلەتكۈچى تۇغۇلغان كۈنى';

  @override
  String get registrationTime => 'تىزىملىتىش ۋاقتى';

  @override
  String get lastLoginTime => 'ئاخىرقى كىرىش';

  @override
  String get userTokens => 'كىرىش بەلگىسى';

  @override
  String get clearAllTokens => 'بارلىق بەلگىنى تازىلاش';

  @override
  String get confirmClearTokens =>
      'بارلىق كىرىش بەلگىسىنى تازىلاشنى جەزملەشتۈرەمسىز؟';

  @override
  String get clearTokensWarning =>
      'تازىلاشتىن كېيىن ئىشلەتكۈچى بارلىق ئۈسكۈنىدە مەجبۇرىي چىقىرىلىدۇ';

  @override
  String get deviceType => 'ئۈسكۈنە تىپى';

  @override
  String get expiryTime => 'ۋاقىت ئۆتۈش';

  @override
  String get createTime => 'قۇرۇش ۋاقتى';

  @override
  String get expired => 'ۋاقتى ئۆتكەن';

  @override
  String get valid => 'ئىناۋەتلىك';

  @override
  String get loadUserListFailed => 'ئىشلەتكۈچى تىزىملىكىنى يۈكلەش مەغلۇپ بولدى';

  @override
  String get loadUserDetailFailed =>
      'ئىشلەتكۈچى تەپسىلاتىنى يۈكلەش مەغلۇپ بولدى';

  @override
  String get updateUserSuccess =>
      'ئىشلەتكۈچى ئۇچۇرىنى يېڭىلاش مۇۋەپپەقىيەتلىك بولدى';

  @override
  String get updateUserFailed => 'ئىشلەتكۈچى ئۇچۇرىنى يېڭىلاش مەغلۇپ بولدى';

  @override
  String get adjustBalanceSuccess => 'بالانس تەڭشەش مۇۋەپپەقىيەتلىك بولدى';

  @override
  String get adjustBalanceFailed => 'بالانس تەڭشەش مەغلۇپ بولدى';

  @override
  String get adjustIntegralSuccess => 'ئىنتېگرال تەڭشەش مۇۋەپپەقىيەتلىك بولدى';

  @override
  String get adjustIntegralFailed => 'ئىنتېگرال تەڭشەش مەغلۇپ بولدى';

  @override
  String get resetPasswordSuccess =>
      'پارولنى ئەسلىگە كەلتۈرۈش مۇۋەپپەقىيەتلىك بولدى';

  @override
  String get resetPasswordFailed => 'پارولنى ئەسلىگە كەلتۈرۈش مەغلۇپ بولدى';

  @override
  String get clearTokensSuccess => 'بەلگە تازىلاش مۇۋەپپەقىيەتلىك بولدى';

  @override
  String get clearTokensFailed => 'بەلگە تازىلاش مەغلۇپ بولدى';

  @override
  String get noUsersFound => 'ئىشلەتكۈچى تېپىلمىدى';

  @override
  String get enableUserSuccess => 'ئىشلەتكۈچى قوزغىتىش مۇۋەپپەقىيەتلىك بولدى';

  @override
  String get disableUserSuccess => 'ئىشلەتكۈچى چەكلەش مۇۋەپپەقىيەتلىك بولدى';

  @override
  String get reset => 'ئەسلىگە كەلتۈرۈش';

  @override
  String get apply => 'قوللىنىش';

  @override
  String get todayNewUsers => 'بۈگۈن قوشۇلغان';

  @override
  String get thisWeekNewUsers => 'بۇ ھەپتە قوشۇلغان';

  @override
  String get totalBalance => 'جەمئىي بالانس';

  @override
  String get totalIntegral => 'جەمئىي ئىنتېگرال';

  @override
  String get expandFilters => 'سۈزگۈچنى يېيىش';

  @override
  String get collapseFilters => 'سۈزگۈچنى يىغىش';

  @override
  String get userDevelopmentInProgress =>
      'ئىشلەتكۈچى تەپسىلات بېتى ئىشلەپ چىقىلىۋاتىدۇ...';

  @override
  String get roleAdmin => 'باشقۇرغۇچى';

  @override
  String get roleDoctor => 'رەقەملىك تەن';

  @override
  String get roleReferrer => 'تەسۋىقچى';

  @override
  String get roleNormalUser => 'ئادەتتىكى ئىشلەتكۈچى';

  @override
  String get editUserInfo => 'ئىشلەتكۈچى ئۇچۇرىنى تەھرىرلەش';

  @override
  String get editUserRole => 'ئىشلەتكۈچى رولىنى تەھرىرلەش';

  @override
  String get userNicknameLabel => 'ئىشلەتكۈچى تەخەللۇسى';

  @override
  String get userPhoneLabel => 'ئىشلەتكۈچى تېلېفونى';

  @override
  String get userBirthdayLabel => 'ئىشلەتكۈچى تۇغۇلغان كۈنى';

  @override
  String get userGenderLabel => 'ئىشلەتكۈچى جىنسى';

  @override
  String get pleaseEnterNickname => 'ئىشلەتكۈچى تەخەللۇسىنى كىرگۈزۈڭ';

  @override
  String get pleaseEnterPhone => 'ئىشلەتكۈچى تېلېفونىنى كىرگۈزۈڭ';

  @override
  String get selectBirthday => 'تۇغۇلغان كۈننى تاللاڭ';

  @override
  String get selectGender => 'جىنسىنى تاللاڭ';

  @override
  String get associatedDoctor => 'مۇناسىۋەتلىك رەقەملىك تەن';

  @override
  String get selectDoctor => 'رەقەملىك تەننى تاللاڭ';

  @override
  String get referrerLevel => 'تەسۋىقچى دەرىجىسى';

  @override
  String get level => 'دەرىجە';

  @override
  String get ipAddress => 'IP ئادرېسى';

  @override
  String get roleManagement => 'رول ھوقۇق باشقۇرۇش';

  @override
  String get currentRoles => 'نۆۋەتتىكى رول';

  @override
  String get roleDetails => 'رول تەپسىلاتى';

  @override
  String get editRole => 'رول تەھرىرلەش';

  @override
  String get balanceIntegralManagement => 'بالانس ئىنتېگرال باشقۇرۇش';

  @override
  String get tokenManagement => 'بەلگە باشقۇرۇش';

  @override
  String get totalTokens => 'جەمئىي بەلگە سانى';

  @override
  String get noTokensFound => 'كىرىش بەلگىسى تېپىلمىدى';

  @override
  String get tokenClearSuccess => 'بەلگە تازىلاش مۇۋەپپەقىيەتلىك بولدى';

  @override
  String get tokenClearFailed => 'بەلگە تازىلاش مەغلۇپ بولدى';

  @override
  String get loadTokensFailed => 'بەلگە تىزىملىكىنى يۈكلەش مەغلۇپ بولدى';

  @override
  String get pleaseCompleteInfo => 'تولۇق ئۇچۇرنى تولدۇرۇڭ';

  @override
  String get pleaseEnterValidAmount => 'ئىناۋەتلىك مىقدارنى كىرگۈزۈڭ';

  @override
  String get pleaseEnterValidPoints => 'ئىناۋەتلىك ئىنتېگرالنى كىرگۈزۈڭ';

  @override
  String get userEditInProgress =>
      'ئىشلەتكۈچى تەھرىرلەش ئىقتىدارى ئىشلەپ چىقىلىۋاتىدۇ...';

  @override
  String get roleEditInProgress =>
      'رول تەھرىرلەش ئىقتىدارى ئىشلەپ چىقىلىۋاتىدۇ...';

  @override
  String get genderMale => 'ئەر';

  @override
  String get genderFemale => 'ئايال';

  @override
  String get genderUnknown => 'نامەلۇم';

  @override
  String get referrerText => 'تەسۋىقچى';

  @override
  String get updateRoleSuccess => 'رول يېڭىلاش مۇۋەپپەقىيەتلىك بولدى';

  @override
  String get updateRoleFailed => 'رول يېڭىلاش مەغلۇپ بولدى';

  @override
  String get adminRoleDescription => 'سىستېما باشقۇرۇش ھوقۇقىغا ئىگە';

  @override
  String get doctorRoleDescription => 'مەھسۇلات ۋە بۇيرۇتمىلارنى باشقۇرالايدۇ';

  @override
  String get referrerRoleDescription => 'تەشۋىق قىلىپ كومىسسىيە ئالالايدۇ';

  @override
  String get pleaseEnterReferrerLevel => 'تەسۋىقچى دەرىجىسىنى كىرگۈزۈڭ';

  @override
  String get registerSourceApp => 'ئەپ';

  @override
  String get registerSourceMiniProgram => 'كىچىك پروگرامما';

  @override
  String get statusEnabled => 'قوزغىتىلغان';

  @override
  String get statusDisabled => 'چەكلەنگەن';

  @override
  String get healthProfile => 'ساغلاملىق ھۆججىتى';

  @override
  String get viewFullHealthProfile => 'تولۇق ھۆججەتنى كۆرۈش';

  @override
  String get heartRate => 'يۈرەك ئۇرۇش سۈرئىتى';

  @override
  String get bodyTemperature => 'بەدەن تېمپېراتۇرىسى';

  @override
  String get weight => 'ئېغىرلىقى';

  @override
  String get height => 'بويى';

  @override
  String get healthProfileFeatureComingSoon =>
      'ساغلاملىق ھۆججىتى ئىقتىدارى پات ئارىدا ئىشلىتىلىدۇ';

  @override
  String get bloodType => 'قان تىپى';

  @override
  String get exerciseFrequency => 'ماشىق قىلىش چاستوتىسى';

  @override
  String get sedentary => 'ئولتۇرۇشلۇق';

  @override
  String get lightExercise => 'يېنىك مەشىق';

  @override
  String get moderateExercise => 'ئوتتۇرا مەشىق';

  @override
  String get activeExercise => 'يۇقىرى كۈچلۈك مەشىق';

  @override
  String get noHealthProfileYet => 'تېخى ساغلاملىق ھۆججىتى يوق';

  @override
  String get createHealthProfile => 'ساغلاملىق ھۆججىتى قۇرۇش';

  @override
  String get healthProfileEditFeatureComingSoon =>
      'ساغلاملىق ھۆججىتى تەھرىرلەش ئىقتىدارى پات ئارىدا ئىشلىتىلىدۇ';

  @override
  String get basicInfo => 'ئاساسىي ئۇچۇرلار';

  @override
  String get allergyHistory => 'ئاللېرگىيە تارىخى';

  @override
  String get chronicDiseaseHistory => 'سۈرۈكلۈك كېسەللىك تارىخى';

  @override
  String get currentMedication => 'ھازىرقى دورا';

  @override
  String get lifestyle => 'تۇرمۇش ئۇسۇلى';

  @override
  String get hasAllergies =>
      'دورا، يېمەكلىك ياكى باشقا ماددىلارغا ئاللېرگىيەڭىز بارمۇ';

  @override
  String get drugAllergies => 'دورا ئاللېرگىيەسى';

  @override
  String get foodAllergies => 'يېمەكلىك ئاللېرگىيەسى';

  @override
  String get otherAllergies => 'باشقا ئاللېرگىيەلەر';

  @override
  String get hasChronicDiseases => 'سوزۇلما خاراكتىلىك كېسەللىكى بارمۇ';

  @override
  String get chronicDiseasesList => 'سوزۇلما خاراكتىلىك كېسەللىك تىزىملىكى';

  @override
  String get bloodPressureRange => 'ئادەتتىكى قان بېسىمى دائىرىسى';

  @override
  String get bloodSugarRange => 'ئادەتتىكى ئاچ قورساق قان شېكىرى دائىرىسى';

  @override
  String get hasCurrentMedication => 'ھازىر دورا ئىچىۋاتامدۇ';

  @override
  String get medicationDetails => 'دورا تەپسىلاتى';

  @override
  String get smokingStatus => 'تاماكا چېكىش ئەھۋالى';

  @override
  String get drinkingStatus => 'ئىچىمەك ئىچىش ئەھۋالى';

  @override
  String get sleepDuration => 'ھەر كېچە ئوتتۇرىچە ئۇخلاش ۋاقتى';

  @override
  String get sleepQuality => 'ئۇخلاش سۈپىتى';

  @override
  String get stressLevel => 'يېقىنقى بېسىم دەرىجىسى';

  @override
  String get yes => 'ھەئە';

  @override
  String get no => 'ياق';

  @override
  String get never => 'ھەرگىز';

  @override
  String get quit => 'توختىتىپ قويدۇم';

  @override
  String get occasional => 'ئارا-ئارىدا';

  @override
  String get daily => 'ھەر كۈنى';

  @override
  String get social => 'ئىجتىمائىي';

  @override
  String get weekly => 'ھەر ھەپتە';

  @override
  String get lessThan6Hours => '6 سائەتتىن ئاز';

  @override
  String get sixToSevenHours => '6-7 سائەت';

  @override
  String get sevenToEightHours => '7-8 سائەت';

  @override
  String get moreThan8Hours => '8 سائەتتىن كۆپ';

  @override
  String get good => 'ياخشى';

  @override
  String get fair => 'ئوتتۇرا';

  @override
  String get poor => 'ناچار';

  @override
  String get veryLow => 'بەك تۆۋەن';

  @override
  String get low => 'تۆۋەن';

  @override
  String get moderate => 'ئوتتۇرا';

  @override
  String get high => 'يۇقىرى';

  @override
  String get veryHigh => 'بەك يۇقىرى';

  @override
  String get pleaseEnterPhoneNumber => 'تېلېفون نومۇرىنى كىرگۈزۈڭ';

  @override
  String get pleaseEnterCorrectPhoneNumber => 'توغرا تېلېفون نومۇرىنى كىرگۈزۈڭ';

  @override
  String get pleaseEnterPassword => 'پارولنى كىرگۈزۈڭ';

  @override
  String get passwordMinLength => 'پارول ئۇزۇنلۇقى ئاز دېگەندە 6 خانە';

  @override
  String get loggingIn => 'كىرىۋاتىدۇ...';

  @override
  String get loginSuccessful => 'كىرىش مۇۋەپپەقىيەتلىك';

  @override
  String get loginSuccessButNoData =>
      'كىرىش مۇۋەپپەقىيەتلىك ئەمما ئىشلەتكۈچى سانلىق مەلۇماتى قۇرۇق';

  @override
  String dataProcessingError(String error) {
    return 'ئىشلەتكۈچى سانلىق مەلۇماتىنى بىر تەرەپ قىلىش خاتا : $error';
  }

  @override
  String loginProcessError(String error) {
    return 'كىرىش جەريانىدا خاتا : $error';
  }

  @override
  String get passwordIncorrect => 'پارول خاتا';

  @override
  String get phoneNotRegistered =>
      'بۇ تېلېفون نومۇرى تىزىملاتمىغان، ئاۋۋال تىزىملىتىڭ';

  @override
  String get passwordLogin => 'پارول كىرىش';

  @override
  String get passwordLoginSubtitle =>
      'تېلېفون نومۇرىڭىز ۋە پارولىڭىز بىلەن كىرىڭ';

  @override
  String get phoneNumberHint => 'تېلېفون نومۇرىنى كىرگۈزۈڭ';

  @override
  String get passwordHint => 'پارولنى كىرگۈزۈڭ';

  @override
  String get forgotPassword => 'پارولنى ئۇنتۇپ قالدىڭىزمۇ؟';

  @override
  String get smsLogin => 'تەستىقلاش كودى كىرگۈزۈڭ';

  @override
  String get registerAccount => 'ھېسابات تىزىملاش';

  @override
  String get orOtherLoginMethods => 'ياكى باشقا كىرىش ئۇسۇلىنى تاللاڭ';

  @override
  String get loginAgreement =>
      'كىرىش «ئىشلەتكۈچى كېلىشىمى» ۋە «شەخسىيەت سىياسىتى» گە قوشۇلغانلىقىڭىزنى بىلدۈرىدۇ';

  @override
  String get verificationCodeSent => 'تەستىق كودى ئەۋەتىلدى';

  @override
  String get sendFailed => 'يوللاش مەغلۇپ بولدى';

  @override
  String get pleaseEnterVerificationCode => 'تەستىق كودىنى كىرگۈزۈڭ';

  @override
  String get verificationCodeShouldBe6Digits =>
      'تەستىق كودى 6 خانىلىق سان بولۇشى كېرەك';

  @override
  String get login => 'كىرىش';

  @override
  String get welcomeBack => 'قايتىپ كەلگىنىڭىزنى قارشى ئالىمىز';

  @override
  String get pleaseLoginWithPhoneNumber =>
      'تېلېفون نومۇرىڭىز بىلەن ھېساباتقا كىرىڭ';

  @override
  String get passwordLoginDesc => 'تېلېفون نومۇرىڭىز ۋە پارولىڭىز بىلەن كىرىڭ';

  @override
  String get agreeToTerms =>
      'كىرىش «ئىشلەتكۈچى كېلىشىمى» ۋە «شەخسىيەت سىياسىتى» گە قوشۇلغانلىقىڭىزنى بىلدۈرىدۇ';

  @override
  String get verificationCodeHint => 'تەستىق كودىنى كىرگۈزۈڭ';

  @override
  String get newPasswordHint => 'يېڭى پارولنى كىرگۈزۈڭ';

  @override
  String get confirmPasswordHint => 'يېڭى پارولنى قايتا كىرگۈزۈڭ';

  @override
  String get getVerificationCode => 'تەستىق كودى ئېلىش';

  @override
  String get resendVerificationCode => 'قايتا ئەۋەتىش';

  @override
  String get resetPasswordDescription =>
      'تېلېفون نومۇرىنى كىرگۈزۈپ دەلىللەش كودى ئېلىڭ، ئاندىن يېڭى پارول بەلگىلەڭ';

  @override
  String get confirmReset => 'ئەسلىگە كەلتۈرۈشنى جەزملەشتۈرۈش';

  @override
  String get passwordsDoNotMatch => 'ئىككى قېتىم كىرگۈزگەن پارول ئوخشاش ئەمەس';

  @override
  String get passwordResetSuccess =>
      'پارول ئەسلىگە كەلتۈرۈش مۇۋەپپەقىيەتلىك، يېڭى پارول بىلەن كىرىڭ';

  @override
  String get contactCustomerService =>
      'مەسىلە بولسا، خېرىدار مۇلازىمىتى بىلەن ئالاقىلىشىڭ';

  @override
  String get resetPasswordTitle => 'پارولنى ئەسلىگە كەلتۈرۈش';

  @override
  String get enterCorrectPhoneNumber => 'توغرا تېلېفون نومۇرىنى كىرگۈزۈڭ';

  @override
  String get enterVerificationCode => 'دەلىللەش كودىنى كىرگۈزۈڭ';

  @override
  String get verificationCodeSixDigits =>
      'دەلىللەش كودى 6 خانىلىق سان بولۇشى كېرەك';

  @override
  String get enterNewPassword => 'يېڭى پارولنى كىرگۈزۈڭ';

  @override
  String get passwordMinSixCharacters => 'پارول ئۇزۇنلۇقى ئاز دېگەندە 6 خانە';

  @override
  String get enterNewPasswordAgain => 'يېڭى پارولنى قايتا كىرگۈزۈڭ';

  @override
  String get getVerificationCodeButton => 'دەلىللەش كودى ئېلىش';

  @override
  String resendCountdown(int seconds) {
    return 'قايتا ئەۋەتىش (${seconds}s)';
  }

  @override
  String get sendVerificationCodeFailed => 'دەلىللەش كودى ئەۋەتىش مەغلۇپ بولدى';

  @override
  String get resettingPasswordLoading => 'پارولنى ئەسلىگە كەلتۈرۈۋاتىدۇ...';

  @override
  String get passwordResetFailed => 'پارولنى ئەسلىگە كەلتۈرۈش مەغلۇپ بولدى';

  @override
  String get networkConnectionFailedRetry =>
      'تور ئۇلىنىشى مەغلۇپ بولدى، سەل تۇرۇپ قايتا سىناڭ';

  @override
  String get clearHistoryTitle => 'پاراڭ خاتىرىسىنى تازىلاش';

  @override
  String get historyCleared => 'پاراڭ خاتىرىسى تازىلاندى';

  @override
  String get clearHistoryFailed => 'پاراڭ خاتىرىسىنى تازىلاش مەغلۇپ بولدى';

  @override
  String get clearAllHistory => 'بارلىق تارىخنى تازىلاش';

  @override
  String get daysAgo => 'كۈن بۇرۇن';

  @override
  String hoursAgo(int hours) {
    return '$hours سائەت بۇرۇن';
  }

  @override
  String minutesAgo(int minutes) {
    return '$minutes مىنۇت بۇرۇن';
  }

  @override
  String get justNow => 'ھازىرلا';

  @override
  String get retakePhoto => 'قايتا رەسىم تارتىش';

  @override
  String get imageProcessingError => 'رەسىمنى بىر تەرەپ قىلىشتا خاتالىق';

  @override
  String get exitAppHint => 'قايتا چېكىلسە يۇمشاق دېتالدىن چىقىشقا بولىدۇ';

  @override
  String get chinese => 'خەنزۇچە';

  @override
  String get uyghur => 'ئۇيغۇرچە';

  @override
  String get kazakh => 'قازاقچە';

  @override
  String get russian => 'رۇسچە';

  @override
  String get french => 'فرانسۇزچە';

  @override
  String get spanish => 'ئىسپانچە';

  @override
  String get cantonese => 'گۇاڭدۇڭچە';

  @override
  String get selectSourceLanguageFirst => 'ئاۋۋال مەنبە تىلىنى تاللاڭ';

  @override
  String get targetLanguageWillUpdate =>
      'نىشان تىل مەنبە تىلغا ئاساسەن ئاپتوماتىك يېڭىلىنىدۇ';

  @override
  String get faceToFaceConversation => 'يۈز تۇرانە سۆزلىشىش';

  @override
  String get conversation => 'سۆزلىشىش';

  @override
  String get newChat => 'يېڭى قۇرۇش';

  @override
  String get aiChatHistory => 'پاراڭ خاتىرىسى  ';

  @override
  String get noChatHistory => 'ھازىرچە پاراڭ خاتىرىسى يوق';

  @override
  String get startNewChat =>
      'ئوڭ ئۈستىدىكى يېڭى قۇرۇش كۇنۇپكىسىنى چېكىپ پاراڭ باشلاڭ';

  @override
  String get startChatting => 'پاراڭ باشلاش';

  @override
  String get sendFirstMessage => 'تۇنجى ئۇچۇرنى ئەۋەتىپ سۆزلىشىشنى باشلاڭ';

  @override
  String get thinking => 'ئويلاۋاتىدۇ...';

  @override
  String get sending => 'ئەۋەتىۋاتىدۇ...';

  @override
  String get audioMessage => 'ئاۋاز ئۇچۇرى';

  @override
  String get typeMessage => 'ئۇچۇر كىرگۈزۈڭ...';

  @override
  String get editTitle => 'ماۋزۇ تەھرىرلەش';

  @override
  String get enterTitle => 'ماۋزۇ كىرگۈزۈڭ';

  @override
  String get deleteConversation => 'سۆزلىشىشنى ئۆچۈرۈش';

  @override
  String get deleteConversationConfirm =>
      'بۇ سۆزلىشىشنى ئۆچۈرۈشنى جەزملەشتۈرەمسىز؟ بۇ مەشغۇلاتنى قايتۇرۇشقا بولمايدۇ.';

  @override
  String get edit => 'تەھرىرلەش';

  @override
  String get delete => 'ئۆچۈرۈش';

  @override
  String get save => 'ساقلاش';

  @override
  String get loadFailed => 'يۈكلەش مەغلۇپ بولدى';

  @override
  String get microphonePermissionDenied => 'مىكروفون ھوقۇقى رەت قىلىندى';

  @override
  String get recordingTooShort => 'ئاۋاز خاتىرىلەش ۋاقتى بەك قىسقا';

  @override
  String get recordingCancelled => 'ئۈن ئېلىش بىكار قىلىندى';

  @override
  String get aiChatHistorySubtitle =>
      'سىز بىلەن ساغلاملىق ياردەمچىسىنىڭ سۆزلىشىش خاتىرىسىنى كۆرۈڭ';

  @override
  String get clearHistoryButton => 'تارىخ خاتىرىسىنى تازىلاش';

  @override
  String get clearHistoryConfirm =>
      'بارلىق پاراڭ خاتىرىسىنى تازىلاشنى جەزملەشتۈرەمسىز؟ بۇ مەشغۇلاتنى قايتۇرۇشقا بولمايدۇ.';

  @override
  String get clearConversation => 'سۆزلىشىشنى تازىلاش';

  @override
  String get holdToSpeak => 'مىكروفوننى بېسىپ تۇرۇپ سۆزلەڭ';

  @override
  String get waitingForOther => 'قارشى تەرەپنىڭ سۆزلىشىنى كۈتۈۋاتىدۇ';

  @override
  String get microphonePermissionNeeded =>
      'ئۈن ئېلىش ئۈچۈن مىكروفون ھوقۇقى كېرەك';

  @override
  String recordingFailed(String error) {
    return 'ئۈن ئېلىش مەغلۇپ بولدى';
  }

  @override
  String get invalidAudioFile => 'ئۈن ئېلىش ھۆججىتى ئىناۋەتسىز';

  @override
  String get audioProcessingFailed => 'ئۈن ئېلىش بىر تەرەپ قىلىش مەغلۇپ بولدى';

  @override
  String get cannotRecognizeVoice => 'ئاۋاز مەزمۇنىنى تونۇيالمىدى';

  @override
  String get confirmClearConversation =>
      'بارلىق پاراڭ خاتىرىسىنى تازىلاشنى جەزملەشتۈرەمسىز؟ بۇ مەشغۇلاتنى قايتۇرۇشقا بولمايدۇ.';

  @override
  String get conversationCleared => 'سۆزلىشىش تازىلاندى';

  @override
  String get user => 'ئىشلەتكۈچى';

  @override
  String get clickToLogin => 'كىرىش ئۈچۈن چېكىڭ';

  @override
  String get loginToEnjoyMoreFeatures =>
      'كىرگەندىن كېيىن تېخىمۇ كۆپ ئىقتىدارلاردىن ھۇزۇرلىنىڭ';

  @override
  String get editProfile => 'ئۇچۇر تەھرىرلەش';

  @override
  String get vipMember => 'VIP ئەزا';

  @override
  String get distributorLevel => 'تارقاتقۇچى';

  @override
  String get appName => 'ساغلاملىق ياردەمچىسى';

  @override
  String get shareSuccess =>
      'ھەمبەھىرلەش مۇۋەپپەقىيەتلىك! دوستلىرىڭىز سىزنىڭ ئۇلىنىشىڭىز ئارقىلىق چۈشۈرسە مۇكاپات ئالالايدۇ';

  @override
  String get shareNotAvailable =>
      'ھەمبەھىرلەش ئىقتىدارى ۋاقىتلىق ئىشلەتكىلى بولمايدۇ، سەل تۇرۇپ قايتا سىناڭ';

  @override
  String get shareSubject => 'ئەپ تەۋسىيە قىلىش';

  @override
  String shareContentWithReferral(String appName, String url) {
    return 'مەن بىر ئاجايىپ ساغلاملىق ياردەمچىسى ئەپىنى تاپتىم: $appName! كەسپىي AI ساغلاملىق يېتەكچىسى، ھەر ۋاقىت سىزگە ساغلاملىق مەسلىھەت ۋە يېتەكچىلىك مۇلازىمىتى تەمىنلەيدۇ. مېنىڭ ئالاھىدە تەۋسىيە ئۇلىنىشىم ئارقىلىق چۈشۈرۈڭ، يەنە قوشۇمچە پايدىمۇ بار! 🎁\n\nدەرھال چۈشۈرۈڭ: $url';
  }

  @override
  String shareContentNormal(String appName, String url) {
    return 'مەن بىر ئاجايىپ ساغلاملىق ياردەمچىسى ئەپىنى تاپتىم: $appName! كەسپىي AI ساغلاملىق يېتەكچىسى، ھەر ۋاقىت سىزگە ساغلاملىق مەسلىھەت ۋە يېتەكچىلىك مۇلازىمىتى تەمىنلەيدۇ. سىزمۇ سىناپ بېقىڭ!\n\nدەرھال چۈشۈرۈڭ: $url';
  }

  @override
  String get logoutConfirmation =>
      'چىقىشنى جەزملەشتۈرەمسىز؟ چىققاندىن كېيىن قايتا كىرىپ تولۇق ئىقتىدارلارنى ئىشلىتىشىڭىز كېرەك.';

  @override
  String get logoutSuccess => 'چىقىش مۇۋەپپەقىيەتلىك';

  @override
  String get logoutFailed => 'چىقىش مەغلۇپ بولدى';

  @override
  String get comingSoon => '(پات ئارىدا چىقىدۇ)';

  @override
  String yearsExperience(int years) {
    return '$years يىل تەجرىبە';
  }

  @override
  String ratingScore(String rating) {
    return '$rating پۇنكت';
  }

  @override
  String get aiAssistant => 'AI';

  @override
  String get professionalIntroduction => 'كەسپىني تونۇشتۇرۇش';

  @override
  String doctorAiAssistantSelected(String doctorName) {
    return '$doctorName نىڭ AI ئاقىلتەن تاللاندى';
  }

  @override
  String get loading => 'يۈكلەۋاتىدۇ...';

  @override
  String get noDoctorInfo => 'ھازىرچە رەقەملىك تەننىڭ ئۇچۇرى يوق';

  @override
  String get doctorTitle => 'رەقەملىك تەن';

  @override
  String get specialtyField => 'ئۇستا ساھە';

  @override
  String get startChatWithAiGuide => 'AI يېتەكچى بىلەن سۆزلىشىشنى باشلاڭ';

  @override
  String get updateTitleFailed => 'ماۋزۇ يېڭىلاش مەغلۇپ بولدى';

  @override
  String get selectAddress => 'ئادرېس تاللاش';

  @override
  String get addressManagement => 'ئادرېس باشقۇرۇش';

  @override
  String get noAddressesYet => 'ھازىرچە تاپشۇرۇپ ئېلىش ئادرېسى يوق';

  @override
  String get clickToAddAddress => 'ئوڭ ئاستىدىكى كۇنۇپكىنى چېكىپ ئادرېس قوشۇڭ';

  @override
  String get setAsDefault => 'كۆڭۈلدىكى قىلىپ بەلگىلەش';

  @override
  String selectedItemsCount(int count) {
    return '$count دانە تاۋار تاللاندى';
  }

  @override
  String get totalAmount => 'جەمئىي: ';

  @override
  String get myLikesTitle => 'ياقتۇرغانلىرىم';

  @override
  String get myFavoritesTitle => ' يىغقانلىرىم';

  @override
  String get noLikedDoctors => 'ھازىرچە ياخشى كۆرگەن رەقەملىك تەن يوق';

  @override
  String get noFavoriteDoctors => 'ھازىرچە يىغقان رەقەملىك تەن يوق';

  @override
  String get goLikeDoctors =>
      'رەقەملىك تەننىڭ تەپسىلاتى بېتىگە بېرىپ ياخشى كۆرگەن رەقەملىك تىنىڭىزغا ياخشى كۆرىمەن دەڭ';

  @override
  String get goFavoriteDoctors =>
      'رەقەملىك تەن تەپسىلاتى بېتىگە بېرىپ ياخشى كۆرگەن رەقەملىك تىنىڭىزنى يىغىڭ';

  @override
  String get selectAll => 'ھەممىنى تاللاش';

  @override
  String get deleteSelected => 'تاللانغاننى ئۆچۈرۈش';

  @override
  String get checkout => 'ھېساب-كىتاب';

  @override
  String deleteWithCount(int count) {
    return 'ئۆچۈرۈش ($count)';
  }

  @override
  String checkoutWithCount(int count) {
    return 'ھېساب-كىتاب ($count)';
  }

  @override
  String get doctorInfo => 'رەقەملىك تەن ئۇچۇرى';

  @override
  String get doctorName => 'رەقەملىك تەن ئىسمى';

  @override
  String get contactPhone => 'ئالاقە تېلېفونى';

  @override
  String get workAddress => 'خىزمەت ئادرېسى';

  @override
  String get call => 'تېلېفون قىلىش';

  @override
  String get noProducts => 'ھازىرچە مەھسۇلات يوق';

  @override
  String productsCount(int count) {
    return '$count دانە مەھسۇلات';
  }

  @override
  String get buyNow => 'دەرھال سېتىۋېلىش';

  @override
  String get addToCart => 'سېتىۋېلىش ھارۋىسىغا قوشۇش';

  @override
  String get doctor => 'رەقەملىك تەن';

  @override
  String get productQuantity => 'تاۋار سانى';

  @override
  String get productTotalPrice => 'تاۋار جەمئىي باھاسى';

  @override
  String get shippingFee => 'يوللاش ھەققى';

  @override
  String get free => 'ھەقسىز';

  @override
  String get actualPayment => 'ئەمەلىي تۆلەش';

  @override
  String get confirmOrder => 'بۇيۇرتمىنى جەزملەشتۈرۈش';

  @override
  String get orderCreatedSuccess => ' زاكاس قۇرۇش مۇۋەپپەقىيەتلىك';

  @override
  String createOrderFailed(String error) {
    return ' زاكاس قۇرۇش مەغلۇپ بولدى: $error';
  }

  @override
  String checkoutFailed(String error) {
    return 'ھېساب-كىتاب مەغلۇپ بولدى: $error';
  }

  @override
  String get quantityRange => '1-99 ئارىلىقىدىكى ئىناۋەتلىك سان كىرگۈزۈڭ';

  @override
  String get items => 'دانە';

  @override
  String get consultation => 'مەسلىھەت';

  @override
  String get appointment => 'ۋاقىت بېكىتىش';

  @override
  String get years => 'يىل';

  @override
  String get yearsOfExperience => 'خىزمەت يىلى';

  @override
  String get productDetail => 'مەھسۇلات تەپسىلاتى';

  @override
  String get price => 'باھا';

  @override
  String get appointmentTime => 'ۋاقىت بېكىتىش';

  @override
  String get detailedDescription => 'تەپسىلىي تونۇشتۇرۇش';

  @override
  String get selectQuantity => 'سان تاللاش';

  @override
  String get quantity => 'سانى';

  @override
  String get cartEmpty => 'مال ھارۋىسى قۇرۇق';

  @override
  String get cartEmptyDescription =>
      'كۆڭلۈڭىزگە ياقىدىغان مەھسۇلاتلارنى تاللاڭ';

  @override
  String get goShopping => 'سېتىۋېلىشقا بېرىش';

  @override
  String get purchaseConfirmation => 'سېتىۋېلىشنى جەزملەشتۈرۈش';

  @override
  String get manufacturer => 'ئىشلەپچىقارغۇچى';

  @override
  String get wednesday => 'چارشەنبە';

  @override
  String get morning => 'ئەتىگەن';

  @override
  String get afternoon => 'چۈشتىن كېيىن';

  @override
  String get evening => 'كەچ';

  @override
  String get confirmPurchase => 'سېتىۋېلىشنى جەزملەشتۈرۈش';

  @override
  String get productName => 'مەھسۇلات نامى';

  @override
  String get unitPrice => 'يەككە باساسى';

  @override
  String get purchaseConfirmationMessage =>
      'سېتىۋېلىش ئۇچۇرىڭىزنى جەزملەشتۈرۈڭ، جەزملەشتۈرۈشنى چەككەندىن كېيىن  زاكاس جەزملەشتۈرۈش بېتىگە ئاتلايدۇ.';

  @override
  String get orderConfirmation => 'بۇيۇرتمىنى جەزملەشتۈرۈش';

  @override
  String get productInfo => 'مەھسۇلات ئۇچۇرى';

  @override
  String get shippingInfo => 'زاكاس ئەۋەتكەن ئۇچۇرى';

  @override
  String get orderAmount => ' زاكاس مىقدارى';

  @override
  String get recipientName => 'تاپشۇرۇپ ئالغۇچى ئىسمى';

  @override
  String get recipientPhone => 'تاپشۇرۇپ ئالغۇچى تېلېفونى';

  @override
  String get shippingAddress => 'يوللاش ئادرېسى';

  @override
  String get getCurrentLocation => 'ھازىرقى ئورۇننى ئېلىش';

  @override
  String get gettingLocation => 'ئورۇن ئېلىۋاتىدۇ...';

  @override
  String get subtotal => 'قىسمەن جۇغلانما';

  @override
  String get submitOrder => ' زاكاس تاپشۇرۇش';

  @override
  String get submittingOrder => ' زاكاس تاپشۇرۇۋاتىدۇ...';

  @override
  String get enterRecipientName => 'تاپشۇرۇپ ئالغۇچى ئىسمىنى كىرگۈزۈڭ';

  @override
  String get enterRecipientPhone => 'تاپشۇرۇپ ئالغۇچى تېلېفونىنى كىرگۈزۈڭ';

  @override
  String get enterShippingAddress => 'يوللاش ئادرېسىنى كىرگۈزۈڭ';

  @override
  String totalItems(int count) {
    return 'جەمئىي $count دانە مەھسۇلات';
  }

  @override
  String get done => 'تامام';

  @override
  String get region => 'تۇرۇشلۇق رايون';

  @override
  String get selectRegion => 'ئۆلكە-شەھەر-رايون تاللاڭ';

  @override
  String get pleaseSelectRegion => 'تۇرۇشلۇق رايوننى تاللاڭ';

  @override
  String get productAmount => 'تاۋار پۇلى';

  @override
  String get freeShipping => 'ھەقسىز يوللاش';

  @override
  String get defaultAddress => 'كۆڭۈلدىكى';

  @override
  String get editAddress => 'تەھرىرلەش';

  @override
  String get deleteAddress => 'ئۆچۈرۈش';

  @override
  String get loadAddressFailed => 'ئادرېس يۈكلەش مەغلۇپ بولدى';

  @override
  String get setDefaultSuccess => 'بەلگىلەش مۇۋەپپەقىيەتلىك';

  @override
  String get setDefaultFailed => 'بەلگىلەش مەغلۇپ بولدى';

  @override
  String get addAddress => 'يېڭى ئادرېس قوشۇش';

  @override
  String get receiverName => 'تاپشۇرۇپ ئالغۇچى ئىسمى';

  @override
  String get enterReceiverName => 'تاپشۇرۇپ ئالغۇچى ئىسمىنى كىرگۈزۈڭ';

  @override
  String get enterContactPhone => 'ئالاقە تېلېفونىنى كىرگۈزۈڭ';

  @override
  String get detailedAddress => 'تەپسىلىي ئادرېس';

  @override
  String get enterDetailedAddress =>
      'تەپسىلىي تاپشۇرۇپ ئېلىش ئادرېسىنى كىرگۈزۈڭ (كوچا، ئۆي نومۇرى قاتارلىقلار)';

  @override
  String get postalCodeOptional => 'پوچتا كودى (تاللاشچان)';

  @override
  String get enterPostalCode => 'پوچتا كودىنى كىرگۈزۈڭ';

  @override
  String get addressLabelOptional => 'ئادرېس بەلگىسى (تاللاشچان)';

  @override
  String get enterAddressLabel => 'مەسىلەن: ئۆي، شىركەت، مەكتەپ قاتارلىقلار';

  @override
  String get addressTooShort =>
      'تەپسىلىي ئادرېس ئاز دېگەندە 5 ھەرپ بولۇشى كېرەك';

  @override
  String get addressTooLong =>
      'تەپسىلىي ئادرېس 200 ھەرپتىن ئېشىپ كەتمەسلىكى كېرەك';

  @override
  String get saveChanges => 'ئۆزگىرىشنى ساقلاش';

  @override
  String get saveAddress => 'ئادرېس ساقلاش';

  @override
  String get setAsDefaultAddress => 'كۆڭۈلدىكى ئادرېس قىلىپ بەلگىلەش';

  @override
  String get likeSuccess => 'ياخشى كۆرىمەن دېيىش مۇۋەپپەقىيەتلىك';

  @override
  String get unlikeSuccess =>
      'ياخشى كۆرىمەن دېيىشنى بىكار قىلىش مۇۋەپپەقىيەتلىك';

  @override
  String get favoriteSuccess => 'يىغىش مۇۋەپپەقىيەتلىك';

  @override
  String get unfavoriteSuccess => 'يىغىشنى بىكار قىلىش مۇۋەپپەقىيەتلىك';

  @override
  String get operationFailed => 'مەشغۇلات مەغلۇپ بولدى';

  @override
  String get consultDoctor => 'رەقەملىك تەن بىلەن مەسلىھەتلىشىش';

  @override
  String get doctorDetails => 'رەقەملىك تەن تەپسىلاتى';

  @override
  String get specialties => 'ئۇستا ساھە';

  @override
  String get doctorRecommendations => 'رەقەملىك تەن تەۋسىيەسى';

  @override
  String get workingHours => 'دۈشەنبىدىن جۈمىگىچە 9:00-17:00';

  @override
  String get cannotOpenPhoneApp =>
      'تېلېفون ئەپىنى ئاچالمىدى، نومۇر چاپلاش تاختىسىغا كۆچۈرۈلدى';

  @override
  String get operationFailedManualDial =>
      'مەشغۇلات مەغلۇپ بولدى، قولدا تېلېفون قىلىڭ';

  @override
  String get physician => 'رەقەملىك تەن';

  @override
  String get noDescription => 'ھازىرچە چۈشەندۈرۈش يوق';

  @override
  String get viewDetails => 'تەپسىلاتىنى كۆرۈش';

  @override
  String copiedToClipboard(String content) {
    return '$content چاپلاش تاختىسىغا كۆچۈرۈلدى';
  }

  @override
  String get copyFailed => 'كۆچۈرۈش مەغلۇپ بولدى';

  @override
  String get mapPreparingPleaseWait =>
      'خەرىتە تەييارلىنىۋاتىدۇ، سەل تۇرۇپ قايتا سىناڭ';

  @override
  String get mapTitle => 'خەرىتە';

  @override
  String get aiGuideVoiceRecognitionFailure =>
      'ساغلاملىق ياردەمچىسى ئاۋاز تونۇش مەغلۇپ بولدى';

  @override
  String searchingCategory(String category) {
    return '$category ئىزدەۋاتىدۇ...';
  }

  @override
  String get tryOtherCategoriesOrCheckNetwork =>
      'باشقا تۈرنى تاللاپ سىناڭ ياكى تور ئۇلىنىشىنى تەكشۈرۈڭ';

  @override
  String noResultsFoundFor(String category, String city) {
    return '$city$category تېپىلمىدى';
  }

  @override
  String noRelatedCategoryFound(String category) {
    return 'مۇناسىپ $category تېپىلمىدى';
  }

  @override
  String get address => 'ئادرېس';

  @override
  String addressLabel(String address) {
    return 'ئادرېس: $address';
  }

  @override
  String get navigation => 'يولباشچىلىق';

  @override
  String openNavigationFailed(String error) {
    return 'يولباشچىلىق ئېچىش مەغلۇپ بولدى: $error';
  }

  @override
  String get parksAndSquares => 'باغ مەيدان';

  @override
  String get parks => 'باغ';

  @override
  String get zoos => 'ھايۋانات باغچىسى';

  @override
  String get botanicalGardens => 'ئۆسۈملۈك باغچىسى';

  @override
  String get aquariums => 'سۇ ھايۋانلىرى باغچىسى';

  @override
  String get citySquares => 'شەھەر مەيدانى';

  @override
  String get memorialHalls => 'ئەسلەمە زالى';

  @override
  String get templesAndTaoistTemples => 'مەسچىت تاۋاپخانا';

  @override
  String get churches => 'چېركاۋ';

  @override
  String get beaches => 'دېڭىز بويى';

  @override
  String loadDetailsFailed(String error) {
    return 'تەپسىلات يۈكلەش مەغلۇپ بولدى: $error';
  }

  @override
  String get specialtyFood => 'ئالاھىدە تاماق';

  @override
  String get contactInfo => 'ئالاقە ئۇسۇلى';

  @override
  String get website => 'تور بېكەت';

  @override
  String get generatingDetailInfo => 'تەپسىلات ئۇچۇرى ھاسىللاۋاتىدۇ...';

  @override
  String get viewAiGeneratedDetailedIntroduction =>
      'AI ھاسىللىغان تەپسىلىي تونۇشتۇرۇشنى كۆرۈش';

  @override
  String get clickToGetAiGeneratedDetailedIntroduction =>
      'AI ھاسىللىغان تەپسىلىي تونۇشتۇرۇش ئېلىش ئۈچۈن چېكىڭ';

  @override
  String get aiGenerating => 'AI ھاسىللاۋاتىدۇ...';

  @override
  String get generateDetailsFailed =>
      'تەپسىلات ھاسىللاش مەغلۇپ بولدى، سەل تۇرۇپ قايتا سىناڭ';

  @override
  String openNavigationFailedError(String error) {
    return 'يولباشچىلىق ئېچىش مەغلۇپ بولدى: $error';
  }

  @override
  String get addressCopiedToClipboard => 'ئادرېس چاپلاش تاختىسىغا كۆچۈرۈلدى';

  @override
  String get scenicSpotType => 'مەنزىرە نامدار جاي';

  @override
  String openNavigationFailedWithError(String error) {
    return 'يولباشچىلىق ئېچىش مەغلۇپ بولدى: $error';
  }

  @override
  String get mapLoadFailed => 'خەرىتە يۈكلەش مەغلۇپ بولدى';

  @override
  String get unableToLoadMapPleaseRetry =>
      'خەرىتە يۈكلىيەلمىدى، قايتىپ قايتا سىناڭ';

  @override
  String get back => 'قايتىش';

  @override
  String get locateToCurrentPosition => 'نۆۋەتتىكى ئورۇنغا ئورۇنلاشتۇرۇش';

  @override
  String get searchLocation => 'ئورۇن ئىزدەش...';

  @override
  String foundLocation(String name) {
    return 'تېپىلدى: $name';
  }

  @override
  String get mapControllerNotInitialized =>
      'خەرىتە كونتروللىغۇچى دەسلەپكى قىممەت بېرىلمىگەن، قايتىپ قايتا سىناڭ';

  @override
  String locationServiceException(String error) {
    return 'ئورۇن بېكىتىش مۇلازىمىتى ئىستىسنا: $error';
  }

  @override
  String get mapNotFullyLoaded =>
      'خەرىتە تولۇق يۈكلەنمىدى، سەل تۇرۇپ قايتا سىناڭ';

  @override
  String locationFailed(String error) {
    return 'ئورۇن بېكىتىش مەغلۇپ بولدى: $error';
  }

  @override
  String get cameraPermissionNeeded =>
      'كامېرا ئاچالمىدى، ھوقۇق تەڭشىكىنى تەكشۈرۈڭ';

  @override
  String get aiTourGuideRecognition => 'ساغلاملىق ياردەمچىسى تونۇش';

  @override
  String get aiTourGuideVoiceRecognition => 'ساغلاملىق ياردەمچىسى ئاۋاز تونۇش';

  @override
  String get userAvatarFeatureInDevelopment =>
      'ئىشلەتكۈچى باش سۈرىتى ئىقتىدارى ئىشلەپ چىقىلىۋاتىدۇ';

  @override
  String get inDevelopment => 'ئىقتىدار ئىشلەپ چىقىلىۋاتىدۇ';

  @override
  String get close => 'تاقاش';

  @override
  String get overview => 'ئومۇمىي كۆرۈنۈش';

  @override
  String get records => 'خاتىرە';

  @override
  String get team => 'گۇرۇپپا';

  @override
  String get promotion => 'تەشۋىقات';

  @override
  String get loadMoreFailed => 'تېخىمۇ كۆپ يۈكلەش مەغلۇپ بولدى';

  @override
  String get getUserListFailed => 'ئىشلەتكۈچى تىزىملىكىنى ئېلىش مەغلۇپ بولدى';

  @override
  String get levelUpdateSuccess =>
      'ئىشلەتكۈچى دەرىجىسى ئۆزگەرتىش مۇۋەپپەقىيەتلىك';

  @override
  String get levelUpdateFailed => 'دەرىجە ئۆزگەرتىش مەغلۇپ بولدى';

  @override
  String get certifiedDistributor => 'تەستىقلانغان تارقاتقۇچى';

  @override
  String get fundsDetail => 'پۇل تەپسىلاتى';

  @override
  String get withdrawing => 'چىقىرىۋاتىدۇ';

  @override
  String get withdrawn => 'چىقىرىلدى';

  @override
  String get totalCommissionIncome => 'تارقىتىش يۈرۈشلۈك پۇلى جەمئىي كىرىم';

  @override
  String get noPromotionPosters => 'ھازىرچە تەشۋىقات ئېلانى يوق';

  @override
  String get testDescription => 'سىناق چۈشەندۈرۈشى:';

  @override
  String get longPressToSelect =>
      'تۆۋەندىكى تېكىستنى ئۇزۇن بېسىپ تاللاڭ، كۆچۈرگەن مەزمۇن پەقەت خەنزۇچە ھەرپلەرنى ئۆز ئىچىگە ئالىدۇ.';

  @override
  String get smartCopyVersion => 'ئەقلىي كۆچۈرۈش نەشرى:';

  @override
  String get plainTextVersion => 'ساپ تېكىست نەشرى:';

  @override
  String get smartCopyTest => 'ئەقلىي كۆچۈرۈش ئىقتىدارى سىنىقى';

  @override
  String loadHistoryFailed(String error) {
    return 'تارىخ خاتىرىسىنى يۈكلەش مەغلۇپ بولدى: $error';
  }

  @override
  String clearHistoryFailure(String error) {
    return 'تارىخ خاتىرىسىنى تازىلاش مەغلۇپ بولدى: $error';
  }

  @override
  String get cameraAccessFailure =>
      'كامېرا ئاچالمىدى، ھوقۇق تەڭشىكىنى تەكشۈرۈڭ';

  @override
  String get instructions =>
      'سىزنىڭ ئالاھىدە AI ساغلاملىق يېتەكچىڭىز، ھەر ۋاقىت سىزگە ساغلاملىق مەسلىھەت ۋە يېتەكچىلىك مۇلازىمىتى تەمىنلەيدۇ';

  @override
  String get enterPhoneNumber => 'تېلېفون نومۇرىنى كىرگۈزۈڭ';

  @override
  String get enterCorrectVerificationCode => 'توغرا دەلىللەش كودىنى كىرگۈزۈڭ';

  @override
  String get sendingVerificationCode => 'تەستىق كودى ئەۋەتىۋاتىدۇ...';

  @override
  String get verificationCodeSentToPhone =>
      'تەستىق كودى سىزنىڭ تېلېفونىڭىزغا ئەۋەتىلدى';

  @override
  String get networkConnectionFailed =>
      'تور ئۇلىنىشى مەغلۇپ بولدى، سەل تۇرۇپ قايتا سىناڭ';

  @override
  String networkRequestFailed(String statusCode) {
    return 'تور ئىلتىماسى مەغلۇپ بولدى، ھالەت كودى: $statusCode';
  }

  @override
  String sendVerificationCodeError(String error) {
    return 'تەستىق كودى ئەۋەتىشتە خاتالىق: $error';
  }

  @override
  String get resettingPassword => 'پارول ئەسلىگە كەلتۈرۈۋاتىدۇ...';

  @override
  String processingUserDataError(String error) {
    return 'ئىشلەتكۈچى ئۇچۇرىنى بىر تەرەپ قىلىشتا خاتالىق: $error';
  }

  @override
  String get loginSuccessButNoUserData =>
      'كىرىش مۇۋەپپەقىيەتلىك ئەمما ئىشلەتكۈچى ئۇچۇرى قۇرۇق';

  @override
  String get userNotRegisteredRedirecting =>
      'ئىشلەتكۈچى تىزىملانمىغان، تىزىملاش بېتىگە ئاتلاۋاتىدۇ';

  @override
  String get historyDeleted => 'پاراڭ خاتىرىسى ئۆچۈرۈلدى';

  @override
  String deleteHistoryFailed(String error) {
    return 'پاراڭ خاتىرىسىنى ئۆچۈرۈش مەغلۇپ بولدى: $error';
  }

  @override
  String get noCameraDetected => 'ئىشلەتكىلى بولىدىغان كامېرا بايقالمىدى';

  @override
  String cameraInitializationFailed(String error) {
    return 'كامېرا دەسلەپكى قىممەت بېرىش مەغلۇپ بولدى: $error';
  }

  @override
  String get cameraNotReady => 'كامېرا تەييار ئەمەس';

  @override
  String capturePhotoFailed(String error) {
    return 'رەسىم تارتىش مەغلۇپ بولدى: $error';
  }

  @override
  String get galleryPermissionRequired => 'رەسىم ئالبومى ھوقۇقى كېرەك';

  @override
  String selectImageFailed(String error) {
    return 'رەسىم تاللاش مەغلۇپ بولدى: $error';
  }

  @override
  String get flashlightOperationFailed => 'چىراغ مەشغۇلاتى مەغلۇپ بولدى';

  @override
  String get switchCameraFailed => 'كامېرا ئالماشتۇرۇش مەغلۇپ بولدى';

  @override
  String languageNotSupportedAsSource(String language) {
    return '$language مەنبە تىل سۈپىتىدە قوللىمايدۇ، ئالماشتۇرالمايدۇ';
  }

  @override
  String get enterUsername => 'ئىشلەتكۈچى نامىنى كىرگۈزۈڭ';

  @override
  String get passwordMinLength6 => 'پارول ئۇزۇنلۇقى ئاز دېگەندە 6 خانە';

  @override
  String get passwordsNotMatch => 'ئىككى قېتىم كىرگۈزگەن پارول ئوخشاش ئەمەس';

  @override
  String get agreeToUserAgreement =>
      'ئىشلەتكۈچى كېلىشىمى ۋە شەخسىيەت سىياسىتىگە قوشۇلۇڭ';

  @override
  String get registering => 'تىزىملاۋاتىدۇ...';

  @override
  String get registerSuccess => 'تىزىملاش مۇۋەپپەقىيەتلىك';

  @override
  String get phoneFormatIncorrect => 'تېلېفون نومۇر فورماتى توغرا ئەمەس';

  @override
  String get verificationCodeExpired =>
      'دەلىللەش كودى خاتا ياكى ۋاقتى ئۆتۈپ كەتتى';

  @override
  String get usernameAlreadyRegistered =>
      'ئىشلەتكۈچى نامى تىزىملانغان، ئىشلەتكۈچى نامىنى ئالماشتۇرۇڭ';

  @override
  String get phoneAlreadyRegistered =>
      'بۇ تېلېفون نومۇرى ئاللىقاچان مەۋجۇت، بىۋاسىتە كىرەلەيسىز';

  @override
  String registerFailed(String message) {
    return 'تىزىملاش مەغلۇپ بولدى: $message';
  }

  @override
  String registerProcessError(String error) {
    return 'تىزىملاش جەريانىدا خاتالىق: $error';
  }

  @override
  String get openUserAgreement => 'ئىشلەتكۈچى كېلىشىمىنى ئېچىش';

  @override
  String get openPrivacyPolicy => 'شەخسىيەت سىياسىتىنى ئېچىش';

  @override
  String get priceInfoLoadingWait => 'باھا ئۇچۇرى يۈكلەۋاتىدۇ، سەل كۈتۈڭ...';

  @override
  String get priceInfoLoadFailed =>
      'باھا ئۇچۇرىنى ئېلىش مەغلۇپ بولدى، كۆڭۈلدىكى باھا كۆرسىتىلىدۇ';

  @override
  String get recordingStartFailed =>
      'ئۈن ئېلىش قوزغىتىش مەغلۇپ بولدى، مىكروفون ھوقۇقىنى تەكشۈرۈڭ';

  @override
  String get recordingStartError => 'ئۈن ئېلىش قوزغىتىشتا خاتالىق، قايتا سىناڭ';

  @override
  String get recordingFailedRetry => 'ئۈن ئېلىش مەغلۇپ بولدى، قايتا سىناڭ';

  @override
  String get audioProcessingFailedRetry =>
      'ئاۋاز بىر تەرەپ قىلىش مەغلۇپ بولدى، قايتا سىناڭ';

  @override
  String voiceProcessingError(String error) {
    return 'ئاۋازنى بىر تەرەپ قىلىشتا خاتالىق: $error';
  }

  @override
  String playbackFailed(String error) {
    return 'قويۇش مەغلۇپ بولدى: $error';
  }

  @override
  String get microphoneRecordingPermissionRequired =>
      'ئۈن ئالالمايدۇ: مىكروفون ھوقۇقى كېرەك';

  @override
  String get permissionGrantedRetryRecording =>
      'ھوقۇق بېرىلدى، ئۈن ئېلىش كۇنۇپكىسىنى قايتا ئۇزۇن بېسىپ ئۈن ئېلىشنى باشلاڭ';

  @override
  String logoutFailedError(String error) {
    return 'چىقىش مەغلۇپ بولدى: $error';
  }

  @override
  String aiTourGuideRecognitionResult(String text) {
    return 'ساغلاملىق ياردەمچىسى تونۇش: $text';
  }

  @override
  String aiTourGuideVoiceRecognitionResult(String text) {
    return 'ساغلاملىق ياردەمچىسى ئاۋاز تونۇش: $text';
  }

  @override
  String get profileTitle => 'مېنىڭ';

  @override
  String get editProfileTitle => 'شەخسىي ئۇچۇرلارنى تەھرىرلەش';

  @override
  String get healthInfo => 'ساغلاملىق ئۇچۇرلىرى';

  @override
  String get heightHint => 'بويىنى كىرگۈزۈڭ (cm)';

  @override
  String get heightValidation => 'ئىناۋەتلىك بويى كىرگۈزۈڭ (50-250cm)';

  @override
  String get weightHint => 'ئېغىرلىقنى كىرگۈزۈڭ (kg)';

  @override
  String get weightValidation => 'ئىناۋەتلىك ئېغىرلىق كىرگۈزۈڭ (20-300kg)';

  @override
  String get selectBloodType => 'قان تىپىنى تاللاڭ';

  @override
  String get bloodTypeA => 'A تىپ';

  @override
  String get bloodTypeB => 'B تىپ';

  @override
  String get bloodTypeAB => 'AB تىپ';

  @override
  String get bloodTypeO => 'O تىپ';

  @override
  String get bloodTypeUnknown => 'بىلمەيمەن';

  @override
  String get residentialAddress => 'تۇرۇشلۇق ئادرېسى';

  @override
  String get locate => 'ئورۇن بەلگىلەش';

  @override
  String get selectResidentialAddress => 'تۇرۇشلۇق ئادرېسنى تاللاڭ';

  @override
  String get regionSelectionFailed => 'رايون تاللاش مەغلۇپ بولدى، قايتا سىناڭ';

  @override
  String get commonAllergens => 'ئادەتتىكى ئاللېرگېنلار';

  @override
  String get penicillinAllergy => 'پېنىسىللىن دورىلىرى';

  @override
  String get cephalosporinAllergy => 'سېفالوسپورىن دورىلىرى';

  @override
  String get aspirinAllergy => 'ئاسپىرىن';

  @override
  String get peanutAllergy => 'يەر ياڭاق';

  @override
  String get seafoodAllergy => 'دېڭىز مەھسۇلاتلىرى';

  @override
  String get milkAllergy => 'سۈت';

  @override
  String get eggAllergy => 'تۇخۇم';

  @override
  String get pollenDustMiteAllergy => 'گۈل چاڭ/چاڭ كېنىسى';

  @override
  String get otherAllergens => 'باشقا ئاللېرگېنلار';

  @override
  String get otherAllergensHint => 'باشقا ئاللېرگېنلارنى تولۇقلاڭ';

  @override
  String get takingMedication => 'ھازىر ھەرقانداق دورا ئىچىۋاتامسىز';

  @override
  String get medicationList => 'دورا تىزىملىكى';

  @override
  String get medicationListHint =>
      'ئىچىۋاتقان دورىلارنىڭ نامى، مىقدارى ۋە چاستوتىسىنى تىزىپ بېرىڭ';

  @override
  String get hasChronicDisease => 'سۈرۈكلۈك كېسەللىكىڭىز بارمۇ';

  @override
  String get specificSymptoms => 'ئالاھىدە كېسەللىكلەر';

  @override
  String get hypertension => 'يۇقىرى قان بېسىمى';

  @override
  String get bloodPressureHint => 'مەسىلەن 130/85 mmHg';

  @override
  String get diabetes => 'شېكەر كېسىلى';

  @override
  String get bloodSugarHint => 'مەسىلەن 5.8 mmol/L';

  @override
  String get otherChronicDiseases => 'باشقا سۈرۈكلۈك كېسەللىكلەر';

  @override
  String get otherChronicDiseasesHint =>
      'باشقا سۈرۈكلۈك كېسەللىكلەرنى تولۇقلاڭ';

  @override
  String get surgeryHistory => 'ئوپېراتسىيە ۋە دوىتۇرخانىدا ياتقان تارىخى';

  @override
  String get hasSurgeryHistory =>
      'ئىلگىرى ئوپېراتسىيە قىلدۇرغان ياكى دوختۇرخانىدا ياتقان تەجرىبىڭىز بارمۇ';

  @override
  String get surgeryDetails => 'تەپسىلاتى';

  @override
  String get surgeryDetailsHint =>
      'ئوپېراتسىيە ياكى دوختۇرخانىدا ياتقان تەپسىلىي ئەھۋالىنى تەسۋىرلەڭ';

  @override
  String get familyHistory => 'ئائىلە كېسەللىك تارىخى';

  @override
  String get familyDiseaseHistory =>
      'يېقىن تۇغقانلار (ئاتا-ئانا، قېرىنداش-سىڭىل، بالىلار) تۆۋەندىكى كېسەللىكلەرگە گىرىپتارمۇ';

  @override
  String get familyHypertension => 'يۇقىرى قان بېسىمى';

  @override
  String get familyDiabetes => 'شېكەر كېسىلى';

  @override
  String get familyHeartDisease => 'يۈرەك كېسىلى';

  @override
  String get familyStroke => 'مىي قان تومۇر توسۇلۇشى';

  @override
  String get familyCancer => 'سەرەتان';

  @override
  String get familyMentalHealth => 'روھىي ساغلاملىق كېسەللىكلىرى';

  @override
  String get otherFamilyHistory => 'باشقا ئائىلە كېسەللىك تارىخى قوشۇمچىسى';

  @override
  String get otherFamilyHistoryHint =>
      'باشقا ئائىلە كېسەللىك تارىخىنى تولۇقلاڭ';

  @override
  String get exerciseSedentary => 'ئۇزۇن ئولتۇرۇش (ئاساسەن ماشىق قىلمايدۇ)';

  @override
  String get exerciseLight => 'ئاز دەرىجىدە ئاكتىپ (ھەپتىدە 1-2 قېتىم ماشىق)';

  @override
  String get exerciseModerate =>
      'ئوتتۇرا دەرىجىدە ئاكتىپ (ھەپتىدە 3-5 قېتىم ماشىق)';

  @override
  String get exerciseActive =>
      'ناھايىتى ئاكتىپ (ھەپتىدە 6 قېتىم ۋە ئۇنىڭدىن كۆپ)';

  @override
  String get dietaryPreferences => 'كۈندىلىك يېمەكلىك مايىللىقى';

  @override
  String get balancedDiet => 'يېمەكلىك تەڭپۇڭلۇق';

  @override
  String get vegetarianDiet => 'ئۆسۈملۈك يېمەكلىكىنى ياخشى كۆرىدۇ';

  @override
  String get meatDiet => 'گۆش يېمەكلىكىنى ياخشى كۆرىدۇ';

  @override
  String get oilyFood => 'ياغلىق يېمەكلىكنى ياخشى كۆرىدۇ';

  @override
  String get saltyFood => 'شورلۇق يېمەكلىكنى ياخشى كۆرىدۇ';

  @override
  String get sweetFood => 'تاتلىق يېمەكلىكنى ياخشى كۆرىدۇ';

  @override
  String get neverSmoke => 'ھەرگىز تاماكا چەكمەيمەن';

  @override
  String get quitSmoking => 'تاماكا چېكىشنى توختاتتىم';

  @override
  String get occasionalSmoking => 'ئارا-ئارىدا تاماكا چېكىمەن (ھەر كۈنى ئەمەس)';

  @override
  String get dailySmoking => 'دائىم تاماكا چېكىمەن (ھەر كۈنى)';

  @override
  String get neverDrink => 'ھەرگىز ئىچىمەك ئىچمەيمەن';

  @override
  String get quitDrinking => 'ئىچىمەك ئىچىشنى توختاتتىم';

  @override
  String get socialDrinking => 'ئارا-ئارىدا ئىجتىمائىي ئىچىمەك ئىچىمەن';

  @override
  String get weeklyDrinking => 'ھەپتىدە 1-3 قېتىم';

  @override
  String get dailyDrinking => 'دېگۈدەك ھەر كۈنى';

  @override
  String get sleepLessThan6 => '6 سائەتتىن ئاز';

  @override
  String get sleep6To7 => '6-7 سائەت';

  @override
  String get sleep7To8 => '7-8 سائەت';

  @override
  String get sleepMoreThan8 => '8 سائەتتىن كۆپ';

  @override
  String get sleepGood => 'ياخشى (ئاسان ئۇخلايدۇ، ئاز ئويغىنىدۇ)';

  @override
  String get sleepFair =>
      'ئادەتتىكى (ئارا-ئارىدا ئۇخلاش قىيىنچىلىقى ياكى بالدۇر ئويغىنىش)';

  @override
  String get sleepPoor =>
      'ناچار (ئۇزۇن مۇددەت ئۇخلاش قىيىنچىلىقى، چۈش كۆرۈش، بالدۇر ئويغىنىش)';

  @override
  String get stressLow => 'بەك ئاز';

  @override
  String get stressMild => 'ئازراق بېسىم';

  @override
  String get stressModerate => 'مۇۋاپىق بېسىم';

  @override
  String get stressHigh => 'چوڭراق بېسىم';

  @override
  String get stressExtreme => 'ناھايىتى چوڭ بېسىم';

  @override
  String get womenHealth => 'ئايال ساغلاملىقى';

  @override
  String get isMenopause => 'ئاي كۆرۈش توختىدىمۇ';

  @override
  String get menstrualCycleRegular => 'ئاي كۆرۈش دەۋرىسى مۇنتىزاممۇ';

  @override
  String get menstrualRegular => 'مۇنتىزام';

  @override
  String get menstrualIrregular => 'مۇنتىزام ئەمەس';

  @override
  String get menstrualUncertain => 'ئېنىق ئەمەس';

  @override
  String get hasPregnancy => 'ھامىلىدار بولغان تەجرىبىڭىز بارمۇ';

  @override
  String get birthCount => 'تۇغۇت سانى';

  @override
  String get birthCount0 => '0 قېتىم';

  @override
  String get birthCount1 => '1 قېتىم';

  @override
  String get birthCount2 => '2 قېتىم';

  @override
  String get birthCount3 => '3 قېتىم';

  @override
  String get birthCount4 => '4 قېتىم';

  @override
  String get birthCount5Plus => '5 قېتىم ۋە ئۇنىڭدىن كۆپ';

  @override
  String get cannotParseImage => 'رەسىم ھۆججىتىنى تەھلىل قىلالمايدۇ';

  @override
  String get vipMemberBadge => 'VIP ئەزا';

  @override
  String get normalUserBadge => 'ئادەتتىكى ئىشلەتكۈچى';

  @override
  String distributorLevelBadge(String level) {
    return 'تارقاتقۇچى Lv.$level';
  }

  @override
  String get distributorBadge => 'تارقاتقۇچى';

  @override
  String get arabic => 'ئەرەبچە';

  @override
  String get english => 'ئىنگلىزچە';

  @override
  String languageSwitchNotSupported(String language) {
    return '$language مەنبە تىل سۈپىتىدە قوللىمايدۇ، ئالماشتۇرالمايدۇ';
  }

  @override
  String get sourceLanguageLabel => ' مەنبە تىلى';

  @override
  String get targetLanguageLabel => 'نىشان تىلى';

  @override
  String get currentBalance => 'نۆۋەتتىكى قالدۇق';

  @override
  String get todayIncome => 'بۈگۈنكى كىرىم';

  @override
  String get totalIncome => 'جەمئىي كىرىم';

  @override
  String get holdMicrophoneToSpeak => 'مىكروفوننى بېسىپ تۇرۇپ سۆزلەڭ';

  @override
  String get waitingForOtherParty => 'قارشى تەرەپنىڭ سۆزلىشىنى كۈتۈۋاتىدۇ';

  @override
  String get confirmClear => 'تازىلاشنى جەزملەشتۈرۈش';

  @override
  String get confirmClearAllChatRecords =>
      'بارلىق پاراڭ خاتىرىسىنى تازىلاشنى جەزملەشتۈرەمسىز؟ بۇ مەشغۇلاتنى قايتۇرۇشقا بولمايدۇ.';

  @override
  String get confirmClearAction => 'تازىلاشنى جەزملەشتۈرۈش';

  @override
  String get registerTitle => 'ئىشلەتكۈچى تىزىملاش';

  @override
  String get usernameHint => 'ئىشلەتكۈچى نامىنى كىرگۈزۈڭ';

  @override
  String get setPassword => 'پارول بەلگىلەش';

  @override
  String get confirmPassword => 'پارولنى قايتا كىرگۈزۈش';

  @override
  String get getCodeButton => 'دەلىللەش كودى ئېلىش';

  @override
  String countdownSeconds(int count) {
    return '$count سېكۇنت';
  }

  @override
  String get register => 'تىزىملاش';

  @override
  String get phoneNumberLogin => 'تېلېفون نومۇرى كىرىش';

  @override
  String get userAgreementAndPrivacyPolicy =>
      '«ئىشلەتكۈچى كېلىشىمى» ۋە «شەخسىيەت سىياسىتى»';

  @override
  String get iAgreeToThe => 'مەن ئوقۇپ چىقتىم ۋە قوشۇلىمەن';

  @override
  String get helpFeedbackTitle => 'ياردەم ۋە پىكىر';

  @override
  String get yourNameOptional => 'سىزنىڭ ئىسمىڭىز (تاللاشچان)';

  @override
  String get yourPhoneNumber => 'سىزنىڭ تېلېفون نومۇرىڭىز';

  @override
  String get describeProblemDetail =>
      'مەسىلە ياكى تەكلىپىڭىزنى تەپسىلىي چۈشەندۈرۈڭ';

  @override
  String get submitFeedback => 'پىكىر يوللاش';

  @override
  String get pleaseEnterCorrectPhoneFormat =>
      'توغرا تېلېفون نومۇرى فورماتىنى كىرگۈزۈڭ';

  @override
  String get pleaseDescribeProblem => 'ئۇچرىغان مەسىلىڭىزنى چۈشەندۈرۈڭ';

  @override
  String get descriptionMinLength =>
      'مەسىلە چۈشەندۈرۈشى ئاز دېگەندە 10 ھەرپ بولۇشى كېرەك';

  @override
  String get descriptionMaxLength =>
      'مەسىلە چۈشەندۈرۈشى 1000 ھەرپتىن ئېشىپ كەتمەسلىكى كېرەك';

  @override
  String get submittingFeedback => 'پىكىر يوللاۋاتىدۇ...';

  @override
  String get feedbackSubmittedSuccess =>
      'پىكىر يوللاش مۇۋەپپەقىيەتلىك، تەكلىپىڭىزگە رەھمەت!';

  @override
  String feedbackSubmissionFailed(String error) {
    return 'پىكىر يوللاش مەغلۇپ بولدى: $error';
  }

  @override
  String get feedbackInstructions => 'پىكىر چۈشەندۈرۈشى';

  @override
  String get feedbackInstructionsText =>
      'بىز سىزنىڭ مەسىلە چۈشەندۈرۈشىڭىز ۋە مۇناسىپ ئەپ سانلىق مەلۇماتلىرىنى (سەزگۈر ئۇچۇرلارنى ئۆز ئىچىگە ئالمايدۇ) يىغىپ مەسىلىڭىزنى تېخىمۇ ياخشى ھەل قىلىمىز. يوللىغاندىن كېيىن بىز سىز تەمىنلىگەن تېلېفون نومۇرى ئارقىلىق سىز بىلەن ئالاقىلىشىمىز.';

  @override
  String get enterYourName => 'ئىسمىڭىزنى كىرگۈزۈڭ';

  @override
  String get problemDescriptionHint =>
      'ئۇچراشقان مەسىلە ياكى تەكلىپىڭىزنى تەپسىلىي چۈشەندۈرۈڭ\nئۆز ئىچىگە ئالىدىغانلار:\n• ئېنىق مەشغۇلات باسقۇچلىرى\n• كۈتۈلگەن نەتىجە\n• ئەمەلىيەتتە يۈز بەرگەن ئەھۋال\n• باشقا مۇناسىپ ئۇچۇرلار';

  @override
  String get submitting => 'يوللاۋاتىدۇ...';

  @override
  String get testLogGeneration => 'سىناق خاتىرە ھاسىللاش';

  @override
  String get viewErrorLogs => 'خاتالىق خاتىرىسىنى كۆرۈش';

  @override
  String get generateTestErrors => 'سىناق خاتالىقى ھاسىللاش';

  @override
  String get privacyNotice =>
      'ئەسكەرتىش: سىزنىڭ شەخسىيەت مەخپىيەتلىكىڭىز بىزگە ئىنتايىن مۇھىم، بىز پارول قاتارلىق سەزگۈر ئۇچۇرلارنى يىغمايمىز، پەقەت مەسىلە ھەل قىلىشقا ياردەم بېرىش ئۈچۈن زۆرۈر ئەپ سەپلىمىسى ۋە خاتىرە سانلىق مەلۇماتلىرىنىلا يىغىمىز.';

  @override
  String get logGenerationSuccess => 'خاتىرە ھاسىللاش مۇۋەپپەقىيەتلىك';

  @override
  String logSize(String size) {
    return 'خاتىرە چوڭلۇقى: ${size}KB';
  }

  @override
  String get testErrorLogsGenerated =>
      'سىناق خاتالىق خاتىرىسى ھاسىللاندى، خاتالىق خاتىرە كۆرگۈچىدە كۆرەلەيسىز';

  @override
  String get feedbackSubmittedSuccessfully =>
      'پىكىر يوللاش مۇۋەپپەقىيەتلىك، بىز مەسىلىڭىزنى تېزلىكتە بىر تەرەپ قىلىمىز';

  @override
  String get submissionFailed => 'يوللاش مەغلۇپ بولدى';

  @override
  String get submissionFailedCheckNetwork =>
      'يوللاش مەغلۇپ بولدى، تور ئۇلىنىشىنى تەكشۈرۈڭ';

  @override
  String logGenerationFailed(String error) {
    return 'خاتىرە ھاسىللاش مەغلۇپ بولدى: $error';
  }

  @override
  String get phoneNumberForContact => '(ئالاقىلىشىش نۇمىرى)';

  @override
  String get nickname => 'تەخەللۇس';

  @override
  String get nicknameRequired => 'تەخەللۇس قۇرۇق بولسا بولمايدۇ';

  @override
  String get nicknameMinLength => 'تەخەللۇس ئاز دېگەندە 2 ھەرپ بولۇشى كېرەك';

  @override
  String get changePassword => 'پارول ئۆزگەرتىش';

  @override
  String get changePasswordDescription =>
      'يېڭى پارولىڭىزنى بەلگىلەڭ، ھېسابات بىخەتەرلىكىنى كاپالەتلەڭ.';

  @override
  String get passwordRequirements => 'پارول تەلىپى';

  @override
  String get passwordLengthRequirement =>
      'پارول ئۇزۇنلۇقى 6-20 ھەرپ بولۇشى كېرەك';

  @override
  String get accountSettings => 'ھېسابات تەڭشىكى';

  @override
  String get changePasswordSubtitle => 'كىرىش پارولىڭىزنى ئۆزگەرتىڭ';

  @override
  String get newPasswordRequired => 'يېڭى پارولنى كىرگۈزۈڭ';

  @override
  String get confirmNewPassword => 'يېڭى پارولنى جەزملەشتۈرۈڭ';

  @override
  String get confirmNewPasswordRequired =>
      'يېڭى پارولنى جەزملەشتۈرۈشنى كىرگۈزۈڭ';

  @override
  String get changeAvatar => 'باش سۈرەت ئالماشتۇرۇش';

  @override
  String get uploading => 'يۈكلەۋاتىدۇ...';

  @override
  String get selectAvatar => 'باش سۈرەت تاللاش';

  @override
  String get takePhoto => 'رەسىم تارتىش';

  @override
  String get selectFromGallery => 'رەسىم ئالبومىدىن تاللاش';

  @override
  String get avatarUploadSuccess => 'باش سۈرەت يۈكلەش مۇۋەپپەقىيەتلىك';

  @override
  String get avatarUploadFailed => 'باش سۈرەت يۈكلەش مەغلۇپ بولدى';

  @override
  String get gender => 'جىنسى';

  @override
  String get notSet => 'بەلگىلەنمىگەن';

  @override
  String get birthday => 'تۇغۇلغان كۈن';

  @override
  String get profileSaveSuccess => 'ئۇچۇر ساقلاش مۇۋەپپەقىيەتلىك';

  @override
  String get saveFailed => 'ساقلاش مەغلۇپ بولدى';

  @override
  String get passwordChangeSuccess => 'پارول ئۆزگەرتىش مۇۋەپپەقىيەتلىك';

  @override
  String get cropAvatar => 'باش سۈرەت كېسىش';

  @override
  String get reselectImage => 'قايتا تاللاش';

  @override
  String get confirmCrop => 'جەزملەشتۈرۈش';

  @override
  String get cannotParseImageFile => 'رەسىم ھۆججىتىنى تەھلىل قىلالمايدۇ';

  @override
  String get loadUserProfileFailed => 'ئىشلەتكۈچى ئۇچۇرىنى يۈكلەش مەغلۇپ بولدى';

  @override
  String takePhotoFailed(String error) {
    return 'رەسىم تارتىش مەغلۇپ بولدى: $error';
  }

  @override
  String get avatarUploadFailedButProfileWillSave =>
      'باش سۈرەت يۈكلەش مەغلۇپ بولدى، ئەمما باشقا ئۇچۇرلار داۋاملىق ساقلىنىدۇ';

  @override
  String get loginExpiredPleaseRelogin =>
      'كىرىش ۋاقتى ئۆتۈپ كەتتى، قايتا كىرىڭ';

  @override
  String processImageFailed(String error) {
    return 'رەسىم بىر تەرەپ قىلىش مەغلۇپ بولدى: $error';
  }

  @override
  String get newPasswordMaxLength =>
      'يېڭى پارول 20 خانىدىن ئېشىپ كەتمەسلىكى كېرەك';

  @override
  String get userCardMemberStatusLabel => 'ئەزا ھالىتى';

  @override
  String get userCardExpiryDateLabel => 'ۋاقتى توشىدىغان ۋاقىت';

  @override
  String get userCardUidLabel => 'UID:';

  @override
  String get languageOptionChineseSimplified => 'خەنزۇچە';

  @override
  String get languageOptionUyghur => 'ئۇيغۇرچە';

  @override
  String get languageOptionEnglish => 'ئىنگلىزچە';

  @override
  String get languageOptionKazakh => 'قازاقچە';

  @override
  String get languageOptionRussian => 'رۇسچە';

  @override
  String get languageOptionFrench => 'فرانسۇزچە';

  @override
  String get languageOptionSpanish => 'ئىسپانچە';

  @override
  String get languageOptionCantonese => 'گۇاڭدۇڭچە';

  @override
  String get languageOptionArabic => 'ئەرەبچە';

  @override
  String get historyListEmpty => 'ھازىرچە تارىخ خاتىرىسى يوق';

  @override
  String get aiGuideVoiceQueryButton => 'ئاۋاز سوئال';

  @override
  String get aiGuidePhotoQueryButton => 'رەسىم تارتىپ سوئال';

  @override
  String get aiGuideQueryHint =>
      'ساغلاملىق ياردەمچىسىنى ئىشلىتىشىڭىزنى قارشى ئالىمىز، تۆۋەندىكى كۇنۇپكىنى بېسىپ تۇرۇپ رەسىم تارتىپ سوئال قويۇڭ';

  @override
  String get faceToFaceSelectLanguagesHint => 'ئىككى تەرەپنىڭ تىلىنى تاللاڭ';

  @override
  String resendCodeTimerLabel(String seconds) {
    return 'قايتا ئەۋەتىش ($seconds سېكۇنت)';
  }

  @override
  String get guestUser => 'مېھمان ئىشلەتكۈچى';

  @override
  String get pleaseLogin => 'ئاۋۋال كىرىڭ';

  @override
  String get membershipStatus => 'ئەزا ھالىتى';

  @override
  String get expiresOn => 'ۋاقتى توشىدىغان ۋاقىت';

  @override
  String get editProfileButton => 'ئۇچۇر تەھرىرلەش';

  @override
  String get notLoggedInUser => 'كىرمىگەن ئىشلەتكۈچى';

  @override
  String get verificationCodeLoginTitle => 'دەلىللەش كودى كىرىش';

  @override
  String get phoneInputLabel => 'تېلېفون نومۇرى';

  @override
  String get phoneInputHint => 'تېلېفون نومۇرىنى كىرگۈزۈڭ';

  @override
  String get codeInputLabel => 'دەلىللەش كودى';

  @override
  String get codeInputHint => 'دەلىللەش كودىنى كىرگۈزۈڭ';

  @override
  String resendCodeTimerButton(String seconds) {
    return 'قايتا ئەۋەتىش ($seconds سېكۇنت)';
  }

  @override
  String get loginButton => 'كىرىش';

  @override
  String get autoRegisterHint =>
      'تىزىملانمىغان تېلېفون نومۇرى ئاپتوماتىك تىزىملىنىدۇ';

  @override
  String get reminderTitle => 'ئەسكەرتىش';

  @override
  String get loginRequiredForDistributionMessage =>
      'ئاۋۋال كىرىپ ئاندىن تارقىتىش باشقۇرۇش ئىقتىدارىنى ئىشلىتىڭ';

  @override
  String get distributionAccessDeniedMessage =>
      'ھېساباتتا تارقاتقۇچى ھوقۇقى يوق، تارقاتقۇچى بولۇشقا ئىلتىماس قىلالايسىز';

  @override
  String get goToLoginButton => 'كىرىشكە بېرىش';

  @override
  String get applyButton => 'ئىلتىماس';

  @override
  String get typeMessageHint => 'ئۇچۇر كىرگۈزۈڭ';

  @override
  String verificationCodeSentSeconds(String seconds) {
    return '$seconds سېكۇنت';
  }

  @override
  String get welcomeBackTitle => 'قايتىپ كەلگىنىڭىزنى قارشى ئالىمىز';

  @override
  String get loginWithPhoneSubtitle =>
      'تېلېفون نومۇرىڭىز بىلەن ھېساباتقا كىرىڭ';

  @override
  String get registerAccountButton => 'ھېساب خەتلىتىش';

  @override
  String get passwordLoginButton => 'پارول كىرىش';

  @override
  String get loginAgreementText =>
      'كىرىش «ئىشلەتكۈچى كېلىشىمى» ۋە «شەخسىيەت سىياسىتى» گە قوشۇلغانلىقىڭىزنى بىلدۈرىدۇ';

  @override
  String get loginFailed => 'كىرىش مەغلۇپ بولدى';

  @override
  String loginRequiredForFeature(String featureName) {
    return 'ئاۋۋال كىرىپ ئاندىن $featureName ئىقتىدارىنى ئىشلىتىڭ';
  }

  @override
  String get loginRequiredGeneral =>
      'بۇ ئىقتىدار كىرگەندىن كېيىنلا ئىشلەتكىلى بولىدۇ، ئاۋۋال كىرىڭ';

  @override
  String get loginButtonText => 'كىرىش';

  @override
  String get applicationSubmitted => 'ئىلتىماس تاپشۇرۇلدى، تەكشۈرۈشنى كۈتۈڭ';

  @override
  String get applicationFailed => 'ئىلتىماس مەغلۇپ بولدى';

  @override
  String get distributorBenefitDescription =>
      'تارقىتىش ئادىمى بولغاندىن كېيىن، مەھسۇلاتلارنى تەشۋىق قىلىپ كومىسسىيە پايدىسى ئالالايسىز';

  @override
  String get aiGuidePhotoQuestion => 'ساغلاملىق ياردەمچىسى رەسىم تارتىپ سوئال';

  @override
  String get aiGuideVoiceQuestion => 'ساغلاملىق ياردەمچىسى ئاۋاز سوئال';

  @override
  String get recordingStartFailedCheckPermission =>
      'ئاۋاز خاتىرىلەش باشلاش مەغلۇپ بولدى، مىكروفون ھوقۇقىنى تەكشۈرۈڭ';

  @override
  String get adjustFontSize => 'خەت چوڭلۇقىنى تەڭشەش';

  @override
  String get fontPreviewText => 'خەت نۇسخا ئالدىن كۆرۈش Font Preview';

  @override
  String get smallSize => 'كىچىك';

  @override
  String get largeSize => 'چوڭ';

  @override
  String currentSizeLabel(String size) {
    return 'ھازىرقى چوڭلۇقى: $size';
  }

  @override
  String get smallSizeLabel => 'كىچىك';

  @override
  String get mediumSizeLabel => 'ئوتتۇراھال';

  @override
  String get largeSizeLabel => 'چوڭ';

  @override
  String get vipMembershipTitle => 'VIP ئەزالىق ئىمتىيازى';

  @override
  String get higherAccuracy => 'ئېنىقلىق يۇقىرىراق';

  @override
  String get adFree => 'ئېلان قاچىلاش يوق';

  @override
  String get unlimitedUsage => 'چەكسىز ئىشلىتىش';

  @override
  String get monthlyMembership => 'ئايلىق ئەزالىق';

  @override
  String get annualMembership => 'يىللىق ئەزالىق';

  @override
  String savePercentage(String percentage) {
    return '$percentage% تېجەش';
  }

  @override
  String get gettingPriceInfo => 'باھا ئۇچۇرىنى ئېلىۋاتىدۇ...';

  @override
  String get originalPrice => 'ئەسلى باھا';

  @override
  String get approximately => 'تەخمىنەن';

  @override
  String get monthlyUnit => 'ئاي';

  @override
  String get yearlyUnit => 'يىل';

  @override
  String get perMonth => '/ئاي';

  @override
  String get perYear => '/يىل';

  @override
  String get activateVipNow => 'ئەزا ئېچىڭ VIP دەرھال';

  @override
  String get serviceTermsAgreement =>
      'ئېچىش «ئىشلەتكۈچى خىزمەت كېلىشىمى» ۋە «شەخسىيەت سىياسىتى» گە قوشۇلغانلىقىڭىزنى بىلدۈرىدۇ';

  @override
  String get monthlyMemberPackage => 'ئايلىق ئەزالىق توپى';

  @override
  String get annualMemberPackage => 'يىللىق ئەزالىق توپى';

  @override
  String get oneMonthVipPrivileges => 'بىر ئاي VIP ئىمتىيازى';

  @override
  String get oneYearVipPrivileges =>
      'بىر يىل VIP ئىمتىيازى، ئاپتوماتىك يېڭىلاشنى قوللايدۇ';

  @override
  String get priceLoadFailed =>
      'باھا ئۇچۇرىنى ئېلىش مەغلۇپ بولدى، كۆڭۈلدىكى باھانى كۆرسىتىدۇ';

  @override
  String get priceInfoLoading => 'باھا ئۇچۇرى يۈكلىنىۋاتىدۇ، سەل كۈتۈڭ...';

  @override
  String aboutToActivate(String packageName) {
    return '$packageName نى ئېچىشقا ئاز قالدى، سەل كۈتۈڭ...';
  }

  @override
  String get annualVipMember => 'يىللىق VIP ئەزا';

  @override
  String get monthlyVipMember => 'ئايلىق VIP ئەزا';

  @override
  String get lifetimeVipMember => 'مەڭگۈلۈك VIP ئەزا';

  @override
  String get fontSizeSmall => 'كىچىك';

  @override
  String get fontSizeMedium => 'ئوتتۇراھال';

  @override
  String get fontSizeLarge => 'چوڭ';

  @override
  String get myOrders => 'زاكاسلىرىم';

  @override
  String get chatHistory => 'پاراڭ خاتىرىسى  ';

  @override
  String get doctorManagement => 'رەقەملىك تەننى باشقۇرۇش';

  @override
  String get adminManagement => 'باشقۇرغۇچى باشقۇرۇش';

  @override
  String get myLikes => ' ياقتۇرغانلىرىم';

  @override
  String get myFavorites => ' يىغقانلىرىم';

  @override
  String get shoppingCart => 'مال ھارۋىسى';

  @override
  String get distributionManagementFeature => 'تارقىتىش باشقۇرۇش';

  @override
  String get adminManagementFeature => 'باشقۇرغۇچى باشقۇرۇش';

  @override
  String get doctorManagementFeature => 'رەقەملىك تەنلەرنى باشقۇرۇش';

  @override
  String get onlyDoctorUsersCanAccess =>
      'پەقەت رەقەملىك تەن ئىشلەتكۈچىلەرلا رەقەملىك تەنلەرنى باشقۇرۇش ئىقتىدارىنى زىيارەت قىلالايدۇ';

  @override
  String get viewShoppingCartFeature => 'سېتىۋېلىش ھارۋىسىنى كۆرۈش';

  @override
  String get pleaseSelectItemsToDelete => 'ئۆچۈرىدىغان مەھسۇلاتلارنى تاللاڭ';

  @override
  String get confirmDelete => 'ئۆچۈرۈشنى جەزملەشتۈرۈش';

  @override
  String confirmDeleteItems(int count) {
    return 'تاللانغان $count دانە مەھسۇلاتنى ئۆچۈرۈشنى جەزملەشتۈرەمسىز؟';
  }

  @override
  String get deleteSuccess => 'ئۆچۈرۈش مۇۋەپپەقىيەتلىك';

  @override
  String deleteFailed(String error) {
    return 'ئۆچۈرۈش مەغلۇپ بولدى';
  }

  @override
  String get pleaseSelectItemsToCheckout =>
      'ھېساپلايدىغان مەھسۇلاتلارنى تاللاڭ';

  @override
  String get cartTitle => 'سېتىۋېلىش ھارۋىسى';

  @override
  String get myOrdersTitle => ' بۇيۇرتمىلىرىم';

  @override
  String get myOrdersFeature => ' بۇيۇرتمىلىرىم';

  @override
  String get orderStatusAll => 'ھەممىسى';

  @override
  String get orderStatusPending => 'تۆلىنىدىغان';

  @override
  String get orderStatusPendingShipment => 'يوللىنىدىغان';

  @override
  String get orderStatusShipped => 'يوللانغان';

  @override
  String get orderStatusCompleted => 'تاماملانغان';

  @override
  String get orderStatusCancelled => 'بىكار قىلىنغان';

  @override
  String get orderStatusUnknown => 'نامەلۇم ھالەت';

  @override
  String get payStatusUnpaid => 'تۆلەنمىدى';

  @override
  String get payStatusPaid => 'تۆلەندى';

  @override
  String get payStatusRefunded => 'قايتۇرۇلدى';

  @override
  String get product => 'مەھسۇلات';

  @override
  String get noOrders => 'ھازىرچە  زاكاس يوق';

  @override
  String get adminManagementTitle => 'باشقۇرغۇچى باشقۇرۇش';

  @override
  String get doctorManagementTab => 'رەقەملىك تەنلەرنى باشقۇرۇش';

  @override
  String get productReviewTab => 'مەھسۇلات تەكشۈرۈش';

  @override
  String get orderManagementTab => ' زاكاس باشقۇرۇش';

  @override
  String get noDoctorData => 'ھازىرچە رەقەملىك تەننىڭ ئۇچۇرى يوق';

  @override
  String get loadDoctorListFailed =>
      'رەقەملىك تەن تىزىملىكىنى يۈكلەش مەغلۇپ بولدى';

  @override
  String get addDoctor => 'رەقەملىك تەن قوشۇش';

  @override
  String get editDoctor => 'رەقەملىك تەن تەھرىرلەش';

  @override
  String get deleteDoctor => 'رەقەملىك تەننى ئۆچۈرۈش';

  @override
  String get confirmDeleteDoctor => 'رەقەملىك تەننى ئۆچۈرۈشنى جەزملەشتۈرۈش';

  @override
  String deleteDoctorConfirmMessage(String doctorName) {
    return 'رەقەملىك تەن $doctorName نى ئۆچۈرۈشنى جەزملەشتۈرەمسىز؟ بۇ مەشغۇلاتنى قايتۇرۇشقا بولمايدۇ.';
  }

  @override
  String get deleteDoctorSuccess => 'رەقەملىك تەننى ئۆچۈرۈش مۇۋەپپەقىيەتلىك';

  @override
  String get deleteDoctorFailed => 'رەقەملىك تەننى ئۆچۈرۈش مەغلۇپ بولدى';

  @override
  String get detailedInfo => 'تەپسىلىي ئۇچۇر';

  @override
  String get aiSettings => 'AI تەڭشىكى';

  @override
  String get statusSettings => 'ھالەت تەڭشىكى';

  @override
  String get enterDoctorName => 'رەقەملىك تەن ئىسمىنى كىرگۈزۈڭ';

  @override
  String get specialty => 'ئۇستا ساھە';

  @override
  String get enterSpecialty =>
      'ئۇستا ساھە نامىنى كىرگۈزۈڭ، مەسىلەن: يۈرەك قان تومۇر ئىچكى كىسەللىكلىرى';

  @override
  String get description => 'چۈشەندۈرۈش';

  @override
  String get enterDescription =>
      'رەقەملىك تەننىڭ قىسقىچە تونۇشتۇرۇشىنى كىرگۈزۈڭ';

  @override
  String get enterDetailedDescription =>
      'مەھسۇلاتنىڭ تەپسىلىي چۈشەندۈرۈشىنى كىرگۈزۈڭ، تەركىبى، ئۈنۈمى، ئىشلىتىش ئۇسۇلى قاتارلىقلارنى ئۆز ئىچىگە ئالىدۇ';

  @override
  String get addSpecialty => 'ئۇستا ساھە قوشۇش';

  @override
  String get enterSpecialtyField =>
      'ئۇستا ساھەنى كىرگۈزۈڭ، مەسىلەن: يۈرەك كېسىلى';

  @override
  String get deleteSpecialty => 'بۇ ئۇستا ساھەنى ئۆچۈرۈش';

  @override
  String get systemPrompt => 'سىستېما ئەسكەرتىش سۆزى';

  @override
  String get enterSystemPrompt => 'AI سىستېما ئەسكەرتىش سۆزىنى كىرگۈزۈڭ';

  @override
  String get avatarUrl => 'باش سۈرەت ئۇلىنىشى';

  @override
  String get enterAvatarUrl => 'باش سۈرەت رەسىم ئۇلىنىشىنى كىرگۈزۈڭ';

  @override
  String get llmModelName => 'AI مودېل نامى';

  @override
  String get enterLlmModelName => 'AI مودېل نامىنى كىرگۈزۈڭ';

  @override
  String get enterYearsOfExperience => 'خىزمەت يىلىنى كىرگۈزۈڭ';

  @override
  String get rating => 'باھالاش';

  @override
  String get enterRating => 'باھالىنىش پۇنكتىنى كىرگۈزۈڭ (1-5)';

  @override
  String get digitalHumanUrl => 'رەقەملىك ئادەم ئۇلىنىشى';

  @override
  String get enterDigitalHumanUrl => 'رەقەملىك ئادەم ئۇلىنىشىنى كىرگۈزۈڭ';

  @override
  String get phone => 'ئالاقە تېلېفونى';

  @override
  String get enterPhone => 'ئالاقە تېلېفونىنى كىرگۈزۈڭ';

  @override
  String get enterAddress => 'ئادرېسنى كىرگۈزۈڭ';

  @override
  String get isActive => 'ئاكتىپ';

  @override
  String get saveDoctor => 'رەقەملىك تەننى ساقلاش';

  @override
  String get saving => 'ساقلاۋاتىدۇ...';

  @override
  String get createDoctorSuccess => 'رەقەملىك تەن قۇرۇش مۇۋەپپەقىيەتلىك';

  @override
  String get updateDoctorSuccess => 'رەقەملىك تەننى يېڭىلاش مۇۋەپپەقىيەتلىك';

  @override
  String get createDoctorFailed => 'رەقەملىك تەن قۇرۇش مەغلۇپ بولدى';

  @override
  String get updateDoctorFailed => 'رەقەملىك تەن يېڭىلاش مەغلۇپ بولدى';

  @override
  String get enabled => 'قوزغىتىلغان';

  @override
  String get disabled => 'چەكلەنگەن';

  @override
  String get enterValidPhone => 'ئىناۋەتلىك تېلېفون نومۇرىنى كىرگۈزۈڭ';

  @override
  String get doctorAvatar => 'رەقەملىك تەن باش سۈرىتى';

  @override
  String get uploadAvatar => 'باش سۈرەت يۈكلەش';

  @override
  String get enableStatus => 'قوزغىتىش ھالىتى';

  @override
  String get doctorEnabledDescription =>
      'رەقەملىك تەن ھازىر قوزغىتىلغان ھالەتتە، ئىشلەتكۈچىلەر ئۇنىڭ بىلەن سۆزلىشەلەيدۇ';

  @override
  String get doctorDisabledDescription =>
      'رەقەملىك تەن ھازىر چەكلەنگەن ھالەتتە، ئىشلەتكۈچىلەر ئۇنىڭ بىلەن سۆزلىشەلمەيدۇ';

  @override
  String get specialtyInputHint =>
      'ئەسكەرتىش: ھەر بىر كىرگۈزۈش رامكىسىغا بىر ئۇستا ساھەنى يېزىڭ، ساقلىغاندا ئاپتوماتىك بىرلەشتۈرۈلىدۇ';

  @override
  String get confirmExit => 'چېكىنىشنى جەزىملەشتۇرەمسىز';

  @override
  String get unsavedChangesWarning =>
      'ساقلانمىغان ئۆزگىرىشلىرىڭىز بار، چېكىنىشنى جەزملەشتۈرەمسىز؟';

  @override
  String get exit => 'چېكىنىش';

  @override
  String get uploadFailed => 'يۈكلەش مەغلۇپ بولدى';

  @override
  String get supportedImageFormats =>
      'JPG، PNG فورماتىنى قوللايدۇ، چوڭلۇقى 5MB دىن ئاشمايدۇ';

  @override
  String get collapse => 'قاتلاش';

  @override
  String get multilingual => 'كۆپ تىللىق';

  @override
  String get all => 'ھەممىسى';

  @override
  String get pending => 'كۈتۈۋاتىدۇ';

  @override
  String get approved => 'تەستىقلاندى';

  @override
  String get rejected => 'رەت قىلىندى';

  @override
  String get offline => 'تاختىدىن چۈشۈرۈلگەن';

  @override
  String loadDataFailed(String error) {
    return 'سانلىق مەلۇمات يۈكلەش مەغلۇپ بولدى: $error';
  }

  @override
  String get loadDoctorMultilingualDataFailed =>
      'رەقەملىك تەنلەرنىىڭ كۆپ تىللىق سانلىق مەلۇماتىنى يۈكلەش مەغلۇپ بولدى';

  @override
  String get doctorCreationCompleteCallback =>
      'رەقەملىك تەننى قۇرۇش تاماملاندى';

  @override
  String get enterModelName => 'مودېل نامىنى كىرگۈزۈڭ';

  @override
  String get imageSizeExceedsLimit =>
      'رەسىم چوڭلۇقى 5MB دىن ئېشىپ كەتمەسلىكى كېرەك';

  @override
  String get doctorManagementTitle => 'رەقەملىك تەنلەرنى باشقۇرۇش';

  @override
  String get productManagementTab => 'مەھسۇلات باشقۇرۇش';

  @override
  String get dataOverview => 'سانلىق مەلۇمات ئومۇمىي كۆرۈنۈشى';

  @override
  String get products => 'مەھسۇلاتلار';

  @override
  String get pendingReview => 'تەكشۈرۈشنى كۈتۈۋاتىدۇ';

  @override
  String get totalProducts => 'جەمئىي مەھسۇلات';

  @override
  String get totalSales => 'جەمئىي سېتىش مىقدارى';

  @override
  String get totalOrders => 'جەمئىي بۇيۇرتما';

  @override
  String get approvedProducts => 'تەستىقلانغان';

  @override
  String get rejectedProducts => 'رەت قىلىنغان';

  @override
  String get reviewStatus => 'تەكشۈرۈش ھالىتى';

  @override
  String get inventory => 'ئامبار';

  @override
  String get salesVolume => 'سېتىش مىقدارى';

  @override
  String get orderOverview => ' زاكاس ئومۇمىي كۆرۈنۈشى';

  @override
  String get shippingStatus => 'يوللاش ھالىتى';

  @override
  String get paymentStatus => 'تۆلەش ھالىتى';

  @override
  String get totalOrderNumber => 'جەمئىي زاكاس نومۇرى';

  @override
  String get customer => 'خېرىدار';

  @override
  String get pendingPayment => 'تۆلىنىۋاتقان';

  @override
  String get pendingShipment => 'يوللىنىدىغان';

  @override
  String get shipped => 'يوللاندى';

  @override
  String get completed => 'تاماملانغان';

  @override
  String get cancelled => 'بىكار قىلىندى';

  @override
  String get reviewApproved => 'تەكشۈرۈش تەستىقلاندى';

  @override
  String get reviewRejected => 'تەكشۈرۈش رەت قىلىندى';

  @override
  String get offShelf => 'سېتىشتىن قالدۇرۇش';

  @override
  String get addProduct => 'مەھسۇلات قوشۇش';

  @override
  String get editProduct => 'مەھسۇلات تەھرىرلەش';

  @override
  String get enterProductName => 'مەھسۇلات نامىنى كىرگۈزۈڭ';

  @override
  String get productDescription => 'مەھسۇلات قىسقىچە تونۇشتۇرۇشى';

  @override
  String get enterProductDescription =>
      'مەھسۇلات قىسقىچە تونۇشتۇرۇشىنى كىرگۈزۈڭ';

  @override
  String get productCategory => 'مەھسۇلات تۈرى';

  @override
  String get enterProductCategory =>
      'مەھسۇلات تۈرىنى كىرگۈزۈڭ، مەسىلەن: ساغلاملىق مەھسۇلاتى، دورا قاتارلىقلار';

  @override
  String get enterManufacturer => 'ئىشلەپچىقارغۇچى نامىنى كىرگۈزۈڭ';

  @override
  String get productMainImage => 'مەھسۇلات ئاساسىي رەسىمى';

  @override
  String get priceInfo => 'باھا ئۇچۇرى';

  @override
  String get currentPrice => 'ھازىرقى باھا';

  @override
  String get enterCurrentPrice => 'ھازىرقى باھانى كىرگۈزۈڭ';

  @override
  String get enterOriginalPrice => 'ئەسلى باھانى كىرگۈزۈڭ (تاللاشچان)';

  @override
  String get inventoryInfo => 'ئامبار ئۇچۇرى';

  @override
  String get inventoryCount => 'ئامبار سانى';

  @override
  String get enterInventoryCount => 'ئامبار سانىنى كىرگۈزۈڭ';

  @override
  String get productDetailImages => 'مەھسۇلات تەپسىلات رەسىملىرى';

  @override
  String get productCreatedSuccess => 'مەھسۇلات قۇرۇش مۇۋەپپەقىيەتلىك';

  @override
  String get productUpdatedSuccess => 'مەھسۇلات يېڭىلاش مۇۋەپپەقىيەتلىك';

  @override
  String get loadProductMultilingualDataFailed =>
      'مەھسۇلاتنىڭ كۆپ تىللىق ئۇچۇرىنى ئىلىش مەغلۇپ بولدى';

  @override
  String get orderNumber => ' زاكاس نومۇرى';

  @override
  String get orderTotal => ' زاكاس جەمئىي';

  @override
  String customerLabel(String customer) {
    return 'خېرىدار: $customer';
  }

  @override
  String get orderNumberShort => 'زاكاس نومۇرى';

  @override
  String get paidStatus => 'تۆلەندى';

  @override
  String get unpaidStatus => 'تۆلەنمىدى';

  @override
  String get trackingNumber => 'زاكاس ئەۋەتىش نومۇرى';

  @override
  String get networkImage => 'تور رەسىمى';

  @override
  String get maxSixImages => 'ئەڭ كۆپ 6 رەسىم';

  @override
  String get addImage => 'رەسىم قوشۇش';

  @override
  String get expressInfo => 'زاكاس ئەۋەتكەن ئۇچۇرى';

  @override
  String get expressNumber => 'زاكاس ئەۋەتكەن نومۇرى';

  @override
  String get expressCompany => 'زاكاس ئەۋەتكەن شىركىتى';

  @override
  String get shippingNote => 'يوللاش ئەسكەرتىشى';

  @override
  String get noShippingInfo => 'ھازىرچە زاكاس ئەۋەتكەن ئۇچۇرى يوق';

  @override
  String orderCount(int count) {
    return '$count زاكاس';
  }

  @override
  String get imageSelected => 'رەسىم تاللاندى';

  @override
  String get unsavedChangesMessage =>
      'ساقلانمىغان ئۆزگەرتكەنلرىڭىز بار، چىقىشنى جەزملەشتۈرەمسىز؟';

  @override
  String get confirmAction => 'جەزملەڭ';

  @override
  String get noProductsMessage => 'ھازىرچە مەھسۇلات يوق';

  @override
  String get addFirstProductHint =>
      'ئوڭ ئاستىدىكى كۇنۇپكىنى چېكىپ تۇنجى مەھسۇلاتىڭىزنى قوشۇڭ';

  @override
  String get noOrdersMessage => ' زاكاس سانلىق مەلۇماتى بۇ يەردە كۆرسىتىلىدۇ';

  @override
  String get ordersWillShowHere =>
      'خېرىدارلار سىزنىڭ مەھسۇلاتلىرىڭىزنى سېتىۋالغاندىن كېيىن زاكاس بۇ يەردە كۆرسىتىلىدۇ';

  @override
  String get reviewSuccess => 'تەكشۈرۈش مۇۋەپپەقىيەتلىك';

  @override
  String get reviewFailed => 'تەكشۈرۈش مەغلۇپ بولدى';

  @override
  String get batchReviewSuccess => 'توپ تەكشۈرۈش مۇۋەپپەقىيەتلىك';

  @override
  String get batchReviewFailed => 'توپ تەكشۈرۈش مەغلۇپ بولدى';

  @override
  String get allDoctors => 'بارلىق رەقەملىك تەنلەر';

  @override
  String get pleaseSelectProducts => 'تەكشۈرىدىغان مەھسۇلاتلارنى تاللاڭ';

  @override
  String get batchApproved => 'توپ تەكشۈرۈش تەستىقلاندى';

  @override
  String get batchRejected => 'توپ تەكشۈرۈش رەت قىلىندى';

  @override
  String get batchApprove => 'توپ تەستىقلاش';

  @override
  String get batchReject => 'توپ رەت قىلىش';

  @override
  String get deselectAll => 'ھەممىنى بىكار قىلىش';

  @override
  String get exitSelection => 'تاللاشتىن چىقىش';

  @override
  String get batchSelection => 'توپ تاللاش';

  @override
  String get reviewStatistics => 'تەكشۈرۈش ستاتىستىكىسى';

  @override
  String get total => 'جەمئىي سانى';

  @override
  String get sales => 'سېتىش مىقدارى';

  @override
  String get approve => 'تەستىقلاش';

  @override
  String get reject => 'رەت قىلىش';

  @override
  String get rejectReview => 'تەكشۈرۈشنى رەت قىلىش';

  @override
  String confirmRejectProduct(String productName) {
    return 'مەھسۇلات \"$productName\" نى رەت قىلىشنى جەزملەشتۈرەمسىز؟';
  }

  @override
  String get rejectReason => 'رەت قىلىش سەۋەبى (تاللاشچان)';

  @override
  String get confirmReject => 'رەت قىلىشنى جەزملەشتۈرۈش';

  @override
  String get filterDoctors => 'رەقەملىك تەنلەرنى سۈزۈش';

  @override
  String get loadProductDetailFailed =>
      'مەھسۇلات تەپسىلاتىنى يۈكلەش مەغلۇپ بولدى';

  @override
  String get unknownDoctor => 'نامەلۇم رەقەملىك تەن';

  @override
  String get createdAt => 'قۇرۇلغان ۋاقىت';

  @override
  String get updatedAt => 'يېڭىلانغان ۋاقىت';

  @override
  String get productImages => 'مەھسۇلات رەسىملىرى';

  @override
  String get productSpecifications => 'مەھسۇلات تەخنىكىلىق كۆرسەتكۈچلىرى';

  @override
  String get reviewInfo => 'تەكشۈرۈش ئۇچۇرى';

  @override
  String get productId => 'مەھسۇلات ID';

  @override
  String get viewShipping => 'يۈك ئەۋەتىشنى كۆرۈش';

  @override
  String get cancelOrder => 'بۇيۇرتمىنى بىكار قىلىش';

  @override
  String get payNow => 'دەرھال تۆلەش';

  @override
  String get confirmCancel => 'بىكار قىلىشنى جەزملەشتۈرۈش';

  @override
  String get confirmCancelOrder =>
      'بۇ بۇيۇرتمىنى بىكار قىلىشنى جەزملەشتۈرەمسىز؟';

  @override
  String get orderCancelled => ' زاكاس بىكار قىلىندى';

  @override
  String cancelOrderFailed(String error) {
    return 'زاكاسنى بىكار قىلىش مەغلۇپ بولدى: $error';
  }

  @override
  String get getPaymentParamsFailed => 'تۆلەش پارامېتىرىنى ئېلىش مەغلۇپ بولدى';

  @override
  String get paymentCancelled => 'تۆلەش بىكار قىلىندى';

  @override
  String get confirmPayment => 'تۆلەشنى جەزملەشتۈرۈش';

  @override
  String get paymentAmount => 'تۆلەش مىقدارى';

  @override
  String get confirmPaymentButton => 'تۆلەشنى جەزملەشتۈرۈش';

  @override
  String get orderPaidSuccessfully =>
      'سىزنىڭ بۇيۇرتمىڭىز مۇۋەپپەقىيەتلىك تۆلەندى';

  @override
  String get currentConversationDeleted => 'نۆۋەتتىكى سۆزلىشىش ئۆچۈرۈلدى';

  @override
  String get newConversation => 'يېڭى سۆزلىشىش';

  @override
  String get refreshSuccess => 'يېڭىلاش مۇۋەپپەقىيەتلىك';

  @override
  String refreshFailed(String error) {
    return 'يېڭىلاش مەغلۇپ بولدى: $error';
  }

  @override
  String get titleUpdateSuccess => 'ماۋزۇ يېڭىلاش مۇۋەپپەقىيەتلىك';

  @override
  String titleUpdateFailed(String error) {
    return 'ماۋزۇ يېڭىلاش مەغلۇپ بولدى: $error';
  }

  @override
  String get conversationNotFound => 'سۆزلىشىش مەۋجۇت ئەمەس';

  @override
  String get chatHistoryPageTitle => 'پاراڭ خاتىرىسى  ';

  @override
  String get noChatRecordsForDate => 'بۇ چېسلادا ھازىرچە پاراڭ خاتىرىسى يوق';

  @override
  String get enterNewTitle => 'يېڭى تىيما  كىرگۈزۈڭ';

  @override
  String get year => 'يىل';

  @override
  String get month => 'ئاي';

  @override
  String get monthLabel => 'ئاي:';

  @override
  String get yearLabel => 'يىل:';

  @override
  String get weekdayMon => 'دۈشەنبە';

  @override
  String get weekdayTue => 'سەيشەنبە';

  @override
  String get weekdayWed => 'چارشەنبە';

  @override
  String get weekdayThu => 'پەيشەنبە';

  @override
  String get weekdayFri => 'جۈمە';

  @override
  String get weekdaySat => 'شەنبە';

  @override
  String get weekdaySun => 'يەكشەنبە';

  @override
  String get selectYearMonth => 'يىل ئاي تاللاش';

  @override
  String get ok => 'جەزملەشتۈرۈش';

  @override
  String get digitalHumanChatInDevelopment =>
      'رەقەملىك ئادەم AI پاراڭ بېتى ئىشلەپ چىقىلىۋاتىدۇ...';

  @override
  String get voiceTranslationFeature => 'ئاۋاز تەرجىمە';

  @override
  String get chatFeature => 'پاراڭ ئىقتىدارى';

  @override
  String get voiceFeature => 'ئاۋاز ئىقتىدارى';

  @override
  String get chatHistoryFeature => 'پاراڭ تارىخى';

  @override
  String get voiceRecognitionSuccess => 'ئاۋاز تونۇش مۇۋەپپەقىيەتلىك';

  @override
  String get voiceRecognitionFailed => 'ئاۋاز تونۇش مەغلۇپ بولدى، قايتا سىناڭ';

  @override
  String get addTextDescriptionOrSendImage =>
      'تېكىست چۈشەندۈرۈش قوشۇڭ ياكى بىۋاسىتە رەسىم ئەۋەتىڭ';

  @override
  String get refresh => 'يېڭىلاش';

  @override
  String get noChatRecords => 'ھازىرچە پاراڭ خاتىرىسى يوق';

  @override
  String get filterDoctorsLabel => 'رەقەملىك تەنلارنى سۈزۈش';

  @override
  String get allDoctorsOption => 'بارلىق رەقەملىك تەنلەر';

  @override
  String get unknownDoctorLabel => 'نامەلۇم رەقەملىك تەن';

  @override
  String quantityLabel(int quantity) {
    return 'سانى: $quantity';
  }

  @override
  String doctorLabel(String doctor) {
    return 'رەقەملىك تەن: $doctor';
  }

  @override
  String unitPriceLabel(String price) {
    return 'بىرلىك باھاسى: ¥$price';
  }

  @override
  String totalAmountLabel(String amount) {
    return 'جەمئىي: ¥$amount';
  }

  @override
  String orderNumberLabel(String orderNumber) {
    return ' زاكاس نومۇرى: $orderNumber';
  }

  @override
  String get adminShipAction => 'باشقۇرغۇچى يوللاش';

  @override
  String get markCompleteAction => 'تاماملاندى دەپ بەلگىلەش';

  @override
  String get markCancelAction => 'بىكار قىلىندى دەپ بەلگىلەش';

  @override
  String get deleteOrderAction => 'بۇيۇرتمىنى ئۆچۈرۈش';

  @override
  String get totalOrdersLabel => 'جەمئىي بۇيۇرتما';

  @override
  String get totalSalesLabel => 'جەمئىي سېتىش مىقدارى';

  @override
  String get completedOrdersLabel => 'تاماملاندى';

  @override
  String get pendingPaymentLabel => 'تۆلەنمىگەنلەر';

  @override
  String get pendingShipmentLabel => 'يوللانمىغانلار';

  @override
  String get cancelledOrdersLabel => 'بىكار قىلىندى';

  @override
  String get ordersUnit => 'دانە';

  @override
  String get orderStatusUpdateSuccess =>
      ' زاكاس ھالىتى يېڭىلاش مۇۋەپپەقىيەتلىك';

  @override
  String updateFailed(String error) {
    return 'يېڭىلاش مەغلۇپ بولدى: $error';
  }

  @override
  String get noOrdersTitle => 'ھازىرچە  زاكاس يوق';

  @override
  String get batchOperationSuccess => 'توپ مەشغۇلات مۇۋەپپەقىيەتلىك';

  @override
  String batchOperationFailed(String error) {
    return 'توپ مەشغۇلات مەغلۇپ بولدى: $error';
  }

  @override
  String get confirmDeleteTitle => 'ئۆچۈرۈشنى جەزملەشتۈرۈش';

  @override
  String get confirmDeleteMessage =>
      'بۇ بۇيۇرتمىنى ئۆچۈرۈشنى جەزملەشتۈرەمسىز؟ بۇ مەشغۇلاتنى قايتۇرۇشقا بولمايدۇ.';

  @override
  String get cancelAction => 'بىكار قىلىش';

  @override
  String get deleteAction => 'ئۆچۈرۈش';

  @override
  String batchOperationsTitle(int count) {
    return 'توپ مەشغۇلات ($count دانە بۇيۇرتما)';
  }

  @override
  String get markAsCompletedAction => 'تاماملاندى دەپ بەلگىلەش';

  @override
  String get markAsCancelledAction => 'بىكار قىلىندى دەپ بەلگىلەش';

  @override
  String get markAsShippedAction => 'يوللاندى دەپ بەلگىلەش';

  @override
  String get ordersUnitSuffix => 'دانە';

  @override
  String get orderStatusUpdateSuccessMessage =>
      ' زاكاس ھالىتى يېڭىلاش مۇۋەپپەقىيەتلىك';

  @override
  String get paymentStatusUpdateSuccessMessage =>
      'تۆلەش ھالىتى يېڭىلاش مۇۋەپپەقىيەتلىك';

  @override
  String get orderDeleteSuccessMessage => ' زاكاس ئۆچۈرۈش مۇۋەپپەقىيەتلىك';

  @override
  String get pleaseSelectOrdersMessage =>
      'ئاۋۋال مەشغۇلات قىلىدىغان بۇيۇرتمىلارنى تاللاڭ';

  @override
  String get markAsPaidAction => 'تۆلەنگەن';

  @override
  String get orderStatusPendingPayment => 'تۆلەنمىگەن';

  @override
  String get orderStatusPaid => 'تۆلەنگەن';

  @override
  String get orderDetailTitle => ' زاكاس تەپسىلاتى';

  @override
  String get markAsRefundAction => 'قايتۇرۇلغان';

  @override
  String get markAsCompleteAction => 'تاماملانغان';

  @override
  String get markAsCancelAction => 'بىكار قىلىنغان';

  @override
  String get waitingForPaymentDescription =>
      'خېرىدارنىڭ تۆلەش تاماملىشىنى كۈتۈۋاتىدۇ';

  @override
  String get waitingForShipmentDescription =>
      'رەقەملىك تەننىڭ يوللىشىنى كۈتۈۋاتىدۇ';

  @override
  String get shippedDescription =>
      'تاۋار يوللاندى، خېرىدارنىڭ تاپشۇرۇپ ئېلىشىنى كۈتۈۋاتىدۇ';

  @override
  String get orderCompletedDescription => ' زاكاس تاماملاندى';

  @override
  String get orderCancelledDescription => ' زاكاس بىكار قىلىندى';

  @override
  String get orderStatusAbnormalDescription => ' زاكاس ھالىتى نورمالسىز';

  @override
  String get orderStatusPendingDescription =>
      'تېزلىكتە تۆلەشنى تاماملاڭ، ۋاقتى ئۆتكەن  زاكاس ئاپتوماتىك بىكار قىلىنىدۇ';

  @override
  String get orderStatusPreparingDescription =>
      'سىزنىڭ بۇيۇرتمىڭىز تەييارلىنىۋاتىدۇ، سەۋرچان كۈتۈڭ';

  @override
  String get orderStatusShippedUserDescription =>
      'تاۋار يوللاندى، تاپشۇرۇپ ئېلىشقا دىققەت قىلىڭ';

  @override
  String get orderStatusCompletedUserDescription =>
      ' زاكاس تاماملاندى، سېتىۋېلىشىڭىزگە رەھمەت';

  @override
  String get orderStatusCancelledUserDescription => ' زاكاس بىكار قىلىندى';

  @override
  String get shippingStatusWaitingReceive =>
      'يوللاندى، تاپشۇرۇپ ئېلىشنى كۈتۈۋاتىدۇ';

  @override
  String get shippingStatusCompleted => 'تاماملاندى';

  @override
  String get shippingStatusShipped => 'يوللاندى';

  @override
  String get shippingStatusPending => 'تۆلىندىغان';

  @override
  String get shippingStatusWaitingShip => 'يوللىنىدىغان';

  @override
  String get shippingStatusCancelled => 'بىكار قىلىنغان';

  @override
  String get shippingStatusUnknown => 'نامەلۇم ھالەت';

  @override
  String get insufficientPermissionDoctorRequired =>
      'ھوقۇق يەتمىدى، رەقەملىك تەننىڭ ھوقۇقى كېرەك';

  @override
  String get getPendingShipmentOrdersFailed =>
      'يولىنىدىغان زاكاسنى ئېلىش مەغلۇپ بولدى';

  @override
  String get trackingNumberCannotBeEmpty =>
      ' ئەۋەتىش نومۇرى قۇرۇق بولسا بولمايدۇ';

  @override
  String get shipmentFailed => 'يوللاش مەغلۇپ بولدى';

  @override
  String get orderNotExistOrNoAccess =>
      ' زاكاس مەۋجۇت ئەمەس ياكى زىيارەت ھوقۇقى يوق';

  @override
  String get shipmentFailedCheckOrderStatus =>
      'يوللاش مەغلۇپ بولدى،  زاكاس ھالىتىنى تەكشۈرۈڭ';

  @override
  String get getShippingStatusFailed =>
      'يۈك ئەۋەتىش ھالىتىنى ئېلىش مەغلۇپ بولدى';

  @override
  String get getShippedOrdersFailed =>
      'يوللانغان بۇيۇرتمىنى ئېلىش مەغلۇپ بولدى';

  @override
  String get productInfoTitle => 'تاۋار ئۇچۇرى';

  @override
  String get orderInfoTitle => ' زاكاس ئۇچۇرى';

  @override
  String get orderNumberFieldLabel => ' زاكاس نومۇرى';

  @override
  String get orderTimeLabel => ' زاكاس بەرگەن ۋاقىت';

  @override
  String get paymentTimeLabel => 'تۆلەش ۋاقتى';

  @override
  String get shipmentTimeLabel => 'يوللاش ۋاقتى';

  @override
  String get completionTimeLabel => 'تاماملاش ۋاقتى';

  @override
  String get customerInfoTitle => 'خېرىدار ئۇچۇرى';

  @override
  String get customerNicknameLabel => 'خېرىدار تەخەللۇسى';

  @override
  String get userIdLabel => 'ئىشلەتكۈچى ID';

  @override
  String get shippingInfoTitle => 'تاپشۇرۇپ ئېلىش ئۇچۇرى';

  @override
  String get recipientLabel => 'تاپشۇرۇپ ئالغۇچى';

  @override
  String get contactPhoneLabel => 'ئالاقە تېلېفونى';

  @override
  String get shippingAddressLabel => 'تاپشۇرۇپ ئېلىش ئادرېسى';

  @override
  String get trackingInfoTitle => 'يۈك ئەۋەتىش ئۇچۇرى';

  @override
  String get viewDetailsAction => 'تەپسىلاتىنى كۆرۈش';

  @override
  String get trackingNumberLabel => 'يۈك ئەۋەتىش نومۇرى';

  @override
  String get shippingCompanyLabel => 'يۈك ئەۋەتىش شىركىتى';

  @override
  String get shippingNoteLabel => 'يوللاش ئەسكەرتىشى';

  @override
  String get priceDetailsTitle => 'چىقىم تەپسىلاتى';

  @override
  String get productAmountLabel => 'تاۋار مىقدارى';

  @override
  String get shippingFeeLabel => 'يوللاش ھەققى';

  @override
  String get totalPaidLabel => 'ئەمەلىي تۆلەنگەن مىقدار';

  @override
  String get cancelOrderAction => 'بۇيۇرتمىنى بىكار قىلىش';

  @override
  String get viewTrackingAction => 'يۈك ئەۋەتىشنى كۆرۈش';

  @override
  String get copiedToClipboardMessage => 'چاپلاش تاختىسىغا كۆچۈرۈلدى';

  @override
  String get confirmDeleteOrderTitle => 'ئۆچۈرۈشنى جەزملەشتۈرۈش';

  @override
  String get confirmDeleteOrderMessage =>
      'بۇ بۇيۇرتمىنى ئۆچۈرۈشنى جەزملەشتۈرەمسىز؟ بۇ مەشغۇلاتنى قايتۇرۇشقا بولمايدۇ.';

  @override
  String get orderDetailLoadFailedMessage =>
      ' زاكاس تەپسىلاتىنى ئوقۇش مەغلۇپ بولدى';

  @override
  String get orderInfoLoadFailedMessage => ' زاكاس ئۇچۇرىنى ئوقۇش مەغلۇپ بولدى';

  @override
  String updateFailedMessage(String error) {
    return 'يېڭىلاش مەغلۇپ بولدى: $error';
  }

  @override
  String deleteFailedMessage(String error) {
    return 'ئۆچۈرۈش مەغلۇپ بولدى: $error';
  }

  @override
  String get editConversationTitle => 'سۆزلىشىش ماۋزۇسىنى تەھرىرلەش';

  @override
  String get replying => 'جاۋاب بېرىۋاتىدۇ...';

  @override
  String get imageLoadFailed => 'رەسىم يۈكلەش مەغلۇپ بولدى';

  @override
  String get imageNotAvailable => 'رەسىم ئىشلەتكىلى بولمايدۇ';

  @override
  String get releaseToCancel => 'قويۇپ بېرىپ ئاۋاز خاتىرىلەشنى بىكار قىلىڭ';

  @override
  String get recording => 'ئاۋاز خاتىرىلەۋاتىدۇ';

  @override
  String get slideUpToCancel => 'ئۈستىگە سۈرۈپ بىكار قىلىڭ';

  @override
  String get continueSlideUpToCancel =>
      'ئاۋاز خاتىرىلەشنى بىكار قىلىش ئۈچۈن داۋاملىق ئۈستىگە سۈرۈڭ';

  @override
  String get takePhotoAndSend => 'رەسىم تارتىپ ئەۋەتىش';

  @override
  String get selectSendArea => 'ئەۋەتىش رايونىنى تاللاش';

  @override
  String get send => 'ئەۋەتىش';

  @override
  String get orderTimeline => ' زاكاس ۋاقىت ئېقىمى';

  @override
  String get orderCreated => ' زاكاس قۇرۇلدى';

  @override
  String get paymentCompleted => 'تۆلەش تاماملاندى';

  @override
  String get goodsShipped => 'تاۋار يوللاندى';

  @override
  String get orderCompleted => ' زاكاس تاماملاندى';

  @override
  String get shipOrder => ' زاكاس يوللاش';

  @override
  String get confirmShip => 'يوللاشنى جەزملەشتۈرۈش';

  @override
  String get ship => 'يوللاش';

  @override
  String get enterTrackingNumber => 'زاكاس يوللاش نومۇرىنى كىرگۈزۈڭ';

  @override
  String get trackingNumberRequired => 'زاكاس يوللاش نومۇرىنى كىرگۈزۈڭ';

  @override
  String get selectShippingCompany =>
      'زاكاس يوللاىغان شىركىتىنى تاللاڭ (تاللاشچان)';

  @override
  String get enterShippingNote => 'يوللاش ئەسكەرتىشىنى كىرگۈزۈڭ (تاللاشچان)';

  @override
  String get shipSuccess => 'يوللاش مۇۋەپپەقىيەتلىك';

  @override
  String shipFailed(String error) {
    return 'يوللاش مەغلۇپ بولدى: $error';
  }

  @override
  String get productLabel => 'مەھسۇلات';

  @override
  String get adminShipment => 'باشقۇرغۇچى يوللاش';

  @override
  String get trackingNumberRequiredField => 'زاكاس يوللاش نومۇرى *';

  @override
  String get shippingCompanyHint =>
      'مەسىلەن: شۇنفېڭ يۈك ئەۋەتىش، يۈەنتوڭ يۈك ئەۋەتىش قاتارلىقلار';

  @override
  String get shippingNoteHint =>
      'يوللاش چۈشەندۈرۈشى ياكى دىققەت قىلىدىغان ئىشلارنى يېزىشقا بولىدۇ';

  @override
  String get shipmentSuccess => 'يوللاش مۇۋەپپەقىيەتلىك';

  @override
  String shipmentFailedWithError(String error) {
    return 'يوللاش مەغلۇپ بولدى: $error';
  }

  @override
  String copiedToClipboardWithTitle(String title) {
    return '$title چاپلاش تاختىسىغا كۆچۈرۈلدى';
  }

  @override
  String get voiceRecognizing => 'ئاۋازنى تونۇۋاتىدۇ...';

  @override
  String get voiceRecognitionRetry => 'ئاۋاز تونۇش مەغلۇپ بولدى، قايتا سىناڭ';

  @override
  String get cannotOpenPhoneAppCopied =>
      'تېلېفون ئەپنى ئاچالمىدى، نومۇر چاپلاش تاختىسىغا كۆچۈرۈلدى';

  @override
  String operationFailedManualDialWithNumber(String phoneNumber) {
    return 'مەشغۇلات مەغلۇپ بولدى، قولدا چاقىرىڭ: $phoneNumber';
  }

  @override
  String healthAssistantVoiceRecognition(String text) {
    return 'ساغلاملىق ياردەمچىسى ئاۋاز تونۇش: $text';
  }

  @override
  String healthAssistantVoiceProcessingFailed(String error) {
    return 'ساغلاملىق ياردەمچىسى ئاۋاز بىر تەرەپ قىلىش مەغلۇپ بولدى: $error';
  }

  @override
  String loginFailedWithMessage(String message) {
    return 'كىرىش مەغلۇپ بولدى: $message';
  }

  @override
  String get verificationCodeIncorrectOrExpired =>
      'تەستىق كودى خاتا ياكى ۋاقتى ئۆتتى';

  @override
  String get usernameAlreadyExists =>
      'ئىشلەتكۈچى نامى ئاللىقاچان مەۋجۇت، ئىشلەتكۈچى نامىنى ئالماشتۇرۇڭ';

  @override
  String get dpiAdaptationSettings => 'DPI ماسلاشتۇرۇش تەڭشىكى';

  @override
  String get dpiAdaptationDescription =>
      'ئوخشىمىغان ئېكران زىچلىقىغا ماسلىشىش ئۈچۈن ئەپنىڭ كۆرسىتىش چوڭايتىش نىسبىتىنى تەڭشەڭ';

  @override
  String get currentDpiScale => 'نۆۋەتتىكى چوڭايتىش نىسبىتى';

  @override
  String get systemDefault => 'سىستېما كۆڭۈلدىكى';

  @override
  String get small => 'كىچىك';

  @override
  String get normal => 'نورمال';

  @override
  String get large => 'چوڭ';

  @override
  String get extraLarge => 'ئىنتايىن چوڭ';

  @override
  String get previewText => 'ئالدىن كۆرۈش تېكىستى';

  @override
  String get sampleText =>
      'بۇ نۆۋەتتىكى چوڭايتىش ئۈنۈمىنى ئالدىن كۆرۈش ئۈچۈن ئىشلىتىلىدىغان مىسال تېكىست.';

  @override
  String get applyChanges => 'ئۆزگىرىشنى قوللىنىش';

  @override
  String get resetToDefault => 'كۆڭۈلدىكىگە قايتۇرۇش';

  @override
  String get dpiSettingsApplied => 'DPI تەڭشىكى قوللىنىلدى';

  @override
  String get dpiSettingsReset => 'DPI تەڭشىكى كۆڭۈلدىكىگە قايتۇرۇلدى';

  @override
  String get dpiModeAuto => 'ئاپتوماتىك ماسلاشتۇرۇش';

  @override
  String get dpiModeAutoDesc =>
      'ئۈسكۈنە DPI گە ئاساسەن كۆرۈنۈش چوڭلۇقىنى ئاپتوماتىك تەڭشەش (تەۋسىيە قىلىنىدۇ)';

  @override
  String get dpiModeSmall => 'ئىخچام ھالەت';

  @override
  String get dpiModeSmallDesc =>
      'كىچىكرەك كۆرۈنۈش ئېلېمېنتلىرى، يۇقىرى DPI ئۈسكۈنىلەرگە ماس';

  @override
  String get dpiModeStandard => 'ئۆلچەملىك ھالەت';

  @override
  String get dpiModeStandardDesc => 'كۆڭۈلدىكى چوڭلۇقتىكى كۆرۈنۈش ئېلېمېنتلىرى';

  @override
  String get dpiModeLarge => 'كەڭ ھالەت';

  @override
  String get dpiModeLargeDesc =>
      'چوڭراق كۆرۈنۈش ئېلېمېنتلىرى، تۆۋەن DPI ئۈسكۈنىلەرگە ماس';

  @override
  String get currentStatus => 'نۆۋەتتىكى ھالەت';

  @override
  String get adaptationMode => 'ماسلاشتۇرۇش ھالىتى';

  @override
  String get scaleFactor => 'چوڭايتىش نىسبىتى';

  @override
  String get deviceInfo => 'ئۈسكۈنە ئۇچۇرى';

  @override
  String get screenSize => 'ئېكران چوڭلۇقى';

  @override
  String get devicePixelRatio => 'ئۈسكۈنە پىكسېل نىسبىتى';

  @override
  String get screenDiagonal => 'ئېكران دىئاگونالى';

  @override
  String get autoScaleFactor => 'ئاپتوماتىك چوڭايتىش نىسبىتى';

  @override
  String get effectPreview => 'ئۈنۈم ئالدىن كۆرۈش';

  @override
  String get sampleButton => 'مىسال كۇنۇپكا';

  @override
  String get titleText => 'ماۋزۇ تېكىستى';

  @override
  String get sampleDescription =>
      'بۇ نۆۋەتتىكى DPI ماسلاشتۇرۇش تەڭشىكىنىڭ ئۈنۈمىنى ئالدىن كۆرۈش ئۈچۈن ئىشلىتىلىدىغان مىسال تېكىست.';

  @override
  String get inches => 'ئىنچ';

  @override
  String get dpiAdaptation => 'DPI ماسلاشتۇرۇش';

  @override
  String get dpiAdaptationSubtitle =>
      'ئوخشىمىغان DPI ئۈسكۈنىلەرگە ماسلىشىش ئۈچۈن كۆرۈنۈش ئېلېمېنت چوڭلۇقىنى تەڭشەش';
}
