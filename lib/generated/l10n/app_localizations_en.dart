// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get settingsTitle => 'Settings';

  @override
  String get languageSettings => 'Language';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get selectSourceLanguage => 'Select Source Language';

  @override
  String get selectTargetLanguage => 'Select Target Language';

  @override
  String get languageChinese => 'Chinese';

  @override
  String get languageEnglish => 'English';

  @override
  String get languageUyghur => 'Uyghur';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get displaySettings => 'Display';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get darkModeDescription => 'Switch between light and dark themes';

  @override
  String get followSystemTheme => 'Follow System';

  @override
  String get followSystemThemeDescription =>
      'Automatically adapt to system light/dark settings';

  @override
  String get fontSize => 'Font Size';

  @override
  String get fontSizeDescription => 'Adjust app font size';

  @override
  String get languageSettingsDescription => 'Choose app display language';

  @override
  String get other => 'Other';

  @override
  String get helpAndFeedback => 'Help & Feedback';

  @override
  String get helpAndFeedbackDescription => 'FAQ and feedback submission';

  @override
  String get aboutUs => 'About';

  @override
  String get aboutUsDescription => 'Version info and company introduction';

  @override
  String get logout => 'Sign Out';

  @override
  String get logoutDescription => 'Clear login status and return to login page';

  @override
  String get aboutDialogTitle => 'About Us';

  @override
  String get aboutDialogContent =>
      'Health Assistant v1.0.0\n\nA professional AI health guide app';

  @override
  String get homeTitle => 'Health Assistant';

  @override
  String get historyTitle => 'History';

  @override
  String get aiTourGuideTitle => 'Health Assistant';

  @override
  String get smartGuide => 'Smart Guide';

  @override
  String get welcomeTo => 'Welcome to';

  @override
  String get shareApp => 'Share App';

  @override
  String get distributionManagement => 'Affiliate Management';

  @override
  String get myLanguage => 'My Language';

  @override
  String get theirLanguage => 'Their Language';

  @override
  String get sourceLanguage => 'From';

  @override
  String get targetLanguage => 'To';

  @override
  String get bottomNavHome => 'Health Assistant';

  @override
  String get bottomNavHistory => 'History';

  @override
  String get bottomNavAiGuide => 'Health Assistant';

  @override
  String get bottomNavSearch => 'List';

  @override
  String get bottomNavProfile => 'Profile';

  @override
  String get bottomNavSettings => 'Settings';

  @override
  String get searchPageTitle => 'List';

  @override
  String get searchHint => 'Search doctors or products...';

  @override
  String get doctorTab => 'Doctors';

  @override
  String get productTab => 'Products';

  @override
  String get noDoctorsAvailable => 'No doctors available';

  @override
  String get noProductsAvailable => 'No products available';

  @override
  String get noSearchResults => 'No search results found';

  @override
  String get inputHint => 'Please enter your question...';

  @override
  String get tapToSpeak => 'Tap to speak';

  @override
  String get listening => 'Listening...';

  @override
  String get processing => 'Processing...';

  @override
  String get clearHistory => 'Clear';

  @override
  String get copy => 'Copy';

  @override
  String get share => 'Share';

  @override
  String get play => 'Play';

  @override
  String get pause => 'Pause';

  @override
  String get retry => 'Retry';

  @override
  String get error => 'Error';

  @override
  String get networkError =>
      'Network connection failed. Please check your network settings';

  @override
  String get permissionDenied => 'Permission denied';

  @override
  String get cameraPermissionRequired => 'Camera permission required';

  @override
  String get microphonePermissionRequired =>
      'Microphone permission required for recording';

  @override
  String get appTitle => 'MinHan Translator';

  @override
  String get welcomeMessage => 'Welcome to MinHan Translator';

  @override
  String get welcomeDescription =>
      'Your personal AI health guide, providing health consultation and guide services anytime';

  @override
  String get exitAppConfirm => 'Press back again to exit';

  @override
  String get themeSwitch => 'Theme Switch';

  @override
  String get themeSwitchMessage =>
      'Switching theme mode requires restarting the app to take full effect. Restart now?';

  @override
  String get languageSwitchMessage =>
      'Switching language requires restarting the app to take full effect. Restart now?';

  @override
  String get restart => 'Restart';

  @override
  String get userManagementTab => 'User Management';

  @override
  String get userManagement => 'User Management';

  @override
  String get userList => 'User List';

  @override
  String get userStatistics => 'User Statistics';

  @override
  String get totalUsers => 'Total Users';

  @override
  String get activeUsers => 'Active Users';

  @override
  String get adminUsers => 'Admins';

  @override
  String get doctorUsers => 'Doctors';

  @override
  String get searchUsers => 'Search Users';

  @override
  String get searchByNicknamePhoneId => 'Search by nickname, phone or ID';

  @override
  String get userStatus => 'User Status';

  @override
  String get allStatus => 'All';

  @override
  String get enabledStatus => 'Enabled';

  @override
  String get disabledStatus => 'Disabled';

  @override
  String get userRole => 'User Role';

  @override
  String get allRoles => 'All Roles';

  @override
  String get normalUser => 'Normal User';

  @override
  String get adminUser => 'Admin';

  @override
  String get doctorUser => 'Doctor';

  @override
  String get userGender => 'Gender';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get unknown => 'Unknown';

  @override
  String get registerSource => 'Register Source';

  @override
  String get appSource => 'APP';

  @override
  String get miniProgramSource => 'Mini Program';

  @override
  String get userBalance => 'User Balance';

  @override
  String get userIntegral => 'User Points';

  @override
  String get adjustBalance => 'Adjust Balance';

  @override
  String get adjustIntegral => 'Adjust Points';

  @override
  String get adjustAmount => 'Adjust Amount';

  @override
  String get adjustReason => 'Adjust Reason';

  @override
  String get pleaseEnterAmount => 'Please enter adjust amount';

  @override
  String get pleaseEnterReason => 'Please enter adjust reason';

  @override
  String get positiveForIncrease =>
      'Positive for increase, negative for decrease';

  @override
  String get userDetail => 'User Detail';

  @override
  String get editUser => 'Edit User';

  @override
  String get enableUser => 'Enable User';

  @override
  String get disableUser => 'Disable User';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get pleaseEnterNewPassword => 'Please enter new password';

  @override
  String get passwordLength => 'Password length 6-20 characters';

  @override
  String get userNickname => 'User Nickname';

  @override
  String get userPhone => 'User Phone';

  @override
  String get userBirthday => 'User Birthday';

  @override
  String get registrationTime => 'Registration Time';

  @override
  String get lastLoginTime => 'Last Login';

  @override
  String get userTokens => 'Login Tokens';

  @override
  String get clearAllTokens => 'Clear All Tokens';

  @override
  String get confirmClearTokens => 'Confirm to clear all login tokens?';

  @override
  String get clearTokensWarning =>
      'Users will be forced to log out on all devices after clearing';

  @override
  String get deviceType => 'Device Type';

  @override
  String get expiryTime => 'Expiry Time';

  @override
  String get createTime => 'Create Time';

  @override
  String get expired => 'Expired';

  @override
  String get valid => 'Valid';

  @override
  String get loadUserListFailed => 'Failed to load user list';

  @override
  String get loadUserDetailFailed => 'Failed to load user detail';

  @override
  String get updateUserSuccess => 'User information updated successfully';

  @override
  String get updateUserFailed => 'Failed to update user information';

  @override
  String get adjustBalanceSuccess => 'Balance adjusted successfully';

  @override
  String get adjustBalanceFailed => 'Failed to adjust balance';

  @override
  String get adjustIntegralSuccess => 'Points adjusted successfully';

  @override
  String get adjustIntegralFailed => 'Failed to adjust points';

  @override
  String get resetPasswordSuccess => 'Password reset successfully';

  @override
  String get resetPasswordFailed => 'Failed to reset password';

  @override
  String get clearTokensSuccess => 'Tokens cleared successfully';

  @override
  String get clearTokensFailed => 'Failed to clear tokens';

  @override
  String get noUsersFound => 'No users found';

  @override
  String get enableUserSuccess => 'User enabled successfully';

  @override
  String get disableUserSuccess => 'User disabled successfully';

  @override
  String get reset => 'Reset';

  @override
  String get apply => 'Apply';

  @override
  String get todayNewUsers => 'Today New';

  @override
  String get thisWeekNewUsers => 'This Week New';

  @override
  String get totalBalance => 'Total Balance';

  @override
  String get totalIntegral => 'Total Points';

  @override
  String get expandFilters => 'Expand Filters';

  @override
  String get collapseFilters => 'Collapse Filters';

  @override
  String get userDevelopmentInProgress =>
      'User detail page under development...';

  @override
  String get roleAdmin => 'Admin';

  @override
  String get roleDoctor => 'Doctor';

  @override
  String get roleReferrer => 'Referrer';

  @override
  String get roleNormalUser => 'Normal User';

  @override
  String get editUserInfo => 'Edit User Info';

  @override
  String get editUserRole => 'Edit User Role';

  @override
  String get userNicknameLabel => 'User Nickname';

  @override
  String get userPhoneLabel => 'User Phone';

  @override
  String get userBirthdayLabel => 'User Birthday';

  @override
  String get userGenderLabel => 'User Gender';

  @override
  String get pleaseEnterNickname => 'Please enter user nickname';

  @override
  String get pleaseEnterPhone => 'Please enter user phone';

  @override
  String get selectBirthday => 'Select Birthday';

  @override
  String get selectGender => 'Select Gender';

  @override
  String get associatedDoctor => 'Associated Doctor';

  @override
  String get selectDoctor => 'Select Doctor';

  @override
  String get referrerLevel => 'Referrer Level';

  @override
  String get level => 'Level';

  @override
  String get ipAddress => 'IP Address';

  @override
  String get roleManagement => 'Role Management';

  @override
  String get currentRoles => 'Current Roles';

  @override
  String get roleDetails => 'Role Details';

  @override
  String get editRole => 'Edit Role';

  @override
  String get balanceIntegralManagement => 'Balance & Points Management';

  @override
  String get tokenManagement => 'Token Management';

  @override
  String get totalTokens => 'Total Tokens';

  @override
  String get noTokensFound => 'No login tokens found';

  @override
  String get tokenClearSuccess => 'Token cleared successfully';

  @override
  String get tokenClearFailed => 'Failed to clear token';

  @override
  String get loadTokensFailed => 'Failed to load token list';

  @override
  String get pleaseCompleteInfo => 'Please complete all information';

  @override
  String get pleaseEnterValidAmount => 'Please enter valid amount';

  @override
  String get pleaseEnterValidPoints => 'Please enter valid points';

  @override
  String get userEditInProgress => 'User edit feature under development...';

  @override
  String get roleEditInProgress => 'Role edit feature under development...';

  @override
  String get genderMale => 'Male';

  @override
  String get genderFemale => 'Female';

  @override
  String get genderUnknown => 'Unknown';

  @override
  String get referrerText => 'Referrer';

  @override
  String get updateRoleSuccess => 'Role updated successfully';

  @override
  String get updateRoleFailed => 'Failed to update role';

  @override
  String get adminRoleDescription => 'Has system administration privileges';

  @override
  String get doctorRoleDescription => 'Can manage products and orders';

  @override
  String get referrerRoleDescription => 'Can promote and earn commissions';

  @override
  String get pleaseEnterReferrerLevel => 'Please enter referrer level';

  @override
  String get registerSourceApp => 'APP';

  @override
  String get registerSourceMiniProgram => 'Mini Program';

  @override
  String get statusEnabled => 'Enabled';

  @override
  String get statusDisabled => 'Disabled';

  @override
  String get healthProfile => 'Health Profile';

  @override
  String get viewFullHealthProfile => 'View Full Profile';

  @override
  String get heartRate => 'Heart Rate';

  @override
  String get bodyTemperature => 'Body Temperature';

  @override
  String get weight => 'Weight';

  @override
  String get height => 'Height';

  @override
  String get healthProfileFeatureComingSoon =>
      'Health profile feature coming soon';

  @override
  String get bloodType => 'Blood Type';

  @override
  String get exerciseFrequency => 'Exercise Frequency';

  @override
  String get sedentary => 'Sedentary';

  @override
  String get lightExercise => 'Light Exercise';

  @override
  String get moderateExercise => 'Moderate Exercise';

  @override
  String get activeExercise => 'Active Exercise';

  @override
  String get noHealthProfileYet => 'No health profile yet';

  @override
  String get createHealthProfile => 'Create Health Profile';

  @override
  String get healthProfileEditFeatureComingSoon =>
      'Health profile editing feature coming soon';

  @override
  String get basicInfo => 'Basic Information';

  @override
  String get allergyHistory => 'Allergy History';

  @override
  String get chronicDiseaseHistory => 'Chronic Disease History';

  @override
  String get currentMedication => 'Current Medication';

  @override
  String get lifestyle => 'Lifestyle';

  @override
  String get hasAllergies =>
      'Do you have any drug, food or other substance allergies';

  @override
  String get drugAllergies => 'Drug Allergies';

  @override
  String get foodAllergies => 'Food Allergies';

  @override
  String get otherAllergies => 'Other Allergies';

  @override
  String get hasChronicDiseases => 'Has Chronic Diseases';

  @override
  String get chronicDiseasesList => 'Chronic Diseases List';

  @override
  String get bloodPressureRange => 'Usual blood pressure range';

  @override
  String get bloodSugarRange => 'Usual fasting blood sugar range';

  @override
  String get hasCurrentMedication => 'Has Current Medication';

  @override
  String get medicationDetails => 'Medication Details';

  @override
  String get smokingStatus => 'Smoking Status';

  @override
  String get drinkingStatus => 'Drinking Status';

  @override
  String get sleepDuration => 'Average nightly sleep duration';

  @override
  String get sleepQuality => 'Sleep Quality';

  @override
  String get stressLevel => 'Recent stress level';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get never => 'Never';

  @override
  String get quit => 'Quit';

  @override
  String get occasional => 'Occasional';

  @override
  String get daily => 'Daily';

  @override
  String get social => 'Social';

  @override
  String get weekly => 'Weekly';

  @override
  String get lessThan6Hours => 'Less than 6 hours';

  @override
  String get sixToSevenHours => '6-7 hours';

  @override
  String get sevenToEightHours => '7-8 hours';

  @override
  String get moreThan8Hours => 'More than 8 hours';

  @override
  String get good => 'Good';

  @override
  String get fair => 'Fair';

  @override
  String get poor => 'Poor';

  @override
  String get veryLow => 'Very Low';

  @override
  String get low => 'Low';

  @override
  String get moderate => 'Moderate';

  @override
  String get high => 'High';

  @override
  String get veryHigh => 'Very High';

  @override
  String get pleaseEnterPhoneNumber => 'Please enter phone number';

  @override
  String get pleaseEnterCorrectPhoneNumber =>
      'Please enter a valid phone number';

  @override
  String get pleaseEnterPassword => 'Please enter password';

  @override
  String get passwordMinLength => 'Password must be at least 6 characters';

  @override
  String get loggingIn => 'Logging in...';

  @override
  String get loginSuccessful => 'Login successful';

  @override
  String get loginSuccessButNoData => 'Login successful but user data is empty';

  @override
  String dataProcessingError(String error) {
    return 'Error processing user data: $error';
  }

  @override
  String loginProcessError(String error) {
    return 'Error during login process: $error';
  }

  @override
  String get passwordIncorrect => 'Incorrect password';

  @override
  String get phoneNotRegistered =>
      'This phone number is not registered, please register first';

  @override
  String get passwordLogin => 'Password Login';

  @override
  String get passwordLoginSubtitle =>
      'Please use your phone number and password to login';

  @override
  String get phoneNumberHint => 'Please enter phone number';

  @override
  String get passwordHint => 'Please enter password';

  @override
  String get forgotPassword => 'Forgot password?';

  @override
  String get smsLogin => 'SMS Login';

  @override
  String get registerAccount => 'Register Account';

  @override
  String get orOtherLoginMethods => 'Or choose other login methods';

  @override
  String get loginAgreement =>
      'By logging in, you agree to the User Agreement and Privacy Policy';

  @override
  String get verificationCodeSent => 'Verification code sent';

  @override
  String get sendFailed => 'Send failed';

  @override
  String get pleaseEnterVerificationCode => 'Please enter verification code';

  @override
  String get verificationCodeShouldBe6Digits =>
      'Verification code should be 6 digits';

  @override
  String get login => 'Sign In';

  @override
  String get welcomeBack => 'Welcome Back';

  @override
  String get pleaseLoginWithPhoneNumber =>
      'Please sign in with your phone number';

  @override
  String get passwordLoginDesc =>
      'Please sign in with your phone number and password';

  @override
  String get agreeToTerms =>
      'By signing in, you agree to our Terms of Service and Privacy Policy';

  @override
  String get verificationCodeHint => 'Enter verification code';

  @override
  String get newPasswordHint => 'Enter new password';

  @override
  String get confirmPasswordHint => 'Enter new password again';

  @override
  String get getVerificationCode => 'Get Code';

  @override
  String get resendVerificationCode => 'Resend';

  @override
  String get resetPasswordDescription =>
      'Enter your phone number to get verification code, then set a new password';

  @override
  String get confirmReset => 'Confirm Reset';

  @override
  String get passwordsDoNotMatch => 'The two passwords do not match';

  @override
  String get passwordResetSuccess =>
      'Password reset successfully. Please sign in with your new password';

  @override
  String get contactCustomerService =>
      'If you have any issues, please contact customer service';

  @override
  String get resetPasswordTitle => 'Reset Password';

  @override
  String get enterCorrectPhoneNumber => 'Please enter a valid phone number';

  @override
  String get enterVerificationCode => 'Please enter verification code';

  @override
  String get verificationCodeSixDigits =>
      'Verification code should be 6 digits';

  @override
  String get enterNewPassword => 'Please enter new password';

  @override
  String get passwordMinSixCharacters =>
      'Password must be at least 6 characters';

  @override
  String get enterNewPasswordAgain => 'Please enter new password again';

  @override
  String get getVerificationCodeButton => 'Get Code';

  @override
  String resendCountdown(int seconds) {
    return 'Resend (${seconds}s)';
  }

  @override
  String get sendVerificationCodeFailed => 'Failed to send verification code';

  @override
  String get resettingPasswordLoading => 'Resetting password...';

  @override
  String get passwordResetFailed => 'Password reset failed';

  @override
  String get networkConnectionFailedRetry =>
      'Network connection failed, please try again later';

  @override
  String get clearHistoryTitle => 'Clear Chat History';

  @override
  String get historyCleared => 'History cleared';

  @override
  String get clearHistoryFailed => 'Failed to clear history';

  @override
  String get clearAllHistory => 'Clear All History';

  @override
  String get daysAgo => 'days ago';

  @override
  String hoursAgo(int hours) {
    return '$hours hours ago';
  }

  @override
  String minutesAgo(int minutes) {
    return '$minutes minutes ago';
  }

  @override
  String get justNow => 'Just now';

  @override
  String get retakePhoto => 'Retake Photo';

  @override
  String get imageProcessingError => 'Error processing image';

  @override
  String get exitAppHint => 'Press back again to exit';

  @override
  String get chinese => 'Chinese';

  @override
  String get uyghur => 'Uyghur';

  @override
  String get kazakh => 'Kazakh';

  @override
  String get russian => 'Russian';

  @override
  String get french => 'French';

  @override
  String get spanish => 'Spanish';

  @override
  String get cantonese => 'Cantonese';

  @override
  String get selectSourceLanguageFirst => 'Please select source language first';

  @override
  String get targetLanguageWillUpdate =>
      'Target language will be updated based on source language';

  @override
  String get faceToFaceConversation => 'Face-to-Face Conversation';

  @override
  String get conversation => 'Chat';

  @override
  String get newChat => 'New';

  @override
  String get aiChatHistory => 'Chat History';

  @override
  String get noChatHistory => 'No chat history';

  @override
  String get startNewChat => 'Tap the New button to start chatting';

  @override
  String get startChatting => 'Start Chatting';

  @override
  String get sendFirstMessage =>
      'Send your first message to start the conversation';

  @override
  String get thinking => 'Thinking...';

  @override
  String get sending => 'Sending...';

  @override
  String get audioMessage => 'Audio Message';

  @override
  String get typeMessage => 'Type a message...';

  @override
  String get editTitle => 'Edit Title';

  @override
  String get enterTitle => 'Enter title';

  @override
  String get deleteConversation => 'Delete Conversation';

  @override
  String get deleteConversationConfirm =>
      'Are you sure you want to delete this conversation? This action cannot be undone.';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get save => 'Save';

  @override
  String get loadFailed => 'Failed to load data';

  @override
  String get microphonePermissionDenied => 'Microphone permission denied';

  @override
  String get recordingTooShort => 'Recording too short';

  @override
  String get recordingCancelled => 'Recording cancelled';

  @override
  String get aiChatHistorySubtitle =>
      'View your conversation history with Health Assistant';

  @override
  String get clearHistoryButton => 'Clear History';

  @override
  String get clearHistoryConfirm =>
      'Are you sure you want to clear all chat history? This action cannot be undone.';

  @override
  String get clearConversation => 'Clear Conversation';

  @override
  String get holdToSpeak => 'Hold to speak';

  @override
  String get waitingForOther => 'Waiting for the other person to speak';

  @override
  String get microphonePermissionNeeded =>
      'Microphone permission needed for recording';

  @override
  String recordingFailed(String error) {
    return 'Recording failed: $error';
  }

  @override
  String get invalidAudioFile => 'Invalid audio file';

  @override
  String get audioProcessingFailed =>
      'Audio processing failed, please try again';

  @override
  String get cannotRecognizeVoice => 'Could not recognize voice content';

  @override
  String get confirmClearConversation =>
      'Are you sure you want to clear all chat history? This action cannot be undone.';

  @override
  String get conversationCleared => 'Conversation cleared';

  @override
  String get user => 'User';

  @override
  String get clickToLogin => 'Tap to sign in';

  @override
  String get loginToEnjoyMoreFeatures => 'Sign in to enjoy more features';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get vipMember => 'VIP Member';

  @override
  String get distributorLevel => 'Affiliate';

  @override
  String get appName => 'MinHan Health Assistant';

  @override
  String get shareSuccess =>
      'Shared successfully! Your friends can get rewards by downloading through your link';

  @override
  String get shareNotAvailable =>
      'Share feature temporarily unavailable, please try again later';

  @override
  String get shareSubject => 'Recommend App';

  @override
  String shareContentWithReferral(String appName, String url) {
    return 'I found an amazing translation tour guide app: $appName! It supports real-time translation in multiple languages, plus voice translation, camera translation, Health Assistant and more. Download using my exclusive referral link for extra benefits! 🎁\n\nDownload now: $url';
  }

  @override
  String shareContentNormal(String appName, String url) {
    return 'I found an amazing translation tour guide app: $appName! It supports real-time translation in multiple languages, plus voice translation, camera translation, Health Assistant and more. Give it a try!\n\nDownload now: $url';
  }

  @override
  String get logoutConfirmation =>
      'Are you sure you want to sign out? You\'ll need to sign in again to use all features.';

  @override
  String get logoutSuccess => 'Signed out successfully';

  @override
  String get logoutFailed => 'Failed to sign out';

  @override
  String get comingSoon => '(Coming Soon)';

  @override
  String yearsExperience(int years) {
    return '$years years exp.';
  }

  @override
  String ratingScore(String rating) {
    return '$rating pts';
  }

  @override
  String get aiAssistant => 'AI';

  @override
  String get professionalIntroduction => 'Professional Introduction';

  @override
  String doctorAiAssistantSelected(String doctorName) {
    return 'Selected $doctorName\'s AI Assistant';
  }

  @override
  String get loading => 'Loading...';

  @override
  String get noDoctorInfo => 'No doctor information';

  @override
  String get doctorTitle => 'Doctor';

  @override
  String get specialtyField => 'Specialty';

  @override
  String get startChatWithAiGuide => 'Start chatting with AI guide';

  @override
  String get updateTitleFailed => 'Failed to update title';

  @override
  String get selectAddress => 'Select Address';

  @override
  String get addressManagement => 'Address Management';

  @override
  String get noAddressesYet => 'No addresses yet';

  @override
  String get clickToAddAddress => 'Click the button below to add an address';

  @override
  String get setAsDefault => 'Set as Default';

  @override
  String selectedItemsCount(int count) {
    return 'Selected $count items';
  }

  @override
  String get totalAmount => 'Total: ';

  @override
  String get myLikesTitle => 'My Likes';

  @override
  String get myFavoritesTitle => 'My Favorites';

  @override
  String get noLikedDoctors => 'No liked doctors yet';

  @override
  String get noFavoriteDoctors => 'No favorite doctors yet';

  @override
  String get goLikeDoctors =>
      'Go to doctor details to like your favorite doctors';

  @override
  String get goFavoriteDoctors =>
      'Go to doctor details to favorite your preferred doctors';

  @override
  String get selectAll => 'Select All';

  @override
  String get deleteSelected => 'Delete Selected';

  @override
  String get checkout => 'Checkout';

  @override
  String deleteWithCount(int count) {
    return 'Delete ($count)';
  }

  @override
  String checkoutWithCount(int count) {
    return 'Checkout ($count)';
  }

  @override
  String get doctorInfo => 'Doctor Information';

  @override
  String get doctorName => 'Doctor Name';

  @override
  String get contactPhone => 'Contact Phone';

  @override
  String get workAddress => 'Work Address';

  @override
  String get call => 'Call';

  @override
  String get noProducts => 'No products';

  @override
  String productsCount(int count) {
    return '$count products';
  }

  @override
  String get buyNow => 'Buy Now';

  @override
  String get addToCart => 'Add to Cart';

  @override
  String get doctor => 'Doctor';

  @override
  String get productQuantity => 'Product Quantity';

  @override
  String get productTotalPrice => 'Product Total';

  @override
  String get shippingFee => 'Shipping Fee';

  @override
  String get free => 'Free';

  @override
  String get actualPayment => 'Total Payment';

  @override
  String get confirmOrder => 'Confirm Order';

  @override
  String get orderCreatedSuccess => 'Order created successfully';

  @override
  String createOrderFailed(String error) {
    return 'Create order failed: $error';
  }

  @override
  String checkoutFailed(String error) {
    return 'Checkout failed: $error';
  }

  @override
  String get quantityRange => 'Please enter a valid quantity between 1-99';

  @override
  String get items => 'items';

  @override
  String get consultation => 'Consult';

  @override
  String get appointment => 'Appoint';

  @override
  String get years => 'years';

  @override
  String get yearsOfExperience => 'Years of Experience';

  @override
  String get productDetail => 'Product Detail';

  @override
  String get price => 'Price';

  @override
  String get appointmentTime => 'Appointment Time';

  @override
  String get detailedDescription => 'Detailed Description';

  @override
  String get selectQuantity => 'Select Quantity';

  @override
  String get quantity => 'Quantity';

  @override
  String get cartEmpty => 'Your cart is empty';

  @override
  String get cartEmptyDescription => 'Go pick some great products';

  @override
  String get goShopping => 'Go Shopping';

  @override
  String get purchaseConfirmation => 'Purchase Confirmation';

  @override
  String get manufacturer => 'Manufacturer';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get morning => 'AM';

  @override
  String get afternoon => 'PM';

  @override
  String get evening => 'PM';

  @override
  String get confirmPurchase => 'Confirm Purchase';

  @override
  String get productName => 'Product Name';

  @override
  String get unitPrice => 'Unit Price';

  @override
  String get purchaseConfirmationMessage =>
      'Please confirm your purchase information. Click confirm to proceed to order confirmation page.';

  @override
  String get orderConfirmation => 'Order Confirmation';

  @override
  String get productInfo => 'Product Information';

  @override
  String get shippingInfo => 'Shipping Info';

  @override
  String get orderAmount => 'Order Amount';

  @override
  String get recipientName => 'Recipient Name';

  @override
  String get recipientPhone => 'Recipient Phone';

  @override
  String get shippingAddress => 'Shipping Address';

  @override
  String get getCurrentLocation => 'Get Current Location';

  @override
  String get gettingLocation => 'Getting location...';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get submitOrder => 'Submit Order';

  @override
  String get submittingOrder => 'Submitting order...';

  @override
  String get enterRecipientName => 'Please enter recipient name';

  @override
  String get enterRecipientPhone => 'Please enter recipient phone';

  @override
  String get enterShippingAddress => 'Please enter shipping address';

  @override
  String totalItems(int count) {
    return '$count items total';
  }

  @override
  String get done => 'Done';

  @override
  String get region => 'Region';

  @override
  String get selectRegion => 'Please select province/city/district';

  @override
  String get pleaseSelectRegion => 'Please select a region';

  @override
  String get productAmount => 'Product Amount';

  @override
  String get freeShipping => 'Free Shipping';

  @override
  String get defaultAddress => 'Default';

  @override
  String get editAddress => 'Edit';

  @override
  String get deleteAddress => 'Delete';

  @override
  String get loadAddressFailed => 'Failed to load addresses';

  @override
  String get setDefaultSuccess => 'Set successfully';

  @override
  String get setDefaultFailed => 'Failed to set';

  @override
  String get addAddress => 'Add Address';

  @override
  String get receiverName => 'Receiver Name';

  @override
  String get enterReceiverName => 'Please enter receiver name';

  @override
  String get enterContactPhone => 'Please enter contact phone';

  @override
  String get detailedAddress => 'Detailed Address';

  @override
  String get enterDetailedAddress =>
      'Please enter detailed address (street, house number, etc.)';

  @override
  String get postalCodeOptional => 'Postal Code (Optional)';

  @override
  String get enterPostalCode => 'Please enter postal code';

  @override
  String get addressLabelOptional => 'Address Label (Optional)';

  @override
  String get enterAddressLabel => 'e.g.: Home, Office, School, etc.';

  @override
  String get addressTooShort => 'Address must be at least 5 characters';

  @override
  String get addressTooLong => 'Address cannot exceed 200 characters';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get saveAddress => 'Save Address';

  @override
  String get setAsDefaultAddress => 'Set as Default Address';

  @override
  String get likeSuccess => 'Liked successfully';

  @override
  String get unlikeSuccess => 'Unliked successfully';

  @override
  String get favoriteSuccess => 'Added to favorites';

  @override
  String get unfavoriteSuccess => 'Removed from favorites';

  @override
  String get operationFailed => 'Operation failed';

  @override
  String get consultDoctor => 'Consult Doctor';

  @override
  String get doctorDetails => 'Details';

  @override
  String get specialties => 'Specialties';

  @override
  String get doctorRecommendations => 'Doctor Recommendations';

  @override
  String get workingHours => 'Mon-Fri 9:00-17:00';

  @override
  String get cannotOpenPhoneApp =>
      'Cannot open phone app, number copied to clipboard';

  @override
  String get operationFailedManualDial =>
      'Operation failed, please dial manually';

  @override
  String get physician => 'Physician';

  @override
  String get noDescription => 'No description available';

  @override
  String get viewDetails => 'View Details';

  @override
  String copiedToClipboard(String content) {
    return '$content copied to clipboard';
  }

  @override
  String get copyFailed => 'Copy failed';

  @override
  String get mapPreparingPleaseWait =>
      'Map is being prepared, please try again later';

  @override
  String get mapTitle => 'Map';

  @override
  String get aiGuideVoiceRecognitionFailure =>
      'Health Assistant voice recognition failed';

  @override
  String searchingCategory(String category) {
    return 'Searching $category...';
  }

  @override
  String get tryOtherCategoriesOrCheckNetwork =>
      'Please try other categories or check network connection';

  @override
  String noResultsFoundFor(String category, String city) {
    return 'No $category found in $city';
  }

  @override
  String noRelatedCategoryFound(String category) {
    return 'No related $category found';
  }

  @override
  String get address => 'Address';

  @override
  String addressLabel(String address) {
    return 'Address: $address';
  }

  @override
  String get navigation => 'Navigate';

  @override
  String openNavigationFailed(String error) {
    return 'Failed to open navigation: $error';
  }

  @override
  String get parksAndSquares => 'Parks & Squares';

  @override
  String get parks => 'Parks';

  @override
  String get zoos => 'Zoos';

  @override
  String get botanicalGardens => 'Botanical Gardens';

  @override
  String get aquariums => 'Aquariums';

  @override
  String get citySquares => 'City Squares';

  @override
  String get memorialHalls => 'Memorial Halls';

  @override
  String get templesAndTaoistTemples => 'Temples & Taoist Temples';

  @override
  String get churches => 'Churches';

  @override
  String get beaches => 'Beaches';

  @override
  String loadDetailsFailed(String error) {
    return 'Failed to load details: $error';
  }

  @override
  String get specialtyFood => 'Specialty Food';

  @override
  String get contactInfo => 'Contact Info';

  @override
  String get website => 'Website';

  @override
  String get generatingDetailInfo => 'Generating detail information...';

  @override
  String get viewAiGeneratedDetailedIntroduction =>
      'View AI-generated detailed introduction';

  @override
  String get clickToGetAiGeneratedDetailedIntroduction =>
      'Click to get AI-generated detailed introduction';

  @override
  String get aiGenerating => 'AI is generating...';

  @override
  String get generateDetailsFailed =>
      'Failed to generate details, please try again later';

  @override
  String openNavigationFailedError(String error) {
    return 'Failed to open navigation: $error';
  }

  @override
  String get addressCopiedToClipboard => 'Address copied to clipboard';

  @override
  String get scenicSpotType => 'Scenic Spot';

  @override
  String openNavigationFailedWithError(String error) {
    return 'Failed to open navigation: $error';
  }

  @override
  String get mapLoadFailed => 'Map Load Failed';

  @override
  String get unableToLoadMapPleaseRetry =>
      'Unable to load map, please go back and try again';

  @override
  String get back => 'Back';

  @override
  String get locateToCurrentPosition => 'Locate to Current Position';

  @override
  String get searchLocation => 'Search location...';

  @override
  String foundLocation(String name) {
    return 'Found: $name';
  }

  @override
  String get mapControllerNotInitialized =>
      'Map controller not initialized, please go back and try again';

  @override
  String locationServiceException(String error) {
    return 'Location service exception: $error';
  }

  @override
  String get mapNotFullyLoaded =>
      'Map not fully loaded, please try again later';

  @override
  String locationFailed(String error) {
    return 'Location failed: $error';
  }

  @override
  String get cameraPermissionNeeded =>
      'Cannot open camera, please check permission settings';

  @override
  String get aiTourGuideRecognition => 'Health Assistant Recognition';

  @override
  String get aiTourGuideVoiceRecognition =>
      'Health Assistant Voice Recognition';

  @override
  String get userAvatarFeatureInDevelopment =>
      'User avatar feature in development';

  @override
  String get inDevelopment => 'Feature in development';

  @override
  String get close => 'Close';

  @override
  String get overview => 'Overview';

  @override
  String get records => 'Records';

  @override
  String get team => 'Team';

  @override
  String get promotion => 'Promotion';

  @override
  String get loadMoreFailed => 'Failed to load more';

  @override
  String get getUserListFailed => 'Failed to get user list';

  @override
  String get levelUpdateSuccess => 'User level updated successfully';

  @override
  String get levelUpdateFailed => 'Failed to update level';

  @override
  String get certifiedDistributor => 'Certified Affiliate';

  @override
  String get fundsDetail => 'Funds Detail';

  @override
  String get withdrawing => 'Withdrawing';

  @override
  String get withdrawn => 'Withdrawn';

  @override
  String get totalCommissionIncome => 'Total Commission Income';

  @override
  String get noPromotionPosters => 'No promotion posters';

  @override
  String get testDescription => 'Test Description:';

  @override
  String get longPressToSelect =>
      'Long press the text below to select, copied content only includes Chinese characters.';

  @override
  String get smartCopyVersion => 'Smart Copy Version:';

  @override
  String get plainTextVersion => 'Plain Text Version:';

  @override
  String get smartCopyTest => 'Smart Copy Feature Test';

  @override
  String loadHistoryFailed(String error) {
    return 'Failed to load history: $error';
  }

  @override
  String clearHistoryFailure(String error) {
    return 'Failed to clear history: $error';
  }

  @override
  String get cameraAccessFailure =>
      'Cannot open camera, please check permission settings';

  @override
  String get instructions =>
      'Tap the icons above or double-tap the screen to start translating\nSupports voice input, camera translation, and more';

  @override
  String get enterPhoneNumber => 'Please enter 11-digit phone number';

  @override
  String get enterCorrectVerificationCode =>
      'Please enter correct verification code';

  @override
  String get sendingVerificationCode => 'Sending verification code...';

  @override
  String get verificationCodeSentToPhone =>
      'Verification code sent to your phone';

  @override
  String get networkConnectionFailed =>
      'Network connection failed, please try again later';

  @override
  String networkRequestFailed(String statusCode) {
    return 'Network request failed, status code: $statusCode';
  }

  @override
  String sendVerificationCodeError(String error) {
    return 'Error sending verification code: $error';
  }

  @override
  String get resettingPassword => 'Resetting password...';

  @override
  String processingUserDataError(String error) {
    return 'Error processing user data: $error';
  }

  @override
  String get loginSuccessButNoUserData => 'Login successful but no user data';

  @override
  String get userNotRegisteredRedirecting =>
      'User not registered, redirecting to registration page';

  @override
  String get historyDeleted => 'History deleted';

  @override
  String deleteHistoryFailed(String error) {
    return 'Failed to delete history: $error';
  }

  @override
  String get noCameraDetected => 'No camera detected';

  @override
  String cameraInitializationFailed(String error) {
    return 'Camera initialization failed: $error';
  }

  @override
  String get cameraNotReady => 'Camera not ready';

  @override
  String capturePhotoFailed(String error) {
    return 'Failed to capture photo: $error';
  }

  @override
  String get galleryPermissionRequired =>
      'Gallery permission required to select images';

  @override
  String selectImageFailed(String error) {
    return 'Select image failed: $error';
  }

  @override
  String get flashlightOperationFailed => 'Flashlight operation failed';

  @override
  String get switchCameraFailed => 'Failed to switch camera';

  @override
  String languageNotSupportedAsSource(String language) {
    return '$language is not supported as source language, cannot switch';
  }

  @override
  String get enterUsername => 'Please enter username';

  @override
  String get passwordMinLength6 => 'Password must be at least 6 characters';

  @override
  String get passwordsNotMatch => 'Passwords do not match';

  @override
  String get agreeToUserAgreement =>
      'Please agree to the User Agreement and Privacy Policy';

  @override
  String get registering => 'Registering...';

  @override
  String get registerSuccess => 'Registration successful';

  @override
  String get phoneFormatIncorrect => 'Phone number format is incorrect';

  @override
  String get verificationCodeExpired =>
      'Verification code is incorrect or expired';

  @override
  String get usernameAlreadyRegistered =>
      'Username is already registered, please choose another';

  @override
  String get phoneAlreadyRegistered =>
      'This phone number is already registered, you can login directly';

  @override
  String registerFailed(String message) {
    return 'Registration failed: $message';
  }

  @override
  String registerProcessError(String error) {
    return 'Error during registration process: $error';
  }

  @override
  String get openUserAgreement => 'Open User Agreement';

  @override
  String get openPrivacyPolicy => 'Open Privacy Policy';

  @override
  String get priceInfoLoadingWait =>
      'Price information loading, please wait...';

  @override
  String get priceInfoLoadFailed =>
      'Failed to get price information, showing default prices';

  @override
  String get recordingStartFailed =>
      'Recording failed to start, please check microphone permission';

  @override
  String get recordingStartError => 'Recording start error, please try again';

  @override
  String get recordingFailedRetry => 'Recording failed, please try again';

  @override
  String get audioProcessingFailedRetry =>
      'Audio processing failed, please try again';

  @override
  String voiceProcessingError(String error) {
    return 'Error processing voice: $error';
  }

  @override
  String playbackFailed(String error) {
    return 'Playback failed: $error';
  }

  @override
  String get microphoneRecordingPermissionRequired =>
      'Cannot record: microphone permission required for voice translation';

  @override
  String get permissionGrantedRetryRecording =>
      'Permission granted, please long press the recording button again to start recording';

  @override
  String logoutFailedError(String error) {
    return 'Logout failed: $error';
  }

  @override
  String aiTourGuideRecognitionResult(String text) {
    return 'Health Assistant recognition: $text';
  }

  @override
  String aiTourGuideVoiceRecognitionResult(String text) {
    return 'Health Assistant voice recognition: $text';
  }

  @override
  String get profileTitle => 'Profile';

  @override
  String get editProfileTitle => 'Edit Profile';

  @override
  String get healthInfo => 'Health Information';

  @override
  String get heightHint => 'Enter height (cm)';

  @override
  String get heightValidation => 'Please enter a valid height (50-250cm)';

  @override
  String get weightHint => 'Enter weight (kg)';

  @override
  String get weightValidation => 'Please enter a valid weight (20-300kg)';

  @override
  String get selectBloodType => 'Select blood type';

  @override
  String get bloodTypeA => 'Type A';

  @override
  String get bloodTypeB => 'Type B';

  @override
  String get bloodTypeAB => 'Type AB';

  @override
  String get bloodTypeO => 'Type O';

  @override
  String get bloodTypeUnknown => 'Unknown';

  @override
  String get residentialAddress => 'Residential Address';

  @override
  String get locate => 'Locate';

  @override
  String get selectResidentialAddress => 'Select residential address';

  @override
  String get regionSelectionFailed =>
      'Region selection failed, please try again';

  @override
  String get commonAllergens => 'Common Allergens';

  @override
  String get penicillinAllergy => 'Penicillin drugs';

  @override
  String get cephalosporinAllergy => 'Cephalosporin drugs';

  @override
  String get aspirinAllergy => 'Aspirin';

  @override
  String get peanutAllergy => 'Peanuts';

  @override
  String get seafoodAllergy => 'Seafood';

  @override
  String get milkAllergy => 'Milk';

  @override
  String get eggAllergy => 'Eggs';

  @override
  String get pollenDustMiteAllergy => 'Pollen/Dust mites';

  @override
  String get otherAllergens => 'Other Allergens';

  @override
  String get otherAllergensHint => 'Please specify other allergens';

  @override
  String get takingMedication => 'Are you currently taking any medications';

  @override
  String get medicationList => 'Medication List';

  @override
  String get medicationListHint =>
      'Please list the names, dosages and frequencies of medications you are taking';

  @override
  String get hasChronicDisease => 'Do you have any chronic diseases';

  @override
  String get specificSymptoms => 'Specific Conditions';

  @override
  String get hypertension => 'Hypertension';

  @override
  String get bloodPressureHint => 'e.g. 130/85 mmHg';

  @override
  String get diabetes => 'Diabetes';

  @override
  String get bloodSugarHint => 'e.g. 5.8 mmol/L';

  @override
  String get otherChronicDiseases => 'Other Chronic Diseases';

  @override
  String get otherChronicDiseasesHint =>
      'Please specify other chronic diseases';

  @override
  String get surgeryHistory => 'Surgery & Hospitalization History';

  @override
  String get hasSurgeryHistory =>
      'Have you had any surgeries or hospitalizations in the past';

  @override
  String get surgeryDetails => 'Details';

  @override
  String get surgeryDetailsHint =>
      'Please describe the details of surgeries or hospitalizations';

  @override
  String get familyHistory => 'Family History';

  @override
  String get familyDiseaseHistory =>
      'Do any immediate family members (parents, siblings, children) have the following diseases';

  @override
  String get familyHypertension => 'Hypertension';

  @override
  String get familyDiabetes => 'Diabetes';

  @override
  String get familyHeartDisease => 'Heart Disease';

  @override
  String get familyStroke => 'Stroke';

  @override
  String get familyCancer => 'Cancer';

  @override
  String get familyMentalHealth => 'Mental Health Disorders';

  @override
  String get otherFamilyHistory => 'Other Family History';

  @override
  String get otherFamilyHistoryHint =>
      'Please specify other family medical history';

  @override
  String get exerciseSedentary => 'Sedentary (little to no exercise)';

  @override
  String get exerciseLight => 'Lightly active (1-2 times per week)';

  @override
  String get exerciseModerate => 'Moderately active (3-5 times per week)';

  @override
  String get exerciseActive => 'Very active (6+ times per week)';

  @override
  String get dietaryPreferences => 'Dietary Preferences';

  @override
  String get balancedDiet => 'Balanced diet';

  @override
  String get vegetarianDiet => 'Prefer vegetarian';

  @override
  String get meatDiet => 'Prefer meat';

  @override
  String get oilyFood => 'Prefer oily food';

  @override
  String get saltyFood => 'Prefer salty food';

  @override
  String get sweetFood => 'Prefer sweet food';

  @override
  String get neverSmoke => 'Never smoke';

  @override
  String get quitSmoking => 'Quit smoking';

  @override
  String get occasionalSmoking => 'Occasional smoking (not daily)';

  @override
  String get dailySmoking => 'Regular smoking (daily)';

  @override
  String get neverDrink => 'Never drink';

  @override
  String get quitDrinking => 'Quit drinking';

  @override
  String get socialDrinking => 'Occasional social drinking';

  @override
  String get weeklyDrinking => '1-3 times per week';

  @override
  String get dailyDrinking => 'Almost daily';

  @override
  String get sleepLessThan6 => 'Less than 6 hours';

  @override
  String get sleep6To7 => '6-7 hours';

  @override
  String get sleep7To8 => '7-8 hours';

  @override
  String get sleepMoreThan8 => 'More than 8 hours';

  @override
  String get sleepGood => 'Good (easy to fall asleep, rarely wake up)';

  @override
  String get sleepFair =>
      'Fair (occasional difficulty falling asleep or early waking)';

  @override
  String get sleepPoor =>
      'Poor (chronic difficulty falling asleep, frequent dreams, early waking)';

  @override
  String get stressLow => 'Very low';

  @override
  String get stressMild => 'Mild stress';

  @override
  String get stressModerate => 'Moderate stress';

  @override
  String get stressHigh => 'High stress';

  @override
  String get stressExtreme => 'Extreme stress';

  @override
  String get womenHealth => 'Women\'s Health';

  @override
  String get isMenopause => 'Have you reached menopause';

  @override
  String get menstrualCycleRegular => 'Is your menstrual cycle regular';

  @override
  String get menstrualRegular => 'Regular';

  @override
  String get menstrualIrregular => 'Irregular';

  @override
  String get menstrualUncertain => 'Uncertain';

  @override
  String get hasPregnancy => 'Have you ever been pregnant';

  @override
  String get birthCount => 'Number of births';

  @override
  String get birthCount0 => '0 times';

  @override
  String get birthCount1 => '1 time';

  @override
  String get birthCount2 => '2 times';

  @override
  String get birthCount3 => '3 times';

  @override
  String get birthCount4 => '4 times';

  @override
  String get birthCount5Plus => '5 or more times';

  @override
  String get cannotParseImage => 'Cannot parse image file';

  @override
  String get vipMemberBadge => 'VIP Member';

  @override
  String get normalUserBadge => 'Regular User';

  @override
  String distributorLevelBadge(String level) {
    return 'Affiliate Lv.$level';
  }

  @override
  String get distributorBadge => 'Affiliate';

  @override
  String get arabic => 'Arabic';

  @override
  String get english => 'English';

  @override
  String languageSwitchNotSupported(String language) {
    return '$language is not supported as source language, cannot switch';
  }

  @override
  String get sourceLanguageLabel => 'From';

  @override
  String get targetLanguageLabel => 'To';

  @override
  String get currentBalance => 'Current Balance';

  @override
  String get todayIncome => 'Today\'s Income';

  @override
  String get totalIncome => 'Total Income';

  @override
  String get holdMicrophoneToSpeak => 'Hold microphone to speak';

  @override
  String get waitingForOtherParty => 'Waiting for other party to speak';

  @override
  String get confirmClear => 'Confirm Clear';

  @override
  String get confirmClearAllChatRecords =>
      'Are you sure you want to clear all chat records? This action cannot be undone.';

  @override
  String get confirmClearAction => 'Confirm Clear';

  @override
  String get registerTitle => 'Create Account';

  @override
  String get usernameHint => 'Please enter username';

  @override
  String get setPassword => 'Set password';

  @override
  String get confirmPassword => 'Confirm password';

  @override
  String get getCodeButton => 'Get Code';

  @override
  String countdownSeconds(int count) {
    return '${count}s';
  }

  @override
  String get register => 'Sign Up';

  @override
  String get phoneNumberLogin => 'Phone Login';

  @override
  String get userAgreementAndPrivacyPolicy =>
      'Terms of Service and Privacy Policy';

  @override
  String get iAgreeToThe => 'I have read and agree to the';

  @override
  String get helpFeedbackTitle => 'Help & Feedback';

  @override
  String get yourNameOptional => 'Your Name (Optional)';

  @override
  String get yourPhoneNumber => 'Your Phone Number';

  @override
  String get describeProblemDetail =>
      'Please describe your problem or suggestion in detail';

  @override
  String get submitFeedback => 'Submit Feedback';

  @override
  String get pleaseEnterCorrectPhoneFormat =>
      'Please enter correct phone number format';

  @override
  String get pleaseDescribeProblem =>
      'Please describe the problem you encountered';

  @override
  String get descriptionMinLength =>
      'Problem description must be at least 10 characters';

  @override
  String get descriptionMaxLength =>
      'Problem description cannot exceed 1000 characters';

  @override
  String get submittingFeedback => 'Submitting feedback...';

  @override
  String get feedbackSubmittedSuccess =>
      'Feedback submitted successfully, thank you for your suggestion!';

  @override
  String feedbackSubmissionFailed(String error) {
    return 'Feedback submission failed: $error';
  }

  @override
  String get feedbackInstructions => 'Feedback Instructions';

  @override
  String get feedbackInstructionsText =>
      'We will collect your problem description and related app data (excluding sensitive information) to better solve your problem. After submission, we will contact you through the phone number you provided.';

  @override
  String get enterYourName => 'Please enter your name';

  @override
  String get problemDescriptionHint =>
      'Please describe your problem or suggestion in detail\nIncluding:\n• Specific operation steps\n• Expected results\n• What actually happened\n• Other relevant information';

  @override
  String get submitting => 'Submitting...';

  @override
  String get testLogGeneration => 'Test Log Generation';

  @override
  String get viewErrorLogs => 'View Error Logs';

  @override
  String get generateTestErrors => 'Generate Test Errors';

  @override
  String get privacyNotice =>
      'Note: Your privacy is important to us. We do not collect sensitive information such as passwords, only necessary app configuration and log data to help solve problems.';

  @override
  String get logGenerationSuccess => 'Log Generation Successful';

  @override
  String logSize(String size) {
    return 'Log size: ${size}KB';
  }

  @override
  String get testErrorLogsGenerated =>
      'Test error logs generated, you can view them in the error log viewer';

  @override
  String get feedbackSubmittedSuccessfully =>
      'Feedback submitted successfully, we will process your issue as soon as possible';

  @override
  String get submissionFailed => 'Submission failed';

  @override
  String get submissionFailedCheckNetwork =>
      'Submission failed, please check network connection';

  @override
  String logGenerationFailed(String error) {
    return 'Log generation failed: $error';
  }

  @override
  String get phoneNumberForContact => '(for problem follow-up)';

  @override
  String get nickname => 'Nickname';

  @override
  String get nicknameRequired => 'Nickname cannot be empty';

  @override
  String get nicknameMinLength => 'Nickname must be at least 2 characters';

  @override
  String get changePassword => 'Change Password';

  @override
  String get changePasswordDescription =>
      'Please set your new password to ensure account security.';

  @override
  String get passwordRequirements => 'Password Requirements';

  @override
  String get passwordLengthRequirement =>
      'Password must be 6-20 characters long';

  @override
  String get accountSettings => 'Account Settings';

  @override
  String get changePasswordSubtitle => 'Change your login password';

  @override
  String get newPasswordRequired => 'Please enter new password';

  @override
  String get confirmNewPassword => 'Confirm New Password';

  @override
  String get confirmNewPasswordRequired => 'Please confirm new password';

  @override
  String get changeAvatar => 'Change Avatar';

  @override
  String get uploading => 'Uploading...';

  @override
  String get selectAvatar => 'Select Avatar';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get selectFromGallery => 'Select from Gallery';

  @override
  String get avatarUploadSuccess => 'Avatar uploaded successfully';

  @override
  String get avatarUploadFailed => 'Avatar upload failed';

  @override
  String get gender => 'Gender';

  @override
  String get notSet => 'Not Set';

  @override
  String get birthday => 'Birthday';

  @override
  String get profileSaveSuccess => 'Profile saved successfully';

  @override
  String get saveFailed => 'Save failed';

  @override
  String get passwordChangeSuccess => 'Password changed successfully';

  @override
  String get cropAvatar => 'Crop Avatar';

  @override
  String get reselectImage => 'Reselect Image';

  @override
  String get confirmCrop => 'Confirm';

  @override
  String get cannotParseImageFile => 'Cannot parse image file';

  @override
  String get loadUserProfileFailed => 'Failed to load user profile';

  @override
  String takePhotoFailed(String error) {
    return 'Take photo failed: $error';
  }

  @override
  String get avatarUploadFailedButProfileWillSave =>
      'Avatar upload failed, but other profile data will continue to save';

  @override
  String get loginExpiredPleaseRelogin => 'Login expired, please login again';

  @override
  String processImageFailed(String error) {
    return 'Process image failed: $error';
  }

  @override
  String get newPasswordMaxLength => 'New password cannot exceed 20 characters';

  @override
  String get userCardMemberStatusLabel => 'Membership:';

  @override
  String get userCardExpiryDateLabel => 'Expires on:';

  @override
  String get userCardUidLabel => 'UID:';

  @override
  String get languageOptionChineseSimplified => 'Chinese (Simplified)';

  @override
  String get languageOptionUyghur => 'Uyghur';

  @override
  String get languageOptionEnglish => 'English';

  @override
  String get languageOptionKazakh => 'Kazakh';

  @override
  String get languageOptionRussian => 'Russian';

  @override
  String get languageOptionFrench => 'French';

  @override
  String get languageOptionSpanish => 'Spanish';

  @override
  String get languageOptionCantonese => 'Cantonese';

  @override
  String get languageOptionArabic => 'Arabic';

  @override
  String get historyListEmpty => 'No history yet';

  @override
  String get aiGuideVoiceQueryButton => 'Voice Query';

  @override
  String get aiGuidePhotoQueryButton => 'Photo Query';

  @override
  String get aiGuideQueryHint =>
      'Welcome to MinHan Translate. Press and hold the button below to ask by photo.';

  @override
  String get faceToFaceSelectLanguagesHint =>
      'Please select languages for both sides.';

  @override
  String resendCodeTimerLabel(String seconds) {
    return 'Resend in ${seconds}s';
  }

  @override
  String get guestUser => 'Guest User';

  @override
  String get pleaseLogin => 'Please Login';

  @override
  String get membershipStatus => 'Membership:';

  @override
  String get expiresOn => 'Expires on:';

  @override
  String get editProfileButton => 'Edit';

  @override
  String get notLoggedInUser => 'Not Logged In';

  @override
  String get verificationCodeLoginTitle => 'Login with Code';

  @override
  String get phoneInputLabel => 'Phone Number';

  @override
  String get phoneInputHint => 'Enter phone number';

  @override
  String get codeInputLabel => 'Verification Code';

  @override
  String get codeInputHint => 'Enter verification code';

  @override
  String resendCodeTimerButton(String seconds) {
    return 'Resend in ${seconds}s';
  }

  @override
  String get loginButton => 'Login';

  @override
  String get autoRegisterHint =>
      'Unregistered numbers will be automatically registered';

  @override
  String get reminderTitle => 'Reminder';

  @override
  String get loginRequiredForDistributionMessage =>
      'Please log in to use the Distribution Management feature';

  @override
  String get distributionAccessDeniedMessage =>
      'Your account does not have distributor permissions. You can apply to become a distributor';

  @override
  String get goToLoginButton => 'Log In';

  @override
  String get applyButton => 'Apply';

  @override
  String get typeMessageHint => 'Type a message';

  @override
  String verificationCodeSentSeconds(String seconds) {
    return '${seconds}s';
  }

  @override
  String get welcomeBackTitle => 'Welcome Back';

  @override
  String get loginWithPhoneSubtitle => 'Please login with your phone number';

  @override
  String get registerAccountButton => 'Register';

  @override
  String get passwordLoginButton => 'Login with Password';

  @override
  String get loginAgreementText =>
      'By logging in, you agree to the Terms of Service and Privacy Policy';

  @override
  String get loginFailed => 'Login failed';

  @override
  String loginRequiredForFeature(String featureName) {
    return 'Please log in to use $featureName feature';
  }

  @override
  String get loginRequiredGeneral =>
      'This feature requires login. Please log in first';

  @override
  String get loginButtonText => 'Login';

  @override
  String get applicationSubmitted =>
      'Application submitted, please wait for review';

  @override
  String get applicationFailed => 'Application failed';

  @override
  String get distributorBenefitDescription =>
      'As a distributor, you can promote products and earn commission income';

  @override
  String get aiGuidePhotoQuestion => 'Health Assistant Photo Question';

  @override
  String get aiGuideVoiceQuestion => 'Health Assistant Voice Question';

  @override
  String get recordingStartFailedCheckPermission =>
      'Recording failed to start, please check microphone permission';

  @override
  String get adjustFontSize => 'Adjust Font Size';

  @override
  String get fontPreviewText => 'Font Preview 字体预览';

  @override
  String get smallSize => 'Small';

  @override
  String get largeSize => 'Large';

  @override
  String currentSizeLabel(String size) {
    return 'Current Size: $size';
  }

  @override
  String get smallSizeLabel => 'S';

  @override
  String get mediumSizeLabel => 'M';

  @override
  String get largeSizeLabel => 'L';

  @override
  String get vipMembershipTitle => 'VIP Membership Benefits';

  @override
  String get higherAccuracy => 'Higher Accuracy';

  @override
  String get adFree => 'Ad-free Experience';

  @override
  String get unlimitedUsage => 'Unlimited Usage';

  @override
  String get monthlyMembership => 'Monthly';

  @override
  String get annualMembership => 'Annual';

  @override
  String savePercentage(String percentage) {
    return 'Save $percentage%';
  }

  @override
  String get gettingPriceInfo => 'Getting price information...';

  @override
  String get originalPrice => 'Original Price';

  @override
  String get approximately => 'Approx.';

  @override
  String get monthlyUnit => 'month';

  @override
  String get yearlyUnit => 'year';

  @override
  String get perMonth => '/month';

  @override
  String get perYear => '/year';

  @override
  String get activateVipNow => 'Activate VIP Now';

  @override
  String get serviceTermsAgreement =>
      'By activating, you agree to the Terms of Service and Privacy Policy';

  @override
  String get monthlyMemberPackage => 'Monthly Plan';

  @override
  String get annualMemberPackage => 'Annual Plan';

  @override
  String get oneMonthVipPrivileges => 'One month VIP privileges';

  @override
  String get oneYearVipPrivileges =>
      'One year VIP privileges with auto-renewal';

  @override
  String get priceLoadFailed =>
      'Failed to get price information, showing default prices';

  @override
  String get priceInfoLoading => 'Price information loading, please wait...';

  @override
  String aboutToActivate(String packageName) {
    return 'About to activate $packageName, please wait...';
  }

  @override
  String get annualVipMember => 'Annual VIP';

  @override
  String get monthlyVipMember => 'Monthly VIP';

  @override
  String get lifetimeVipMember => 'Lifetime VIP';

  @override
  String get fontSizeSmall => 'Small';

  @override
  String get fontSizeMedium => 'Medium';

  @override
  String get fontSizeLarge => 'Large';

  @override
  String get myOrders => 'My Orders';

  @override
  String get chatHistory => 'Chat History';

  @override
  String get doctorManagement => 'Doctor Management';

  @override
  String get adminManagement => 'Admin';

  @override
  String get myLikes => 'My Likes';

  @override
  String get myFavorites => 'My Favorites';

  @override
  String get shoppingCart => 'Shopping Cart';

  @override
  String get distributionManagementFeature => 'Affiliate Management';

  @override
  String get adminManagementFeature => 'Admin Management';

  @override
  String get doctorManagementFeature => 'Doctor Management';

  @override
  String get onlyDoctorUsersCanAccess =>
      'Only doctor users can access doctor management features';

  @override
  String get viewShoppingCartFeature => 'View Shopping Cart';

  @override
  String get pleaseSelectItemsToDelete => 'Please select items to delete';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String confirmDeleteItems(int count) {
    return 'Are you sure you want to delete the selected $count items?';
  }

  @override
  String get deleteSuccess => 'Deleted successfully';

  @override
  String deleteFailed(String error) {
    return 'Delete failed: $error';
  }

  @override
  String get pleaseSelectItemsToCheckout => 'Please select items to checkout';

  @override
  String get cartTitle => 'Shopping Cart';

  @override
  String get myOrdersTitle => 'My Orders';

  @override
  String get myOrdersFeature => 'My Orders';

  @override
  String get orderStatusAll => 'All';

  @override
  String get orderStatusPending => 'Pending Payment';

  @override
  String get orderStatusPendingShipment => 'Pending Shipment';

  @override
  String get orderStatusShipped => 'Shipped';

  @override
  String get orderStatusCompleted => 'Completed';

  @override
  String get orderStatusCancelled => 'Cancelled';

  @override
  String get orderStatusUnknown => 'Unknown Status';

  @override
  String get payStatusUnpaid => 'Unpaid';

  @override
  String get payStatusPaid => 'Paid';

  @override
  String get payStatusRefunded => 'Refunded';

  @override
  String get product => 'Product';

  @override
  String get noOrders => 'No orders';

  @override
  String get adminManagementTitle => 'Admin Management';

  @override
  String get doctorManagementTab => 'Doctor Management';

  @override
  String get productReviewTab => 'Product Review';

  @override
  String get orderManagementTab => 'Order Management';

  @override
  String get noDoctorData => 'No doctor data';

  @override
  String get loadDoctorListFailed => 'Failed to load doctor list';

  @override
  String get addDoctor => 'Add Doctor';

  @override
  String get editDoctor => 'Edit Doctor';

  @override
  String get deleteDoctor => 'Delete Doctor';

  @override
  String get confirmDeleteDoctor => 'Confirm Delete Doctor';

  @override
  String deleteDoctorConfirmMessage(String doctorName) {
    return 'Are you sure you want to delete doctor $doctorName? This action cannot be undone.';
  }

  @override
  String get deleteDoctorSuccess => 'Doctor deleted successfully';

  @override
  String get deleteDoctorFailed => 'Failed to delete doctor';

  @override
  String get detailedInfo => 'Detailed Information';

  @override
  String get aiSettings => 'AI Settings';

  @override
  String get statusSettings => 'Status Settings';

  @override
  String get enterDoctorName => 'Please enter doctor name';

  @override
  String get specialty => 'Specialty';

  @override
  String get enterSpecialty => 'Please enter specialty, e.g.: Cardiology';

  @override
  String get description => 'Description';

  @override
  String get enterDescription => 'Please enter doctor description';

  @override
  String get enterDetailedDescription =>
      'Please enter detailed description, including ingredients, effects, usage, etc.';

  @override
  String get addSpecialty => 'Add Specialty';

  @override
  String get enterSpecialtyField =>
      'Please enter specialty, e.g.: Coronary Heart Disease';

  @override
  String get deleteSpecialty => 'Delete this specialty';

  @override
  String get systemPrompt => 'System Prompt';

  @override
  String get enterSystemPrompt =>
      'Enter AI system prompt to define AI doctor\'s behavior and response style';

  @override
  String get avatarUrl => 'Avatar URL';

  @override
  String get enterAvatarUrl =>
      'Enter avatar image URL or click above to upload avatar';

  @override
  String get llmModelName => 'Model Name';

  @override
  String get enterLlmModelName =>
      'Enter LLM model name, e.g.: qwen-max-latest, claude-3-sonnet, etc.';

  @override
  String get enterYearsOfExperience => 'Enter years of experience';

  @override
  String get rating => 'Rating';

  @override
  String get enterRating => 'Enter rating (0.0-5.0)';

  @override
  String get digitalHumanUrl => 'Digital Human URL';

  @override
  String get enterDigitalHumanUrl => 'Enter digital human URL (optional)';

  @override
  String get phone => 'Phone';

  @override
  String get enterPhone => 'Enter doctor\'s contact phone';

  @override
  String get enterAddress => 'Enter doctor\'s work address';

  @override
  String get isActive => 'Active Status';

  @override
  String get saveDoctor => 'Save Doctor';

  @override
  String get saving => 'Saving...';

  @override
  String get createDoctorSuccess => 'Doctor created successfully';

  @override
  String get updateDoctorSuccess => 'Doctor updated successfully';

  @override
  String get createDoctorFailed => 'Failed to create doctor';

  @override
  String get updateDoctorFailed => 'Failed to update doctor';

  @override
  String get enabled => 'Enabled';

  @override
  String get disabled => 'Disabled';

  @override
  String get enterValidPhone => 'Enter a valid phone number';

  @override
  String get doctorAvatar => 'Doctor Avatar';

  @override
  String get uploadAvatar => 'Upload Avatar';

  @override
  String get enableStatus => 'Enable Status';

  @override
  String get doctorEnabledDescription =>
      'Doctor is currently enabled, users can chat with them';

  @override
  String get doctorDisabledDescription =>
      'Doctor is currently disabled, users cannot chat with them';

  @override
  String get specialtyInputHint =>
      'Tip: Fill in one specialty per input field, they will be automatically merged when saved';

  @override
  String get confirmExit => 'Confirm Exit';

  @override
  String get unsavedChangesWarning =>
      'You have unsaved changes, are you sure you want to exit?';

  @override
  String get exit => 'Exit';

  @override
  String get uploadFailed => 'Upload failed';

  @override
  String get supportedImageFormats => 'Supports JPG, PNG formats, max size 5MB';

  @override
  String get collapse => 'Collapse';

  @override
  String get multilingual => 'Multilingual';

  @override
  String get all => 'All';

  @override
  String get pending => 'Pending';

  @override
  String get approved => 'Approved';

  @override
  String get rejected => 'Rejected';

  @override
  String get offline => 'Offline';

  @override
  String loadDataFailed(String error) {
    return 'Failed to load data: $error';
  }

  @override
  String get loadDoctorMultilingualDataFailed =>
      'Failed to load doctor multilingual data';

  @override
  String get doctorCreationCompleteCallback =>
      'Doctor creation complete callback';

  @override
  String get enterModelName => 'Please enter model name';

  @override
  String get imageSizeExceedsLimit => 'Image size cannot exceed 5MB';

  @override
  String get doctorManagementTitle => 'Doctor Management';

  @override
  String get productManagementTab => 'Product Management';

  @override
  String get dataOverview => 'Data Overview';

  @override
  String get products => 'Products';

  @override
  String get pendingReview => 'Pending Review';

  @override
  String get totalProducts => 'Total Products';

  @override
  String get totalSales => 'Total Sales';

  @override
  String get totalOrders => 'Total Orders';

  @override
  String get approvedProducts => 'Approved';

  @override
  String get rejectedProducts => 'Rejected';

  @override
  String get reviewStatus => 'Review Status';

  @override
  String get inventory => 'Inventory';

  @override
  String get salesVolume => 'Sales Volume';

  @override
  String get orderOverview => 'Order Overview';

  @override
  String get shippingStatus => 'Shipping Status';

  @override
  String get paymentStatus => 'Payment Status';

  @override
  String get totalOrderNumber => 'Total Order Number';

  @override
  String get customer => 'Customer';

  @override
  String get pendingPayment => 'Pending Payment';

  @override
  String get pendingShipment => 'Pending Shipment';

  @override
  String get shipped => 'Shipped';

  @override
  String get completed => 'Completed';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get reviewApproved => 'Review approved';

  @override
  String get reviewRejected => 'Rejected';

  @override
  String get offShelf => 'Off Shelf';

  @override
  String get addProduct => 'Add Product';

  @override
  String get editProduct => 'Edit Product';

  @override
  String get enterProductName => 'Please enter product name';

  @override
  String get productDescription => 'Product Description';

  @override
  String get enterProductDescription => 'Please enter product description';

  @override
  String get productCategory => 'Product Category';

  @override
  String get enterProductCategory =>
      'Please enter product category, e.g.: Health supplements, Medicine, etc.';

  @override
  String get enterManufacturer => 'Please enter manufacturer name';

  @override
  String get productMainImage => 'Product Main Image';

  @override
  String get priceInfo => 'Price Information';

  @override
  String get currentPrice => 'Current Price';

  @override
  String get enterCurrentPrice => 'Please enter current price';

  @override
  String get enterOriginalPrice => 'Please enter original price (optional)';

  @override
  String get inventoryInfo => 'Inventory Information';

  @override
  String get inventoryCount => 'Inventory Count';

  @override
  String get enterInventoryCount => 'Please enter inventory count';

  @override
  String get productDetailImages => 'Product Detail Images';

  @override
  String get productCreatedSuccess => 'Product created successfully';

  @override
  String get productUpdatedSuccess => 'Product updated successfully';

  @override
  String get loadProductMultilingualDataFailed =>
      'Failed to load product multilingual data';

  @override
  String get orderNumber => 'Order Number';

  @override
  String get orderTotal => 'Total';

  @override
  String customerLabel(String customer) {
    return 'Customer: $customer';
  }

  @override
  String get orderNumberShort => 'Order No.';

  @override
  String get paidStatus => 'Paid';

  @override
  String get unpaidStatus => 'Unpaid';

  @override
  String get trackingNumber => 'Tracking No.';

  @override
  String get networkImage => 'Network Image';

  @override
  String get maxSixImages => 'Max 6 images';

  @override
  String get addImage => 'Add Image';

  @override
  String get expressInfo => 'Express Info';

  @override
  String get expressNumber => 'Express Number';

  @override
  String get expressCompany => 'Express Company';

  @override
  String get shippingNote => 'Shipping Note';

  @override
  String get noShippingInfo => 'No shipping information';

  @override
  String orderCount(int count) {
    return '$count orders';
  }

  @override
  String get imageSelected => 'Image Selected';

  @override
  String get unsavedChangesMessage =>
      'You have unsaved changes, are you sure you want to exit?';

  @override
  String get confirmAction => 'Confirm';

  @override
  String get noProductsMessage => 'No products yet';

  @override
  String get addFirstProductHint =>
      'Click the button below to add your first product';

  @override
  String get noOrdersMessage => 'Order data will be displayed here';

  @override
  String get ordersWillShowHere =>
      'Orders will appear here when customers purchase your products';

  @override
  String get reviewSuccess => 'Review successful';

  @override
  String get reviewFailed => 'Review failed';

  @override
  String get batchReviewSuccess => 'Batch review successful';

  @override
  String get batchReviewFailed => 'Batch review failed';

  @override
  String get allDoctors => 'All Doctors';

  @override
  String get pleaseSelectProducts => 'Please select products to review';

  @override
  String get batchApproved => 'Batch approved';

  @override
  String get batchRejected => 'Batch rejected';

  @override
  String get batchApprove => 'Batch Approve';

  @override
  String get batchReject => 'Batch Reject';

  @override
  String get deselectAll => 'Deselect All';

  @override
  String get exitSelection => 'Exit Selection';

  @override
  String get batchSelection => 'Batch Selection';

  @override
  String get reviewStatistics => 'Review Statistics';

  @override
  String get total => 'Total';

  @override
  String get sales => 'Sales';

  @override
  String get approve => 'Approve';

  @override
  String get reject => 'Reject';

  @override
  String get rejectReview => 'Reject Review';

  @override
  String confirmRejectProduct(String productName) {
    return 'Are you sure you want to reject product \"$productName\"?';
  }

  @override
  String get rejectReason => 'Reject reason (optional)';

  @override
  String get confirmReject => 'Confirm Reject';

  @override
  String get filterDoctors => 'Filter Doctors';

  @override
  String get loadProductDetailFailed => 'Failed to load product detail';

  @override
  String get unknownDoctor => 'Unknown Doctor';

  @override
  String get createdAt => 'Created At';

  @override
  String get updatedAt => 'Updated At';

  @override
  String get productImages => 'Product Images';

  @override
  String get productSpecifications => 'Product Specifications';

  @override
  String get reviewInfo => 'Review Information';

  @override
  String get productId => 'Product ID';

  @override
  String get viewShipping => 'View Shipping';

  @override
  String get cancelOrder => 'Cancel Order';

  @override
  String get payNow => 'Pay Now';

  @override
  String get confirmCancel => 'Confirm Cancel';

  @override
  String get confirmCancelOrder =>
      'Are you sure you want to cancel this order?';

  @override
  String get orderCancelled => 'Order cancelled';

  @override
  String cancelOrderFailed(String error) {
    return 'Cancel order failed: $error';
  }

  @override
  String get getPaymentParamsFailed => 'Failed to get payment parameters';

  @override
  String get paymentCancelled => 'Payment cancelled';

  @override
  String get confirmPayment => 'Confirm Payment';

  @override
  String get paymentAmount => 'Payment Amount';

  @override
  String get confirmPaymentButton => 'Confirm Payment';

  @override
  String get orderPaidSuccessfully => 'Your order has been paid successfully';

  @override
  String get currentConversationDeleted =>
      'Current conversation has been deleted';

  @override
  String get newConversation => 'New Conversation';

  @override
  String get refreshSuccess => 'Refresh successful';

  @override
  String refreshFailed(String error) {
    return 'Refresh failed: $error';
  }

  @override
  String get titleUpdateSuccess => 'Title updated successfully';

  @override
  String titleUpdateFailed(String error) {
    return 'Title update failed: $error';
  }

  @override
  String get conversationNotFound => 'Conversation not found';

  @override
  String get chatHistoryPageTitle => 'Chat History';

  @override
  String get noChatRecordsForDate => 'No chat records for this date';

  @override
  String get enterNewTitle => 'Please enter a new title';

  @override
  String get year => '';

  @override
  String get month => '';

  @override
  String get monthLabel => 'Month:';

  @override
  String get yearLabel => 'Year:';

  @override
  String get weekdayMon => 'Mon';

  @override
  String get weekdayTue => 'Tue';

  @override
  String get weekdayWed => 'Wed';

  @override
  String get weekdayThu => 'Thu';

  @override
  String get weekdayFri => 'Fri';

  @override
  String get weekdaySat => 'Sat';

  @override
  String get weekdaySun => 'Sun';

  @override
  String get selectYearMonth => 'Select Year and Month';

  @override
  String get ok => 'OK';

  @override
  String get digitalHumanChatInDevelopment =>
      'Digital human AI chat page is under development...';

  @override
  String get voiceTranslationFeature => 'Voice Translation';

  @override
  String get chatFeature => 'Chat Feature';

  @override
  String get voiceFeature => 'Voice Feature';

  @override
  String get chatHistoryFeature => 'Chat History';

  @override
  String get voiceRecognitionSuccess => 'Voice recognition successful';

  @override
  String get voiceRecognitionFailed =>
      'Voice recognition failed, please try again';

  @override
  String get addTextDescriptionOrSendImage =>
      'Add text description or send image directly';

  @override
  String get refresh => 'Refresh';

  @override
  String get noChatRecords => 'No chat records';

  @override
  String get filterDoctorsLabel => 'Filter Doctors';

  @override
  String get allDoctorsOption => 'All Doctors';

  @override
  String get unknownDoctorLabel => 'Unknown Doctor';

  @override
  String quantityLabel(int quantity) {
    return 'Quantity: $quantity';
  }

  @override
  String doctorLabel(String doctor) {
    return 'Doctor: $doctor';
  }

  @override
  String unitPriceLabel(String price) {
    return 'Unit Price: ¥$price';
  }

  @override
  String totalAmountLabel(String amount) {
    return 'Total: ¥$amount';
  }

  @override
  String orderNumberLabel(String orderNumber) {
    return 'Order No: $orderNumber';
  }

  @override
  String get adminShipAction => 'Admin Ship';

  @override
  String get markCompleteAction => 'Mark Complete';

  @override
  String get markCancelAction => 'Mark Cancel';

  @override
  String get deleteOrderAction => 'Delete Order';

  @override
  String get totalOrdersLabel => 'Total Orders';

  @override
  String get totalSalesLabel => 'Total Sales';

  @override
  String get completedOrdersLabel => 'Completed';

  @override
  String get pendingPaymentLabel => 'Pending Payment';

  @override
  String get pendingShipmentLabel => 'Pending Shipment';

  @override
  String get cancelledOrdersLabel => 'Cancelled';

  @override
  String get ordersUnit => ' orders';

  @override
  String get orderStatusUpdateSuccess => 'Order status updated successfully';

  @override
  String updateFailed(String error) {
    return 'Update failed: $error';
  }

  @override
  String get noOrdersTitle => 'No Orders';

  @override
  String get batchOperationSuccess => 'Batch operation successful';

  @override
  String batchOperationFailed(String error) {
    return 'Batch operation failed: $error';
  }

  @override
  String get confirmDeleteTitle => 'Confirm Delete';

  @override
  String get confirmDeleteMessage =>
      'Are you sure you want to delete this order? This action cannot be undone.';

  @override
  String get cancelAction => 'Cancel';

  @override
  String get deleteAction => 'Delete';

  @override
  String batchOperationsTitle(int count) {
    return 'Batch Operations ($count orders)';
  }

  @override
  String get markAsCompletedAction => 'Mark as Completed';

  @override
  String get markAsCancelledAction => 'Mark as Cancelled';

  @override
  String get markAsShippedAction => 'Mark as Shipped';

  @override
  String get ordersUnitSuffix => ' orders';

  @override
  String get orderStatusUpdateSuccessMessage =>
      'Order status updated successfully';

  @override
  String get paymentStatusUpdateSuccessMessage =>
      'Payment status updated successfully';

  @override
  String get orderDeleteSuccessMessage => 'Order deleted successfully';

  @override
  String get pleaseSelectOrdersMessage => 'Please select orders to operate';

  @override
  String get markAsPaidAction => 'Mark as Paid';

  @override
  String get orderStatusPendingPayment => 'Pending Payment';

  @override
  String get orderStatusPaid => 'Paid';

  @override
  String get orderDetailTitle => 'Order Details';

  @override
  String get markAsRefundAction => 'Mark as Refund';

  @override
  String get markAsCompleteAction => 'Mark as Complete';

  @override
  String get markAsCancelAction => 'Mark as Cancel';

  @override
  String get waitingForPaymentDescription =>
      'Waiting for customer to complete payment';

  @override
  String get waitingForShipmentDescription => 'Waiting for doctor to ship';

  @override
  String get shippedDescription =>
      'Product has been shipped, waiting for customer to receive';

  @override
  String get orderCompletedDescription => 'Order has been completed';

  @override
  String get orderCancelledDescription => 'Order has been cancelled';

  @override
  String get orderStatusAbnormalDescription => 'Order status is abnormal';

  @override
  String get orderStatusPendingDescription =>
      'Please complete payment as soon as possible, overdue orders will be automatically cancelled';

  @override
  String get orderStatusPreparingDescription =>
      'Your order is being prepared, please wait patiently';

  @override
  String get orderStatusShippedUserDescription =>
      'The product has been shipped, please pay attention to receive it';

  @override
  String get orderStatusCompletedUserDescription =>
      'Order completed, thank you for your purchase';

  @override
  String get orderStatusCancelledUserDescription => 'Order has been cancelled';

  @override
  String get shippingStatusWaitingReceive => 'Shipped, waiting for delivery';

  @override
  String get shippingStatusCompleted => 'Completed';

  @override
  String get shippingStatusShipped => 'Shipped';

  @override
  String get shippingStatusPending => 'Pending Payment';

  @override
  String get shippingStatusWaitingShip => 'Pending Shipment';

  @override
  String get shippingStatusCancelled => 'Cancelled';

  @override
  String get shippingStatusUnknown => 'Unknown Status';

  @override
  String get insufficientPermissionDoctorRequired =>
      'Insufficient permission, doctor permission required';

  @override
  String get getPendingShipmentOrdersFailed =>
      'Failed to get pending shipment orders';

  @override
  String get trackingNumberCannotBeEmpty => 'Tracking number cannot be empty';

  @override
  String get shipmentFailed => 'Shipment failed';

  @override
  String get orderNotExistOrNoAccess => 'Order does not exist or no access';

  @override
  String get shipmentFailedCheckOrderStatus =>
      'Shipment failed, please check order status';

  @override
  String get getShippingStatusFailed => 'Failed to get shipping status';

  @override
  String get getShippedOrdersFailed => 'Failed to get shipped orders';

  @override
  String get productInfoTitle => 'Product Information';

  @override
  String get orderInfoTitle => 'Order Information';

  @override
  String get orderNumberFieldLabel => 'Order No.';

  @override
  String get orderTimeLabel => 'Order Time';

  @override
  String get paymentTimeLabel => 'Payment Time';

  @override
  String get shipmentTimeLabel => 'Shipment Time';

  @override
  String get completionTimeLabel => 'Completion Time';

  @override
  String get customerInfoTitle => 'Customer Information';

  @override
  String get customerNicknameLabel => 'Customer Nickname';

  @override
  String get userIdLabel => 'User ID';

  @override
  String get shippingInfoTitle => 'Shipping Information';

  @override
  String get recipientLabel => 'Recipient';

  @override
  String get contactPhoneLabel => 'Contact Phone';

  @override
  String get shippingAddressLabel => 'Shipping Address';

  @override
  String get trackingInfoTitle => 'Tracking Information';

  @override
  String get viewDetailsAction => 'View Details';

  @override
  String get trackingNumberLabel => 'Tracking Number';

  @override
  String get shippingCompanyLabel => 'Shipping Company';

  @override
  String get shippingNoteLabel => 'Shipping Note';

  @override
  String get priceDetailsTitle => 'Price Details';

  @override
  String get productAmountLabel => 'Product Amount';

  @override
  String get shippingFeeLabel => 'Shipping Fee';

  @override
  String get totalPaidLabel => 'Total Paid';

  @override
  String get cancelOrderAction => 'Cancel Order';

  @override
  String get viewTrackingAction => 'View Tracking';

  @override
  String get copiedToClipboardMessage => 'Copied to clipboard';

  @override
  String get confirmDeleteOrderTitle => 'Confirm Delete';

  @override
  String get confirmDeleteOrderMessage =>
      'Are you sure you want to delete this order? This action cannot be undone.';

  @override
  String get orderDetailLoadFailedMessage => 'Failed to load order details';

  @override
  String get orderInfoLoadFailedMessage => 'Order information failed to load';

  @override
  String updateFailedMessage(String error) {
    return 'Update failed: $error';
  }

  @override
  String deleteFailedMessage(String error) {
    return 'Delete failed: $error';
  }

  @override
  String get editConversationTitle => 'Edit Conversation Title';

  @override
  String get replying => 'Replying...';

  @override
  String get imageLoadFailed => 'Image load failed';

  @override
  String get imageNotAvailable => 'Image not available';

  @override
  String get releaseToCancel => 'Release to cancel recording';

  @override
  String get recording => 'Recording';

  @override
  String get slideUpToCancel => 'Slide up to cancel';

  @override
  String get continueSlideUpToCancel =>
      'Continue sliding up to cancel recording';

  @override
  String get takePhotoAndSend => 'Take Photo & Send';

  @override
  String get selectSendArea => 'Select Send Area';

  @override
  String get send => 'Send';

  @override
  String get orderTimeline => 'Order Timeline';

  @override
  String get orderCreated => 'Order Created';

  @override
  String get paymentCompleted => 'Payment Completed';

  @override
  String get goodsShipped => 'Goods Shipped';

  @override
  String get orderCompleted => 'Order Completed';

  @override
  String get shipOrder => 'Ship Order';

  @override
  String get confirmShip => 'Confirm Ship';

  @override
  String get ship => 'Ship';

  @override
  String get enterTrackingNumber => 'Please enter tracking number';

  @override
  String get trackingNumberRequired => 'Please enter tracking number';

  @override
  String get selectShippingCompany =>
      'Please select shipping company (optional)';

  @override
  String get enterShippingNote => 'Please enter shipping note (optional)';

  @override
  String get shipSuccess => 'Shipped successfully';

  @override
  String shipFailed(String error) {
    return 'Ship failed: $error';
  }

  @override
  String get productLabel => 'Product';

  @override
  String get adminShipment => 'Admin Shipment';

  @override
  String get trackingNumberRequiredField => 'Tracking Number *';

  @override
  String get shippingCompanyHint => 'e.g.: SF Express, YTO Express, etc.';

  @override
  String get shippingNoteHint => 'Optional shipping instructions or notes';

  @override
  String get shipmentSuccess => 'Shipment successful';

  @override
  String shipmentFailedWithError(String error) {
    return 'Shipment failed: $error';
  }

  @override
  String copiedToClipboardWithTitle(String title) {
    return '$title copied to clipboard';
  }

  @override
  String get voiceRecognizing => 'Recognizing voice...';

  @override
  String get voiceRecognitionRetry =>
      'Voice recognition failed, please try again';

  @override
  String get cannotOpenPhoneAppCopied =>
      'Cannot open phone app, number copied to clipboard';

  @override
  String operationFailedManualDialWithNumber(String phoneNumber) {
    return 'Operation failed, please dial manually: $phoneNumber';
  }

  @override
  String healthAssistantVoiceRecognition(String text) {
    return 'Health Assistant voice recognition: $text';
  }

  @override
  String healthAssistantVoiceProcessingFailed(String error) {
    return 'Health Assistant voice processing failed: $error';
  }

  @override
  String loginFailedWithMessage(String message) {
    return 'Login failed: $message';
  }

  @override
  String get verificationCodeIncorrectOrExpired =>
      'Verification code is incorrect or expired';

  @override
  String get usernameAlreadyExists =>
      'Username already exists, please choose another';

  @override
  String get dpiAdaptationSettings => 'DPI Adaptation Settings';

  @override
  String get dpiAdaptationDescription =>
      'Adjust the app\'s display scaling ratio to adapt to different screen densities';

  @override
  String get currentDpiScale => 'Current Scale Ratio';

  @override
  String get systemDefault => 'System Default';

  @override
  String get small => 'Small';

  @override
  String get normal => 'Normal';

  @override
  String get large => 'Large';

  @override
  String get extraLarge => 'Extra Large';

  @override
  String get previewText => 'Preview Text';

  @override
  String get sampleText =>
      'This is a sample text to preview the current scaling effect.';

  @override
  String get applyChanges => 'Apply Changes';

  @override
  String get resetToDefault => 'Reset to Default';

  @override
  String get dpiSettingsApplied => 'DPI settings applied';

  @override
  String get dpiSettingsReset => 'DPI settings reset to default';

  @override
  String get dpiModeAuto => 'Auto Adaptation';

  @override
  String get dpiModeAutoDesc =>
      'Automatically adjust interface size based on device DPI (Recommended)';

  @override
  String get dpiModeSmall => 'Compact Mode';

  @override
  String get dpiModeSmallDesc =>
      'Smaller interface elements, suitable for high DPI devices';

  @override
  String get dpiModeStandard => 'Standard Mode';

  @override
  String get dpiModeStandardDesc => 'Default size interface elements';

  @override
  String get dpiModeLarge => 'Relaxed Mode';

  @override
  String get dpiModeLargeDesc =>
      'Larger interface elements, suitable for low DPI devices';

  @override
  String get currentStatus => 'Current Status';

  @override
  String get adaptationMode => 'Adaptation Mode';

  @override
  String get scaleFactor => 'Scale Factor';

  @override
  String get deviceInfo => 'Device Information';

  @override
  String get screenSize => 'Screen Size';

  @override
  String get devicePixelRatio => 'Device Pixel Ratio';

  @override
  String get screenDiagonal => 'Screen Diagonal';

  @override
  String get autoScaleFactor => 'Auto Scale Factor';

  @override
  String get effectPreview => 'Effect Preview';

  @override
  String get sampleButton => 'Sample Button';

  @override
  String get titleText => 'Title Text';

  @override
  String get sampleDescription =>
      'This is a sample text to preview the current DPI adaptation settings effect.';

  @override
  String get inches => 'inches';

  @override
  String get dpiAdaptation => 'DPI Adaptation';

  @override
  String get dpiAdaptationSubtitle =>
      'Adjust interface element size to adapt to different DPI devices';
}
