import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'src/config/config.dart';
import 'src/config/app_theme.dart';
import 'src/services/theme_service.dart';
import 'src/services/font_size_service.dart';
import 'src/services/language_service.dart';
import 'src/services/text_direction_service.dart';
import 'src/services/image_cache_service.dart';
import 'src/services/dpi_adaptation_service.dart';

import 'generated/l10n/app_localizations.dart';

/// 应用根组件 - 支持主题切换、字体大小调整和国际化
class App extends StatefulWidget {
  const App({super.key});

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  @override
  void initState() {
    super.initState();
    // 语言服务现在通过Provider管理，会自动初始化
  }

  /// 获取组合了字体和DPI适配的MediaQueryData
  MediaQueryData _getModifiedMediaQueryData(BuildContext context) {
    final originalData = MediaQuery.of(context);

    // 先应用字体适配
    final fontAdaptedData = FontSizeService().getModifiedMediaQueryData(
      originalData,
    );

    // 再应用DPI适配
    final dpiAdaptedData = DpiAdaptationService().getModifiedMediaQueryData(
      context,
      fontAdaptedData,
    );

    return dpiAdaptedData;
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // 语言服务
        ChangeNotifierProvider<LanguageService>(
          create: (_) => LanguageService(),
        ),
        // 文本方向服务
        ChangeNotifierProvider<TextDirectionService>(
          create: (_) => TextDirectionService(),
        ),
      ],
      child: AnimatedBuilder(
        animation: Listenable.merge([
          ThemeService(),
          FontSizeService(),
          LanguageService(),
          TextDirectionService(),
          DpiAdaptationService(),
        ]),
        builder: (context, child) {
          return MaterialApp(
            navigatorKey: NavigationService.navigatorKey,
            title: LanguageService().currentLocale.languageCode == 'en'
                ? 'Health Assistant'
                : LanguageService().currentLocale.languageCode == 'ug'
                ? 'ساغلاملىق ياردەمچىسى'
                : '健康助手',
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeService().themeMode,
            onGenerateRoute: AppRoutes.generateRoute,
            initialRoute: AppRoutes.initialRoute,
            debugShowCheckedModeBanner: false,
            // 国际化配置
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: AppLocalizations.supportedLocales,
            locale: LanguageService().currentLocale,
            // RTL支持配置
            localeResolutionCallback: (locale, supportedLocales) {
              // 确保文本方向服务已初始化
              if (TextDirectionService().isInitialized) {
                // 根据语言自动更新文本方向
                final isRTL = TextDirectionService.rtlLanguages.contains(
                  locale?.languageCode ?? 'zh',
                );
                final expectedDirection = isRTL
                    ? TextDirection.rtl
                    : TextDirection.ltr;
                if (TextDirectionService().currentDirection !=
                    expectedDirection) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    TextDirectionService().setTextDirection(expectedDirection);
                  });
                }
              }
              return locale;
            },
            builder: (context, child) {
              return Directionality(
                textDirection: TextDirectionService().currentDirection,
                child: MediaQuery(
                  data: _getModifiedMediaQueryData(context),
                  child: child ?? const SizedBox.shrink(),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
