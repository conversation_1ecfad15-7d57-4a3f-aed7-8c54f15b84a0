import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'dart:developer' as developer;
import 'app.dart';
import 'src/services/theme_service.dart';
import 'src/services/auth_service.dart';
import 'src/services/avatar_manager_service.dart';
import 'src/services/user_info_manager_service.dart';
import 'src/services/image_cache_service.dart';
import 'src/services/dpi_adaptation_service.dart';
import 'src/services/health_profile_cache_service.dart';
import 'src/services/user_profile_cache_service.dart';
import 'src/services/doctor_product_cache_service.dart';
import 'src/services/user_product_cache_service.dart';
import 'src/services/cart_cache_service.dart';
import 'src/services/user_interaction_cache_service.dart';
import 'src/services/order_cache_service.dart';

import 'src/services/font_size_service.dart';
import 'src/services/language_service.dart';
import 'src/services/text_direction_service.dart';
import 'src/services/error_logger_service.dart';
import 'src/services/app_initialization_service.dart';

// 过滤特定的Android系统警告日志
void _filterAndroidSystemLogs() {
  developer.log('正在设置Android系统日志过滤器...');

  // 由于这些警告来自底层系统而非Flutter，无法在Flutter层面完全过滤
  // 但可以减少Flutter日志中的干扰信息
  debugPrint = (String? message, {int? wrapWidth}) {
    if (message == null) return;

    // 过滤掉特定的系统级别警告
    if (message.contains('Access denied finding property "vendor.display') ||
        message.contains('vendor.gpp.create_frc_extension') ||
        message.contains('GetGpuPixelFormat: No map for format') ||
        message.contains('Memory Layout input parameter validation failed')) {
      // 完全忽略这些日志
      return;
    }

    // 正常输出其他日志
    developer.log(message);
  };
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 设置日志过滤
  _filterAndroidSystemLogs();

  // 初始化错误日志服务（最优先初始化，用于捕获后续初始化过程中的错误）
  await ErrorLoggerService().initialize();

  // 等待Flutter引擎完全初始化
  await Future.delayed(const Duration(milliseconds: 100));

  // 地图相关服务已移除

  // 初始化核心服务（必须成功）
  await Future.wait([
    ThemeService().initialize(),
    AuthService().initialize(),
    AvatarManagerService().initialize(),
    UserInfoManagerService().initialize(),
    FontSizeService().initialize(),
    LanguageService().initialize(),
    TextDirectionService().initialize(), // 添加文本方向服务初始化
    ImageCacheService.initialize(), // 初始化图片缓存服务
    DpiAdaptationService().initialize(), // 初始化DPI适配服务
    HealthProfileCacheService().initialize(), // 初始化健康档案缓存服务
    UserProfileCacheService().initialize(), // 初始化用户资料缓存服务
    DoctorProductCacheService().initialize(), // 初始化产品缓存服务
    UserProductCacheService().initialize(), // 初始化用户端产品缓存服务
    CartCacheService().initialize(), // 初始化购物车缓存服务
    UserInteractionCacheService().initialize(), // 初始化用户互动缓存服务
    OrderCacheService().initialize(), // 初始化订单缓存服务
  ]);

  // 异步初始化应用数据（不阻塞启动）
  AppInitializationService().initializeApp().catchError((e) {
    print('应用数据初始化失败，将在后续重试: $e');
    return false; // 返回false表示初始化失败
  });

  // 设置系统UI样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(Phoenix(child: const App()));
}
