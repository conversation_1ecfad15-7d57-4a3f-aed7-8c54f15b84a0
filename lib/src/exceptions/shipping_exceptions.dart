/// 物流相关异常类
abstract class ShippingException implements Exception {
  final String message;
  const ShippingException(this.message);

  @override
  String toString() => message;
}

/// 权限不足异常
class InsufficientPermissionException extends ShippingException {
  const InsufficientPermissionException() : super('INSUFFICIENT_PERMISSION');
}

/// 获取待发货订单失败异常
class GetPendingShipmentOrdersFailedException extends ShippingException {
  const GetPendingShipmentOrdersFailedException() : super('GET_PENDING_SHIPMENT_ORDERS_FAILED');
}

/// 快递单号为空异常
class TrackingNumberEmptyException extends ShippingException {
  const TrackingNumberEmptyException() : super('TRACKING_NUMBER_EMPTY');
}

/// 发货失败异常
class ShipmentFailedException extends ShippingException {
  const ShipmentFailedException() : super('SHIPMENT_FAILED');
}

/// 订单不存在或无权访问异常
class OrderNotExistException extends ShippingException {
  const OrderNotExistException() : super('ORDER_NOT_EXIST');
}

/// 发货失败请检查订单状态异常
class ShipmentFailedCheckStatusException extends ShippingException {
  const ShipmentFailedCheckStatusException() : super('SHIPMENT_FAILED_CHECK_STATUS');
}

/// 获取物流状态失败异常
class GetShippingStatusFailedException extends ShippingException {
  const GetShippingStatusFailedException() : super('GET_SHIPPING_STATUS_FAILED');
}

/// 获取已发货订单失败异常
class GetShippedOrdersFailedException extends ShippingException {
  const GetShippedOrdersFailedException() : super('GET_SHIPPED_ORDERS_FAILED');
}
