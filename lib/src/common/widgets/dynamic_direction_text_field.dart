import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/rtl_util.dart';

/// 动态文本方向输入框组件
/// 根据输入内容自动调整文本方向和对齐方式
class DynamicDirectionTextField extends StatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final String? hintText;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final bool enabled;
  final bool readOnly;
  final bool obscureText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onTap;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final bool autofocus;
  final bool enableSuggestions;
  final bool autocorrect;

  const DynamicDirectionTextField({
    super.key,
    this.controller,
    this.focusNode,
    this.hintText,
    this.style,
    this.hintStyle,
    this.decoration,
    this.keyboardType,
    this.textInputAction,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.enabled = true,
    this.readOnly = false,
    this.obscureText = false,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.inputFormatters,
    this.validator,
    this.autofocus = false,
    this.enableSuggestions = true,
    this.autocorrect = true,
  });

  @override
  State<DynamicDirectionTextField> createState() => _DynamicDirectionTextFieldState();
}

class _DynamicDirectionTextFieldState extends State<DynamicDirectionTextField> {
  late TextEditingController _controller;
  bool _isControllerOwned = false;
  
  // 文本方向状态
  TextDirection _textDirection = TextDirection.ltr;
  TextAlign _textAlign = TextAlign.left;

  @override
  void initState() {
    super.initState();
    
    // 初始化控制器
    if (widget.controller != null) {
      _controller = widget.controller!;
    } else {
      _controller = TextEditingController();
      _isControllerOwned = true;
    }
    
    // 监听文本变化
    _controller.addListener(_onTextChanged);
    
    // 初始化文本方向
    _updateTextDirection(_controller.text);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    if (_isControllerOwned) {
      _controller.dispose();
    }
    super.dispose();
  }

  /// 文本变化监听器
  void _onTextChanged() {
    _updateTextDirection(_controller.text);
    widget.onChanged?.call(_controller.text);
  }

  /// 更新文本方向
  void _updateTextDirection(String text) {
    final newDirection = RTLUtil.detectTextDirection(text);
    final newAlign = RTLUtil.detectTextAlign(text);
    
    if (newDirection != _textDirection || newAlign != _textAlign) {
      setState(() {
        _textDirection = newDirection;
        _textAlign = newAlign;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 合并装饰器
    final decoration = widget.decoration ?? InputDecoration(
      hintText: widget.hintText,
      hintStyle: widget.hintStyle,
    );

    // 动态字体样式（维吾尔语使用专用字体）
    final dynamicStyle = widget.style?.copyWith(
      fontFamily: RTLUtil.containsUyghurCharacters(_controller.text) 
          ? 'UKIJTor' 
          : widget.style?.fontFamily,
    ) ?? TextStyle(
      fontFamily: RTLUtil.containsUyghurCharacters(_controller.text) 
          ? 'UKIJTor' 
          : null,
    );

    return TextFormField(
      controller: _controller,
      focusNode: widget.focusNode,
      style: dynamicStyle,
      decoration: decoration,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      obscureText: widget.obscureText,
      onTap: widget.onTap,
      inputFormatters: widget.inputFormatters,
      validator: widget.validator,
      autofocus: widget.autofocus,
      enableSuggestions: widget.enableSuggestions,
      autocorrect: widget.autocorrect,
      // 动态文本方向和对齐
      textDirection: _textDirection,
      textAlign: _textAlign,
      onFieldSubmitted: widget.onSubmitted,
    );
  }
}

/// 动态文本方向普通输入框组件（非表单）
class DynamicDirectionTextFieldPlain extends StatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final String? hintText;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final bool enabled;
  final bool readOnly;
  final bool obscureText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onTap;
  final List<TextInputFormatter>? inputFormatters;
  final bool autofocus;
  final bool enableSuggestions;
  final bool autocorrect;

  const DynamicDirectionTextFieldPlain({
    super.key,
    this.controller,
    this.focusNode,
    this.hintText,
    this.style,
    this.hintStyle,
    this.decoration,
    this.keyboardType,
    this.textInputAction,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.enabled = true,
    this.readOnly = false,
    this.obscureText = false,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.inputFormatters,
    this.autofocus = false,
    this.enableSuggestions = true,
    this.autocorrect = true,
  });

  @override
  State<DynamicDirectionTextFieldPlain> createState() => _DynamicDirectionTextFieldPlainState();
}

class _DynamicDirectionTextFieldPlainState extends State<DynamicDirectionTextFieldPlain> {
  late TextEditingController _controller;
  bool _isControllerOwned = false;
  
  // 文本方向状态
  TextDirection _textDirection = TextDirection.ltr;
  TextAlign _textAlign = TextAlign.left;

  @override
  void initState() {
    super.initState();
    
    // 初始化控制器
    if (widget.controller != null) {
      _controller = widget.controller!;
    } else {
      _controller = TextEditingController();
      _isControllerOwned = true;
    }
    
    // 监听文本变化
    _controller.addListener(_onTextChanged);
    
    // 初始化文本方向
    _updateTextDirection(_controller.text);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    if (_isControllerOwned) {
      _controller.dispose();
    }
    super.dispose();
  }

  /// 文本变化监听器
  void _onTextChanged() {
    _updateTextDirection(_controller.text);
    widget.onChanged?.call(_controller.text);
  }

  /// 更新文本方向
  void _updateTextDirection(String text) {
    final newDirection = RTLUtil.detectTextDirection(text);
    final newAlign = RTLUtil.detectTextAlign(text);
    
    if (newDirection != _textDirection || newAlign != _textAlign) {
      setState(() {
        _textDirection = newDirection;
        _textAlign = newAlign;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 合并装饰器
    final decoration = widget.decoration ?? InputDecoration(
      hintText: widget.hintText,
      hintStyle: widget.hintStyle,
    );

    // 动态字体样式（维吾尔语使用专用字体）
    final dynamicStyle = widget.style?.copyWith(
      fontFamily: RTLUtil.containsUyghurCharacters(_controller.text) 
          ? 'UKIJTor' 
          : widget.style?.fontFamily,
    ) ?? TextStyle(
      fontFamily: RTLUtil.containsUyghurCharacters(_controller.text) 
          ? 'UKIJTor' 
          : null,
    );

    return TextField(
      controller: _controller,
      focusNode: widget.focusNode,
      style: dynamicStyle,
      decoration: decoration,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      obscureText: widget.obscureText,
      onTap: widget.onTap,
      inputFormatters: widget.inputFormatters,
      autofocus: widget.autofocus,
      enableSuggestions: widget.enableSuggestions,
      autocorrect: widget.autocorrect,
      // 动态文本方向和对齐
      textDirection: _textDirection,
      textAlign: _textAlign,
      onSubmitted: widget.onSubmitted,
    );
  }
}
