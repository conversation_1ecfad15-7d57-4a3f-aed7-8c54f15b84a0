import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// 高性能Skeleton加载组件
/// 基于uView设计风格，实现由左到右的流畅加载动画
class SkeletonLoading extends StatelessWidget {
  final double width;
  final double height;
  final bool animate;
  final bool loading;
  final BorderRadius? borderRadius;
  final List<SkeletonItem>? items;
  final Widget? child;

  const SkeletonLoading({
    super.key,
    this.width = double.infinity,
    this.height = double.infinity, // 改为填充父容器
    this.animate = true,
    this.loading = true,
    this.borderRadius,
    this.items,
    this.child,
  });

  /// 地图专用Skeleton
  const SkeletonLoading.map({
    super.key,
    this.width = double.infinity,
    this.height = double.infinity, // 改为填充父容器
    this.animate = true,
    this.loading = true,
    this.borderRadius,
    this.child,
  }) : items = null;

  /// 全屏地图专用Skeleton - 覆盖整个屏幕
  const SkeletonLoading.fullscreenMap({
    super.key,
    this.width = double.infinity,
    this.height = double.infinity,
    this.animate = true,
    this.loading = true,
    this.borderRadius,
    this.child,
  }) : items = null;

  /// 卡片专用Skeleton
  const SkeletonLoading.card({
    super.key,
    this.width = double.infinity,
    this.height = 120,
    this.animate = true,
    this.loading = true,
    this.borderRadius,
    this.child,
  }) : items = null;

  @override
  Widget build(BuildContext context) {
    if (!loading && child != null) {
      return child!;
    }

    final isDark = Theme.of(context).brightness == Brightness.dark;
    final baseColor = isDark
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFE1E9EE);
    final highlightColor = isDark
        ? const Color(0xFF3A3A3A)
        : const Color(0xFFF5F5F5);

    Widget skeletonWidget;

    if (items != null && items!.isNotEmpty) {
      // 自定义布局
      skeletonWidget = _buildCustomLayout(context, baseColor, highlightColor);
    } else {
      // 默认地图布局
      skeletonWidget = _buildMapLayout(context, baseColor, highlightColor);
    }

    if (!animate) {
      return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: baseColor,
          borderRadius: borderRadius ?? BorderRadius.circular(12),
        ),
        child: skeletonWidget,
      );
    }

    // 使用高性能Shimmer实现由左到右动画
    return SizedBox(
      width: width,
      height: height,
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        child: Shimmer.fromColors(
          baseColor: baseColor,
          highlightColor: highlightColor,
          direction: ShimmerDirection.ltr, // 由左到右
          period: const Duration(milliseconds: 1500), // 流畅的动画周期
          child: skeletonWidget,
        ),
      ),
    );
  }

  /// 构建地图专用布局
  Widget _buildMapLayout(
    BuildContext context,
    Color baseColor,
    Color highlightColor,
  ) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: baseColor,
      child: Stack(
        children: [
          // 主要内容区域 - 模拟地图
          Positioned.fill(
            child: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: highlightColor,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),

          // 顶部信息条
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Row(
              children: [
                // 位置图标
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: baseColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                const SizedBox(width: 12),
                // 地址文本
                Expanded(
                  child: Container(
                    height: 16,
                    decoration: BoxDecoration(
                      color: baseColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 右下角定位按钮
          Positioned(
            bottom: 16,
            right: 16,
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: baseColor,
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
          ),

          // 左下角控制面板模拟
          Positioned(
            bottom: 16,
            left: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 80,
                  height: 12,
                  decoration: BoxDecoration(
                    color: baseColor,
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: 120,
                  height: 10,
                  decoration: BoxDecoration(
                    color: baseColor,
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
              ],
            ),
          ),

          // 中心区域模拟POI点
          Positioned(
            top: height * 0.3,
            left: width * 0.2,
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: baseColor,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          Positioned(
            top: height * 0.5,
            right: width * 0.3,
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: baseColor,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          Positioned(
            bottom: height * 0.3,
            left: width * 0.4,
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: baseColor,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建自定义布局
  Widget _buildCustomLayout(
    BuildContext context,
    Color baseColor,
    Color highlightColor,
  ) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: items!
            .map((item) => _buildSkeletonItem(item, baseColor))
            .toList(),
      ),
    );
  }

  /// 构建单个Skeleton项目
  Widget _buildSkeletonItem(SkeletonItem item, Color baseColor) {
    return Container(
      width: item.width,
      height: item.height,
      margin: item.margin,
      decoration: BoxDecoration(
        color: baseColor,
        borderRadius: item.borderRadius ?? BorderRadius.circular(4),
      ),
    );
  }
}

/// Skeleton项目配置
class SkeletonItem {
  final double? width;
  final double height;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;

  const SkeletonItem({
    this.width,
    required this.height,
    this.margin,
    this.borderRadius,
  });

  /// 创建文本行
  factory SkeletonItem.text({
    double? width,
    double height = 16,
    EdgeInsets? margin,
  }) {
    return SkeletonItem(
      width: width,
      height: height,
      margin: margin ?? const EdgeInsets.symmetric(vertical: 4),
      borderRadius: BorderRadius.circular(height / 2),
    );
  }

  /// 创建圆形头像
  factory SkeletonItem.avatar({double size = 40, EdgeInsets? margin}) {
    return SkeletonItem(
      width: size,
      height: size,
      margin: margin,
      borderRadius: BorderRadius.circular(size / 2),
    );
  }

  /// 创建矩形块
  factory SkeletonItem.rect({
    double? width,
    required double height,
    EdgeInsets? margin,
    BorderRadius? borderRadius,
  }) {
    return SkeletonItem(
      width: width,
      height: height,
      margin: margin,
      borderRadius: borderRadius ?? BorderRadius.circular(8),
    );
  }
}

/// 高性能地图Skeleton预设组件
class MapSkeleton extends StatelessWidget {
  final bool loading;
  final Widget? child;

  const MapSkeleton({super.key, this.loading = true, this.child});

  @override
  Widget build(BuildContext context) {
    return SkeletonLoading.map(loading: loading, child: child);
  }
}

/// 全屏地图加载组件 - 覆盖整个屏幕
class FullscreenMapSkeleton extends StatelessWidget {
  final bool loading;
  final Widget? child;

  const FullscreenMapSkeleton({super.key, this.loading = true, this.child});

  @override
  Widget build(BuildContext context) {
    // 使用Positioned.fill确保覆盖整个屏幕
    return Positioned.fill(
      child: SkeletonLoading.fullscreenMap(loading: loading, child: child),
    );
  }
}

/// 全屏地图加载动画 - 更复杂的版本，带有加载状态指示
class FullscreenMapLoadingAnimation extends StatelessWidget {
  final bool loading;
  final double? progress;
  final String? statusText;
  final Widget? child;

  const FullscreenMapLoadingAnimation({
    super.key,
    this.loading = true,
    this.progress,
    this.statusText,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    if (!loading && child != null) {
      return child!;
    }

    return Positioned.fill(
      child: Stack(
        children: [
          // 基础骨架屏
          const FullscreenMapSkeleton(),

          // 中央加载状态
          Center(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 加载指示器
                  SizedBox(
                    width: 48,
                    height: 48,
                    child: CircularProgressIndicator(
                      value: progress,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        Colors.white,
                      ),
                      strokeWidth: 3,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 状态文本
                  Text(
                    statusText ?? '正在加载地图...',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  // 进度文本
                  if (progress != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        '${(progress! * 100).toInt()}%',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
