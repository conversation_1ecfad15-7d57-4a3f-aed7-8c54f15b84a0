import 'package:flutter/material.dart';
import '../../services/text_direction_service.dart';

/// RTL感知的行组件
class RTLAwareRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const RTLAwareRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  Widget build(BuildContext context) {
    final textDirectionService = TextDirectionService();

    // 如果是RTL，需要反转children的顺序
    final adjustedChildren = textDirectionService.isRTL
        ? children.reversed.toList()
        : children;

    // 调整主轴对齐方式
    MainAxisAlignment adjustedMainAxisAlignment = mainAxisAlignment;
    if (textDirectionService.isRTL) {
      switch (mainAxisAlignment) {
        case MainAxisAlignment.start:
          adjustedMainAxisAlignment = MainAxisAlignment.end;
          break;
        case MainAxisAlignment.end:
          adjustedMainAxisAlignment = MainAxisAlignment.start;
          break;
        default:
          adjustedMainAxisAlignment = mainAxisAlignment;
      }
    }

    return Row(
      mainAxisAlignment: adjustedMainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: adjustedChildren,
    );
  }
}

/// RTL感知的列表项
class RTLAwareListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? contentPadding;

  const RTLAwareListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    final textDirectionService = TextDirectionService();

    return ListTile(
      leading: textDirectionService.isRTL ? trailing : leading,
      title: title,
      subtitle: subtitle,
      trailing: textDirectionService.isRTL ? leading : trailing,
      onTap: onTap,
      contentPadding: contentPadding,
    );
  }
}

/// RTL感知的容器
class RTLAwareContainer extends StatelessWidget {
  final Widget? child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Decoration? decoration;
  final double? width;
  final double? height;
  final AlignmentGeometry? alignment;

  const RTLAwareContainer({
    super.key,
    this.child,
    this.padding,
    this.margin,
    this.decoration,
    this.width,
    this.height,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    final textDirectionService = TextDirectionService();

    // 调整对齐方式
    AlignmentGeometry? adjustedAlignment = alignment;
    if (textDirectionService.isRTL && alignment != null) {
      if (alignment == Alignment.centerLeft) {
        adjustedAlignment = Alignment.centerRight;
      } else if (alignment == Alignment.centerRight) {
        adjustedAlignment = Alignment.centerLeft;
      } else if (alignment == Alignment.topLeft) {
        adjustedAlignment = Alignment.topRight;
      } else if (alignment == Alignment.topRight) {
        adjustedAlignment = Alignment.topLeft;
      } else if (alignment == Alignment.bottomLeft) {
        adjustedAlignment = Alignment.bottomRight;
      } else if (alignment == Alignment.bottomRight) {
        adjustedAlignment = Alignment.bottomLeft;
      }
    }

    return Container(
      padding: padding,
      margin: margin,
      decoration: decoration,
      width: width,
      height: height,
      alignment: adjustedAlignment,
      child: child,
    );
  }
}

/// RTL感知的文本组件
class RTLAwareText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const RTLAwareText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final textDirectionService = TextDirectionService();

    // 如果没有指定对齐方式，根据文本方向自动设置
    TextAlign? adjustedTextAlign = textAlign;
    if (textAlign == null) {
      adjustedTextAlign = textDirectionService.isRTL
          ? TextAlign.right
          : TextAlign.left;
    }

    return Text(
      text,
      style: style,
      textAlign: adjustedTextAlign,
      maxLines: maxLines,
      overflow: overflow,
      textDirection: textDirectionService.currentDirection,
    );
  }
}

/// RTL感知的图标按钮
class RTLAwareIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final double? iconSize;
  final Color? color;
  final String? tooltip;
  final bool flipInRTL;

  const RTLAwareIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.iconSize,
    this.color,
    this.tooltip,
    this.flipInRTL = false,
  });

  @override
  Widget build(BuildContext context) {
    final textDirectionService = TextDirectionService();

    Widget iconWidget = Icon(icon, size: iconSize, color: color);

    // 如果需要在RTL模式下翻转图标
    if (flipInRTL && textDirectionService.isRTL) {
      iconWidget = Transform.scale(scaleX: -1, child: iconWidget);
    }

    return IconButton(onPressed: onPressed, icon: iconWidget, tooltip: tooltip);
  }
}

/// RTL感知的边距
class RTLAwarePadding extends StatelessWidget {
  final Widget child;
  final double start;
  final double top;
  final double end;
  final double bottom;

  const RTLAwarePadding({
    super.key,
    required this.child,
    this.start = 0.0,
    this.top = 0.0,
    this.end = 0.0,
    this.bottom = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.only(
        start: start,
        top: top,
        end: end,
        bottom: bottom,
      ),
      child: child,
    );
  }
}

/// RTL感知的卡片
class RTLAwareCard extends StatelessWidget {
  final Widget? child;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? elevation;
  final ShapeBorder? shape;

  const RTLAwareCard({
    super.key,
    this.child,
    this.margin,
    this.color,
    this.elevation,
    this.shape,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: margin,
      color: color,
      elevation: elevation,
      shape: shape,
      child: child,
    );
  }
}

/// RTL感知的应用栏
class RTLAwareAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final double? elevation;

  const RTLAwareAppBar({
    super.key,
    this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    final textDirectionService = TextDirectionService();

    return AppBar(
      title: title,
      actions: textDirectionService.isRTL && actions != null
          ? actions!.reversed.toList()
          : actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor,
      elevation: elevation,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// RTL感知的抽屉
class RTLAwareDrawer extends StatelessWidget {
  final Widget? child;
  final Color? backgroundColor;
  final double? elevation;
  final ShapeBorder? shape;
  final double? width;

  const RTLAwareDrawer({
    super.key,
    this.child,
    this.backgroundColor,
    this.elevation,
    this.shape,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
      width: width,
      child: child,
    );
  }
}

/// RTL感知的底部导航栏
class RTLAwareBottomNavigationBar extends StatelessWidget {
  final List<BottomNavigationBarItem> items;
  final int currentIndex;
  final ValueChanged<int>? onTap;
  final BottomNavigationBarType? type;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;

  const RTLAwareBottomNavigationBar({
    super.key,
    required this.items,
    required this.currentIndex,
    this.onTap,
    this.type,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
  });

  @override
  Widget build(BuildContext context) {
    final textDirectionService = TextDirectionService();

    // 在RTL模式下反转导航项的顺序
    final adjustedItems = textDirectionService.isRTL
        ? items.reversed.toList()
        : items;

    // 在RTL模式下调整当前索引
    final adjustedCurrentIndex = textDirectionService.isRTL
        ? items.length - 1 - currentIndex
        : currentIndex;

    return BottomNavigationBar(
      items: adjustedItems,
      currentIndex: adjustedCurrentIndex,
      onTap: onTap != null
          ? (index) {
              // 在RTL模式下调整回调的索引
              final realIndex = textDirectionService.isRTL
                  ? items.length - 1 - index
                  : index;
              onTap!(realIndex);
            }
          : null,
      type: type,
      backgroundColor: backgroundColor,
      selectedItemColor: selectedItemColor,
      unselectedItemColor: unselectedItemColor,
    );
  }
}

/// RTL感知的浮动操作按钮
class RTLAwareFloatingActionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget? child;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool mini;

  const RTLAwareFloatingActionButton({
    super.key,
    this.onPressed,
    this.child,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.mini = false,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
      mini: mini,
      child: child,
    );
  }
}

/// RTL感知的滑动删除
class RTLAwareDismissible extends StatelessWidget {
  final Key dismissKey;
  final Widget child;
  final DismissDirectionCallback? onDismissed;
  final ConfirmDismissCallback? confirmDismiss;
  final Widget? background;
  final Widget? secondaryBackground;

  const RTLAwareDismissible({
    super.key,
    required this.dismissKey,
    required this.child,
    this.onDismissed,
    this.confirmDismiss,
    this.background,
    this.secondaryBackground,
  });

  @override
  Widget build(BuildContext context) {
    final textDirectionService = TextDirectionService();

    return Dismissible(
      key: dismissKey,
      direction: textDirectionService.isRTL
          ? DismissDirection.endToStart
          : DismissDirection.startToEnd,
      onDismissed: onDismissed,
      confirmDismiss: confirmDismiss,
      background: textDirectionService.isRTL ? secondaryBackground : background,
      secondaryBackground: textDirectionService.isRTL
          ? background
          : secondaryBackground,
      child: child,
    );
  }
}
