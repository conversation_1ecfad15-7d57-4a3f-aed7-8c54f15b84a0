import 'dart:io';
import 'package:flutter/material.dart';
import '../../config/themes/app_colors.dart';
import '../../services/avatar_manager_service.dart';

/// 通用用户头像组件
/// 自动从本地加载用户头像，支持多种尺寸和样式
class UserAvatarWidget extends StatelessWidget {
  /// 头像尺寸
  final double size;

  /// 是否显示边框
  final bool showBorder;

  /// 边框宽度
  final double borderWidth;

  /// 边框颜色
  final Color? borderColor;

  /// 是否可点击
  final bool clickable;

  /// 点击回调
  final VoidCallback? onTap;

  /// 是否显示加载指示器
  final bool showLoadingIndicator;

  /// 默认头像图标
  final IconData defaultIcon;

  const UserAvatarWidget({
    super.key,
    this.size = 40.0,
    this.showBorder = false,
    this.borderWidth = 2.0,
    this.borderColor,
    this.clickable = false,
    this.onTap,
    this.showLoadingIndicator = true,
    this.defaultIcon = Icons.person,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: AvatarManagerService(),
      builder: (context, child) {
        final avatarManager = AvatarManagerService();

        // 在调试模式下打印当前头像状态
        assert(() {
          // ignore: avoid_print
          print('🖼️ UserAvatarWidget 构建中:');
          // ignore: avoid_print
          print('- 尺寸: ${size}x$size');
          // ignore: avoid_print
          print('- 当前头像路径: ${avatarManager.currentAvatarPath}');
          // ignore: avoid_print
          print('- 是否正在加载: ${avatarManager.isLoading}');
          return true;
        }());

        Widget avatarContent = _buildAvatarContent(context, avatarManager);

        // 如果需要边框，包装在装饰容器中
        if (showBorder) {
          avatarContent = Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: borderColor ?? AppColors.primary,
                width: borderWidth,
              ),
            ),
            child: ClipOval(
              child: Container(
                width: size - borderWidth * 2,
                height: size - borderWidth * 2,
                margin: EdgeInsets.all(borderWidth),
                child: ClipOval(child: avatarContent),
              ),
            ),
          );
        } else {
          avatarContent = ClipOval(
            child: SizedBox(width: size, height: size, child: avatarContent),
          );
        }

        // 如果可点击，包装在GestureDetector中
        if (clickable && onTap != null) {
          return GestureDetector(onTap: onTap, child: avatarContent);
        }

        return avatarContent;
      },
    );
  }

  /// 构建头像内容
  Widget _buildAvatarContent(
    BuildContext context,
    AvatarManagerService avatarManager,
  ) {
    // 如果正在加载且需要显示加载指示器
    if (avatarManager.isLoading && showLoadingIndicator) {
      return _buildLoadingAvatar(context);
    }

    // 如果有本地头像文件
    if (avatarManager.currentAvatarPath != null) {
      final avatarFile = File(avatarManager.currentAvatarPath!);

      return Image.file(
        avatarFile,
        fit: BoxFit.cover,
        width: size,
        height: size,
        // 使用AvatarManagerService的缓存键，确保头像更新后能立即显示
        key: ValueKey(avatarManager.avatarCacheKey ?? 'no_avatar'),
        errorBuilder: (context, error, stackTrace) {
          // 如果本地文件损坏，显示默认头像
          assert(() {
            // ignore: avoid_print
            print('⚠️ 头像文件加载失败: $error');
            return true;
          }());
          return _buildDefaultAvatar(context);
        },
      );
    }

    // 显示默认头像
    return _buildDefaultAvatar(context);
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary, AppColors.secondary],
        ),
      ),
      child: Icon(defaultIcon, size: size * 0.5, color: Colors.white),
    );
  }

  /// 构建加载状态头像
  Widget _buildLoadingAvatar(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppColors.cardBackground(context),
        border: Border.all(color: AppColors.border(context), width: 1),
      ),
      child: Center(
        child: SizedBox(
          width: size * 0.4,
          height: size * 0.4,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: AppColors.primary,
          ),
        ),
      ),
    );
  }
}

/// 大尺寸头像组件 - 用于个人资料页面
class LargeUserAvatar extends StatelessWidget {
  final VoidCallback? onTap;
  final bool showEditIndicator;

  const LargeUserAvatar({
    super.key,
    this.onTap,
    this.showEditIndicator = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        UserAvatarWidget(
          size: 100.0,
          showBorder: true,
          borderWidth: 3.0,
          clickable: onTap != null,
          onTap: onTap,
          borderColor: AppColors.border(context),
        ),

        // 编辑指示器
        if (showEditIndicator)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.background(context),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.camera_alt,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }
}

/// 小尺寸头像组件 - 用于聊天界面
class SmallUserAvatar extends StatelessWidget {
  final VoidCallback? onTap;

  const SmallUserAvatar({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return UserAvatarWidget(
      size: 36.0,
      clickable: onTap != null,
      onTap: onTap,
      showLoadingIndicator: false, // 聊天界面不显示加载指示器
    );
  }
}

/// 中等尺寸头像组件 - 用于个人信息卡片
class MediumUserAvatar extends StatelessWidget {
  final VoidCallback? onTap;

  const MediumUserAvatar({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return UserAvatarWidget(
      size: 60.0,
      showBorder: true,
      borderWidth: 2.0,
      clickable: onTap != null,
      onTap: onTap,
    );
  }
}
