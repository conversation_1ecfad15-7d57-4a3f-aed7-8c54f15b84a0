import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../config/themes/app_colors.dart';

/// 缓存图片组件 - 统一处理图片加载和缓存
class CachedImageWidget extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;
  final bool isCircle;
  final Map<String, String>? httpHeaders;
  final Duration? fadeInDuration;
  final Duration? fadeOutDuration;

  const CachedImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
    this.isCircle = false,
    this.httpHeaders,
    this.fadeInDuration,
    this.fadeOutDuration,
  });

  /// 创建圆形头像
  factory CachedImageWidget.avatar({
    Key? key,
    required String imageUrl,
    required double size,
    Map<String, String>? httpHeaders,
    Widget? errorWidget,
  }) {
    return CachedImageWidget(
      key: key,
      imageUrl: imageUrl,
      width: size,
      height: size,
      isCircle: true,
      httpHeaders: httpHeaders,
      errorWidget: errorWidget,
    );
  }

  /// 创建产品图片
  factory CachedImageWidget.product({
    Key? key,
    required String imageUrl,
    double? width,
    double? height,
    BorderRadius? borderRadius,
    Map<String, String>? httpHeaders,
  }) {
    return CachedImageWidget(
      key: key,
      imageUrl: imageUrl,
      width: width,
      height: height,
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      httpHeaders: httpHeaders,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (imageUrl.isEmpty) {
      return _buildErrorWidget();
    }

    // 获取设备像素比，确保高DPI屏幕上图片清晰
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;

    Widget imageWidget = CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      httpHeaders: httpHeaders,
      fadeInDuration: fadeInDuration ?? const Duration(milliseconds: 300),
      fadeOutDuration: fadeOutDuration ?? const Duration(milliseconds: 100),
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      // 缓存配置 - 考虑设备像素比，确保高DPI屏幕上图片清晰
      cacheKey: _generateCacheKey(devicePixelRatio),
      memCacheWidth: width != null ? (width! * devicePixelRatio).toInt() : null,
      memCacheHeight: height != null
          ? (height! * devicePixelRatio).toInt()
          : null,
    );

    // 应用边框圆角或圆形
    if (isCircle) {
      imageWidget = ClipOval(child: imageWidget);
    } else if (borderRadius != null) {
      imageWidget = ClipRRect(borderRadius: borderRadius!, child: imageWidget);
    }

    return imageWidget;
  }

  /// 生成缓存键
  String _generateCacheKey(double devicePixelRatio) {
    // 使用URL、尺寸信息和设备像素比生成唯一的缓存键
    final sizeKey = '${width ?? 0}x${height ?? 0}';
    final pixelRatioKey = devicePixelRatio.toStringAsFixed(1);
    return '${imageUrl}_${sizeKey}_$pixelRatioKey';
  }

  /// 构建占位符
  Widget _buildPlaceholder() {
    if (placeholder != null) {
      return placeholder!;
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: isCircle ? null : borderRadius,
        shape: isCircle ? BoxShape.circle : BoxShape.rectangle,
      ),
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: AppColors.primary,
          value: null,
        ),
      ),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget() {
    if (errorWidget != null) {
      return errorWidget!;
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withValues(alpha: 0.8),
            AppColors.primary.withValues(alpha: 0.6),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: isCircle ? null : borderRadius,
        shape: isCircle ? BoxShape.circle : BoxShape.rectangle,
      ),
      child: Icon(_getDefaultIcon(), color: Colors.white, size: _getIconSize()),
    );
  }

  /// 获取默认图标
  IconData _getDefaultIcon() {
    if (isCircle) {
      return Icons.person;
    }
    return Icons.image;
  }

  /// 获取图标大小
  double _getIconSize() {
    if (width != null && height != null) {
      return (width! + height!) / 4;
    }
    return 24;
  }
}

/// 医生头像组件
class DoctorAvatarWidget extends StatelessWidget {
  final String avatarUrl;
  final double size;
  final Map<String, String>? httpHeaders;

  const DoctorAvatarWidget({
    super.key,
    required this.avatarUrl,
    required this.size,
    this.httpHeaders,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: ClipOval(
        child: CachedImageWidget.avatar(
          imageUrl: avatarUrl,
          size: size - 4, // 减去边框宽度
          httpHeaders: httpHeaders,
        ),
      ),
    );
  }
}

/// 产品图片组件
class ProductImageWidget extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final Map<String, String>? httpHeaders;

  const ProductImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.borderRadius,
    this.httpHeaders,
  });

  @override
  Widget build(BuildContext context) {
    return CachedImageWidget.product(
      imageUrl: imageUrl,
      width: width,
      height: height,
      borderRadius: borderRadius,
      httpHeaders: httpHeaders,
    );
  }
}
