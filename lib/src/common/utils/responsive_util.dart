import 'package:flutter/material.dart';
import '../../services/dpi_adaptation_service.dart';
import '../../services/font_size_service.dart';

/// 响应式布局工具类 - 提供基于屏幕尺寸和字体大小的相对尺寸计算
/// 用于减少硬编码像素值，提升UI的DPI适配能力
class ResponsiveUtil {
  static final FontSizeService _fontSizeService = FontSizeService();

  /// 获取屏幕宽度
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// 获取屏幕高度
  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// 获取屏幕密度
  static double devicePixelRatio(BuildContext context) {
    return MediaQuery.of(context).devicePixelRatio;
  }

  /// 根据屏幕宽度获取相对宽度
  /// [percentage] 屏幕宽度的百分比 (0.0 - 1.0)
  static double width(BuildContext context, double percentage) {
    return screenWidth(context) * percentage;
  }

  /// 根据屏幕高度获取相对高度
  /// [percentage] 屏幕高度的百分比 (0.0 - 1.0)
  static double height(BuildContext context, double percentage) {
    return screenHeight(context) * percentage;
  }

  /// 获取基于字体大小的相对间距
  /// 这些间距会随字体档位变化而调整
  static double spacing(double baseSpacing) {
    return _fontSizeService.getRelativeSpacing(baseSpacing);
  }

  /// 获取基于字体大小的相对图标尺寸
  static double iconSize(double baseIconSize) {
    return _fontSizeService.getRelativeIconSize(baseIconSize);
  }

  /// 获取基于字体大小的相对最小高度
  static double minHeight(double baseMinHeight) {
    return _fontSizeService.getRelativeMinHeight(baseMinHeight);
  }

  /// 获取基于文本主题的相对尺寸
  static double fromTextTheme(
    BuildContext context, {
    required double multiplier,
    double fallbackBaseFontSize = 14.0,
  }) {
    return _fontSizeService.getRelativeSizeFromTextTheme(
      context,
      multiplier: multiplier,
      fallbackBaseFontSize: fallbackBaseFontSize,
    );
  }

  /// 获取标准的卡片内边距
  static EdgeInsets cardPadding(BuildContext context) {
    final padding = fromTextTheme(context, multiplier: 1.0);
    return EdgeInsets.all(padding);
  }

  /// 获取标准的按钮内边距
  static EdgeInsets buttonPadding(BuildContext context) {
    final verticalPadding = fromTextTheme(context, multiplier: 0.6);
    final horizontalPadding = fromTextTheme(context, multiplier: 1.0);
    return EdgeInsets.symmetric(
      vertical: verticalPadding,
      horizontal: horizontalPadding,
    );
  }

  /// 获取列表项的内边距
  static EdgeInsets listItemPadding(BuildContext context) {
    final verticalPadding = fromTextTheme(context, multiplier: 0.8);
    final horizontalPadding = fromTextTheme(context, multiplier: 1.2);
    return EdgeInsets.symmetric(
      vertical: verticalPadding,
      horizontal: horizontalPadding,
    );
  }

  /// 获取小间距 (4dp基准)
  static double smallSpacing(BuildContext context) {
    return spacing(4.0);
  }

  /// 获取中等间距 (8dp基准)
  static double mediumSpacing(BuildContext context) {
    return spacing(8.0);
  }

  /// 获取大间距 (16dp基准)
  static double largeSpacing(BuildContext context) {
    return spacing(16.0);
  }

  /// 获取特大间距 (24dp基准)
  static double xLargeSpacing(BuildContext context) {
    return spacing(24.0);
  }

  /// 获取小图标尺寸 (16dp基准)
  static double smallIcon(BuildContext context) {
    return iconSize(16.0);
  }

  /// 获取中等图标尺寸 (24dp基准)
  static double mediumIcon(BuildContext context) {
    return iconSize(24.0);
  }

  /// 获取大图标尺寸 (32dp基准)
  static double largeIcon(BuildContext context) {
    return iconSize(32.0);
  }

  /// 获取圆角半径（基于字体大小）
  static double borderRadius(BuildContext context, {double baseRadius = 8.0}) {
    return spacing(baseRadius);
  }

  /// 判断是否为小屏设备
  static bool isSmallScreen(BuildContext context) {
    return screenWidth(context) < 360;
  }

  /// 判断是否为中等屏幕设备
  static bool isMediumScreen(BuildContext context) {
    final width = screenWidth(context);
    return width >= 360 && width < 600;
  }

  /// 判断是否为大屏设备
  static bool isLargeScreen(BuildContext context) {
    return screenWidth(context) >= 600;
  }

  /// 获取基于屏幕尺寸的自适应值
  /// [small] 小屏设备的值
  /// [medium] 中屏设备的值
  /// [large] 大屏设备的值
  static T adaptive<T>(
    BuildContext context, {
    required T small,
    required T medium,
    required T large,
  }) {
    if (isSmallScreen(context)) {
      return small;
    } else if (isMediumScreen(context)) {
      return medium;
    } else {
      return large;
    }
  }

  /// 获取最小触摸目标尺寸（遵循Material Design指导原则）
  static double minTouchTarget(BuildContext context) {
    return minHeight(48.0); // Material Design推荐的最小触摸目标
  }

  // ==================== DPI适配方法 ====================

  /// 获取DPI适配后的尺寸
  static double dpiSize(BuildContext context, double baseSize) {
    return DpiUtil.size(context, baseSize);
  }

  /// 获取DPI适配后的字体大小
  static double dpiFontSize(BuildContext context, double baseFontSize) {
    return DpiUtil.fontSize(context, baseFontSize);
  }

  /// 获取DPI适配后的间距
  static double dpiSpacing(BuildContext context, double baseSpacing) {
    return DpiUtil.spacing(context, baseSpacing);
  }

  /// 获取DPI适配后的图标尺寸
  static double dpiIconSize(BuildContext context, double baseIconSize) {
    return DpiUtil.iconSize(context, baseIconSize);
  }

  /// 获取DPI适配后的边框圆角
  static double dpiBorderRadius(BuildContext context, double baseBorderRadius) {
    return DpiUtil.borderRadius(context, baseBorderRadius);
  }

  /// 获取组合适配后的间距（同时考虑字体设置和DPI）
  static double adaptedSpacing(BuildContext context, double baseSpacing) {
    final fontAdapted = spacing(baseSpacing);
    return DpiUtil.spacing(context, fontAdapted);
  }

  /// 获取组合适配后的图标尺寸（同时考虑字体设置和DPI）
  static double adaptedIconSize(BuildContext context, double baseIconSize) {
    final fontAdapted = iconSize(baseIconSize);
    return DpiUtil.iconSize(context, fontAdapted);
  }
}
