import 'package:flutter/material.dart';
import '../../services/font_size_service.dart';
import '../../config/themes/app_colors.dart';
import 'font_util.dart';

/// 文本样式工具类 - 提供统一的文本样式管理
/// 现在通过全局textScaleFactor自动调整所有文本大小
class TextStyleUtil {
  static final FontSizeService _fontSizeService = FontSizeService();

  /// 获取基础字体大小（应用缩放因子）
  static double _getScaledFontSize(double baseFontSize) {
    return baseFontSize * _fontSizeService.textScaleFactor;
  }

  /// 获取主标题样式
  static TextStyle getHeadlineStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(20),
      fontWeight: FontWeight.w600,
      color: AppColors.textPrimary(context),
    );
  }

  /// 获取副标题样式
  static TextStyle getSubtitleStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(18),
      fontWeight: FontWeight.w500,
      color: AppColors.textPrimary(context),
    );
  }

  /// 获取正文样式
  static TextStyle getBodyStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(16),
      fontWeight: FontWeight.normal,
      color: AppColors.textPrimary(context),
    );
  }

  /// 获取小字体样式
  static TextStyle getCaptionStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(14),
      fontWeight: FontWeight.normal,
      color: AppColors.textSecondary(context),
    );
  }

  /// 获取提示文字样式
  static TextStyle getHintStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(14),
      fontWeight: FontWeight.normal,
      color: AppColors.textHint(context),
    );
  }

  /// 获取按钮文字样式
  static TextStyle getButtonStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(16),
      fontWeight: FontWeight.w500,
      color: Colors.white,
    );
  }

  /// 获取AppBar标题样式
  static TextStyle getAppBarTitleStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(18),
      fontWeight: FontWeight.w600,
      color: AppColors.textPrimary(context),
    );
  }

  /// 获取聊天气泡文字样式
  static TextStyle getChatBubbleStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(16),
      fontWeight: FontWeight.normal,
      color: AppColors.textPrimary(context),
    );
  }

  /// 获取时间戳样式
  static TextStyle getTimestampStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(12),
      fontWeight: FontWeight.normal,
      color: AppColors.textHint(context),
    );
  }

  /// 获取设置项标题样式
  static TextStyle getSettingTitleStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(17),
      fontWeight: FontWeight.w500,
      color: AppColors.textPrimary(context),
    );
  }

  /// 获取设置项副标题样式
  static TextStyle getSettingSubtitleStyle(BuildContext context) {
    return TextStyle(
      fontSize: _getScaledFontSize(14),
      fontWeight: FontWeight.normal,
      color: AppColors.textHint(context),
    );
  }

  /// 获取固定大小的文本样式（不受全局字体大小影响）
  /// 用于语言按钮和语言选择器等特殊场景
  static TextStyle getFixedSizeStyle({
    required double fontSize,
    FontWeight? fontWeight,
    required Color color,
    double? height,
    TextDecoration? decoration,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight ?? FontWeight.normal,
      color: color,
      height: height,
      decoration: decoration,
      fontFamily: 'UKIJTor',
    );
  }

  /// 获取语言按钮样式（固定大小）
  static TextStyle getLanguageButtonStyle(BuildContext context) {
    return getFixedSizeStyle(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      color: AppColors.textPrimary(context),
    );
  }

  /// 获取语言选择器列表项样式（固定大小）
  static TextStyle getLanguageSelectorStyle(BuildContext context) {
    return getFixedSizeStyle(
      fontSize: 16,
      fontWeight: FontWeight.normal,
      color: AppColors.textPrimary(context),
    );
  }

  /// 获取自定义样式（应用字体大小缩放）
  /// 这些样式会自动应用全局textScaleFactor和合适的字体
  static TextStyle getCustomStyle({
    required BuildContext context,
    required double baseFontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
    String? text,
  }) {
    return FontUtil.createTextStyle(
      text: text,
      fontSize: _getScaledFontSize(baseFontSize),
      fontWeight: fontWeight,
      color: color ?? AppColors.textPrimary(context),
      height: height,
      decoration: decoration,
    );
  }

  /// 获取相对按钮内边距（基于字体大小）
  static EdgeInsets getRelativeButtonPadding(BuildContext context) {
    return EdgeInsets.symmetric(
      vertical: _fontSizeService.getButtonVerticalPadding(context),
      horizontal: _fontSizeService.getButtonHorizontalPadding(context),
    );
  }

  /// 获取相对卡片内边距（基于字体大小）
  static EdgeInsets getRelativeCardPadding(BuildContext context) {
    final padding = _fontSizeService.getCardPadding(context);
    return EdgeInsets.all(padding);
  }

  /// 获取列表项的最小高度（基于字体大小）
  static double getListItemMinHeight(BuildContext context) {
    return _fontSizeService.getListItemMinHeight(context);
  }
}
