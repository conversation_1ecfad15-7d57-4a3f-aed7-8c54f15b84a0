import 'package:flutter/material.dart';
import '../../services/font_size_service.dart';

/// 应用响应式尺寸服务 - 基于字体档位提供统一的相对尺寸
/// 这是解决UI元素对字体档位变化不敏感问题的核心类
class AppResponsiveSizes {
  final BuildContext context;
  final FontSizeService _fontSizeService;

  /// 当前生效的基础字体大小（受textScaler影响）
  late final double baseFontSize;

  /// 当前字体档位的缩放因子
  late final double scaleFactor;

  /// 原始基础字体大小（不受textScaler影响）
  late final double _rawBaseFontSize;

  AppResponsiveSizes(this.context) : _fontSizeService = FontSizeService() {
    // 获取字体缩放因子
    scaleFactor = _fontSizeService.textScaleFactor;

    // 获取原始字体大小（不受textScaler影响）
    // 设置为16.0以匹配应用其他页面的标准字体大小（如TextStyleUtil.getBodyStyle）
    _rawBaseFontSize = 16.0; // 调整为标准基础字体大小

    // 获取经过textScaler处理后的实际字体大小（仅用于文本相对尺寸计算）
    final textTheme = Theme.of(context).textTheme;
    baseFontSize =
        textTheme.bodyMedium?.fontSize ?? (_rawBaseFontSize * scaleFactor);
  }

  // ==================== 基础间距系列 ====================

  /// 超小间距 (4dp基准，调整后)
  double get spacingXS => _scaleSize(4.0);

  /// 小间距 (8dp基准，调整后)
  double get spacingS => _scaleSize(8.0);

  /// 中等间距 (12dp基准，调整后)
  double get spacingM => _scaleSize(12.0);

  /// 大间距 (16dp基准，调整后)
  double get spacingL => _scaleSize(16.0);

  /// 特大间距 (20dp基准，调整后)
  double get spacingXL => _scaleSize(20.0);

  /// 超大间距 (28dp基准，调整后)
  double get spacingXXL => _scaleSize(28.0);

  // ==================== 图标尺寸系列 ====================

  /// 小图标 (20dp基准，调整后)
  double get iconS => _scaleSize(20.0);

  /// 中等图标 (24dp基准，调整后)
  double get iconM => _scaleSize(24.0);

  /// 大图标 (28dp基准，调整后)
  double get iconL => _scaleSize(28.0);

  /// 特大图标 (36dp基准，调整后)
  double get iconXL => _scaleSize(36.0);

  /// 超大图标 (48dp基准，调整后)
  double get iconXXL => _scaleSize(48.0);

  // ==================== 按钮相关尺寸 ====================

  /// 按钮最小高度
  double get buttonMinHeight => _scaleSize(48.0);

  /// 紧凑按钮高度
  double get buttonCompactHeight => _scaleSize(40.0);

  /// 大按钮高度
  double get buttonLargeHeight => _scaleSize(56.0);

  /// 按钮垂直内边距（基于字体大小，调整倍数）
  double get buttonPaddingV => _rawBaseFontSize * scaleFactor * 0.75;

  /// 按钮水平内边距（基于字体大小，调整倍数）
  double get buttonPaddingH => _rawBaseFontSize * scaleFactor * 1.25;

  // ==================== 列表项尺寸 ====================

  /// 列表项最小高度（基于字体大小，调整倍数）
  double get listItemMinHeight => _rawBaseFontSize * scaleFactor * 3.2;

  /// 紧凑列表项高度（基于字体大小，调整倍数）
  double get listItemCompactHeight => _rawBaseFontSize * scaleFactor * 2.5;

  /// 大列表项高度（基于字体大小，调整倍数）
  double get listItemLargeHeight => _rawBaseFontSize * scaleFactor * 3.8;

  /// 列表项垂直内边距（基于字体大小，调整倍数）
  double get listItemPaddingV => _rawBaseFontSize * scaleFactor * 0.8;

  /// 列表项水平内边距（基于字体大小，调整倍数）
  double get listItemPaddingH => _rawBaseFontSize * scaleFactor * 1.2;

  // ==================== 卡片相关尺寸 ====================

  /// 卡片内边距（基于字体大小，调整倍数）
  double get cardPadding => _rawBaseFontSize * scaleFactor * 1.0;

  /// 卡片圆角半径
  double get cardBorderRadius => _scaleSize(12.0);

  /// 小卡片内边距（基于字体大小，调整倍数）
  double get cardPaddingS => _rawBaseFontSize * scaleFactor * 0.8;

  /// 大卡片内边距（基于字体大小，调整倍数）
  double get cardPaddingL => _rawBaseFontSize * scaleFactor * 1.5;

  // ==================== 特定组件尺寸 ====================

  /// 语音播放按钮尺寸（基于字体大小，与文本大小协调）
  /// 从2.2倍调整为1.4倍，使其与气泡内文本大小更协调
  double get voicePlayButtonSize => _rawBaseFontSize * scaleFactor * 1.2;

  /// 语言选择器高度（基于字体大小，避免双重缩放R
  double get languageSelectorHeight => _rawBaseFontSize * scaleFactor * 3.2;

  /// 语言选择器按钮最小高度（基于字体大小，避免双重缩放）
  double get languageButtonMinHeight => _rawBaseFontSize * scaleFactor * 2.8;

  /// 头像尺寸 - 小
  double get avatarS => _scaleSize(32.0);

  /// 头像尺寸 - 中
  double get avatarM => _scaleSize(48.0);

  /// 头像尺寸 - 大
  double get avatarL => _scaleSize(64.0);

  /// 头像尺寸 - 特大
  double get avatarXL => _scaleSize(80.0);

  // ==================== 输入框相关尺寸 ====================

  /// 输入框最小高度（基于文字高度，调整为更舒适的尺寸）
  /// 从2.4倍调整为3.0倍，在中字体档位下更舒适
  double get inputMinHeight => _rawBaseFontSize * scaleFactor * 3.0;

  /// 输入框内边距（基于字体大小，调整为更合适的内边距）
  /// 从1.0倍调整为1.2倍，增加内部空间感
  double get inputPadding => _rawBaseFontSize * scaleFactor * 1.2;

  /// 搜索框高度（基于字体大小，避免双重缩放）
  double get searchBarHeight => _rawBaseFontSize * scaleFactor * 2.8;

  // ==================== App Bar 相关尺寸 ====================

  /// AppBar 按钮尺寸
  double get appBarButtonSize => _scaleSize(24.0);

  /// AppBar 标题间距（基于字体大小，避免双重缩放）
  double get appBarTitleSpacing => _rawBaseFontSize * scaleFactor * 1.0;

  // ==================== 浮动操作按钮尺寸 ====================

  /// 标准FAB尺寸
  double get fabNormal => _scaleSize(56.0);

  /// 小FAB尺寸
  double get fabSmall => _scaleSize(40.0);

  /// 大FAB尺寸
  double get fabLarge => _scaleSize(96.0);

  // ==================== 底部导航尺寸 ====================

  /// 底部导航栏高度
  double get bottomNavHeight => _scaleSize(60.0);

  /// 底部导航图标尺寸
  double get bottomNavIconSize => _scaleSize(24.0);

  // ==================== 工具方法 ====================

  /// 计算缩放后的尺寸（仅适用于固定尺寸元素，如图标、间距等）
  double _scaleSize(double baseSize) {
    return baseSize * scaleFactor;
  }

  /// 获取基于倍数的相对尺寸（直接基于已缩放的baseFontSize）
  /// 用于文本相关的尺寸计算，避免双重缩放
  double getRelativeSize(double multiplier) {
    return _rawBaseFontSize * scaleFactor * multiplier;
  }

  /// 获取自定义缩放的尺寸
  double getScaledSize(double baseSize) {
    return _scaleSize(baseSize);
  }

  // ==================== EdgeInsets 快捷方法 ====================

  /// 所有方向相等的内边距
  EdgeInsets paddingAll(double size) => EdgeInsets.all(_scaleSize(size));

  /// 对称内边距
  EdgeInsets paddingSymmetric({double vertical = 0, double horizontal = 0}) =>
      EdgeInsets.symmetric(
        vertical: _scaleSize(vertical),
        horizontal: _scaleSize(horizontal),
      );

  /// 只有水平内边距
  EdgeInsets paddingHorizontal(double size) =>
      EdgeInsets.symmetric(horizontal: _scaleSize(size));

  /// 只有垂直内边距
  EdgeInsets paddingVertical(double size) =>
      EdgeInsets.symmetric(vertical: _scaleSize(size));

  /// 标准列表项内边距（基于字体大小，避免双重缩放）
  EdgeInsets get listItemPadding => EdgeInsets.symmetric(
    vertical: listItemPaddingV,
    horizontal: listItemPaddingH,
  );

  /// 标准按钮内边距（基于字体大小，避免双重缩放）
  EdgeInsets get buttonPadding => EdgeInsets.symmetric(
    vertical: buttonPaddingV,
    horizontal: buttonPaddingH,
  );

  /// 标准卡片内边距（基于字体大小，避免双重缩放）
  EdgeInsets get cardPaddingInsets => EdgeInsets.all(cardPadding);

  // ==================== 尺寸约束 ====================

  /// 最小触摸目标约束
  BoxConstraints get minTouchTarget =>
      BoxConstraints(minWidth: _scaleSize(48.0), minHeight: _scaleSize(48.0));

  /// 列表项约束
  BoxConstraints get listItemConstraints =>
      BoxConstraints(minHeight: listItemMinHeight);

  /// 按钮约束
  BoxConstraints get buttonConstraints =>
      BoxConstraints(minHeight: buttonMinHeight);

  // ==================== 调试信息 ====================

  /// 获取当前缩放信息（用于调试）
  Map<String, dynamic> get debugInfo => {
    'baseFontSize': baseFontSize,
    'rawBaseFontSize': _rawBaseFontSize,
    'scaleFactor': scaleFactor,
    'currentLevel': _fontSizeService.currentLevel.toString(),
  };
}
