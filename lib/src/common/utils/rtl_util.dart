import 'package:flutter/material.dart';
import '../../services/text_direction_service.dart';

/// RTL布局工具类
class RTLUtil {
  /// 获取文本方向服务实例
  static TextDirectionService get _textDirectionService =>
      TextDirectionService();

  /// 判断当前是否为RTL模式
  static bool get isRTL => _textDirectionService.isRTL;

  /// 判断当前是否为LTR模式
  static bool get isLTR => _textDirectionService.isLTR;

  /// 获取当前文本方向
  static TextDirection get currentDirection =>
      _textDirectionService.currentDirection;

  /// 获取适合当前方向的文本对齐方式
  static TextAlign getTextAlign({TextAlign? defaultAlign}) {
    if (defaultAlign != null) return defaultAlign;
    return isRTL ? TextAlign.right : TextAlign.left;
  }

  /// 获取相反的文本对齐方式
  static TextAlign getOppositeTextAlign({TextAlign? defaultAlign}) {
    if (defaultAlign != null) {
      switch (defaultAlign) {
        case TextAlign.left:
          return TextAlign.right;
        case TextAlign.right:
          return TextAlign.left;
        case TextAlign.start:
          return TextAlign.end;
        case TextAlign.end:
          return TextAlign.start;
        default:
          return defaultAlign;
      }
    }
    return isRTL ? TextAlign.left : TextAlign.right;
  }

  /// 获取主轴对齐方式
  static MainAxisAlignment getMainAxisAlignment({
    MainAxisAlignment defaultAlignment = MainAxisAlignment.start,
  }) {
    if (isRTL) {
      switch (defaultAlignment) {
        case MainAxisAlignment.start:
          return MainAxisAlignment.end;
        case MainAxisAlignment.end:
          return MainAxisAlignment.start;
        default:
          return defaultAlignment;
      }
    }
    return defaultAlignment;
  }

  /// 获取交叉轴对齐方式
  static CrossAxisAlignment getCrossAxisAlignment({
    CrossAxisAlignment defaultAlignment = CrossAxisAlignment.start,
  }) {
    if (isRTL) {
      switch (defaultAlignment) {
        case CrossAxisAlignment.start:
          return CrossAxisAlignment.end;
        case CrossAxisAlignment.end:
          return CrossAxisAlignment.start;
        default:
          return defaultAlignment;
      }
    }
    return defaultAlignment;
  }

  /// 获取对齐方式
  static Alignment getAlignment({
    Alignment defaultAlignment = Alignment.centerLeft,
  }) {
    if (isRTL) {
      switch (defaultAlignment) {
        case Alignment.centerLeft:
          return Alignment.centerRight;
        case Alignment.centerRight:
          return Alignment.centerLeft;
        case Alignment.topLeft:
          return Alignment.topRight;
        case Alignment.topRight:
          return Alignment.topLeft;
        case Alignment.bottomLeft:
          return Alignment.bottomRight;
        case Alignment.bottomRight:
          return Alignment.bottomLeft;
        default:
          return defaultAlignment;
      }
    }
    return defaultAlignment;
  }

  /// 获取方向性边距
  static EdgeInsetsDirectional getDirectionalPadding({
    double start = 0.0,
    double top = 0.0,
    double end = 0.0,
    double bottom = 0.0,
  }) {
    return EdgeInsetsDirectional.only(
      start: start,
      top: top,
      end: end,
      bottom: bottom,
    );
  }

  /// 获取方向性边距（所有边相同）
  static EdgeInsetsDirectional getDirectionalPaddingAll(double value) {
    return EdgeInsetsDirectional.all(value);
  }

  /// 获取方向性边距（水平和垂直）
  static EdgeInsetsDirectional getDirectionalPaddingSymmetric({
    double horizontal = 0.0,
    double vertical = 0.0,
  }) {
    return EdgeInsetsDirectional.symmetric(
      horizontal: horizontal,
      vertical: vertical,
    );
  }

  /// 获取方向性外边距
  static EdgeInsetsDirectional getDirectionalMargin({
    double start = 0.0,
    double top = 0.0,
    double end = 0.0,
    double bottom = 0.0,
  }) {
    return EdgeInsetsDirectional.only(
      start: start,
      top: top,
      end: end,
      bottom: bottom,
    );
  }

  /// 反转子组件列表（用于RTL布局）
  static List<Widget> reverseChildrenIfRTL(List<Widget> children) {
    return isRTL ? children.reversed.toList() : children;
  }

  /// 获取图标的旋转角度（用于方向性图标）
  static double getIconRotation() {
    return isRTL ? 3.14159 : 0.0; // 180度 = π弧度
  }

  /// 判断是否需要翻转图标
  static bool shouldFlipIcon(IconData icon) {
    // 这些图标在RTL模式下需要翻转
    final directionalIcons = <IconData>{
      Icons.arrow_back,
      Icons.arrow_forward,
      Icons.arrow_back_ios,
      Icons.arrow_forward_ios,
      Icons.chevron_left,
      Icons.chevron_right,
      Icons.keyboard_arrow_left,
      Icons.keyboard_arrow_right,
      Icons.navigate_before,
      Icons.navigate_next,
      Icons.first_page,
      Icons.last_page,
      Icons.undo,
      Icons.redo,
      Icons.reply,
      Icons.forward,
      Icons.send,
      Icons.exit_to_app,
      Icons.login,
      Icons.logout,
    };

    return directionalIcons.contains(icon);
  }

  /// 获取翻转后的图标
  static Widget getFlippedIcon(IconData icon, {double? size, Color? color}) {
    Widget iconWidget = Icon(icon, size: size, color: color);

    if (isRTL && shouldFlipIcon(icon)) {
      iconWidget = Transform.scale(scaleX: -1, child: iconWidget);
    }

    return iconWidget;
  }

  /// 获取适合RTL的边框半径
  static BorderRadiusDirectional getDirectionalBorderRadius({
    double topStart = 0.0,
    double topEnd = 0.0,
    double bottomStart = 0.0,
    double bottomEnd = 0.0,
  }) {
    return BorderRadiusDirectional.only(
      topStart: Radius.circular(topStart),
      topEnd: Radius.circular(topEnd),
      bottomStart: Radius.circular(bottomStart),
      bottomEnd: Radius.circular(bottomEnd),
    );
  }

  /// 获取适合RTL的圆形边框半径
  static BorderRadiusDirectional getDirectionalBorderRadiusCircular(
    double radius,
  ) {
    return BorderRadiusDirectional.circular(radius);
  }

  /// 获取滑动方向（用于手势识别）
  static DismissDirection getDismissDirection() {
    return isRTL ? DismissDirection.endToStart : DismissDirection.startToEnd;
  }

  /// 获取相反的滑动方向
  static DismissDirection getOppositeDismissDirection() {
    return isRTL ? DismissDirection.startToEnd : DismissDirection.endToStart;
  }

  /// 获取抽屉对齐方式
  static DrawerAlignment getDrawerAlignment() {
    return isRTL ? DrawerAlignment.end : DrawerAlignment.start;
  }

  /// 获取相反的抽屉对齐方式
  static DrawerAlignment getOppositeDrawerAlignment() {
    return isRTL ? DrawerAlignment.start : DrawerAlignment.end;
  }

  /// 获取浮动操作按钮位置
  static FloatingActionButtonLocation getFABLocation() {
    return isRTL
        ? FloatingActionButtonLocation.startFloat
        : FloatingActionButtonLocation.endFloat;
  }

  /// 获取相反的浮动操作按钮位置
  static FloatingActionButtonLocation getOppositeFABLocation() {
    return isRTL
        ? FloatingActionButtonLocation.endFloat
        : FloatingActionButtonLocation.startFloat;
  }

  /// 创建RTL感知的动画
  static Animation<Offset> createSlideAnimation(
    AnimationController controller, {
    Offset begin = const Offset(1.0, 0.0),
    Offset end = Offset.zero,
  }) {
    // 在RTL模式下反转水平偏移
    if (isRTL) {
      begin = Offset(-begin.dx, begin.dy);
      end = Offset(-end.dx, end.dy);
    }

    return Tween<Offset>(
      begin: begin,
      end: end,
    ).animate(CurvedAnimation(parent: controller, curve: Curves.easeInOut));
  }

  /// 获取文本输入装饰的前缀和后缀图标位置
  static InputDecoration getInputDecoration({
    String? hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? errorText,
  }) {
    return InputDecoration(
      hintText: hintText,
      labelText: labelText,
      prefixIcon: isRTL ? suffixIcon : prefixIcon,
      suffixIcon: isRTL ? prefixIcon : suffixIcon,
      errorText: errorText,
    );
  }

  /// 检测文本是否包含RTL字符
  static bool containsRTLCharacters(String text) {
    if (text.isEmpty) return false;

    // RTL字符Unicode范围
    final rtlRegex = RegExp(
      r'[\u0590-\u05FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB1D-\uFDFF\uFE70-\uFEFF]',
    );
    return rtlRegex.hasMatch(text);
  }

  /// 检测文本是否包含维吾尔语字符
  static bool containsUyghurCharacters(String text) {
    if (text.isEmpty) return false;

    // 维吾尔语Unicode范围：U+0600-U+06FF (阿拉伯文字块)
    // 维吾尔语扩展范围：U+FB50-U+FDFF, U+FE70-U+FEFF
    final uyghurRegex = RegExp(r'[\u0600-\u06FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return uyghurRegex.hasMatch(text);
  }

  /// 检测文本是否包含阿拉伯语字符
  static bool containsArabicCharacters(String text) {
    if (text.isEmpty) return false;

    // 阿拉伯语Unicode范围
    final arabicRegex = RegExp(
      r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]',
    );
    return arabicRegex.hasMatch(text);
  }

  /// 根据文本内容自动检测文本方向
  static TextDirection detectTextDirection(String text) {
    if (text.isEmpty) {
      // 空文本时使用当前系统方向
      return currentDirection;
    }

    // 检测是否包含RTL字符
    if (containsRTLCharacters(text)) {
      return TextDirection.rtl;
    }

    // 默认使用LTR
    return TextDirection.ltr;
  }

  /// 根据文本内容自动获取文本对齐方式
  static TextAlign detectTextAlign(String text) {
    final direction = detectTextDirection(text);
    return direction == TextDirection.rtl ? TextAlign.right : TextAlign.left;
  }
}
