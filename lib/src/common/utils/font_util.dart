import 'package:flutter/material.dart';
import '../../services/language_service.dart';
import 'rtl_util.dart';

/// 字体工具类 - 根据语言和文本内容自动选择合适的字体
class FontUtil {
  /// 获取当前语言服务实例
  static LanguageService get _languageService => LanguageService();

  /// 维吾尔语字体名称
  static const String uyghurFontFamily = 'UKIJTor';

  /// 根据当前语言获取字体
  static String? getFontFamilyForCurrentLanguage() {
    final currentLanguage = _languageService.getCurrentLanguageCode();
    return currentLanguage == 'ug' ? uyghurFontFamily : null;
  }

  /// 根据文本内容获取字体
  static String? getFontFamilyForText(String text) {
    if (RTLUtil.containsUyghurCharacters(text)) {
      return uyghurFontFamily;
    }
    return null;
  }

  /// 根据当前语言和文本内容获取最合适的字体
  static String? getOptimalFontFamily(String? text) {
    // 如果有文本内容，优先根据文本内容判断
    if (text != null && text.isNotEmpty) {
      final textBasedFont = getFontFamilyForText(text);
      if (textBasedFont != null) {
        return textBasedFont;
      }
    }

    // 如果文本内容无法判断，则根据当前语言判断
    return getFontFamilyForCurrentLanguage();
  }

  /// 创建带有正确字体的TextStyle
  static TextStyle createTextStyle({
    String? text,
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
    TextDecoration? decoration,
    Color? decorationColor,
    TextDecorationStyle? decorationStyle,
    FontStyle? fontStyle,
    String? fontFamily,
  }) {
    // 如果明确指定了字体，使用指定的字体
    final finalFontFamily = fontFamily ?? getOptimalFontFamily(text);

    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      letterSpacing: letterSpacing,
      decoration: decoration,
      decorationColor: decorationColor,
      decorationStyle: decorationStyle,
      fontStyle: fontStyle,
      fontFamily: finalFontFamily,
    );
  }

  /// 为AppBar标题创建TextStyle
  static TextStyle createAppBarTitleStyle({
    required BuildContext context,
    String? text,
    double fontSize = 20,
    FontWeight fontWeight = FontWeight.w600,
    Color? color,
  }) {
    return createTextStyle(
      text: text,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }

  /// 为Tab标签创建TextStyle
  static TextStyle createTabLabelStyle({
    String? text,
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.w600,
    Color? color,
  }) {
    return createTextStyle(
      text: text,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }

  /// 为Tab标签创建未选中状态的TextStyle
  static TextStyle createTabUnselectedLabelStyle({
    String? text,
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.normal,
    Color? color,
  }) {
    return createTextStyle(
      text: text,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }

  /// 为按钮文本创建TextStyle
  static TextStyle createButtonTextStyle({
    String? text,
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.w500,
    Color? color,
  }) {
    return createTextStyle(
      text: text,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }

  /// 为普通文本创建TextStyle
  static TextStyle createBodyTextStyle({
    String? text,
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.normal,
    Color? color,
    double? height,
    double? letterSpacing,
  }) {
    return createTextStyle(
      text: text,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      letterSpacing: letterSpacing,
    );
  }

  /// 为标题文本创建TextStyle
  static TextStyle createHeadingTextStyle({
    String? text,
    double fontSize = 18,
    FontWeight fontWeight = FontWeight.w600,
    Color? color,
  }) {
    return createTextStyle(
      text: text,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }

  /// 为小标题文本创建TextStyle
  static TextStyle createSubheadingTextStyle({
    String? text,
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.w500,
    Color? color,
  }) {
    return createTextStyle(
      text: text,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }

  /// 为说明文本创建TextStyle
  static TextStyle createCaptionTextStyle({
    String? text,
    double fontSize = 12,
    FontWeight fontWeight = FontWeight.normal,
    Color? color,
  }) {
    return createTextStyle(
      text: text,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
    );
  }

  /// 检查当前是否为维吾尔语环境
  static bool get isUyghurLanguage {
    return _languageService.getCurrentLanguageCode() == 'ug';
  }

  /// 为维吾尔语文本优化样式
  static TextStyle optimizeForUyghur(TextStyle baseStyle, String? text) {
    if (text != null && RTLUtil.containsUyghurCharacters(text)) {
      return baseStyle.copyWith(
        fontFamily: uyghurFontFamily,
        letterSpacing: (baseStyle.letterSpacing ?? 0) + 0.3, // 增加字符间距
        height: (baseStyle.height ?? 1.0) * 1.1, // 增加行高
      );
    }
    return baseStyle;
  }
}
