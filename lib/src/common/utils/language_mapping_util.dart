import 'package:flutter/material.dart';
import '../../../generated/l10n/app_localizations.dart';

/// 语言映射工具类
/// 用于将硬编码的语言名称映射到国际化的文本
class LanguageMappingUtil {
  /// 获取国际化的语言名称
  static String getLocalizedLanguageName(
    BuildContext context,
    String language,
  ) {
    final localizations = AppLocalizations.of(context);

    switch (language) {
      // 中文名称匹配
      case '中文':
      case 'zh':
        return localizations.languageOptionChineseSimplified;
      case '维吾尔语':
      case 'ug':
        return localizations.languageOptionUyghur;
      case '英语':
      case 'en':
        return localizations.languageOptionEnglish;
      case '哈萨克语':
      case 'kk':
        return localizations.languageOptionKazakh;
      case '俄语':
      case 'ru':
        return localizations.languageOptionRussian;
      case '法语':
      case 'fr':
        return localizations.languageOptionFrench;
      case '西班牙语':
      case 'es':
        return localizations.languageOptionSpanish;
      case '粤语':
      case 'yue':
        return localizations.languageOptionCantonese;
      case '阿拉伯语':
      case 'ar':
        return localizations.languageOptionArabic;
      default:
        return language; // 如果没有匹配的，返回原始名称
    }
  }

  /// 获取语言的简短显示名称（用于按钮等空间有限的地方）
  static String getShortLanguageName(
    BuildContext context,
    String language, {
    bool isSmallScreen = false,
    bool isTinyScreen = false,
  }) {
    // 首先获取国际化的完整名称
    final localizedName = getLocalizedLanguageName(context, language);

    // 如果是英文环境，进行适当的缩短
    final locale = Localizations.localeOf(context);
    if (locale.languageCode == 'en') {
      // 英文环境下的缩短逻辑
      switch (language) {
        case '维吾尔语':
        case 'ug':
          return isTinyScreen ? 'Uyg' : (isSmallScreen ? 'Uyghur' : 'Uyghur');
        case '哈萨克语':
        case 'kk':
          return isTinyScreen ? 'Kaz' : (isSmallScreen ? 'Kazakh' : 'Kazakh');
        case '西班牙语':
        case 'es':
          return isTinyScreen ? 'Spa' : (isSmallScreen ? 'Spanish' : 'Spanish');
        case '阿拉伯语':
        case 'ar':
          return isTinyScreen ? 'Ara' : (isSmallScreen ? 'Arabic' : 'Arabic');
        case '中文':
        case 'zh':
          return isTinyScreen ? 'Zh' : (isSmallScreen ? 'Chinese' : 'Chinese');
        default:
          return localizedName;
      }
    } else {
      // 中文环境下的缩短逻辑（保持原有逻辑）
      switch (language) {
        case '维吾尔语':
        case 'ug':
          if (isTinyScreen) {
            return '维语';
          } else if (isSmallScreen) {
            return '维吾尔';
          } else {
            return localizedName;
          }
        case '哈萨克语':
        case 'kk':
          if (isTinyScreen) {
            return '哈语';
          } else if (isSmallScreen) {
            return '哈萨克';
          } else {
            return localizedName;
          }
        case '西班牙语':
        case 'es':
          if (isTinyScreen) {
            return '西语';
          } else if (isSmallScreen) {
            return '西班牙';
          } else {
            return localizedName;
          }
        case '中文':
        case 'zh':
          return localizedName; // 中文总是显示完整名称
        default:
          return localizedName;
      }
    }
  }
}
