/// 聊天导航助手 - 用于在不同页面间传递聊天对话数据
class ChatNavigationHelper {
  static final ChatNavigationHelper _instance =
      ChatNavigationHelper._internal();

  factory ChatNavigationHelper() {
    return _instance;
  }

  ChatNavigationHelper._internal();

  // 存储要加载的对话ID
  String? _pendingConversationId;

  // 对话加载回调（支持异步）
  Future<void> Function(String)? _onConversationLoadRequested;

  // 存储要选择的医生ID
  int? _pendingDoctorId;

  // 医生选择回调（支持异步）
  Future<void> Function(int)? _onDoctorSelectionRequested;

  // 是否需要切换到AI导游页面
  bool _needSwitchToAiGuide = false;

  /// 注册对话加载回调（AI导游页面调用）
  void registerConversationLoadCallback(
    Future<void> Function(String) callback,
  ) {
    _onConversationLoadRequested = callback;

    // 如果有待加载的对话ID，立即加载
    if (_pendingConversationId != null) {
      // 使用延迟执行确保UI已经完全准备好
      Future.microtask(() async {
        await _loadPendingConversation();
      });
    }
  }

  /// 强制处理待处理的对话（用于页面可见时主动检查）
  void processPendingConversationIfNeeded() {
    if (_pendingConversationId != null &&
        _onConversationLoadRequested != null) {
      Future.microtask(() async {
        await _loadPendingConversation();
      });
    }
  }

  /// 注销对话加载回调
  void unregisterConversationLoadCallback() {
    _onConversationLoadRequested = null;
  }

  /// 请求加载对话（聊天历史页面调用）
  void requestLoadConversation(String conversationId) {
    _pendingConversationId = conversationId;

    if (_onConversationLoadRequested != null) {
      Future.microtask(() async {
        await _loadPendingConversation();
      });
    }
    // 如果回调还没注册，对话ID会被保存在_pendingConversationId中，等待回调注册后自动加载
  }

  /// 加载待处理的对话
  Future<void> _loadPendingConversation() async {
    if (_pendingConversationId != null &&
        _onConversationLoadRequested != null) {
      final conversationId = _pendingConversationId!;
      _pendingConversationId = null; // 清空待处理记录
      await _onConversationLoadRequested!(conversationId);
    }
  }

  /// 清空待处理的对话
  void clearPendingConversation() {
    _pendingConversationId = null;
  }

  /// 检查是否有待处理的对话
  bool get hasPendingConversation => _pendingConversationId != null;

  /// 标记需要切换到AI导游页面
  void markSwitchToAiGuideNeeded() {
    _needSwitchToAiGuide = true;
  }

  /// 检查是否需要切换到AI导游页面
  bool get needSwitchToAiGuide => _needSwitchToAiGuide;

  /// 清除切换到AI导游页面的标记
  void clearSwitchToAiGuideFlag() {
    _needSwitchToAiGuide = false;
  }

  /// 注册医生选择回调（健康助手页面调用）
  void registerDoctorSelectionCallback(Future<void> Function(int) callback) {
    _onDoctorSelectionRequested = callback;

    // 如果有待选择的医生ID，立即选择
    if (_pendingDoctorId != null) {
      Future.microtask(() async {
        await _selectPendingDoctor();
      });
    }
  }

  /// 注销医生选择回调
  void unregisterDoctorSelectionCallback() {
    _onDoctorSelectionRequested = null;
  }

  /// 设置选择的医生ID（简化版本）
  void setSelectedDoctorId(int doctorId) {
    _pendingDoctorId = doctorId;
    _needSwitchToAiGuide = true;
  }

  /// 请求选择医生（产品列表页面调用）
  void requestSelectDoctor(int doctorId) {
    print('🔄 ChatNavigationHelper: 请求选择医生 ID: $doctorId');
    _pendingDoctorId = doctorId;
    _needSwitchToAiGuide = true; // 标记需要切换到健康助手页面
    print('🎯 ChatNavigationHelper: 已标记需要切换到健康助手页面');

    if (_onDoctorSelectionRequested != null) {
      print('✅ ChatNavigationHelper: 医生选择回调已注册，立即执行');
      Future.microtask(() async {
        await _selectPendingDoctor();
      });
    } else {
      print('⏳ ChatNavigationHelper: 医生选择回调未注册，等待注册后执行');
    }
    // 如果回调还没注册，医生ID会被保存在_pendingDoctorId中，等待回调注册后自动选择
  }

  /// 选择待处理的医生
  Future<void> _selectPendingDoctor() async {
    if (_pendingDoctorId != null && _onDoctorSelectionRequested != null) {
      final doctorId = _pendingDoctorId!;
      print('🔄 ChatNavigationHelper: 开始选择医生 ID: $doctorId');
      await _onDoctorSelectionRequested!(doctorId);
      print('✅ ChatNavigationHelper: 医生选择回调执行完成');
      _pendingDoctorId = null; // 清空待处理记录
      print('🧹 ChatNavigationHelper: 已清空待处理的医生ID');
      // 注意：不在这里清除 _needSwitchToAiGuide 标记，让主页面来清除
      print('🏠 ChatNavigationHelper: 保持切换标记，等待主页面处理');
    }
  }

  /// 清空待处理的医生选择
  void clearPendingDoctorSelection() {
    _pendingDoctorId = null;
  }

  /// 检查是否有待处理的医生选择
  bool get hasPendingDoctorSelection => _pendingDoctorId != null;

  /// 获取待处理的医生ID
  int? getPendingDoctorId() => _pendingDoctorId;

  /// 强制处理待处理的医生选择（用于页面可见时主动检查）
  void processPendingDoctorSelectionIfNeeded() {
    if (_pendingDoctorId != null && _onDoctorSelectionRequested != null) {
      Future.microtask(() async {
        await _selectPendingDoctor();
      });
    }
  }
}
