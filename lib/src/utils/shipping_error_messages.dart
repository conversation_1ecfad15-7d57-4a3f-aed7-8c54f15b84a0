import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';
import '../exceptions/shipping_exceptions.dart';

/// 物流错误消息辅助类
class ShippingErrorMessages {
  /// 将物流异常转换为国际化错误消息
  static String getLocalizedErrorMessage(
    BuildContext context,
    Exception error,
  ) {
    if (error is InsufficientPermissionException) {
      return AppLocalizations.of(context).insufficientPermissionDoctorRequired;
    } else if (error is GetPendingShipmentOrdersFailedException) {
      return AppLocalizations.of(context).getPendingShipmentOrdersFailed;
    } else if (error is TrackingNumberEmptyException) {
      return AppLocalizations.of(context).trackingNumberCannotBeEmpty;
    } else if (error is ShipmentFailedException) {
      return AppLocalizations.of(context).shipmentFailed;
    } else if (error is OrderNotExistException) {
      return AppLocalizations.of(context).orderNotExistOrNoAccess;
    } else if (error is ShipmentFailedCheckStatusException) {
      return AppLocalizations.of(context).shipmentFailedCheckOrderStatus;
    } else if (error is GetShippingStatusFailedException) {
      return AppLocalizations.of(context).getShippingStatusFailed;
    } else if (error is GetShippedOrdersFailedException) {
      return AppLocalizations.of(context).getShippedOrdersFailed;
    }
    return error.toString();
  }

  /// 获取权限不足错误消息
  static String getInsufficientPermissionError(BuildContext context) {
    return AppLocalizations.of(context).insufficientPermissionDoctorRequired;
  }

  /// 获取待发货订单失败错误消息
  static String getPendingShipmentOrdersFailedError(BuildContext context) {
    return AppLocalizations.of(context).getPendingShipmentOrdersFailed;
  }

  /// 获取快递单号不能为空错误消息
  static String getTrackingNumberEmptyError(BuildContext context) {
    return AppLocalizations.of(context).trackingNumberCannotBeEmpty;
  }

  /// 获取发货失败错误消息
  static String getShipmentFailedError(BuildContext context) {
    return AppLocalizations.of(context).shipmentFailed;
  }

  /// 获取订单不存在或无权访问错误消息
  static String getOrderNotExistError(BuildContext context) {
    return AppLocalizations.of(context).orderNotExistOrNoAccess;
  }

  /// 获取发货失败请检查订单状态错误消息
  static String getShipmentFailedCheckStatusError(BuildContext context) {
    return AppLocalizations.of(context).shipmentFailedCheckOrderStatus;
  }

  /// 获取物流状态失败错误消息
  static String getShippingStatusFailedError(BuildContext context) {
    return AppLocalizations.of(context).getShippingStatusFailed;
  }

  /// 获取已发货订单失败错误消息
  static String getShippedOrdersFailedError(BuildContext context) {
    return AppLocalizations.of(context).getShippedOrdersFailed;
  }
}
