import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';

/// 订单状态辅助类
class OrderStatusHelper {
  /// 获取订单状态文本
  static String getOrderStatusText(BuildContext context, int orderStatus) {
    switch (orderStatus) {
      case 0:
        return AppLocalizations.of(context).orderStatusPending;
      case 1:
        return AppLocalizations.of(context).orderStatusPendingShipment;
      case 2:
        return AppLocalizations.of(context).orderStatusShipped;
      case 3:
        return AppLocalizations.of(context).orderStatusCompleted;
      case 4:
        return AppLocalizations.of(context).orderStatusCancelled;
      default:
        return AppLocalizations.of(context).orderStatusUnknown;
    }
  }

  /// 获取支付状态文本
  static String getPayStatusText(BuildContext context, int payStatus) {
    switch (payStatus) {
      case 0:
        return AppLocalizations.of(context).payStatusUnpaid;
      case 1:
        return AppLocalizations.of(context).payStatusPaid;
      case 2:
        return AppLocalizations.of(context).payStatusRefunded;
      default:
        return AppLocalizations.of(context).orderStatusUnknown;
    }
  }

  /// 获取订单状态颜色
  static Color getOrderStatusColor(int orderStatus) {
    switch (orderStatus) {
      case 0:
        return const Color(0xFFF39C12); // 橙色 - 待支付
      case 1:
        return const Color(0xFF3498DB); // 蓝色 - 待发货
      case 2:
        return const Color(0xFF109D58); // 绿色 - 已发货
      case 3:
        return const Color(0xFF27AE60); // 深绿色 - 已完成
      case 4:
        return const Color(0xFFE74C3C); // 红色 - 已取消
      default:
        return const Color(0xFFBDC3C7); // 灰色 - 未知
    }
  }

  /// 获取支付状态颜色
  static Color getPayStatusColor(int payStatus) {
    switch (payStatus) {
      case 0:
        return const Color(0xFFF39C12); // 橙色 - 未支付
      case 1:
        return const Color(0xFF27AE60); // 绿色 - 已支付
      case 2:
        return const Color(0xFF3498DB); // 蓝色 - 已退款
      default:
        return const Color(0xFFBDC3C7); // 灰色 - 未知
    }
  }
}
