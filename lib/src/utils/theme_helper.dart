import 'package:flutter/material.dart';
import '../config/themes/app_colors.dart';

/// 主题帮助工具 - 解决颜色函数在常量构造器中的使用问题
/// 提供静态颜色和主题感知颜色之间的桥接
class ThemeHelper {
  /// 获取背景色 - 支持在常量构造器中使用
  static Color getBackground(BuildContext? context) {
    if (context == null) return AppColors.backgroundStatic;
    return AppColors.background(context);
  }

  /// 获取卡片背景色 - 支持在常量构造器中使用
  static Color getCardBackground(BuildContext? context) {
    if (context == null) return AppColors.cardBackgroundStatic;
    return AppColors.cardBackground(context);
  }

  /// 获取图标背景色 - 支持在常量构造器中使用
  static Color getIconBackground(BuildContext? context) {
    if (context == null) return AppColors.iconBackgroundStatic;
    return AppColors.iconBackground(context);
  }

  /// 获取主要文字色 - 支持在常量构造器中使用
  static Color getTextPrimary(BuildContext? context) {
    if (context == null) return AppColors.textPrimaryStatic;
    return AppColors.textPrimary(context);
  }

  /// 获取次要文字色 - 支持在常量构造器中使用
  static Color getTextSecondary(BuildContext? context) {
    if (context == null) return AppColors.textSecondaryStatic;
    return AppColors.textSecondary(context);
  }

  /// 获取提示文字色 - 支持在常量构造器中使用
  static Color getTextHint(BuildContext? context) {
    if (context == null) return AppColors.textHintStatic;
    return AppColors.textHint(context);
  }

  /// 获取边框色 - 支持在常量构造器中使用
  static Color getBorder(BuildContext? context) {
    if (context == null) return AppColors.borderStatic;
    return AppColors.border(context);
  }

  /// 获取分割线色 - 支持在常量构造器中使用
  static Color getDivider(BuildContext? context) {
    if (context == null) return AppColors.dividerStatic;
    return AppColors.divider(context);
  }

  /// 获取翻译页面背景色 - 支持在常量构造器中使用
  static Color getTranslationBackground(BuildContext? context) {
    if (context == null) return AppColors.translationBackgroundLight;
    return AppColors.translationBackground(context);
  }

  /// 获取输入框背景色 - 支持在常量构造器中使用
  static Color getInputBackground(BuildContext? context) {
    if (context == null) return AppColors.backgroundStatic.withAlpha(230);
    return Theme.of(context).brightness == Brightness.dark
        ? AppColors.withAlpha(Colors.grey[800]!, 180)
        : AppColors.withAlpha(Colors.grey[100]!, 180);
  }
}
