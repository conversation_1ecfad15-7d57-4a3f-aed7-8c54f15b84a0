import 'package:flutter/material.dart';

/// Toast消息工具类 - 根据输入法状态动态调整显示位置，支持渐变消失
class ToastUtil {
  static OverlayEntry? _overlayEntry;
  static bool _isVisible = false;

  /// 显示消息提示
  /// [context] - 上下文
  /// [message] - 显示的消息文本
  /// [duration] - 显示时长，默认2秒
  static void show(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
  }) {
    // 如果已经有Toast在显示，先移除
    dismiss();

    // 创建叠加层 - 不再预先计算位置，而是在Widget内部实时计算
    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) =>
          _ToastAnimatedWidget(message: message, duration: duration),
    );

    // 显示Toast
    _isVisible = true;
    Overlay.of(context).insert(_overlayEntry!);
  }

  /// 手动移除当前显示的Toast
  static void dismiss() {
    if (_isVisible && _overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isVisible = false;
    }
  }

  /// 检查Toast是否可见
  static bool get isVisible => _isVisible;
}

/// Toast动画控件 - 支持实时键盘适配
class _ToastAnimatedWidget extends StatefulWidget {
  final String message;
  final Duration duration;

  const _ToastAnimatedWidget({required this.message, required this.duration});

  @override
  _ToastAnimatedWidgetState createState() => _ToastAnimatedWidgetState();
}

class _ToastAnimatedWidgetState extends State<_ToastAnimatedWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // 创建动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // 创建淡入淡出动画
    _fadeAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    // 延迟后启动淡出动画
    Future.delayed(widget.duration, () {
      if (mounted) {
        _animationController.forward();
      }
    });

    // 动画结束后关闭
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        ToastUtil.dismiss();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 计算Toast位置 - 实时根据键盘状态调整
  double _calculatePosition(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final bottomInset = mediaQuery.viewInsets.bottom;
    final screenHeight = mediaQuery.size.height;
    final isKeyboardVisible = bottomInset > 0;

    // 根据键盘状态计算位置，为语音翻译按钮留出足够空间
    if (isKeyboardVisible) {
      // 键盘打开时：显示在键盘上方120像素
      return screenHeight - bottomInset - 120;
    } else {
      // 键盘关闭时：显示在屏幕底部上方150像素，更靠近底部
      return screenHeight - 150;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Positioned(
          top: _calculatePosition(context), // 实时计算位置
          width: MediaQuery.of(context).size.width,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Material(
              color: Colors.transparent,
              child: Center(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width - 32,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(0, 0, 0, 0.8),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: const Color.fromRGBO(0, 0, 0, 0.2),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Text(
                    widget.message,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
