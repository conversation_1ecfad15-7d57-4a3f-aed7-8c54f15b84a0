import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import '../services/http_client_service.dart';

/// HTTP辅助工具类 - 提供便捷的方法来处理token过期
class HttpHelper {
  /// 检查HTTP响应是否为token过期，如果是则自动处理
  ///
  /// 这个方法可以用于现有代码的快速迁移，无需大幅修改现有逻辑
  ///
  /// 使用示例：
  /// ```dart
  /// final response = await http.get(uri, headers: headers);
  /// await HttpHelper.checkTokenExpiry(response, context: context);
  /// // 继续处理响应...
  /// ```
  static Future<void> checkTokenExpiry(
    http.Response response, {
    BuildContext? context,
  }) async {
    await HttpClientService.checkTokenExpiry(response, context: context);
  }

  /// 检查StreamedResponse是否为token过期
  static Future<void> checkStreamedTokenExpiry(
    http.StreamedResponse response, {
    BuildContext? context,
  }) async {
    if (response.statusCode == 401) {
      // 创建一个临时的Response对象来使用现有的检查方法
      final tempResponse = http.Response('', 401);
      await HttpClientService.checkTokenExpiry(tempResponse, context: context);
    }
  }

  /// 为现有的HTTP请求添加token过期检查的包装器
  ///
  /// 使用示例：
  /// ```dart
  /// final response = await HttpHelper.wrapRequest(
  ///   () => http.get(uri, headers: headers),
  ///   context: context,
  /// );
  /// ```
  static Future<http.Response> wrapRequest(
    Future<http.Response> Function() request, {
    BuildContext? context,
  }) async {
    final response = await request();
    await checkTokenExpiry(response, context: context);
    return response;
  }

  /// 为现有的StreamedResponse请求添加token过期检查的包装器
  static Future<http.StreamedResponse> wrapStreamedRequest(
    Future<http.StreamedResponse> Function() request, {
    BuildContext? context,
  }) async {
    final response = await request();
    await checkStreamedTokenExpiry(response, context: context);
    return response;
  }
}
