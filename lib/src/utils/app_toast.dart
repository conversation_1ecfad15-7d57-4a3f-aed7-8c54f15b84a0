import 'package:flutter/material.dart';
import '../common/utils/font_util.dart';

/// 应用统一Toast组件 - 支持暗色模式，提供一致的视觉风格
class AppToast {
  static OverlayEntry? _overlayEntry;
  static bool _isVisible = false;

  /// 显示普通消息提示
  /// [context] - 上下文
  /// [message] - 显示的消息文本
  /// [duration] - 显示时长，默认2秒
  static void show(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
  }) {
    _showToast(context, message, duration: duration, type: _ToastType.normal);
  }

  /// 显示成功消息提示
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
  }) {
    _showToast(context, message, duration: duration, type: _ToastType.success);
  }

  /// 显示错误消息提示
  static void showError(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
  }) {
    _showToast(context, message, duration: duration, type: _ToastType.error);
  }

  /// 显示警告消息提示
  static void showWarning(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
  }) {
    _showToast(context, message, duration: duration, type: _ToastType.warning);
  }

  /// 显示退出确认提示（特殊的长时间显示）
  static void showExitConfirm(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    _showToast(context, message, duration: duration, type: _ToastType.exit);
  }

  /// 内部通用显示方法
  static void _showToast(
    BuildContext context,
    String message, {
    required Duration duration,
    required _ToastType type,
  }) {
    // 如果已经有Toast在显示，先移除
    dismiss();

    // 获取输入法高度
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = bottomInset > 0;
    final screenHeight = MediaQuery.of(context).size.height;

    // 根据输入法状态决定位置，为语音翻译按钮留出足够空间
    final position = isKeyboardVisible
        ? screenHeight -
              bottomInset -
              120 // 输入法上方120像素
        : screenHeight - 200; // 屏幕底部上方200像素，避免挡住语音翻译按钮

    // 创建叠加层
    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) => _ToastAnimatedWidget(
        position: position,
        message: message,
        duration: duration,
        type: type,
      ),
    );

    // 显示Toast
    _isVisible = true;
    Overlay.of(context).insert(_overlayEntry!);
  }

  /// 手动移除当前显示的Toast
  static void dismiss() {
    if (_isVisible && _overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isVisible = false;
    }
  }

  /// 检查是否有Toast正在显示
  static bool get isVisible => _isVisible;
}

/// Toast类型枚举
enum _ToastType { normal, success, error, warning, exit }

/// Toast动画控件
class _ToastAnimatedWidget extends StatefulWidget {
  final double position;
  final String message;
  final Duration duration;
  final _ToastType type;

  const _ToastAnimatedWidget({
    required this.position,
    required this.message,
    required this.duration,
    required this.type,
  });

  @override
  _ToastAnimatedWidgetState createState() => _ToastAnimatedWidgetState();
}

class _ToastAnimatedWidgetState extends State<_ToastAnimatedWidget>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // 创建动画控制器
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // 创建动画
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    // 启动进入动画
    _fadeController.forward();
    _scaleController.forward();

    // 延迟后启动退出动画
    Future.delayed(widget.duration, () {
      if (mounted) {
        _fadeController.reverse();
        _scaleController.reverse();
      }
    });

    // 动画结束后关闭
    _fadeController.addStatusListener((status) {
      if (status == AnimationStatus.dismissed) {
        AppToast.dismiss();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  /// 获取Toast样式配置
  _ToastStyleConfig _getStyleConfig(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (widget.type) {
      case _ToastType.success:
        return _ToastStyleConfig(
          backgroundColor: isDark
              ? const Color(0xFF1B5E20).withValues(alpha: 0.9)
              : const Color(0xFF4CAF50).withValues(alpha: 0.9),
          textColor: Colors.white,
          iconData: Icons.check_circle_outline,
          iconColor: Colors.white,
        );
      case _ToastType.error:
        return _ToastStyleConfig(
          backgroundColor: isDark
              ? const Color(0xFFB71C1C).withValues(alpha: 0.9)
              : const Color(0xFFF44336).withValues(alpha: 0.9),
          textColor: Colors.white,
          iconData: Icons.error_outline,
          iconColor: Colors.white,
        );
      case _ToastType.warning:
        return _ToastStyleConfig(
          backgroundColor: isDark
              ? const Color(0xFFE65100).withValues(alpha: 0.9)
              : const Color(0xFFFF9800).withValues(alpha: 0.9),
          textColor: Colors.white,
          iconData: Icons.warning_amber_outlined,
          iconColor: Colors.white,
        );
      case _ToastType.exit:
        return _ToastStyleConfig(
          backgroundColor: const Color.fromRGBO(0, 0, 0, 0.8), // 与ToastUtil保持一致
          textColor: Colors.white,
          iconData: null, // 不显示图标，与ToastUtil保持一致
          iconColor: Colors.white,
        );
      case _ToastType.normal:
        return _ToastStyleConfig(
          backgroundColor: isDark
              ? const Color(0xFF1E1E1E).withValues(alpha: 0.9)
              : const Color(0xFF424242).withValues(alpha: 0.9),
          textColor: Colors.white,
          iconData: null,
          iconColor: Colors.white,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final styleConfig = _getStyleConfig(context);

    return Positioned(
      top: widget.position,
      left: 16,
      right: 16,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: widget.type == _ToastType.exit
                  ? const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ) // 与ToastUtil一致
                  : const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
              decoration: BoxDecoration(
                color: styleConfig.backgroundColor,
                borderRadius: BorderRadius.circular(
                  widget.type == _ToastType.exit ? 20 : 12, // 退出提示使用更圆的角
                ),
                boxShadow: widget.type == _ToastType.exit
                    ? [
                        // 与ToastUtil一致的阴影
                        BoxShadow(
                          color: const Color.fromRGBO(0, 0, 0, 0.2),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ]
                    : [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 12,
                          spreadRadius: 2,
                          offset: const Offset(0, 4),
                        ),
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 6,
                          spreadRadius: 1,
                          offset: const Offset(0, 2),
                        ),
                      ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (styleConfig.iconData != null) ...[
                    Icon(
                      styleConfig.iconData,
                      color: styleConfig.iconColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Flexible(
                    child: Text(
                      widget.message,
                      style: widget.type == _ToastType.exit
                          ? FontUtil.createBodyTextStyle(
                              text: widget.message,
                            ).copyWith(
                              color: styleConfig.textColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            )
                          : TextStyle(
                              color: styleConfig.textColor,
                              fontSize: 15,
                              fontWeight: FontWeight.w500,
                              height: 1.2,
                            ),
                      textAlign: TextAlign.center,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Toast样式配置类
class _ToastStyleConfig {
  final Color backgroundColor;
  final Color textColor;
  final IconData? iconData;
  final Color iconColor;

  const _ToastStyleConfig({
    required this.backgroundColor,
    required this.textColor,
    this.iconData,
    required this.iconColor,
  });
}
