import '../services/auth_service.dart';

/// 认证辅助类 - 简单的token管理接口
class AuthHelper {
  /// 获取当前token
  static String? getToken() {
    return AuthService().currentUser?.token;
  }

  /// 检查用户是否已登录
  static bool isLoggedIn() {
    final authService = AuthService();
    final token = getToken();
    final currentUser = authService.currentUser;
    final authServiceIsLoggedIn = authService.isLoggedIn;

    print('AuthHelper: token = ${token ?? "null"}');
    print('AuthHelper: currentUser = ${currentUser?.toString() ?? "null"}');
    print('AuthHelper: authService.isLoggedIn = $authServiceIsLoggedIn');

    final result = token != null && token.isNotEmpty;
    print('AuthHelper: 最终结果 = $result');

    return result;
  }

  /// 设置用户token（已废弃，使用AuthService.login）
  @Deprecated('使用AuthService.login替代')
  static void setToken(String token) {
    // 不再直接管理token，通过AuthService管理
  }

  /// 清除token（已废弃，使用AuthService.logout）
  @Deprecated('使用AuthService.logout替代')
  static void clearToken() {
    // 不再直接管理token，通过AuthService管理
  }

  /// 从用户模型中设置token（已废弃，使用AuthService.login）
  @Deprecated('使用AuthService.login替代')
  static void setTokenFromUser(String userToken) {
    // 不再直接管理token，通过AuthService管理
  }

  // 兼容性方法，保持向后兼容
  /// @deprecated 使用AuthService替代
  static void setSessionKey(String sessionKey) {}

  /// @deprecated 使用AuthService替代
  static String? getSessionKey() => getToken();

  /// @deprecated 使用AuthService替代
  static void clearSession() {}
}
