import 'package:flutter/material.dart';

/// Color扩展方法 - 提供额外的颜色操作功能
extension ColorExtensions on Color {
  /// 用于替代withOpacity和withAlpha，支持命名参数
  /// @param alpha 0-255的透明度值
  /// @param opacity 0.0-1.0的透明度值
  Color withValues({int? alpha, double? opacity}) {
    if (opacity != null) {
      // 手动实现withOpacity避免使用弃用的方法
      return Color.fromARGB(
        (opacity * 255).round(),
        (r * 255).round(),
        (g * 255).round(),
        (b * 255).round(),
      );
    }

    if (alpha != null) {
      // 使用带有alpha值的ARGB颜色
      return Color.fromARGB(
        alpha,
        (r * 255).round(),
        (g * 255).round(),
        (b * 255).round(),
      );
    }

    return this;
  }
}
