/// 聊天状态管理器 - 用于在不同页面间同步聊天状态
class ChatStateManager {
  static final ChatStateManager _instance = ChatStateManager._internal();

  factory ChatStateManager() {
    return _instance;
  }

  ChatStateManager._internal();

  // 当前正在聊天的对话ID
  String? _currentConversationId;

  // 聊天状态变化回调
  void Function()? _onChatStateChanged;

  /// 注册聊天状态变化回调（AI导游页面调用）
  void registerChatStateCallback(void Function() callback) {
    _onChatStateChanged = callback;
  }

  /// 注销聊天状态变化回调
  void unregisterChatStateCallback() {
    _onChatStateChanged = null;
  }

  /// 设置当前对话ID
  void setCurrentConversationId(String? conversationId) {
    _currentConversationId = conversationId;
  }

  /// 获取当前对话ID
  String? get currentConversationId => _currentConversationId;

  /// 通知聊天状态已变化（删除对话时调用）
  void notifyChatStateChanged() {
    if (_onChatStateChanged != null) {
      _onChatStateChanged!();
    }
  }

  /// 检查指定对话是否是当前正在聊天的对话
  bool isCurrentConversation(String conversationId) {
    return _currentConversationId == conversationId;
  }

  /// 清空当前聊天状态
  void clearCurrentConversation() {
    _currentConversationId = null;
    notifyChatStateChanged();
  }
}
