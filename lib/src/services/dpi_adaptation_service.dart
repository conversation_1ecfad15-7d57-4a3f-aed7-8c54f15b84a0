import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// DPI适配模式枚举
enum DpiAdaptationMode {
  /// 自动适配 - 根据设备DPI自动调整
  auto,

  /// 小尺寸 - 适合高DPI设备
  small,

  /// 标准尺寸 - 默认尺寸
  standard,

  /// 大尺寸 - 适合低DPI设备
  large,
}

/// DPI界面适配服务 - 根据设备DPI自动调整界面元素大小
class DpiAdaptationService extends ChangeNotifier {
  static final DpiAdaptationService _instance =
      DpiAdaptationService._internal();

  /// 获取DPI适配服务单例实例
  factory DpiAdaptationService() => _instance;

  DpiAdaptationService._internal();

  /// DPI适配模式持久化键名
  static const String _dpiModePreferenceKey = 'app_dpi_adaptation_mode';

  /// 当前DPI适配模式
  DpiAdaptationMode _currentMode = DpiAdaptationMode.auto;

  /// DPI适配缩放因子映射
  static const Map<DpiAdaptationMode, double> _scaleFactorMap = {
    DpiAdaptationMode.small: 0.85, // 小尺寸，适合高DPI设备
    DpiAdaptationMode.standard: 1.0, // 标准尺寸
    DpiAdaptationMode.large: 1.15, // 大尺寸，适合低DPI设备
  };

  /// DPI阈值配置
  static const double _lowDpiThreshold = 1.5; // 低DPI阈值
  static const double _highDpiThreshold = 2.5; // 高DPI阈值

  /// 获取当前DPI适配模式
  DpiAdaptationMode get currentMode => _currentMode;

  /// 初始化DPI适配服务
  Future<void> initialize() async {
    await _loadDpiModePreference();
  }

  /// 从持久化存储加载DPI适配模式
  Future<void> _loadDpiModePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final modeString = prefs.getString(_dpiModePreferenceKey);

      if (modeString != null) {
        _currentMode = DpiAdaptationMode.values.firstWhere(
          (mode) => mode.name == modeString,
          orElse: () => DpiAdaptationMode.auto,
        );
      }
    } catch (e) {
      _currentMode = DpiAdaptationMode.auto;
    }
  }

  /// 保存DPI适配模式到持久化存储
  Future<void> _saveDpiModePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_dpiModePreferenceKey, _currentMode.name);
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 设置DPI适配模式
  Future<void> setDpiAdaptationMode(DpiAdaptationMode mode) async {
    if (_currentMode == mode) return;

    _currentMode = mode;
    await _saveDpiModePreference();
    notifyListeners();
  }

  /// 获取当前生效的缩放因子
  double getScaleFactor(BuildContext context) {
    if (_currentMode == DpiAdaptationMode.auto) {
      return _getAutoScaleFactor(context);
    }
    return _scaleFactorMap[_currentMode] ?? 1.0;
  }

  /// 根据设备DPI自动计算缩放因子
  double _getAutoScaleFactor(BuildContext context) {
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    final screenSize = MediaQuery.of(context).size;
    final screenDiagonal = _calculateScreenDiagonal(
      screenSize,
      devicePixelRatio,
    );

    // 根据设备DPI和屏幕尺寸智能调整
    if (devicePixelRatio <= _lowDpiThreshold) {
      // 低DPI设备，使用较大的界面元素
      return screenDiagonal < 5.0 ? 1.1 : 1.15;
    } else if (devicePixelRatio >= _highDpiThreshold) {
      // 高DPI设备，使用较小的界面元素
      return screenDiagonal > 6.0 ? 0.9 : 0.85;
    } else {
      // 中等DPI设备，根据屏幕尺寸微调
      if (screenDiagonal < 4.5) {
        return 1.05; // 小屏幕稍微放大
      } else if (screenDiagonal > 6.5) {
        return 0.95; // 大屏幕稍微缩小
      }
      return 1.0; // 标准尺寸
    }
  }

  /// 计算屏幕对角线尺寸（英寸）
  double _calculateScreenDiagonal(Size screenSize, double devicePixelRatio) {
    final widthInches = screenSize.width / (devicePixelRatio * 160);
    final heightInches = screenSize.height / (devicePixelRatio * 160);
    return sqrt(widthInches * widthInches + heightInches * heightInches);
  }

  /// 获取适配后的尺寸
  double getAdaptedSize(BuildContext context, double baseSize) {
    return baseSize * getScaleFactor(context);
  }

  /// 获取适配后的字体大小
  double getAdaptedFontSize(BuildContext context, double baseFontSize) {
    return baseFontSize * getScaleFactor(context);
  }

  /// 获取适配后的间距
  double getAdaptedSpacing(BuildContext context, double baseSpacing) {
    return baseSpacing * getScaleFactor(context);
  }

  /// 获取适配后的图标尺寸
  double getAdaptedIconSize(BuildContext context, double baseIconSize) {
    return baseIconSize * getScaleFactor(context);
  }

  /// 获取适配后的边框圆角
  double getAdaptedBorderRadius(BuildContext context, double baseBorderRadius) {
    return baseBorderRadius * getScaleFactor(context);
  }

  /// 获取适配后的阴影模糊半径
  double getAdaptedBlurRadius(BuildContext context, double baseBlurRadius) {
    return baseBlurRadius * getScaleFactor(context);
  }

  /// 获取当前模式的显示名称
  String get currentModeDisplayName {
    switch (_currentMode) {
      case DpiAdaptationMode.auto:
        return '自动适配';
      case DpiAdaptationMode.small:
        return '紧凑';
      case DpiAdaptationMode.standard:
        return '标准';
      case DpiAdaptationMode.large:
        return '宽松';
    }
  }

  /// 获取设备信息（用于调试）
  Map<String, dynamic> getDeviceInfo(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenSize = mediaQuery.size;
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    final screenDiagonal = _calculateScreenDiagonal(
      screenSize,
      devicePixelRatio,
    );

    return {
      'screenWidth': screenSize.width,
      'screenHeight': screenSize.height,
      'devicePixelRatio': devicePixelRatio,
      'screenDiagonal': screenDiagonal.toStringAsFixed(1),
      'currentMode': _currentMode.name,
      'scaleFactor': getScaleFactor(context).toStringAsFixed(2),
      'autoScaleFactor': _getAutoScaleFactor(context).toStringAsFixed(2),
    };
  }

  /// 重置到默认模式
  Future<void> resetToDefault() async {
    await setDpiAdaptationMode(DpiAdaptationMode.auto);
  }

  /// 为全局MediaQuery提供修改后的MediaQueryData
  MediaQueryData getModifiedMediaQueryData(
    BuildContext context,
    MediaQueryData originalData,
  ) {
    final scaleFactor = getScaleFactor(context);

    return originalData.copyWith(
      textScaler: TextScaler.linear(originalData.textScaler.scale(scaleFactor)),
    );
  }
}

/// DPI适配工具类 - 提供便捷的适配方法
class DpiUtil {
  static final DpiAdaptationService _service = DpiAdaptationService();

  /// 获取适配后的尺寸
  static double size(BuildContext context, double baseSize) {
    return _service.getAdaptedSize(context, baseSize);
  }

  /// 获取适配后的字体大小
  static double fontSize(BuildContext context, double baseFontSize) {
    return _service.getAdaptedFontSize(context, baseFontSize);
  }

  /// 获取适配后的间距
  static double spacing(BuildContext context, double baseSpacing) {
    return _service.getAdaptedSpacing(context, baseSpacing);
  }

  /// 获取适配后的图标尺寸
  static double iconSize(BuildContext context, double baseIconSize) {
    return _service.getAdaptedIconSize(context, baseIconSize);
  }

  /// 获取适配后的边框圆角
  static double borderRadius(BuildContext context, double baseBorderRadius) {
    return _service.getAdaptedBorderRadius(context, baseBorderRadius);
  }

  /// 获取适配后的阴影模糊半径
  static double blurRadius(BuildContext context, double baseBlurRadius) {
    return _service.getAdaptedBlurRadius(context, baseBlurRadius);
  }
}
