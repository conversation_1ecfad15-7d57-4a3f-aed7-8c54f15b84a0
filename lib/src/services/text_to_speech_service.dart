import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../config/api/api_config.dart';
import '../utils/auth_helper.dart';

/// TTS服务结果
class TtsResult {
  final String audioUrl;
  final String language;
  final bool success;
  final String? errorMessage;

  const TtsResult({
    required this.audioUrl,
    required this.language,
    required this.success,
    this.errorMessage,
  });

  /// 成功结果
  factory TtsResult.success({
    required String audioUrl,
    required String language,
  }) {
    return TtsResult(
      audioUrl: audioUrl,
      language: language,
      success: true,
    );
  }

  /// 失败结果
  factory TtsResult.error(String errorMessage) {
    return TtsResult(
      audioUrl: '',
      language: '',
      success: false,
      errorMessage: errorMessage,
    );
  }

  /// 从JSON创建对象
  factory TtsResult.fromJson(Map<String, dynamic> json) {
    return TtsResult(
      audioUrl: json['url'] as String? ?? '',
      language: json['lang'] as String? ?? '',
      success: true,
    );
  }
}

/// TTS支持的语言信息
class TtsLanguage {
  final String code;
  final String name;
  final String nameEn;
  final String nameUg;

  const TtsLanguage({
    required this.code,
    required this.name,
    required this.nameEn,
    required this.nameUg,
  });

  /// 从JSON创建对象
  factory TtsLanguage.fromJson(Map<String, dynamic> json) {
    return TtsLanguage(
      code: json['code'] as String,
      name: json['name'] as String,
      nameEn: json['name_en'] as String,
      nameUg: json['name_ug'] as String,
    );
  }
}

/// 文本转语音服务
class TextToSpeechService {
  /// 将文本转换为语音
  /// [text] - 要转换的文本内容（1-5000字符）
  /// [language] - 目标语言代码（zh/en/ug），默认为zh
  /// [speed] - 语速（1-10），默认为5
  /// 返回音频URL
  static Future<TtsResult> convertTextToSpeech({
    required String text,
    String language = 'zh',
    int speed = 5,
  }) async {
    try {
      // 检查用户是否已登录
      if (!AuthHelper.isLoggedIn()) {
        if (kDebugMode) {
          debugPrint('🔊 TTS: 用户未登录');
        }
        return TtsResult.error('用户未登录');
      }

      // 验证文本内容
      if (text.trim().isEmpty) {
        if (kDebugMode) {
          debugPrint('🔊 TTS: 文本内容为空');
        }
        return TtsResult.error('文本内容不能为空');
      }

      // 验证文本长度
      if (text.length > 5000) {
        if (kDebugMode) {
          debugPrint('🔊 TTS: 文本过长: ${text.length}字符 > 5000字符');
        }
        return TtsResult.error('文本内容过长，最大支持5000字符');
      }

      // 验证语速范围
      if (speed < 1 || speed > 10) {
        if (kDebugMode) {
          debugPrint('🔊 TTS: 语速超出范围: $speed，应在1-10之间');
        }
        return TtsResult.error('语速应在1-10之间');
      }

      // 获取用户token
      final token = AuthHelper.getToken();
      if (token == null) {
        return TtsResult.error('获取用户token失败');
      }

      // 使用API配置
      final apiUrl = ApiConfig.textToSpeechUrl;

      if (kDebugMode) {
        debugPrint('🔊 TTS: 发送文本转语音请求');
        debugPrint('- API端点: $apiUrl');
        debugPrint('- 文本长度: ${text.length}字符');
        debugPrint('- 语言: $language');
        debugPrint('- 语速: $speed');
      }

      // 创建请求体
      final requestBody = {
        'text': text.trim(),
        'lang': language,
        'speed': speed.toString(),
      };

      // 发送POST请求
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: requestBody,
      ).timeout(
        const Duration(seconds: 30), // 30秒超时
        onTimeout: () {
          throw Exception('请求超时，请检查网络连接');
        },
      );

      if (kDebugMode) {
        debugPrint('🔊 TTS: 响应状态码: ${response.statusCode}');
        debugPrint('🔊 TTS: 响应内容: ${response.body}');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (jsonData['code'] == 200) {
          final data = jsonData['data'] as Map<String, dynamic>;

          if (kDebugMode) {
            debugPrint('✅ TTS: 文本转语音成功');
            debugPrint('- 音频URL: ${data['url']}');
            debugPrint('- 语言: ${data['lang']}');
          }

          return TtsResult.fromJson(data);
        } else {
          // API返回错误
          final errorMessage = jsonData['message'] ?? '文本转语音失败';
          if (kDebugMode) {
            debugPrint('❌ TTS: API返回错误: $errorMessage');
          }
          return TtsResult.error(errorMessage);
        }
      } else if (response.statusCode == 401) {
        if (kDebugMode) {
          debugPrint('❌ TTS: 身份验证失败');
        }
        return TtsResult.error('登录已过期，请重新登录');
      } else {
        if (kDebugMode) {
          debugPrint('❌ TTS: 网络请求失败: ${response.statusCode}');
        }
        return TtsResult.error('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ TTS: 文本转语音异常: $e');
      }
      return TtsResult.error('文本转语音异常: $e');
    }
  }

  /// 获取支持的语言列表
  static Future<List<TtsLanguage>> getSupportedLanguages() async {
    try {
      // 检查用户是否已登录
      if (!AuthHelper.isLoggedIn()) {
        if (kDebugMode) {
          debugPrint('🔊 TTS: 用户未登录，无法获取语言列表');
        }
        return [];
      }

      // 获取用户token
      final token = AuthHelper.getToken();
      if (token == null) {
        if (kDebugMode) {
          debugPrint('🔊 TTS: 获取用户token失败');
        }
        return [];
      }

      // 使用API配置
      final apiUrl = ApiConfig.ttsLanguagesUrl;

      if (kDebugMode) {
        debugPrint('🔊 TTS: 获取支持的语言列表');
        debugPrint('- API端点: $apiUrl');
      }

      // 发送GET请求
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          'Authorization': 'Bearer $token',
        },
      ).timeout(
        const Duration(seconds: 10), // 10秒超时
        onTimeout: () {
          throw Exception('请求超时，请检查网络连接');
        },
      );

      if (kDebugMode) {
        debugPrint('🔊 TTS: 响应状态码: ${response.statusCode}');
        debugPrint('🔊 TTS: 响应内容: ${response.body}');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (jsonData['code'] == 200) {
          final List<dynamic> languageList = jsonData['data'] as List<dynamic>;
          final languages = languageList
              .map((lang) => TtsLanguage.fromJson(lang as Map<String, dynamic>))
              .toList();

          if (kDebugMode) {
            debugPrint('✅ TTS: 获取语言列表成功，共${languages.length}种语言');
            for (final lang in languages) {
              debugPrint('- ${lang.code}: ${lang.name}');
            }
          }

          return languages;
        } else {
          // API返回错误
          final errorMessage = jsonData['message'] ?? '获取语言列表失败';
          if (kDebugMode) {
            debugPrint('❌ TTS: API返回错误: $errorMessage');
          }
          return [];
        }
      } else {
        if (kDebugMode) {
          debugPrint('❌ TTS: 网络请求失败: ${response.statusCode}');
        }
        return [];
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ TTS: 获取语言列表异常: $e');
      }
      return [];
    }
  }
}
