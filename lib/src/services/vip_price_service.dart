import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../config/api/api_config.dart';
import '../models/vip_price_model.dart';

/// VIP价格服务 - 获取会员套餐价格列表
class VipPriceService {
  static final VipPriceService _instance = VipPriceService._internal();
  factory VipPriceService() => _instance;
  VipPriceService._internal();

  List<VipPriceModel>? _cachedPrices;
  DateTime? _cacheTime;
  static const Duration _cacheTimeout = Duration(minutes: 30); // 缓存30分钟

  /// 获取VIP价格列表
  Future<List<VipPriceModel>> getVipPrices({bool forceRefresh = false}) async {
    // 检查缓存
    if (!forceRefresh && _cachedPrices != null && _cacheTime != null) {
      final now = DateTime.now();
      if (now.difference(_cacheTime!) < _cacheTimeout) {
        debugPrint('📊 使用缓存的VIP价格数据');
        return _cachedPrices!;
      }
    }

    try {
      debugPrint('🌐 请求VIP价格列表...');

      // 使用API配置中的VIP价格接口
      final apiUrl = ApiConfig.vipPriceListUrl;

      debugPrint('📡 VIP价格接口: $apiUrl');

      // 发送GET请求
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      );

      debugPrint('📊 VIP价格接口响应状态码: ${response.statusCode}');
      debugPrint('📊 VIP价格接口响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);

        if (responseData['code'] == 200) {
          // 成功响应
          final List<dynamic> data = responseData['data'] as List<dynamic>;

          final prices = data
              .map(
                (item) => VipPriceModel.fromJson(item as Map<String, dynamic>),
              )
              .toList();

          // 更新缓存
          _cachedPrices = prices;
          _cacheTime = DateTime.now();

          debugPrint('✅ 成功获取${prices.length}个VIP套餐');
          for (final price in prices) {
            debugPrint(
              '  - ${price.name}: ¥${price.currentPrice} (原价: ¥${price.price})',
            );
          }

          return prices;
        } else {
          throw Exception(responseData['message'] ?? 'VIP价格接口返回错误');
        }
      } else {
        throw Exception('VIP价格接口请求失败，状态码: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ 获取VIP价格失败: $e');

      // 如果有缓存数据，在出错时返回缓存
      if (_cachedPrices != null && _cachedPrices!.isNotEmpty) {
        debugPrint('📊 网络错误，使用缓存的VIP价格数据');
        return _cachedPrices!;
      }

      // 如果没有缓存，返回默认价格
      debugPrint('📊 使用默认VIP价格数据');
      return _getDefaultPrices();
    }
  }

  /// 根据类型获取特定的VIP套餐
  Future<VipPriceModel?> getVipPriceByType({required bool isYearly}) async {
    try {
      final prices = await getVipPrices();

      if (isYearly) {
        // 查找年度会员，优先返回包年会员
        return prices.firstWhere(
          (p) => p.isYearly,
          orElse: () => prices.firstWhere(
            (p) => p.isPermanent,
            orElse: () => prices.first,
          ),
        );
      } else {
        // 查找月度会员
        return prices.firstWhere(
          (p) => p.isMonthly,
          orElse: () => prices.first,
        );
      }
    } catch (e) {
      debugPrint('❌ 获取特定VIP套餐失败: $e');
      return null;
    }
  }

  /// 清除缓存
  void clearCache() {
    _cachedPrices = null;
    _cacheTime = null;
    debugPrint('🗑️ VIP价格缓存已清除');
  }

  /// 获取默认价格（网络错误时的备选方案）
  List<VipPriceModel> _getDefaultPrices() {
    return [
      const VipPriceModel(
        id: 1,
        name: '包月会员',
        price: 29.90,
        currentPrice: 19.90,
        automaticRenewal: 1,
        aRPrice: 15.00,
        describe: '一个月VIP特权',
      ),
      const VipPriceModel(
        id: 2,
        name: '包年会员',
        price: 199.90,
        currentPrice: 59.90,
        automaticRenewal: 1,
        aRPrice: 50.00,
        describe: '一年VIP特权，支持自动续费',
      ),
      const VipPriceModel(
        id: 3,
        name: '永久会员',
        price: 299.90,
        currentPrice: 99.90,
        automaticRenewal: 0,
        describe: '永久享受所有VIP特权',
      ),
    ];
  }
}
