import 'dart:async';
import 'dart:io';
import '../models/conversation_model.dart';
import '../models/chat_message_model.dart';
import '../data/services/chat_api_service.dart';
import '../data/services/chat_cache_service.dart';
import 'language_service.dart';

/// 聊天仓库 - 协调API服务和缓存服务，实现Cache-then-Network策略
class ChatRepository {
  static final ChatRepository _instance = ChatRepository._internal();
  factory ChatRepository() => _instance;
  ChatRepository._internal();

  final ChatApiService _apiService = ChatApiService();
  final ChatCacheService _cacheService = ChatCacheService();

  /// 获取对话列表 - 使用Cache-then-Network策略
  Stream<List<ConversationModel>> getConversations({
    int page = 1,
    int pageSize = 20,
  }) async* {
    try {
      // 1. 立即返回缓存数据
      final cachedConversations = await _cacheService.getConversations();
      yield cachedConversations;

      // 2. 获取网络数据
      try {
        // 不传递lang参数，让后端使用默认语言或用户设置
        final networkConversations = await _apiService.fetchConversations(
          page: page,
          pageSize: pageSize,
        );

        // 3. 更新缓存
        await _cacheService.saveConversations(networkConversations);

        // 4. 返回最新数据
        final updatedConversations = await _cacheService.getConversations();
        yield updatedConversations;
      } catch (networkError) {
        // 网络请求失败时，如果没有缓存数据，则抛出异常
        if (cachedConversations.isEmpty) {
          rethrow;
        }
        // 如果有缓存数据，则忽略网络错误，继续使用缓存
      }
    } catch (e) {
      // 如果缓存也失败，抛出异常
      throw Exception('获取对话列表失败: $e');
    }
  }

  /// 获取对话消息列表 - 使用Cache-then-Network策略
  Stream<List<ChatMessageModel>> getMessages(
    String conversationId, {
    int page = 1,
    int pageSize = 50,
    String? beforeMessageId,
  }) async* {
    try {
      // 1. 立即返回缓存数据
      final cachedMessages = await _cacheService.getMessages(
        conversationId,
        limit: pageSize,
        offset: (page - 1) * pageSize,
      );
      yield cachedMessages;

      // 2. 获取网络数据
      try {
        final networkMessages = await _apiService.fetchMessages(
          conversationId,
          limit: pageSize,
          beforeMessageId: beforeMessageId,
        );

        // 3. 更新缓存
        if (page == 1) {
          // 如果是第一页，替换所有消息
          await _cacheService.saveMessages(conversationId, networkMessages);
        } else {
          // 如果是分页加载，添加新消息
          for (final message in networkMessages) {
            await _cacheService.addMessage(message);
          }
        }

        // 4. 返回最新数据
        final updatedMessages = await _cacheService.getMessages(conversationId);
        yield updatedMessages;
      } catch (networkError) {
        // 网络请求失败时，如果没有缓存数据，则抛出异常
        if (cachedMessages.isEmpty) {
          rethrow;
        }
        // 如果有缓存数据，则忽略网络错误，继续使用缓存
      }
    } catch (e) {
      throw Exception('获取消息列表失败: $e');
    }
  }

  /// 发送图片消息 (流式响应)
  Stream<ChatMessageModel> sendImageMessageStream({
    required String conversationId,
    required File imageFile,
    required int doctorId,
    String? description,
    double? latitude,
    double? longitude,
    String? address,
  }) async* {
    try {
      // 直接调用API的流式图片发送方法
      await for (final aiMessage in _apiService.sendImageMessageStream(
        conversationId: conversationId,
        imageFile: imageFile,
        doctorId: doctorId,
        description: description,
        latitude: latitude,
        longitude: longitude,
        address: address,
      )) {
        yield aiMessage;
      }
    } catch (e) {
      throw Exception('发送流式图片消息失败: $e');
    }
  }

  /// 上传聊天图片
  Future<String> uploadChatImage(File imageFile) async {
    try {
      return await _apiService.uploadChatImage(imageFile);
    } catch (e) {
      throw Exception('上传图片失败: $e');
    }
  }

  /// 发送语音消息
  Future<ChatMessageModel> sendAudioMessage({
    required String conversationId,
    required File audioFile,
    int? duration,
    double? latitude,
    double? longitude,
    String? address,
  }) async {
    try {
      // 1. 创建用户语音消息并立即添加到缓存
      final userMessage = ChatMessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        conversationId: conversationId,
        role: MessageRole.user,
        content: [MessageContent.audio(audioFile.path, duration: duration)],
        timestamp: DateTime.now(),
        metadata: {
          if (latitude != null && longitude != null) ...{
            'latitude': latitude,
            'longitude': longitude,
            if (address != null) 'address': address,
          },
        },
      );
      await _cacheService.addMessage(userMessage);

      // 2. 创建加载中的AI消息
      final loadingMessage = ChatMessageModel.loading(
        id: '${DateTime.now().millisecondsSinceEpoch + 1}',
        conversationId: conversationId,
      );
      await _cacheService.addMessage(loadingMessage);

      // 3. 调用API发送语音消息
      final aiResponse = await _apiService.sendAudioMessage(
        conversationId: conversationId,
        audioFile: audioFile,
        duration: duration,
        latitude: latitude,
        longitude: longitude,
        address: address,
      );

      // 4. 更新缓存中的AI回复
      await _cacheService.updateMessage(aiResponse);

      // 5. 删除加载中的消息
      await _cacheService.deleteMessage(loadingMessage.id);

      return aiResponse;
    } catch (e) {
      // 发送失败时，删除加载中的消息
      try {
        final loadingMessages = await _cacheService.getMessages(conversationId);
        for (final message in loadingMessages) {
          if (message.isLoading) {
            await _cacheService.deleteMessage(message.id);
          }
        }
      } catch (_) {
        // 忽略清理错误
      }

      throw Exception('发送语音消息失败: $e');
    }
  }

  /// 创建新对话并发送第一条消息 (流式响应)
  Stream<Map<String, dynamic>> createConversationWithMessageStream({
    required String content,
    required int doctorId,
    String? title,
    String messageType = 'text',
    double? latitude,
    double? longitude,
    String? address,
  }) {
    return _apiService.createConversationWithMessageStream(
      content: content,
      doctorId: doctorId,
      title: title,
      messageType: messageType,
      latitude: latitude,
      longitude: longitude,
      address: address,
    );
  }

  /// 创建新对话并发送第一条图片消息 (流式响应)
  Stream<Map<String, dynamic>> createConversationWithImageStream({
    required File imageFile,
    required int doctorId,
    String? description,
    String? title,
    double? latitude,
    double? longitude,
    String? address,
  }) {
    return _apiService.createConversationWithImageStream(
      imageFile: imageFile,
      doctorId: doctorId,
      description: description,
      title: title,
      latitude: latitude,
      longitude: longitude,
      address: address,
    );
  }

  /// 发送文本消息 (流式响应)
  Stream<ChatMessageModel> sendTextMessageStream({
    required String conversationId,
    required String content,
    required int doctorId,
    List<ChatMessageModel>? history,
    double? latitude,
    double? longitude,
    String? address,
  }) {
    return _apiService.sendTextMessageStream(
      conversationId: conversationId,
      content: content,
      doctorId: doctorId,
      history: history,
      latitude: latitude,
      longitude: longitude,
      address: address,
    );
  }

  /// 删除对话
  Future<void> deleteConversation(String conversationId) async {
    try {
      // 调用API删除对话
      await _apiService.deleteConversation(conversationId);

      // 从缓存中删除
      await _cacheService.deleteConversation(conversationId);
    } catch (e) {
      throw Exception('删除对话失败: $e');
    }
  }

  /// 删除所有对话
  Future<void> deleteAllConversations() async {
    try {
      // 调用API删除所有对话
      await _apiService.deleteAllConversations();

      // 清空所有缓存
      await _cacheService.clearCache();
    } catch (e) {
      throw Exception('删除所有对话失败: $e');
    }
  }

  /// 刷新对话列表（强制从服务器获取最新数据）
  Future<List<ConversationModel>> refreshConversations({
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      // 获取当前语言设置
      final currentLang = LanguageService().getCurrentLanguageCode();

      // 直接从网络获取最新数据
      final networkConversations = await _apiService.fetchConversations(
        page: page,
        pageSize: pageSize,
        lang: currentLang,
      );

      // 更新缓存
      await _cacheService.saveConversations(networkConversations);

      return networkConversations;
    } catch (e) {
      throw Exception('刷新对话列表失败: $e');
    }
  }

  /// 清空所有缓存
  Future<void> clearCache() async {
    await _cacheService.clearCache();
  }

  /// 检查对话是否存在
  Future<bool> conversationExists(String conversationId) async {
    return await _cacheService.conversationExists(conversationId);
  }

  /// 获取对话消息数量
  Future<int> getMessageCount(String conversationId) async {
    return await _cacheService.getMessageCount(conversationId);
  }

  /// 更新对话标题
  Future<void> updateConversationTitle(
    String conversationId,
    String title,
  ) async {
    try {
      // 调用API更新标题
      await _apiService.updateConversationTitle(conversationId, title);

      // 更新缓存中的对话标题
      final conversations = await _cacheService.getConversations();
      final updatedConversations = conversations.map((conversation) {
        if (conversation.id == conversationId) {
          return conversation.copyWith(title: title, updatedAt: DateTime.now());
        }
        return conversation;
      }).toList();

      await _cacheService.saveConversations(updatedConversations);
    } catch (e) {
      throw Exception('更新对话标题失败: $e');
    }
  }
}
