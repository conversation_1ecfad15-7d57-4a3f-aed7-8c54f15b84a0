import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:flutter/material.dart';
import '../config/api/api_config.dart';
import '../models/user_profile_model.dart';
import 'auth_service.dart';
import 'http_client_service.dart';

/// 用户资料API服务
class UserProfileService {
  // 单例模式
  static final UserProfileService _instance = UserProfileService._internal();
  factory UserProfileService() => _instance;
  UserProfileService._internal();

  final AuthService _authService = AuthService();
  final HttpClientService _httpClient = HttpClientService();

  /// 获取认证头
  Map<String, String> _getAuthHeaders() {
    final token = _authService.currentUser?.token;
    if (token == null) {
      throw Exception('用户未登录');
    }

    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  /// 获取用户个人资料
  Future<UserProfileModel> getUserProfile({BuildContext? context}) async {
    try {
      final response = await _httpClient.get(
        ApiConfig.getUserProfileUrl,
        context: context,
      );

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        return UserProfileModel.fromJson(responseData['data']);
      } else {
        // 其他错误都作为业务错误处理，不触发登出
        throw Exception(responseData['message'] ?? '获取用户资料失败');
      }
    } on FormatException {
      throw Exception('服务器响应格式错误');
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('网络请求失败，请检查网络连接');
    }
  }

  /// 更新用户个人资料
  Future<void> updateUserProfile({
    String? nickname,
    int? sex,
    String? birthday,
    String? avatar,
  }) async {
    try {
      final Map<String, dynamic> requestData = {};

      // 只添加有值的字段
      if (nickname != null) requestData['nickname'] = nickname;
      if (sex != null) requestData['sex'] = sex;
      if (birthday != null) requestData['birthday'] = birthday;
      if (avatar != null) requestData['avatar'] = avatar;

      final response = await http.post(
        Uri.parse(ApiConfig.updateUserProfileUrl),
        headers: _getAuthHeaders(),
        body: json.encode(requestData),
      );

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        return; // 成功
      } else if (response.statusCode == 401) {
        // 只有HTTP 401才是真正的认证错误
        throw Exception('登录已过期，请重新登录');
      } else {
        // 其他错误都作为业务错误处理，不触发登出
        throw Exception(responseData['message'] ?? '更新个人资料失败');
      }
    } on FormatException {
      throw Exception('服务器响应格式错误');
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('网络请求失败，请检查网络连接');
    }
  }

  /// 上传头像
  Future<String> uploadAvatar(File imageFile) async {
    try {
      final token = _authService.currentUser?.token;
      if (token == null) {
        throw Exception('用户未登录');
      }

      // 在调试模式下打印上传文件信息
      assert(() {
        // ignore: avoid_print
        print('准备上传头像文件: ${imageFile.path}');
        // ignore: avoid_print
        print('文件是否存在: ${imageFile.existsSync()}');
        if (imageFile.existsSync()) {
          // ignore: avoid_print
          print('文件大小: ${imageFile.lengthSync()} bytes');
        }
        return true;
      }());

      var request = http.MultipartRequest(
        'POST',
        Uri.parse(ApiConfig.uploadAvatarUrl),
      );

      // 添加认证头
      request.headers['Authorization'] = token;

      // 添加文件，确保保持原始文件名和扩展名，并设置正确的Content-Type
      final fileName = imageFile.path.split('/').last.toLowerCase();

      // 根据文件扩展名确定MIME类型
      String mimeType;
      if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
        mimeType = 'image/jpeg';
      } else if (fileName.endsWith('.png')) {
        mimeType = 'image/png';
      } else if (fileName.endsWith('.gif')) {
        mimeType = 'image/gif';
      } else if (fileName.endsWith('.webp')) {
        mimeType = 'image/webp';
      } else {
        // 如果无法识别扩展名，默认为jpeg，但记录警告
        mimeType = 'image/jpeg';
        assert(() {
          // ignore: avoid_print
          print('警告: 无法识别文件扩展名 $fileName，使用默认MIME类型 image/jpeg');
          return true;
        }());
      }

      // 在调试模式下打印MIME类型设置
      assert(() {
        // ignore: avoid_print
        print('文件名: $fileName');
        // ignore: avoid_print
        print('设置的MIME类型: $mimeType');
        return true;
      }());

      // 读取文件字节并创建MultipartFile，确保MIME类型正确
      final imageBytes = await imageFile.readAsBytes();
      final multipartFile = http.MultipartFile.fromBytes(
        'avatar',
        imageBytes,
        filename: fileName,
        contentType: MediaType.parse(mimeType),
      );

      request.files.add(multipartFile);

      // 在调试模式下打印请求信息
      assert(() {
        // ignore: avoid_print
        print('上传请求URL: ${request.url}');
        // ignore: avoid_print
        print('上传文件字段名: ${multipartFile.field}');
        // ignore: avoid_print
        print('上传文件名: ${multipartFile.filename}');
        // ignore: avoid_print
        print('实际Content-Type: ${multipartFile.contentType}');
        // ignore: avoid_print
        print('文件字节大小: ${imageBytes.length}');
        return true;
      }());

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      // 在调试模式下打印响应信息
      assert(() {
        // ignore: avoid_print
        print('上传响应状态码: ${response.statusCode}');
        // ignore: avoid_print
        print('上传响应内容: ${response.body}');
        return true;
      }());

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        // 返回新的头像文件名
        final avatarFileName = responseData['data']['avatar'];

        // 在调试模式下打印返回的头像文件名
        assert(() {
          // ignore: avoid_print
          print('服务器返回的头像文件名: $avatarFileName');
          return true;
        }());

        // 注意：这里不再自动处理本地存储
        // 因为前端在上传成功后会直接保存裁剪后的文件
        // 这样避免了重复下载和404错误

        return avatarFileName;
      } else if (response.statusCode == 401) {
        // 只有HTTP 401才是真正的认证错误
        throw Exception('登录已过期，请重新登录');
      } else {
        // 其他错误（包括code 400）都作为业务错误处理，不触发登出
        final errorMessage = responseData['message'] ?? '头像上传失败';
        throw Exception(errorMessage);
      }
    } on FormatException {
      throw Exception('服务器响应格式错误');
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('头像上传失败，请检查网络连接');
    }
  }

  /// 修改密码
  Future<void> changePassword({
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final requestData = {
        'new_password': newPassword,
        'confirm_password': confirmPassword,
      };

      // 在调试模式下打印请求数据
      assert(() {
        // ignore: avoid_print
        print('密码修改请求: ${requestData.keys.toList()}');
        return true;
      }());

      final response = await http.post(
        Uri.parse(ApiConfig.changePasswordUrl),
        headers: _getAuthHeaders(),
        body: json.encode(requestData),
      );

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        return; // 成功
      } else if (response.statusCode == 401) {
        // 只有HTTP 401才是真正的认证错误
        throw Exception('登录已过期，请重新登录');
      } else {
        // 其他错误都作为业务错误处理，不触发登出
        throw Exception(responseData['message'] ?? '密码修改失败');
      }
    } on FormatException {
      throw Exception('服务器响应格式错误');
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('网络请求失败，请检查网络连接');
    }
  }

  /// 获取头像完整URL
  String getAvatarUrl(String? avatar) {
    if (avatar == null || avatar.isEmpty) {
      return '';
    }

    // 如果已经是完整URL，直接返回
    if (avatar.startsWith('http')) {
      return avatar;
    }

    // 使用API配置中的头像URL构建方法
    final avatarUrl = ApiConfig.buildAvatarUrl(avatar);

    // 在调试模式下打印URL，帮助调试
    assert(() {
      // ignore: avoid_print
      print('Avatar字段: $avatar');
      // ignore: avoid_print
      print('构造的头像URL: $avatarUrl');
      return true;
    }());

    return avatarUrl;
  }
}
