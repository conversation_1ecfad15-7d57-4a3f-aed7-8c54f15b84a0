import 'package:flutter/foundation.dart';

/// 语言检测服务
/// 用于自动检测文本的语言类型
class LanguageDetectionService {
  /// 检测文本的语言
  /// 返回语言代码：zh（中文）、en（英文）、ug（维吾尔语）
  static String detectLanguage(String text) {
    if (text.trim().isEmpty) {
      return 'zh'; // 默认返回中文
    }

    // 清理文本，移除标点符号和空格
    final cleanText = text.replaceAll(RegExp(r'[^\p{L}\p{N}]', unicode: true), '');
    
    if (cleanText.isEmpty) {
      return 'zh'; // 默认返回中文
    }

    // 统计各种语言的字符数量
    int chineseCount = 0;
    int englishCount = 0;
    int uyghurCount = 0;
    int totalCount = cleanText.length;

    for (int i = 0; i < cleanText.length; i++) {
      final char = cleanText[i];
      final codeUnit = char.codeUnitAt(0);

      if (_isChineseCharacter(codeUnit)) {
        chineseCount++;
      } else if (_isEnglishCharacter(char)) {
        englishCount++;
      } else if (_isUyghurCharacter(codeUnit)) {
        uyghurCount++;
      }
    }

    if (kDebugMode) {
      print('🔍 语言检测结果:');
      print('- 文本长度: $totalCount');
      print('- 中文字符: $chineseCount (${(chineseCount / totalCount * 100).toStringAsFixed(1)}%)');
      print('- 英文字符: $englishCount (${(englishCount / totalCount * 100).toStringAsFixed(1)}%)');
      print('- 维吾尔语字符: $uyghurCount (${(uyghurCount / totalCount * 100).toStringAsFixed(1)}%)');
    }

    // 计算各语言的占比
    final chineseRatio = chineseCount / totalCount;
    final englishRatio = englishCount / totalCount;
    final uyghurRatio = uyghurCount / totalCount;

    // 设置阈值，如果某种语言的字符占比超过30%，则认为是该语言
    const threshold = 0.3;

    // 优先检测维吾尔语（因为比较特殊）
    if (uyghurRatio >= threshold) {
      if (kDebugMode) {
        print('🔍 检测结果: 维吾尔语 (ug)');
      }
      return 'ug';
    }

    // 检测中文
    if (chineseRatio >= threshold) {
      if (kDebugMode) {
        print('🔍 检测结果: 中文 (zh)');
      }
      return 'zh';
    }

    // 检测英文
    if (englishRatio >= threshold) {
      if (kDebugMode) {
        print('🔍 检测结果: 英文 (en)');
      }
      return 'en';
    }

    // 如果没有明显的主导语言，选择占比最高的
    if (chineseRatio >= englishRatio && chineseRatio >= uyghurRatio) {
      if (kDebugMode) {
        print('🔍 检测结果: 中文 (zh) - 占比最高');
      }
      return 'zh';
    } else if (englishRatio >= uyghurRatio) {
      if (kDebugMode) {
        print('🔍 检测结果: 英文 (en) - 占比最高');
      }
      return 'en';
    } else {
      if (kDebugMode) {
        print('🔍 检测结果: 维吾尔语 (ug) - 占比最高');
      }
      return 'ug';
    }
  }

  /// 检测是否为中文字符
  /// 包括汉字、中文标点符号等
  static bool _isChineseCharacter(int codeUnit) {
    return (codeUnit >= 0x4E00 && codeUnit <= 0x9FFF) ||      // CJK统一汉字
           (codeUnit >= 0x3400 && codeUnit <= 0x4DBF) ||      // CJK扩展A
           (codeUnit >= 0x20000 && codeUnit <= 0x2A6DF) ||    // CJK扩展B
           (codeUnit >= 0x2A700 && codeUnit <= 0x2B73F) ||    // CJK扩展C
           (codeUnit >= 0x2B740 && codeUnit <= 0x2B81F) ||    // CJK扩展D
           (codeUnit >= 0x2B820 && codeUnit <= 0x2CEAF) ||    // CJK扩展E
           (codeUnit >= 0xF900 && codeUnit <= 0xFAFF) ||      // CJK兼容汉字
           (codeUnit >= 0x2F800 && codeUnit <= 0x2FA1F);     // CJK兼容汉字补充
  }

  /// 检测是否为英文字符
  /// 包括大小写字母和数字
  static bool _isEnglishCharacter(String char) {
    return RegExp(r'^[a-zA-Z0-9]$').hasMatch(char);
  }

  /// 检测是否为维吾尔语字符
  /// 维吾尔语使用阿拉伯文字系统
  static bool _isUyghurCharacter(int codeUnit) {
    return (codeUnit >= 0x0600 && codeUnit <= 0x06FF) ||      // 阿拉伯文基本块
           (codeUnit >= 0x0750 && codeUnit <= 0x077F) ||      // 阿拉伯文补充
           (codeUnit >= 0x08A0 && codeUnit <= 0x08FF) ||      // 阿拉伯文扩展A
           (codeUnit >= 0xFB50 && codeUnit <= 0xFDFF) ||      // 阿拉伯文表现形式A
           (codeUnit >= 0xFE70 && codeUnit <= 0xFEFF);       // 阿拉伯文表现形式B
  }

  /// 获取语言的显示名称
  static String getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'zh':
        return '中文';
      case 'en':
        return 'English';
      case 'ug':
        return 'ئۇيغۇرچە';
      default:
        return '未知语言';
    }
  }

  /// 检测文本中是否包含多种语言
  /// 返回检测到的所有语言及其占比
  static Map<String, double> detectMultipleLanguages(String text) {
    if (text.trim().isEmpty) {
      return {'zh': 1.0};
    }

    // 清理文本，移除标点符号和空格
    final cleanText = text.replaceAll(RegExp(r'[^\p{L}\p{N}]', unicode: true), '');
    
    if (cleanText.isEmpty) {
      return {'zh': 1.0};
    }

    // 统计各种语言的字符数量
    int chineseCount = 0;
    int englishCount = 0;
    int uyghurCount = 0;
    int totalCount = cleanText.length;

    for (int i = 0; i < cleanText.length; i++) {
      final char = cleanText[i];
      final codeUnit = char.codeUnitAt(0);

      if (_isChineseCharacter(codeUnit)) {
        chineseCount++;
      } else if (_isEnglishCharacter(char)) {
        englishCount++;
      } else if (_isUyghurCharacter(codeUnit)) {
        uyghurCount++;
      }
    }

    // 计算占比
    final result = <String, double>{};
    
    if (chineseCount > 0) {
      result['zh'] = chineseCount / totalCount;
    }
    
    if (englishCount > 0) {
      result['en'] = englishCount / totalCount;
    }
    
    if (uyghurCount > 0) {
      result['ug'] = uyghurCount / totalCount;
    }

    // 如果没有检测到任何已知语言，默认返回中文
    if (result.isEmpty) {
      result['zh'] = 1.0;
    }

    return result;
  }

  /// 智能选择TTS语言
  /// 根据文本内容和用户偏好选择最合适的TTS语言
  static String selectTtsLanguage(String text, {String? userPreference}) {
    // 如果用户有明确偏好且文本中包含该语言，优先使用用户偏好
    if (userPreference != null) {
      final languages = detectMultipleLanguages(text);
      if (languages.containsKey(userPreference) && languages[userPreference]! > 0.1) {
        if (kDebugMode) {
          print('🔍 TTS语言选择: $userPreference (用户偏好)');
        }
        return userPreference;
      }
    }

    // 否则使用自动检测的主要语言
    final detectedLanguage = detectLanguage(text);
    if (kDebugMode) {
      print('🔍 TTS语言选择: $detectedLanguage (自动检测)');
    }
    return detectedLanguage;
  }
}
