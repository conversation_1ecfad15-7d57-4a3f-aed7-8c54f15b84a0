import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api/api_config.dart';
import '../models/doctor_product_model.dart';
import '../models/shipping_model.dart';
import '../services/auth_service.dart';
import '../services/language_service.dart';
import '../exceptions/shipping_exceptions.dart';

/// 物流服务
class ShippingService {
  /// 获取认证头部
  Map<String, String> _getAuthHeaders({bool needAuth = true}) {
    final headers = {'Content-Type': 'application/json'};

    if (needAuth) {
      final user = AuthService().currentUser;
      if (user?.token != null) {
        headers['Authorization'] = 'Bearer ${user!.token}';
      }
    }

    return headers;
  }

  /// 获取待发货订单列表（医生端）
  Future<List<ProductOrderModel>> getPendingShipmentOrders() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getDoctorPendingShipmentOrdersUrl),
        headers: _getAuthHeaders(),
      );

      print('ShippingService: 获取待发货订单响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        final orders = jsonList
            .map((json) => ProductOrderModel.fromJson(json))
            .toList();
        print('ShippingService: 成功获取 ${orders.length} 个待发货订单');
        return orders;
      } else if (response.statusCode == 403) {
        throw const InsufficientPermissionException();
      } else {
        throw const GetPendingShipmentOrdersFailedException();
      }
    } catch (e) {
      print('ShippingService: 获取待发货订单失败: $e');
      rethrow;
    }
  }

  /// 订单发货（医生端）
  Future<ShipOrderResponse> shipOrder({
    required int orderId,
    required ShipOrderRequest request,
  }) async {
    try {
      // 验证请求数据
      if (!request.isValid()) {
        throw const TrackingNumberEmptyException();
      }

      final response = await http.post(
        Uri.parse(ApiConfig.shipDoctorOrderUrl(orderId)),
        headers: _getAuthHeaders(),
        body: json.encode(request.toJson()),
      );

      print('ShippingService: 发货响应状态码: ${response.statusCode}');
      print('ShippingService: 发货响应内容: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        return ShipOrderResponse.fromJson(jsonData);
      } else if (response.statusCode == 403) {
        throw const InsufficientPermissionException();
      } else if (response.statusCode == 404) {
        throw const OrderNotExistException();
      } else if (response.statusCode == 400) {
        throw const ShipmentFailedCheckStatusException();
      } else {
        throw const ShipmentFailedException();
      }
    } catch (e) {
      print('ShippingService: 发货失败: $e');
      rethrow;
    }
  }

  /// 获取订单物流状态（医生端）
  Future<ShippingStatusResponse> getDoctorOrderShippingStatus(
    int orderId,
  ) async {
    try {
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getDoctorOrderShippingStatusUrl(orderId),
      ).replace(queryParameters: {'lang': languageCode});
      final response = await http.get(uri, headers: _getAuthHeaders());

      print('ShippingService: 获取医生端物流状态响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ShippingStatusResponse.fromJson(jsonData);
      } else if (response.statusCode == 403) {
        throw const InsufficientPermissionException();
      } else if (response.statusCode == 404) {
        throw const OrderNotExistException();
      } else {
        throw const GetShippingStatusFailedException();
      }
    } catch (e) {
      print('ShippingService: 获取医生端物流状态失败: $e');
      rethrow;
    }
  }

  /// 获取订单物流状态（用户端）
  Future<ShippingStatusResponse> getUserOrderShippingStatus(int orderId) async {
    try {
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getUserOrderShippingStatusUrl(orderId),
      ).replace(queryParameters: {'lang': languageCode});
      final response = await http.get(uri, headers: _getAuthHeaders());

      print('ShippingService: 获取用户端物流状态响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ShippingStatusResponse.fromJson(jsonData);
      } else if (response.statusCode == 404) {
        throw const OrderNotExistException();
      } else {
        throw const GetShippingStatusFailedException();
      }
    } catch (e) {
      print('ShippingService: 获取用户端物流状态失败: $e');
      rethrow;
    }
  }

  /// 获取我的已发货订单（用户端）
  Future<List<ProductOrderModel>> getMyShippedOrders() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getMyShippedOrdersUrl),
        headers: _getAuthHeaders(),
      );

      print('ShippingService: 获取已发货订单响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        final orders = jsonList
            .map((json) => ProductOrderModel.fromJson(json))
            .toList();
        print('ShippingService: 成功获取 ${orders.length} 个已发货订单');
        return orders;
      } else {
        throw const GetShippedOrdersFailedException();
      }
    } catch (e) {
      print('ShippingService: 获取已发货订单失败: $e');
      rethrow;
    }
  }

  /// 检查用户是否有医生权限
  bool checkDoctorPermission() {
    final user = AuthService().currentUser;
    return user != null && user.isDoctor == true && user.doctorId != null;
  }
}
