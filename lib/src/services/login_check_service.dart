import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';
import '../utils/auth_helper.dart';
import '../config/themes/app_colors.dart';
import '../config/routes/app_routes.dart';
import '../utils/theme_helper.dart';
import '../common/utils/font_util.dart';

/// 统一的登录检查和提示服务
class LoginCheckService {
  /// 检查用户是否已登录，如果未登录则显示登录提示弹窗
  ///
  /// [context] - 当前页面的BuildContext
  /// [featureName] - 功能名称，用于自定义提示文本，如 "拍照翻译"、"语音翻译" 等
  ///
  /// 返回值：
  /// - true: 用户已登录，可以继续执行功能
  /// - false: 用户未登录，已显示登录提示弹窗
  static Future<bool> ensureUserIsLoggedIn(
    BuildContext context, {
    String? featureName,
  }) async {
    print('LoginCheckService: 检查用户登录状态，功能: $featureName');

    // 检查登录状态
    final isLoggedIn = AuthHelper.isLoggedIn();
    print('LoginCheckService: AuthHelper.isLoggedIn() 返回: $isLoggedIn');

    if (isLoggedIn) {
      print('LoginCheckService: 用户已登录，允许访问功能');
      return true;
    }

    print('LoginCheckService: 用户未登录，显示登录提示弹窗');
    // 用户未登录，显示登录提示弹窗
    await _showLoginDialog(context, featureName: featureName);
    return false;
  }

  /// 显示统一的登录提示弹窗
  ///
  /// [context] - 当前页面的BuildContext
  /// [featureName] - 功能名称，用于自定义提示文本
  static Future<void> _showLoginDialog(
    BuildContext context, {
    String? featureName,
  }) async {
    final localizations = AppLocalizations.of(context);
    final promptText = featureName != null
        ? localizations.loginRequiredForFeature(featureName)
        : localizations.loginRequiredGeneral;

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isDarkMode ? const Color(0xFF1F1F1F) : Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 图标
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.blue.withAlpha(26)
                        : AppColors.primary.withAlpha(26),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.lock_outline,
                    color: isDarkMode ? Colors.blue : AppColors.primary,
                    size: 28,
                  ),
                ),
                const SizedBox(height: 16),

                // 提示文本
                Text(
                  promptText,
                  style: FontUtil.createBodyTextStyle(
                    text: promptText,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // 登录按钮
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context); // 关闭对话框
                    AppRoutes.navigateToLogin(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isDarkMode
                        ? Colors.blue
                        : AppColors.primary,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    minimumSize: const Size(double.infinity, 48),
                  ),
                  child: Text(
                    localizations.loginButtonText,
                    style: FontUtil.createButtonTextStyle(
                      text: localizations.loginButton,
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
