import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api/api_config.dart';
import '../models/user_product_model.dart';
import '../models/payment_model.dart';
import '../services/auth_service.dart';
import '../services/language_service.dart';

/// 用户端产品服务
class UserProductService {
  /// 获取认证头（可选，某些接口需要登录）
  Map<String, String> _getAuthHeaders({bool needAuth = false}) {
    final headers = {'Content-Type': 'application/json'};

    if (needAuth) {
      final token = AuthService().currentUser?.token;
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
        print('UserProductService: 添加认证头 Authorization = Bearer $token');
      } else {
        print('UserProductService: 警告 - 需要认证但token为空');
      }
    }

    return headers;
  }

  /// 获取产品列表（无需登录）
  Future<ProductListResponse> getProducts({
    String? category,
    int? doctorId,
    String? search,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final uri = Uri.parse(ApiConfig.getUserProductsUrl);
      final queryParams = <String, String>{};

      // 添加语言参数
      final languageCode = LanguageService().getCurrentLanguageCode();
      queryParams['lang'] = languageCode;

      if (category != null && category.isNotEmpty) {
        queryParams['category'] = category;
      }
      if (doctorId != null) {
        queryParams['doctor_id'] = doctorId.toString();
      }
      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      queryParams['page'] = page.toString();
      queryParams['page_size'] = pageSize.toString();

      final finalUri = uri.replace(queryParameters: queryParams);

      final response = await http.get(finalUri, headers: _getAuthHeaders());

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ProductListResponse.fromJson(jsonData);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取产品列表失败');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// 获取产品分类列表（无需登录）
  Future<List<ProductCategoryModel>> getCategories() async {
    try {
      // 添加语言参数
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getUserProductCategoriesUrl,
      ).replace(queryParameters: {'lang': languageCode});

      final response = await http.get(uri, headers: _getAuthHeaders());

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        return jsonData
            .map((item) => ProductCategoryModel.fromJson(item))
            .toList();
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取产品分类失败');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// 获取产品详情（无需登录）
  Future<UserProductModel> getProductDetail(int productId) async {
    try {
      // 添加语言参数
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getUserProductDetailUrl(productId),
      ).replace(queryParameters: {'lang': languageCode});

      final response = await http.get(uri, headers: _getAuthHeaders());

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return UserProductModel.fromJson(jsonData);
      } else if (response.statusCode == 404) {
        throw Exception('产品不存在');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取产品详情失败');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// 获取医生的产品列表（无需登录）
  Future<ProductListResponse> getDoctorProducts(int doctorId) async {
    try {
      // 添加语言参数
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getUserProductsByDoctorUrl(doctorId),
      ).replace(queryParameters: {'lang': languageCode});

      final response = await http.get(uri, headers: _getAuthHeaders());

      print(
        'UserProductService: getDoctorProducts 响应状态码: ${response.statusCode}',
      );
      print('UserProductService: getDoctorProducts 响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        // 检查返回的数据格式
        if (jsonData is List) {
          // 如果API直接返回产品列表，我们需要包装成ProductListResponse格式
          final products = jsonData
              .map((item) => UserProductModel.fromJson(item))
              .toList();

          // 创建默认的分页信息
          final pagination = PaginationModel(
            currentPage: 1,
            pageSize: products.length,
            total: products.length,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          );

          return ProductListResponse(
            products: products,
            pagination: pagination,
          );
        } else {
          // 如果API返回的是标准格式，直接解析
          return ProductListResponse.fromJson(jsonData);
        }
      } else if (response.statusCode == 404) {
        throw Exception('医生不存在');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取医生产品失败');
      }
    } catch (e) {
      print('UserProductService: getDoctorProducts 错误: $e');
      rethrow;
    }
  }

  /// 创建订单（需要登录）
  Future<ProductOrderModel> createOrder({
    required int productId,
    required int quantity,
    required String shippingAddress,
    required String shippingPhone,
    required String shippingName,
  }) async {
    try {
      final orderData = {
        'product_id': productId,
        'quantity': quantity,
        'shipping_address': shippingAddress,
        'shipping_phone': shippingPhone,
        'shipping_name': shippingName,
      };

      final response = await http.post(
        Uri.parse(ApiConfig.createUserProductOrderUrl),
        headers: _getAuthHeaders(needAuth: true),
        body: json.encode(orderData),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        return ProductOrderModel.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else if (response.statusCode == 400) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '订单创建失败');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '订单创建失败');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// 获取我的订单列表（需要登录）
  Future<List<ProductOrderModel>> getMyOrders({int? status}) async {
    try {
      final uri = Uri.parse(ApiConfig.getUserProductOrdersUrl);
      final queryParams = <String, String>{};

      // 添加语言参数
      final languageCode = LanguageService().getCurrentLanguageCode();
      queryParams['lang'] = languageCode;

      if (status != null) {
        queryParams['status'] = status.toString();
      }

      final finalUri = uri.replace(queryParameters: queryParams);
      final headers = _getAuthHeaders(needAuth: true);

      print('UserProductService: 获取我的订单');
      print('UserProductService: URL = $finalUri');
      print('UserProductService: Headers = $headers');

      final response = await http.get(finalUri, headers: headers);

      print('UserProductService: 响应状态码 = ${response.statusCode}');
      print('UserProductService: 响应内容 = ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        return jsonData
            .map((item) => ProductOrderModel.fromJson(item))
            .toList();
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取订单列表失败');
      }
    } catch (e) {
      print('UserProductService: 获取订单失败: $e');
      rethrow;
    }
  }

  /// 获取订单详情（需要登录）
  Future<ProductOrderModel> getOrderDetail(int orderId) async {
    try {
      // 添加语言参数
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getUserProductOrderDetailUrl(orderId),
      ).replace(queryParameters: {'lang': languageCode});

      final response = await http.get(
        uri,
        headers: _getAuthHeaders(needAuth: true),
      );

      print('UserProductService: 获取订单详情响应状态码 = ${response.statusCode}');
      print('UserProductService: 获取订单详情响应内容 = ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ProductOrderModel.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else if (response.statusCode == 404) {
        throw Exception('订单不存在');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取订单详情失败');
      }
    } catch (e) {
      print('UserProductService: 获取订单详情失败: $e');
      rethrow;
    }
  }

  /// 取消订单（需要登录）
  Future<void> cancelOrder(int orderId) async {
    try {
      final response = await http.put(
        Uri.parse(ApiConfig.cancelUserProductOrderUrl(orderId)),
        headers: _getAuthHeaders(needAuth: true),
      );

      if (response.statusCode == 200) {
        // 取消成功
        return;
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else if (response.statusCode == 400) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '订单取消失败');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '订单取消失败');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// 创建订单支付（需要登录）
  Future<ProductOrderPaymentOut> createOrderPayment({
    required int orderId,
    String payType = 'app',
    String? openid,
  }) async {
    try {
      final paymentData = {
        'pay_type': payType,
        if (openid != null) 'openid': openid,
      };

      print('UserProductService: 创建订单支付');
      print('UserProductService: orderId = $orderId');
      print('UserProductService: paymentData = $paymentData');

      final response = await http.post(
        Uri.parse(ApiConfig.createOrderPaymentUrl(orderId)),
        headers: _getAuthHeaders(needAuth: true),
        body: json.encode(paymentData),
      );

      print('UserProductService: 支付响应状态码 = ${response.statusCode}');
      print('UserProductService: 支付响应内容 = ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);

        // 检查响应中是否有错误
        if (jsonData['code'] != null && jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '创建支付失败');
        }

        return ProductOrderPaymentOut.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else if (response.statusCode == 400) {
        final errorData = json.decode(response.body);
        throw Exception(
          errorData['detail'] ?? errorData['message'] ?? '创建支付失败',
        );
      } else if (response.statusCode == 404) {
        throw Exception('订单不存在或无权访问');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          errorData['detail'] ?? errorData['message'] ?? '创建支付失败',
        );
      }
    } catch (e) {
      print('UserProductService: 创建支付失败: $e');
      rethrow;
    }
  }

  /// 查询订单支付状态（需要登录）
  Future<ProductOrderPaymentStatusOut> getOrderPaymentStatus(
    int orderId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getOrderPaymentStatusUrl(orderId)),
        headers: _getAuthHeaders(needAuth: true),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ProductOrderPaymentStatusOut.fromJson(jsonData);
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else if (response.statusCode == 404) {
        throw Exception('订单不存在或无权访问');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '查询支付状态失败');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// 同步订单支付状态（需要登录）
  Future<void> syncOrderPaymentStatus(int orderId) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConfig.syncOrderPaymentStatusUrl(orderId)),
        headers: _getAuthHeaders(needAuth: true),
      );

      if (response.statusCode == 200) {
        // 同步成功
        return;
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else if (response.statusCode == 404) {
        throw Exception('订单不存在或无权访问');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '同步支付状态失败');
      }
    } catch (e) {
      rethrow;
    }
  }
}
