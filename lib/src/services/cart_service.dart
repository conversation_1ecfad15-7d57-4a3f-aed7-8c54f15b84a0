import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api/api_config.dart';
import '../models/cart_model.dart';
import '../services/secure_storage_service.dart';
import 'language_service.dart';

/// 购物车服务
class CartService {
  static final CartService _instance = CartService._internal();
  factory CartService() => _instance;
  CartService._internal();

  final SecureStorageService _storageService = SecureStorageService();

  /// 获取认证头部
  Future<Map<String, String>> _getAuthHeaders({bool needAuth = true}) async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (needAuth) {
      final user = await _storageService.getSavedUserCredentials();
      if (user != null && user.token.isNotEmpty) {
        headers['Authorization'] = 'Bearer ${user.token}';
      }
    }

    return headers;
  }

  /// 添加商品到购物车
  Future<CartItemModel> addToCart({
    required int productId,
    required int quantity,
  }) async {
    try {
      final requestData = {'product_id': productId, 'quantity': quantity};

      final response = await http.post(
        Uri.parse(ApiConfig.addToCartUrl),
        headers: await _getAuthHeaders(needAuth: true),
        body: json.encode(requestData),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          return CartItemModel.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? '添加到购物车失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '添加到购物车失败');
      }
    } catch (e) {
      print('CartService: 添加到购物车失败: $e');
      rethrow;
    }
  }

  /// 获取购物车列表
  Future<CartListResponseModel> getCartList() async {
    try {
      // 获取当前语言
      final currentLanguage = LanguageService().getCurrentLanguageCode();
      final response = await http.get(
        Uri.parse('${ApiConfig.getCartListUrl}?lang=$currentLanguage'),
        headers: await _getAuthHeaders(needAuth: true),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          return CartListResponseModel.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? '获取购物车列表失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取购物车列表失败');
      }
    } catch (e) {
      print('CartService: 获取购物车列表失败: $e');
      rethrow;
    }
  }

  /// 更新购物车商品
  Future<CartItemModel> updateCartItem({
    required int cartId,
    int? quantity,
    bool? selected,
  }) async {
    try {
      final requestData = <String, dynamic>{};
      if (quantity != null) requestData['quantity'] = quantity;
      if (selected != null) requestData['selected'] = selected;

      final response = await http.put(
        Uri.parse(ApiConfig.updateCartItemUrl(cartId)),
        headers: await _getAuthHeaders(needAuth: true),
        body: json.encode(requestData),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          return CartItemModel.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? '更新购物车商品失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '更新购物车商品失败');
      }
    } catch (e) {
      print('CartService: 更新购物车商品失败: $e');
      rethrow;
    }
  }

  /// 删除购物车商品
  Future<void> deleteCartItem(int cartId) async {
    try {
      final response = await http.delete(
        Uri.parse(ApiConfig.deleteCartItemUrl(cartId)),
        headers: await _getAuthHeaders(needAuth: true),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '删除购物车商品失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '删除购物车商品失败');
      }
    } catch (e) {
      print('CartService: 删除购物车商品失败: $e');
      rethrow;
    }
  }

  /// 批量删除购物车商品
  Future<void> batchDeleteCartItems(List<int> cartIds) async {
    try {
      final requestData = {'cart_ids': cartIds};

      final response = await http.post(
        Uri.parse(ApiConfig.batchDeleteCartItemsUrl),
        headers: await _getAuthHeaders(needAuth: true),
        body: json.encode(requestData),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '批量删除购物车商品失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '批量删除购物车商品失败');
      }
    } catch (e) {
      print('CartService: 批量删除购物车商品失败: $e');
      rethrow;
    }
  }

  /// 选择/取消选择购物车商品
  Future<void> selectCartItems({
    required List<int> cartIds,
    required bool selected,
  }) async {
    try {
      final requestData = {'cart_ids': cartIds, 'selected': selected};

      print('CartService: 选择购物车商品请求');
      print('CartService: URL: ${ApiConfig.selectCartItemsUrl}');
      print('CartService: 请求数据: ${json.encode(requestData)}');
      print('CartService: cart_ids: $cartIds');
      print('CartService: selected: $selected');

      final response = await http.put(
        Uri.parse(ApiConfig.selectCartItemsUrl),
        headers: await _getAuthHeaders(needAuth: true),
        body: json.encode(requestData),
      );

      print('CartService: 响应状态码: ${response.statusCode}');
      print('CartService: 响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        print('CartService: 解析后的响应数据: $jsonData');
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '选择购物车商品失败');
        }
        print('CartService: 选择购物车商品成功');
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else {
        final errorData = json.decode(response.body);
        print('CartService: 错误响应数据: $errorData');
        throw Exception(errorData['message'] ?? '选择购物车商品失败');
      }
    } catch (e) {
      print('CartService: 选择购物车商品失败: $e');
      rethrow;
    }
  }

  /// 获取购物车商品数量
  Future<CartCountResponseModel> getCartCount() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getCartCountUrl),
        headers: await _getAuthHeaders(needAuth: true),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          return CartCountResponseModel.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? '获取购物车数量失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取购物车数量失败');
      }
    } catch (e) {
      print('CartService: 获取购物车数量失败: $e');
      rethrow;
    }
  }

  /// 购物车结算（批量下单）- 使用已保存地址
  Future<CartCheckoutResponseModel> checkoutWithAddress({
    required int addressId,
    List<int>? cartIds,
  }) async {
    try {
      final requestData = <String, dynamic>{'address_id': addressId};

      if (cartIds != null && cartIds.isNotEmpty) {
        requestData['cart_ids'] = cartIds;
      }

      final response = await http.post(
        Uri.parse(ApiConfig.cartCheckoutUrl),
        headers: await _getAuthHeaders(needAuth: true),
        body: json.encode(requestData),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          return CartCheckoutResponseModel.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? '购物车结算失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '购物车结算失败');
      }
    } catch (e) {
      print('CartService: 购物车结算失败: $e');
      rethrow;
    }
  }

  /// 购物车结算（批量下单）- 使用手动填写地址
  Future<CartCheckoutResponseModel> checkoutWithManualAddress({
    required String shippingName,
    required String shippingPhone,
    required String shippingAddress,
    List<int>? cartIds,
  }) async {
    try {
      final requestData = <String, dynamic>{
        'shipping_name': shippingName,
        'shipping_phone': shippingPhone,
        'shipping_address': shippingAddress,
      };

      if (cartIds != null && cartIds.isNotEmpty) {
        requestData['cart_ids'] = cartIds;
      }

      final response = await http.post(
        Uri.parse(ApiConfig.cartCheckoutUrl),
        headers: await _getAuthHeaders(needAuth: true),
        body: json.encode(requestData),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          return CartCheckoutResponseModel.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? '购物车结算失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '购物车结算失败');
      }
    } catch (e) {
      print('CartService: 购物车结算失败: $e');
      rethrow;
    }
  }
}
