import 'package:fluwx/fluwx.dart';
import '../models/payment_model.dart';

/// 微信支付服务
class WechatPaymentService {
  static final WechatPaymentService _instance =
      WechatPaymentService._internal();
  factory WechatPaymentService() => _instance;
  WechatPaymentService._internal();

  final Fluwx _fluwx = Fluwx();

  /// 初始化微信支付
  Future<bool> initialize() async {
    try {
      // 注册微信App
      await _fluwx.registerApi(
        appId: "wxcc948750d28ead51", // 从支付参数中获取的appId
        universalLink: "https://your.univerallink.com/link/", // iOS需要
      );

      // 检查微信是否安装
      final isInstalled = await _fluwx.isWeChatInstalled;
      print('WechatPaymentService: 微信是否安装 = $isInstalled');

      return isInstalled;
    } catch (e) {
      print('WechatPaymentService: 初始化失败: $e');
      return false;
    }
  }

  /// 发起微信支付
  Future<PaymentResult> pay(AppPayParams payParams) async {
    try {
      // 检查微信是否安装
      final isInstalled = await _fluwx.isWeChatInstalled;
      if (!isInstalled) {
        throw Exception('未安装微信，无法使用微信支付');
      }

      print('WechatPaymentService: 开始微信支付');
      print('WechatPaymentService: 支付参数 = ${payParams.toJson()}');

      // 检查支付参数是否完整
      if (payParams.appid.isEmpty ||
          payParams.partnerid.isEmpty ||
          payParams.prepayid.isEmpty ||
          payParams.timestamp.isEmpty) {
        throw Exception('支付参数不完整，无法发起支付');
      }

      // 创建支付对象
      final payment = Payment(
        appId: payParams.appid,
        partnerId: payParams.partnerid,
        prepayId: payParams.prepayid,
        packageValue: payParams.package,
        nonceStr: payParams.nonceStr,
        timestamp: int.parse(payParams.timestamp),
        sign: payParams.paySign,
      );

      // 发起支付
      final success = await _fluwx.pay(which: payment);

      print('WechatPaymentService: 支付调用结果 = $success');

      if (!success) {
        return PaymentResult.failed;
      }

      // 等待支付结果
      return await _waitForPaymentResult();
    } catch (e) {
      print('WechatPaymentService: 支付失败: $e');
      return PaymentResult.failed;
    }
  }

  /// 等待支付结果
  Future<PaymentResult> _waitForPaymentResult() async {
    try {
      // 暂时返回成功，实际项目中需要监听支付结果
      // TODO: 实现真正的支付结果监听
      await Future.delayed(const Duration(seconds: 2));
      return PaymentResult.success;
    } catch (e) {
      print('WechatPaymentService: 等待支付结果超时或出错: $e');
      return PaymentResult.unknown;
    }
  }
}
