import 'payment_service.dart';
import 'wechat_pay_service.dart';
import 'doctor_service.dart';
import 'health_profile_cache_service.dart';
import 'user_profile_cache_service.dart';
import 'doctor_product_cache_service.dart';
import 'user_product_cache_service.dart';
import 'cart_cache_service.dart';
import 'user_interaction_cache_service.dart';
import 'order_cache_service.dart';
import '../models/doctor_model.dart';

/// 应用初始化服务
class AppInitializationService {
  static final AppInitializationService _instance =
      AppInitializationService._internal();
  factory AppInitializationService() => _instance;
  AppInitializationService._internal();

  final PaymentService _paymentService = PaymentService();
  bool _isInitialized = false;

  /// 初始化应用
  Future<bool> initializeApp() async {
    if (_isInitialized) {
      return true;
    }

    try {
      // 初始化微信支付SDK
      await _initializeWeChatPay();

      // 预加载医生数据（强制刷新，确保获取最新的语言数据）
      await _preloadData();

      _isInitialized = true;
      return true;
    } catch (e) {
      print('AppInitializationService: 应用初始化失败: $e');
      return false;
    }
  }

  /// 预加载数据
  Future<void> _preloadData() async {
    try {
      // 并行预加载医生数据和健康档案数据
      await Future.wait([
        // 预加载医生数据
        DoctorService()
            .getDoctors(forceRefresh: true)
            .timeout(
              const Duration(seconds: 8), // 8秒超时，比单个请求超时稍短
              onTimeout: () {
                print('AppInitializationService: 医生数据预加载超时，使用缓存数据');
                return <DoctorModel>[]; // 返回空列表，不阻塞启动
              },
            ),

        // 预加载健康档案数据
        HealthProfileCacheService().preloadHealthProfile().timeout(
          const Duration(seconds: 5), // 5秒超时
          onTimeout: () {
            print('AppInitializationService: 健康档案预加载超时');
            return;
          },
        ),

        // 预加载用户资料数据
        UserProfileCacheService().preloadUserProfile().timeout(
          const Duration(seconds: 5), // 5秒超时
          onTimeout: () {
            print('AppInitializationService: 用户资料预加载超时');
            return;
          },
        ),

        // 预加载产品数据
        DoctorProductCacheService().preloadProductData().timeout(
          const Duration(seconds: 5), // 5秒超时
          onTimeout: () {
            print('AppInitializationService: 产品数据预加载超时');
            return;
          },
        ),

        // 预加载用户端产品数据
        UserProductCacheService().preloadProductData().timeout(
          const Duration(seconds: 5), // 5秒超时
          onTimeout: () {
            print('AppInitializationService: 用户端产品数据预加载超时');
            return;
          },
        ),

        // 预加载购物车数据
        CartCacheService().preloadCartData().timeout(
          const Duration(seconds: 3), // 3秒超时
          onTimeout: () {
            print('AppInitializationService: 购物车数据预加载超时');
            return;
          },
        ),

        // 预加载用户互动数据
        UserInteractionCacheService().preloadInteractionData().timeout(
          const Duration(seconds: 3), // 3秒超时
          onTimeout: () {
            print('AppInitializationService: 用户互动数据预加载超时');
            return;
          },
        ),

        // 预加载订单数据
        OrderCacheService().preloadOrderData().timeout(
          const Duration(seconds: 3), // 3秒超时
          onTimeout: () {
            print('AppInitializationService: 订单数据预加载超时');
            return;
          },
        ),
      ]);

      print('AppInitializationService: 数据预加载完成');
    } catch (e) {
      print('AppInitializationService: 数据预加载失败: $e');
      // 预加载失败不应该阻止应用启动，应用会在后续使用缓存数据或重试
    }
  }

  /// 初始化微信支付
  Future<void> _initializeWeChatPay() async {
    try {
      // 使用配置中的AppID和Universal Link
      final success = await _paymentService.initWeChatPay(
        appId: WeChatPayConfig.appId,
        universalLink: WeChatPayConfig.universalLink,
      );

      if (success) {
        print('AppInitializationService: 微信支付SDK初始化成功');
      } else {
        print('AppInitializationService: 微信支付SDK初始化失败');
      }
    } catch (e) {
      print('AppInitializationService: 微信支付SDK初始化异常: $e');
      // 微信支付初始化失败不应该阻止应用启动
    }
  }

  /// 检查初始化状态
  bool get isInitialized => _isInitialized;

  /// 重新初始化
  Future<bool> reinitialize() async {
    _isInitialized = false;
    return await initializeApp();
  }
}
