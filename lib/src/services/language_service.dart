import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../generated/l10n/app_localizations.dart';
import 'doctor_service.dart';

/// 语言设置服务 - 管理用户语言偏好和系统语言检测
class LanguageService extends ChangeNotifier {
  static const String _languageKey = 'app_language';

  // 私有构造函数
  LanguageService._internal();

  // 单例实例
  static final LanguageService _instance = LanguageService._internal();

  // 工厂构造函数，返回单例
  factory LanguageService() => _instance;

  Locale? _currentLocale;
  bool _isInitialized = false;

  /// 获取当前语言设置
  Locale get currentLocale => _currentLocale ?? const Locale('zh', 'CN');

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 支持的语言列表
  static const List<Locale> supportedLocales = [
    Locale('zh', 'CN'), // 中文简体
    Locale('en', 'US'), // 英语
    Locale('ug', 'CN'), // 维吾尔语
  ];

  /// 语言代码到显示名称的映射
  static const Map<String, String> languageNames = {
    'zh': '中文',
    'en': 'English',
    'ug': 'ئۇيغۇرچە',
  };

  /// 初始化语言设置
  Future<void> initialize() async {
    if (_isInitialized) return;

    final prefs = await SharedPreferences.getInstance();
    final savedLanguageCode = prefs.getString(_languageKey);

    if (savedLanguageCode != null) {
      // 使用用户保存的语言偏好
      _currentLocale = _parseLocale(savedLanguageCode);
    } else {
      // 首次启动，根据系统语言自动设置
      _currentLocale = _detectSystemLanguage();
      // 保存检测到的语言作为用户隐式偏好
      await _saveLanguage(_currentLocale!.languageCode);
    }

    _isInitialized = true;
    notifyListeners();
  }

  /// 解析语言代码为Locale对象
  Locale _parseLocale(String languageCode) {
    switch (languageCode) {
      case 'zh':
        return const Locale('zh', 'CN');
      case 'en':
        return const Locale('en', 'US');
      case 'ug':
        return const Locale('ug', 'CN');
      default:
        return const Locale('zh', 'CN'); // 默认中文
    }
  }

  /// 检测系统语言
  Locale _detectSystemLanguage() {
    final systemLocale = PlatformDispatcher.instance.locale;
    final systemLanguageCode = systemLocale.languageCode.toLowerCase();

    // 检查系统语言是否在支持列表中
    for (final supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == systemLanguageCode) {
        return supportedLocale;
      }
    }

    // 特殊处理中文变体
    if (systemLanguageCode == 'zh' ||
        systemLanguageCode.startsWith('zh_') ||
        systemLanguageCode.contains('cn') ||
        systemLanguageCode.contains('hans') ||
        systemLanguageCode.contains('hant')) {
      return const Locale('zh', 'CN');
    }

    // 如果系统语言不支持，默认使用中文
    return const Locale('zh', 'CN');
  }

  /// 设置应用语言
  Future<void> setLanguage(String languageCode) async {
    final newLocale = _parseLocale(languageCode);

    if (newLocale == _currentLocale) return;

    _currentLocale = newLocale;
    await _saveLanguage(languageCode);

    // 清除语言相关的缓存数据
    await _clearLanguageRelatedCaches();

    notifyListeners();
  }

  /// 保存语言设置到本地存储
  Future<void> _saveLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
  }

  /// 清除语言相关的缓存数据
  Future<void> _clearLanguageRelatedCaches() async {
    try {
      // 清除医生服务的缓存
      await DoctorService().clearCache();

      // 清除其他语言相关的缓存
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('cached_products');
      await prefs.remove('cached_products_time');

      print('LanguageService: 已清除语言相关的缓存数据');
    } catch (e) {
      print('LanguageService: 清除缓存失败: $e');
    }
  }

  /// 获取当前语言的显示名称
  String getCurrentLanguageName() {
    return languageNames[_currentLocale?.languageCode] ?? '中文';
  }

  /// 获取当前语言代码（用于API请求）
  String getCurrentLanguageCode() {
    return _currentLocale?.languageCode ?? 'zh';
  }

  /// 获取语言选项列表（用于设置页面显示）
  List<LanguageOption> getLanguageOptions(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return [
      LanguageOption(
        code: 'zh',
        name: l10n.languageOptionChineseSimplified,
        locale: const Locale('zh', 'CN'),
      ),
      LanguageOption(
        code: 'en',
        name: l10n.languageOptionEnglish,
        locale: const Locale('en', 'US'),
      ),
      // 维吾尔语选项
      LanguageOption(
        code: 'ug',
        name: l10n.languageOptionUyghur,
        locale: const Locale('ug', 'CN'),
        isEnabled: true, // 现在已支持
      ),
    ];
  }

  /// 重置为系统语言
  Future<void> resetToSystemLanguage() async {
    final systemLocale = _detectSystemLanguage();
    await setLanguage(systemLocale.languageCode);
  }
}

/// 语言选项数据类
class LanguageOption {
  final String code;
  final String name;
  final Locale locale;
  final bool isEnabled;

  const LanguageOption({
    required this.code,
    required this.name,
    required this.locale,
    this.isEnabled = true,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LanguageOption && other.code == code;
  }

  @override
  int get hashCode => code.hashCode;
}
