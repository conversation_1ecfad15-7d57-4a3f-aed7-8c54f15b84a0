import 'package:flutter/material.dart';
import 'language_service.dart';

/// 文本方向管理服务 - 根据语言自动管理RTL/LTR布局方向
class TextDirectionService extends ChangeNotifier {
  // 私有构造函数
  TextDirectionService._internal() {
    // 监听语言变化，自动更新文本方向
    LanguageService().addListener(_onLanguageChanged);
  }

  // 单例实例
  static final TextDirectionService _instance = TextDirectionService._internal();

  // 工厂构造函数，返回单例
  factory TextDirectionService() => _instance;

  TextDirection? _currentDirection;
  bool _isInitialized = false;

  /// 获取当前文本方向
  TextDirection get currentDirection => _currentDirection ?? TextDirection.ltr;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 是否为RTL方向
  bool get isRTL => currentDirection == TextDirection.rtl;

  /// 是否为LTR方向
  bool get isLTR => currentDirection == TextDirection.ltr;

  /// RTL语言列表
  static const Set<String> rtlLanguages = {
    'ar', // 阿拉伯语
    'he', // 希伯来语
    'fa', // 波斯语
    'ur', // 乌尔都语
    'ug', // 维吾尔语
    'ku', // 库尔德语
    'ps', // 普什图语
    'sd', // 信德语
  };

  /// 初始化文本方向设置
  Future<void> initialize() async {
    if (_isInitialized) return;

    // 根据当前语言自动检测方向
    _currentDirection = _detectDirectionFromLanguage();
    _isInitialized = true;
    notifyListeners();
  }

  /// 根据当前语言检测文本方向
  TextDirection _detectDirectionFromLanguage() {
    final currentLanguage = LanguageService().getCurrentLanguageCode();
    return rtlLanguages.contains(currentLanguage) 
        ? TextDirection.rtl 
        : TextDirection.ltr;
  }

  /// 语言变化监听器
  void _onLanguageChanged() {
    final newDirection = _detectDirectionFromLanguage();
    if (newDirection != _currentDirection) {
      _currentDirection = newDirection;
      notifyListeners();
    }
  }

  /// 设置文本方向（仅供内部使用）
  void setTextDirection(TextDirection direction) {
    if (direction == _currentDirection) return;
    _currentDirection = direction;
    notifyListeners();
  }

  @override
  void dispose() {
    LanguageService().removeListener(_onLanguageChanged);
    super.dispose();
  }
}
