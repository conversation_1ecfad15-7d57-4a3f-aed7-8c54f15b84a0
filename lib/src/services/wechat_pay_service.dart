// 暂时注释掉fluwx导入，避免API错误
// import 'package:fluwx/fluwx.dart';
import '../models/payment_model.dart';

/// 微信支付服务类
///
/// 注意：当前为模拟实现，实际使用时需要配置正确的微信SDK
class WeChatPayService {
  static final WeChatPayService _instance = WeChatPayService._internal();
  factory WeChatPayService() => _instance;
  WeChatPayService._internal();

  /// 初始化微信SDK
  ///
  /// [appId] 微信开放平台申请的应用AppID
  /// [universalLink] iOS平台的Universal Link（iOS必需）
  Future<bool> initWeChatSDK({
    required String appId,
    String? universalLink,
  }) async {
    try {
      // 模拟初始化过程
      print('WeChatPayService: 模拟初始化微信SDK');
      print('WeChatPayService: AppID: $appId');

      // 在调试模式下总是返回成功
      if (WeChatPayConfig.isDebug) {
        print('WeChatPayService: 调试模式，初始化成功');
        return true;
      }

      // 实际使用时，这里应该调用真正的微信SDK初始化
      // await registerWxApi(appId: appId, universalLink: universalLink);
      // final isInstalled = await isWeChatInstalled;
      // if (!isInstalled) {
      //   throw WeChatPayException(
      //     type: WeChatPayErrorType.wechatNotInstalled,
      //     message: '未安装微信应用',
      //   );
      // }

      return true;
    } catch (e) {
      print('WeChatPayService: 初始化微信SDK失败: $e');
      if (WeChatPayConfig.isDebug) {
        print('WeChatPayService: 调试模式，忽略初始化错误');
        return true;
      }
      rethrow;
    }
  }

  /// 发起APP支付
  ///
  /// [paymentParams] 从后端获取的支付参数
  ///
  /// 返回支付结果
  Future<PaymentResult> payWithApp(AppPayParams paymentParams) async {
    try {
      print('WeChatPayService: 模拟发起微信支付请求');
      print('WeChatPayService: appId=${paymentParams.appid}');
      print('WeChatPayService: partnerId=${paymentParams.partnerid}');
      print('WeChatPayService: prepayId=${paymentParams.prepayid}');

      // 在调试模式下模拟支付成功
      if (WeChatPayConfig.isDebug) {
        print('WeChatPayService: 调试模式，模拟支付成功');
        await Future.delayed(const Duration(seconds: 1)); // 模拟支付延迟
        return PaymentResult.success;
      }

      // 实际使用时，这里应该调用真正的微信支付
      // final isInstalled = await isWeChatInstalled;
      // if (!isInstalled) {
      //   throw WeChatPayException(
      //     type: WeChatPayErrorType.wechatNotInstalled,
      //     message: '未安装微信应用',
      //   );
      // }

      // final payReq = WeChatPaymentOrder(
      //   appId: paymentParams.appid,
      //   partnerId: paymentParams.partnerid,
      //   prepayId: paymentParams.prepayid,
      //   packageValue: paymentParams.package,
      //   nonceStr: paymentParams.nonceStr,
      //   timeStamp: int.parse(paymentParams.timestamp),
      //   sign: paymentParams.paySign,
      // );

      // final response = await payWithWeChat(order: payReq);
      // return _parsePaymentResponse(response);

      // 默认返回成功（实际使用时应该根据真实结果返回）
      return PaymentResult.success;
    } catch (e) {
      print('WeChatPayService: 微信支付失败: $e');
      if (e is WeChatPayException) {
        rethrow;
      }
      throw WeChatPayException(
        type: WeChatPayErrorType.paymentFailed,
        message: '支付失败: $e',
      );
    }
  }

  /// 检查微信是否支持支付
  Future<bool> isWeChatPaySupported() async {
    try {
      // 在调试模式下总是返回true
      if (WeChatPayConfig.isDebug) {
        print('WeChatPayService: 调试模式，支持支付');
        return true;
      }

      // 实际使用时，这里应该检查真正的微信安装和版本
      // final isInstalled = await isWeChatInstalled;
      // if (!isInstalled) {
      //   return false;
      // }
      // final isSupportApi = await isWeChatSupportApi;
      // return isSupportApi;

      return true;
    } catch (e) {
      print('WeChatPayService: 检查微信支付支持失败: $e');
      return false;
    }
  }

  /// 获取微信版本信息
  Future<String?> getWeChatVersion() async {
    try {
      // 模拟返回版本信息
      if (WeChatPayConfig.isDebug) {
        return '8.0.0'; // 模拟版本号
      }
      return null;
    } catch (e) {
      print('WeChatPayService: 获取微信版本失败: $e');
      return null;
    }
  }

  /// 打开微信应用
  Future<bool> openWeChat() async {
    try {
      // 在调试模式下模拟成功
      if (WeChatPayConfig.isDebug) {
        print('WeChatPayService: 调试模式，模拟打开微信');
        return true;
      }

      // 实际使用时，这里应该调用真正的打开微信方法
      // return await openWeChatApp();

      return false;
    } catch (e) {
      print('WeChatPayService: 打开微信失败: $e');
      return false;
    }
  }
}

/// 微信支付错误类型
enum WeChatPayErrorType {
  wechatNotInstalled, // 微信未安装
  wechatVersionTooLow, // 微信版本过低
  paymentFailed, // 支付失败
  networkError, // 网络错误
  invalidParams, // 参数错误
  unknown, // 未知错误
}

/// 微信支付异常类
class WeChatPayException implements Exception {
  final WeChatPayErrorType type;
  final String message;
  final int? errCode;

  const WeChatPayException({
    required this.type,
    required this.message,
    this.errCode,
  });

  @override
  String toString() {
    return 'WeChatPayException: $message (type: $type, errCode: $errCode)';
  }

  /// 获取用户友好的错误信息
  String get userFriendlyMessage {
    switch (type) {
      case WeChatPayErrorType.wechatNotInstalled:
        return '请先安装微信应用';
      case WeChatPayErrorType.wechatVersionTooLow:
        return '微信版本过低，请升级微信';
      case WeChatPayErrorType.paymentFailed:
        return '支付失败，请重试';
      case WeChatPayErrorType.networkError:
        return '网络连接失败，请检查网络';
      case WeChatPayErrorType.invalidParams:
        return '支付参数错误';
      case WeChatPayErrorType.unknown:
        return '支付出现未知错误';
    }
  }
}

/// 微信支付配置类
class WeChatPayConfig {
  /// 微信开放平台AppID
  static const String appId = 'your_wechat_app_id'; // 需要替换为实际的AppID

  /// iOS Universal Link（iOS必需）
  static const String universalLink =
      'https://your-domain.com/'; // 需要替换为实际的Universal Link

  /// 是否为调试模式
  static const bool isDebug = true;
}

/// 微信支付工具类
class WeChatPayUtils {
  /// 格式化支付金额（分转元）
  static String formatAmount(int amountInCents) {
    return (amountInCents / 100).toStringAsFixed(2);
  }

  /// 解析支付金额（元转分）
  static int parseAmount(double amountInYuan) {
    return (amountInYuan * 100).round();
  }

  /// 生成随机字符串
  static String generateNonceStr([int length = 32]) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    var result = '';

    for (int i = 0; i < length; i++) {
      result += chars[(random + i) % chars.length];
    }

    return result;
  }

  /// 获取当前时间戳
  static String getCurrentTimestamp() {
    return (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
  }

  /// 验证支付参数
  static bool validatePaymentParams(AppPayParams params) {
    return params.appid.isNotEmpty &&
        params.partnerid.isNotEmpty &&
        params.prepayid.isNotEmpty &&
        params.package.isNotEmpty &&
        params.nonceStr.isNotEmpty &&
        params.timestamp.isNotEmpty &&
        params.paySign.isNotEmpty;
  }
}
