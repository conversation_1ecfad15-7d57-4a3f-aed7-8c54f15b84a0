import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';

/// 音频录制服务 - 处理录音、权限管理、格式转换
class AudioRecordingService {
  static final AudioRecordingService _instance =
      AudioRecordingService._internal();
  factory AudioRecordingService() => _instance;
  AudioRecordingService._internal();

  final AudioRecorder _recorder = AudioRecorder();

  bool _isRecording = false;
  String? _currentRecordingPath;
  DateTime? _recordingStartTime;

  // 实时音量监控
  double _currentAudioLevel = 0.0;
  StreamSubscription<Amplitude>? _amplitudeSubscription;
  StreamController<double>? _amplitudeController;
  Timer? _mockTimer;

  /// 当前是否正在录音
  bool get isRecording => _isRecording;

  /// 当前录音时长（毫秒）
  int get recordingDuration {
    if (_recordingStartTime == null) return 0;
    return DateTime.now().difference(_recordingStartTime!).inMilliseconds;
  }

  /// 当前音量等级 (0.0 - 1.0)
  double get currentAudioLevel {
    // 确保返回最新的音量值
    return _currentAudioLevel.clamp(0.0, 1.0);
  }

  /// 预检查麦克风权限（不触发权限请求）
  Future<bool> checkPermissionStatus() async {
    try {
      final micStatus = await Permission.microphone.status;
      final speechStatus = await Permission.speech.status;

      debugPrint('🎤 权限状态检查:');
      debugPrint('- 麦克风权限: $micStatus');
      debugPrint('- 语音识别权限: $speechStatus');

      return micStatus.isGranted && speechStatus.isGranted;
    } catch (e) {
      debugPrint('权限状态检查失败: $e');
      return false;
    }
  }

  /// 请求麦克风和语音识别权限
  Future<bool> requestPermission() async {
    try {
      debugPrint('🎤 请求录音和语音识别权限...');

      // 同时请求麦克风和语音识别权限
      final Map<Permission, PermissionStatus> statuses = await [
        Permission.microphone,
        Permission.speech,
      ].request();

      final micGranted = statuses[Permission.microphone]?.isGranted ?? false;
      final speechGranted = statuses[Permission.speech]?.isGranted ?? false;

      debugPrint('🎤 权限请求结果:');
      debugPrint('- 麦克风权限: ${statuses[Permission.microphone]}');
      debugPrint('- 语音识别权限: ${statuses[Permission.speech]}');

      return micGranted && speechGranted;
    } catch (e) {
      debugPrint('权限请求失败: $e');
      return false;
    }
  }

  /// 检查并请求麦克风权限
  Future<bool> checkAndRequestPermission() async {
    try {
      var status = await Permission.microphone.status;

      if (status.isDenied) {
        status = await Permission.microphone.request();
      }

      return status.isGranted;
    } catch (e) {
      debugPrint('权限检查失败: $e');
      return false;
    }
  }

  /// 开始录音 - 极速响应版本，直接尝试录音
  Future<bool> startRecording([String? language]) async {
    try {
      // 如果已经在录音，先停止
      if (_isRecording) {
        await stopRecording();
      }

      // 立即设置录音状态和开始时间
      _isRecording = true;
      _recordingStartTime = DateTime.now();
      _currentAudioLevel = 0.0;

      // 生成录音文件路径
      _currentRecordingPath = await _generateRecordingPath();

      // 统一的高质量录音配置
      const config = RecordConfig(
        encoder: AudioEncoder.aacLc, // AAC-LC编码，生成m4a格式
        bitRate: 64000, // 高质量码率，提升识别准确性
        sampleRate: 16000, // 高质量采样率，确保音频清晰
        numChannels: 1, // 单声道
        autoGain: true, // 启用自动增益
        echoCancel: true, // 启用回声消除
        noiseSuppress: true, // 启用噪声抑制
      );

      // 直接尝试开始录音，不做任何预检查
      await _recorder.start(config, path: _currentRecordingPath!);
      debugPrint('🎤 录音极速启动成功: $_currentRecordingPath');

      // 立即开始监听音量变化
      await startAmplitudeMonitoring();

      return true;
    } catch (e) {
      debugPrint('🎤 录音启动失败: $e');
      // 录音启动失败，重置状态
      _isRecording = false;
      _currentRecordingPath = null;
      _recordingStartTime = null;
      return false;
    }
  }

  /// 开始音量监听
  Future<void> startAmplitudeMonitoring() async {
    try {
      // 先停止之前的监听
      await _stopAmplitudeMonitoring();

      if (await _recorder.isRecording()) {
        debugPrint('🎤 开始音量监听');
        _amplitudeController = StreamController<double>.broadcast();

        // 创建新的订阅 - 使用不同的监听方式避免Stream冲突
        _amplitudeSubscription = _recorder
            .onAmplitudeChanged(const Duration(milliseconds: 50))
            .listen(
              (amplitude) {
                if (_amplitudeController != null &&
                    !_amplitudeController!.isClosed) {
                  final normalizedLevel = _dbToNormalized(amplitude.current);
                  _currentAudioLevel = normalizedLevel; // 立即更新当前音量值
                  _amplitudeController!.add(normalizedLevel);
                  debugPrint('🎤 实时音量: ${normalizedLevel.toStringAsFixed(3)}');
                }
              },
              onError: (error) {
                debugPrint('音量监听错误: $error');
                _startMockAmplitudeMonitoring();
              },
            );
      } else {
        debugPrint('🎤 录音未开始，启动模拟音量监听');
        _startMockAmplitudeMonitoring();
      }
    } catch (e) {
      debugPrint('音量监听启动失败: $e');
      debugPrint('🎤 启动模拟音量监听');
      _startMockAmplitudeMonitoring();
    }
  }

  /// 停止音量监听
  Future<void> _stopAmplitudeMonitoring() async {
    await _amplitudeSubscription?.cancel();
    _amplitudeSubscription = null;

    if (!(_amplitudeController?.isClosed ?? true)) {
      await _amplitudeController?.close();
    }
    _amplitudeController = null;

    _mockTimer?.cancel();
    _mockTimer = null;
  }

  /// 模拟音量监听（备用方案）
  void _startMockAmplitudeMonitoring() {
    debugPrint('🎤 启动模拟音量监听');
    Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      // 生成更真实的模拟音量数据
      final random = DateTime.now().millisecondsSinceEpoch % 1000;

      // 90% 的时间保持真正的低音量状态（接近静音）
      if (random % 10 < 9) {
        _currentAudioLevel = 0.01 + (random % 50) / 10000.0; // 0.01-0.015
      } else {
        // 10% 的时间有明显的音量变化
        final base = 0.3 + (random % 400) / 1000.0; // 0.3-0.7 基础音量
        final spike = random % 100 < 20 ? (random % 300) / 1000.0 : 0; // 偶尔音量峰值
        _currentAudioLevel = (base + spike).clamp(0.0, 1.0);
      }

      if (DateTime.now().millisecondsSinceEpoch % 1000 < 100) {
        debugPrint('🎤 模拟音量: $_currentAudioLevel');
      }
    });
  }

  /// 停止录音并返回录音文件路径
  Future<String?> stopRecording() async {
    try {
      if (!_isRecording) {
        debugPrint('当前没有在录音');
        return null;
      }

      // 停止音量监听
      await _stopAmplitudeMonitoring();

      // 停止录音
      final path = await _recorder.stop();

      _isRecording = false;
      _recordingStartTime = null;
      _currentAudioLevel = 0.0;

      if (path != null && await File(path).exists()) {
        final file = File(path);
        final fileSize = await file.length();
        debugPrint('🎤 录音完成详细信息:');
        debugPrint('- 文件路径: $path');
        debugPrint(
          '- 文件大小: ${fileSize}bytes (${(fileSize / 1024).toStringAsFixed(2)}KB)',
        );
        debugPrint('- 文件存在: ${await file.exists()}');

        if (fileSize == 0) {
          debugPrint('❌ 录音文件为空，删除文件');
          try {
            await file.delete();
          } catch (e) {
            debugPrint('删除空文件失败: $e');
          }
          return null;
        }

        return path;
      } else {
        debugPrint('❌ 录音文件不存在或路径为空: $path');
        return null;
      }
    } catch (e) {
      debugPrint('停止录音失败: $e');
      _isRecording = false;
      _recordingStartTime = null;
      _currentAudioLevel = 0.0;
      return null;
    }
  }

  /// 取消录音
  Future<void> cancelRecording() async {
    try {
      if (_isRecording) {
        // 停止音量监听
        await _stopAmplitudeMonitoring();

        await _recorder.stop();

        // 删除录音文件
        if (_currentRecordingPath != null) {
          final file = File(_currentRecordingPath!);
          if (await file.exists()) {
            await file.delete();
            debugPrint('已删除录音文件: $_currentRecordingPath');
          }
        }
      }

      _isRecording = false;
      _currentRecordingPath = null;
      _recordingStartTime = null;
      _currentAudioLevel = 0.0;

      debugPrint('录音已取消');
    } catch (e) {
      debugPrint('取消录音失败: $e');
      _isRecording = false;
      _currentRecordingPath = null;
      _recordingStartTime = null;
      _currentAudioLevel = 0.0;
    }
  }

  /// 生成录音文件路径
  Future<String> _generateRecordingPath() async {
    final directory = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${directory.path}/recording_$timestamp.m4a';
  }

  /// 将录音文件转换为m4a格式（使用AAC编码，现在已经是目标格式）
  /// 实际项目中可能需要使用ffmpeg或其他工具进行格式转换
  Future<File?> convertToMp3(String audioPath) async {
    try {
      final file = File(audioPath);
      if (!await file.exists()) {
        debugPrint('音频文件不存在: $audioPath');
        return null;
      }

      // m4a格式（AAC编码）在大多数情况下可以直接使用
      // 如果需要严格的MP3格式，需要集成ffmpeg或使用云端转换服务
      debugPrint('音频文件准备就绪: $audioPath (m4a格式)');
      return file;
    } catch (e) {
      debugPrint('音频格式转换失败: $e');
      return null;
    }
  }

  /// 释放资源
  void dispose() {
    _stopAmplitudeMonitoring();
    _recorder.dispose();
  }

  double _dbToNormalized(double db) {
    // 将分贝值转换为标准化的0.0-1.0范围
    // 通常语音分贝值在 -60dB 到 0dB 之间
    if (db == double.negativeInfinity || !db.isFinite) {
      return 0.0;
    }

    // 将 -60dB 到 0dB 映射到 0.0 到 1.0
    final normalizedLevel = ((db + 60) / 60).clamp(0.0, 1.0);
    return normalizedLevel;
  }
}
