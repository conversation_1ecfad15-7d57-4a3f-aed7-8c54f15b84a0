import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'auth_service.dart';
import '../config/routes/app_routes.dart';

/// 统一的HTTP客户端服务 - 处理token过期自动退出登录
class HttpClientService {
  static final HttpClientService _instance = HttpClientService._internal();
  factory HttpClientService() => _instance;
  HttpClientService._internal();

  final AuthService _authService = AuthService();

  // 用于避免重复弹出登录提示
  static bool _isHandlingTokenExpiry = false;

  /// 发送POST请求（带token过期检查）
  Future<http.Response> post(
    String url, {
    Map<String, String>? headers,
    Object? body,
    bool requireAuth = true,
    BuildContext? context,
    Duration? timeout,
  }) async {
    final finalHeaders = await _buildHeaders(headers, requireAuth);

    final response = await http
        .post(Uri.parse(url), headers: finalHeaders, body: body)
        .timeout(timeout ?? const Duration(seconds: 10));

    // 检查token过期
    if (response.statusCode == 401 && requireAuth) {
      await HttpClientService._handleTokenExpiryStatic(context);
      throw Exception('登录已过期，请重新登录');
    }

    return response;
  }

  /// 发送PUT请求（带token过期检查）
  Future<http.Response> put(
    String url, {
    Map<String, String>? headers,
    Object? body,
    bool requireAuth = true,
    BuildContext? context,
    Duration? timeout,
  }) async {
    final finalHeaders = await _buildHeaders(headers, requireAuth);

    final response = await http
        .put(Uri.parse(url), headers: finalHeaders, body: body)
        .timeout(timeout ?? const Duration(seconds: 10));

    // 检查token过期
    if (response.statusCode == 401 && requireAuth) {
      await HttpClientService._handleTokenExpiryStatic(context);
      throw Exception('登录已过期，请重新登录');
    }

    return response;
  }

  /// 发送DELETE请求（带token过期检查）
  Future<http.Response> delete(
    String url, {
    Map<String, String>? headers,
    bool requireAuth = true,
    BuildContext? context,
    Duration? timeout,
  }) async {
    final finalHeaders = await _buildHeaders(headers, requireAuth);

    final response = await http
        .delete(Uri.parse(url), headers: finalHeaders)
        .timeout(timeout ?? const Duration(seconds: 10));

    // 检查token过期
    if (response.statusCode == 401 && requireAuth) {
      await HttpClientService._handleTokenExpiryStatic(context);
      throw Exception('登录已过期，请重新登录');
    }

    return response;
  }

  /// 静态方法：检查响应是否为token过期，如果是则自动处理
  static Future<void> checkTokenExpiry(
    http.Response response, {
    BuildContext? context,
  }) async {
    if (response.statusCode == 401) {
      await _handleTokenExpiryStatic(context);
      throw Exception('登录已过期，请重新登录');
    }
  }

  /// 静态方法：处理token过期
  static Future<void> _handleTokenExpiryStatic(BuildContext? context) async {
    // 避免重复处理
    if (_isHandlingTokenExpiry) {
      return;
    }

    _isHandlingTokenExpiry = true;

    try {
      // 自动退出登录
      await AuthService().logout();

      // 如果有context，尝试跳转到登录页面
      if (context != null && context.mounted) {
        // 延迟一下再跳转
        await Future.delayed(const Duration(milliseconds: 500));

        // 跳转到登录页面
        if (context.mounted) {
          AppRoutes.navigateToLogin(context);
        }
      }
    } catch (e) {
      // 处理错误但不抛出，避免影响主流程
    } finally {
      // 重置标志，允许下次处理
      Future.delayed(const Duration(seconds: 3), () {
        _isHandlingTokenExpiry = false;
      });
    }
  }

  /// 发送GET请求（带token过期检查）
  Future<http.Response> get(
    String url, {
    Map<String, String>? headers,
    bool requireAuth = true,
    BuildContext? context,
    Duration? timeout,
  }) async {
    final finalHeaders = await _buildHeaders(headers, requireAuth);

    final response = await http
        .get(Uri.parse(url), headers: finalHeaders)
        .timeout(timeout ?? const Duration(seconds: 10));

    // 检查token过期
    if (response.statusCode == 401 && requireAuth) {
      await HttpClientService._handleTokenExpiryStatic(context);
      throw Exception('登录已过期，请重新登录');
    }

    return response;
  }

  /// 发送multipart请求（用于文件上传）
  Future<http.StreamedResponse> multipartRequest(
    String method,
    String url, {
    Map<String, String>? headers,
    Map<String, String>? fields,
    List<http.MultipartFile>? files,
    bool requireAuth = true,
    BuildContext? context,
  }) async {
    final request = http.MultipartRequest(method, Uri.parse(url));

    // 添加认证头
    if (requireAuth) {
      final token = _authService.currentUser?.token;
      if (token != null && token.isNotEmpty) {
        request.headers['Authorization'] = 'Bearer $token';
      }
    }

    // 添加其他头部
    if (headers != null) {
      request.headers.addAll(headers);
    }

    // 添加字段
    if (fields != null) {
      request.fields.addAll(fields);
    }

    // 添加文件
    if (files != null) {
      request.files.addAll(files);
    }

    try {
      final streamedResponse = await request.send();

      // 检查401错误
      if (streamedResponse.statusCode == 401 && requireAuth) {
        await HttpClientService._handleTokenExpiryStatic(context);
        throw Exception('登录已过期，请重新登录');
      }

      return streamedResponse;
    } catch (e) {
      rethrow;
    }
  }

  /// 构建请求头
  Future<Map<String, String>> _buildHeaders(
    Map<String, String>? customHeaders,
    bool requireAuth,
  ) async {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // 添加认证头
    if (requireAuth) {
      final token = _authService.currentUser?.token;
      if (token != null && token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    // 添加自定义头部
    if (customHeaders != null) {
      headers.addAll(customHeaders);
    }

    return headers;
  }

  /// 获取认证头（兼容现有代码）
  Map<String, String> getAuthHeaders() {
    final token = _authService.currentUser?.token;
    if (token == null || token.isEmpty) {
      throw Exception('用户未登录，请先登录');
    }

    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  /// 获取认证头（不包含Content-Type，用于multipart请求）
  Map<String, String> getAuthHeadersWithoutContentType() {
    final token = _authService.currentUser?.token;
    if (token == null || token.isEmpty) {
      throw Exception('用户未登录，请先登录');
    }

    return {'Authorization': 'Bearer $token'};
  }
}
