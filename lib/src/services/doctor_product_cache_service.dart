import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/doctor_product_model.dart';
import 'doctor_product_service.dart';

/// 医生产品缓存管理服务 - 提供产品数据的缓存机制和后台加载
class DoctorProductCacheService extends ChangeNotifier {
  static final DoctorProductCacheService _instance =
      DoctorProductCacheService._internal();

  /// 获取医生产品缓存服务单例实例
  factory DoctorProductCacheService() => _instance;

  DoctorProductCacheService._internal();

  /// 缓存键名
  static const String _productsCacheKey = 'doctor_products_cache';
  static const String _productsCacheTimeKey = 'doctor_products_cache_time';
  static const String _statisticsCacheKey = 'doctor_statistics_cache';
  static const String _statisticsCacheTimeKey = 'doctor_statistics_cache_time';

  /// 缓存有效期（小时）
  static const int _cacheValidityHours = 6; // 产品数据变化较频繁，6小时过期

  /// 服务实例
  final DoctorProductService _productService = DoctorProductService();

  /// 内存缓存
  List<DoctorProductModel>? _cachedProducts;
  ProductStatisticsModel? _cachedStatistics;
  DateTime? _lastProductsFetchTime;
  DateTime? _lastStatisticsFetchTime;
  bool _isLoadingProducts = false;
  bool _isLoadingStatistics = false;
  String? _lastError;

  /// 获取当前缓存的产品列表
  List<DoctorProductModel>? get cachedProducts => _cachedProducts;

  /// 获取当前缓存的统计信息
  ProductStatisticsModel? get cachedStatistics => _cachedStatistics;

  /// 同步获取缓存的产品列表（如果有的话）
  List<DoctorProductModel> getCachedProductsSync({int? status}) {
    if (_cachedProducts == null) return [];
    return _filterProductsByStatus(_cachedProducts!, status);
  }

  /// 同步获取缓存的统计信息（如果有的话）
  ProductStatisticsModel getCachedStatisticsSync() {
    return _cachedStatistics ?? const ProductStatisticsModel();
  }

  /// 是否正在加载产品
  bool get isLoadingProducts => _isLoadingProducts;

  /// 是否正在加载统计信息
  bool get isLoadingStatistics => _isLoadingStatistics;

  /// 最后的错误信息
  String? get lastError => _lastError;

  /// 是否有缓存的产品数据
  bool get hasCachedProducts =>
      _cachedProducts != null && _cachedProducts!.isNotEmpty;

  /// 是否有缓存的统计数据
  bool get hasCachedStatistics => _cachedStatistics != null;

  /// 初始化缓存服务
  Future<void> initialize() async {
    await Future.wait([
      _loadProductsFromLocalCache(),
      _loadStatisticsFromLocalCache(),
    ]);
  }

  /// 获取产品列表（优先使用缓存，后台刷新）
  Future<List<DoctorProductModel>> getProducts({
    bool forceRefresh = false,
    int? status,
  }) async {
    // 如果用户未登录或没有医生权限，返回空列表
    if (!_productService.checkDoctorPermission()) {
      await clearCache();
      return [];
    }

    // 如果不是强制刷新且有内存缓存，直接返回
    if (!forceRefresh && _cachedProducts != null) {
      // 后台检查是否需要刷新
      _backgroundRefreshProducts(status: status);
      return _filterProductsByStatus(_cachedProducts!, status);
    }

    // 如果没有缓存或需要强制刷新，立即加载
    return await _loadProducts(status: status);
  }

  /// 立即加载产品列表
  Future<List<DoctorProductModel>> _loadProducts({int? status}) async {
    if (_isLoadingProducts) return _cachedProducts ?? [];

    _isLoadingProducts = true;
    _lastError = null;
    notifyListeners();

    try {
      final products = await _productService.getProducts(status: status);

      // 更新缓存
      _cachedProducts = products;
      _lastProductsFetchTime = DateTime.now();

      // 保存到本地缓存
      await _saveProductsToLocalCache(products);

      _isLoadingProducts = false;
      notifyListeners();

      return _filterProductsByStatus(products, status);
    } catch (e) {
      _lastError = e.toString();
      _isLoadingProducts = false;
      notifyListeners();

      // 如果加载失败，返回现有缓存
      return _filterProductsByStatus(_cachedProducts ?? [], status);
    }
  }

  /// 获取统计信息（优先使用缓存，后台刷新）
  Future<ProductStatisticsModel> getStatistics({
    bool forceRefresh = false,
  }) async {
    // 如果用户未登录或没有医生权限，返回默认统计
    if (!_productService.checkDoctorPermission()) {
      return const ProductStatisticsModel();
    }

    // 如果不是强制刷新且有内存缓存，直接返回
    if (!forceRefresh && _cachedStatistics != null) {
      // 后台检查是否需要刷新
      _backgroundRefreshStatistics();
      return _cachedStatistics!;
    }

    // 如果没有缓存或需要强制刷新，立即加载
    return await _loadStatistics();
  }

  /// 立即加载统计信息
  Future<ProductStatisticsModel> _loadStatistics() async {
    if (_isLoadingStatistics) {
      return _cachedStatistics ?? const ProductStatisticsModel();
    }

    _isLoadingStatistics = true;
    _lastError = null;
    notifyListeners();

    try {
      final statistics = await _productService.getStatistics();

      // 更新缓存
      _cachedStatistics = statistics;
      _lastStatisticsFetchTime = DateTime.now();

      // 保存到本地缓存
      await _saveStatisticsToLocalCache(statistics);

      _isLoadingStatistics = false;
      notifyListeners();

      return statistics;
    } catch (e) {
      _lastError = e.toString();
      _isLoadingStatistics = false;
      notifyListeners();

      // 如果加载失败，返回现有缓存或默认值
      return _cachedStatistics ?? const ProductStatisticsModel();
    }
  }

  /// 后台刷新产品列表（不阻塞UI）
  void _backgroundRefreshProducts({int? status}) {
    // 检查是否需要刷新
    if (!_shouldRefreshProductsCache()) return;

    // 异步后台刷新
    Future.microtask(() async {
      try {
        final products = await _productService.getProducts(status: status);

        // 只有数据真正变化时才更新缓存和通知UI
        if (_hasProductsDataChanged(products)) {
          _cachedProducts = products;
          _lastProductsFetchTime = DateTime.now();
          await _saveProductsToLocalCache(products);
          notifyListeners();
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
        _lastError = e.toString();
      }
    });
  }

  /// 后台刷新统计信息（不阻塞UI）
  void _backgroundRefreshStatistics() {
    // 检查是否需要刷新
    if (!_shouldRefreshStatisticsCache()) return;

    // 异步后台刷新
    Future.microtask(() async {
      try {
        final statistics = await _productService.getStatistics();

        // 只有数据真正变化时才更新缓存和通知UI
        if (_hasStatisticsDataChanged(statistics)) {
          _cachedStatistics = statistics;
          _lastStatisticsFetchTime = DateTime.now();
          await _saveStatisticsToLocalCache(statistics);
          notifyListeners();
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
        _lastError = e.toString();
      }
    });
  }

  /// 检查是否需要刷新产品缓存
  bool _shouldRefreshProductsCache() {
    if (_lastProductsFetchTime == null) return true;

    final now = DateTime.now();
    final difference = now.difference(_lastProductsFetchTime!);
    return difference.inHours >= _cacheValidityHours;
  }

  /// 检查是否需要刷新统计缓存
  bool _shouldRefreshStatisticsCache() {
    if (_lastStatisticsFetchTime == null) return true;

    final now = DateTime.now();
    final difference = now.difference(_lastStatisticsFetchTime!);
    return difference.inHours >= _cacheValidityHours;
  }

  /// 检查产品数据是否发生变化
  bool _hasProductsDataChanged(List<DoctorProductModel>? newProducts) {
    if (_cachedProducts == null && newProducts == null) return false;
    if (_cachedProducts == null || newProducts == null) return true;
    if (_cachedProducts!.length != newProducts.length) return true;

    // 比较产品ID和更新时间
    for (int i = 0; i < _cachedProducts!.length; i++) {
      if (_cachedProducts![i].id != newProducts[i].id ||
          _cachedProducts![i].updatedAt != newProducts[i].updatedAt) {
        return true;
      }
    }

    return false;
  }

  /// 检查统计数据是否发生变化
  bool _hasStatisticsDataChanged(ProductStatisticsModel? newStatistics) {
    if (_cachedStatistics == null && newStatistics == null) return false;
    if (_cachedStatistics == null || newStatistics == null) return true;

    // 比较关键统计字段
    return _cachedStatistics!.totalProducts != newStatistics.totalProducts ||
        _cachedStatistics!.pendingReview != newStatistics.pendingReview ||
        _cachedStatistics!.approvedProducts != newStatistics.approvedProducts ||
        _cachedStatistics!.rejectedProducts != newStatistics.rejectedProducts;
  }

  /// 根据状态过滤产品
  List<DoctorProductModel> _filterProductsByStatus(
    List<DoctorProductModel> products,
    int? status,
  ) {
    if (status == null) return products;
    return products.where((product) => product.status == status).toList();
  }

  /// 保存产品到本地缓存
  Future<void> _saveProductsToLocalCache(
    List<DoctorProductModel> products,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(products.map((p) => p.toJson()).toList());
      await prefs.setString(_productsCacheKey, jsonString);
      await prefs.setString(
        _productsCacheTimeKey,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 保存统计到本地缓存
  Future<void> _saveStatisticsToLocalCache(
    ProductStatisticsModel statistics,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(statistics.toJson());
      await prefs.setString(_statisticsCacheKey, jsonString);
      await prefs.setString(
        _statisticsCacheTimeKey,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 从本地缓存加载产品
  Future<void> _loadProductsFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_productsCacheKey);
      final cacheTimeString = prefs.getString(_productsCacheTimeKey);

      if (cachedJson != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        // 检查缓存是否过期
        if (now.difference(cacheTime).inHours < _cacheValidityHours) {
          final List<dynamic> jsonList = json.decode(cachedJson);
          _cachedProducts = jsonList
              .map((json) => DoctorProductModel.fromJson(json))
              .toList();
          _lastProductsFetchTime = cacheTime;
        } else {
          // 缓存过期，清除
          await _clearProductsLocalCache();
        }
      }
    } catch (e) {
      // 加载失败时清除缓存
      await _clearProductsLocalCache();
    }
  }

  /// 从本地缓存加载统计
  Future<void> _loadStatisticsFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_statisticsCacheKey);
      final cacheTimeString = prefs.getString(_statisticsCacheTimeKey);

      if (cachedJson != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        // 检查缓存是否过期
        if (now.difference(cacheTime).inHours < _cacheValidityHours) {
          final statisticsJson = json.decode(cachedJson);
          _cachedStatistics = ProductStatisticsModel.fromJson(statisticsJson);
          _lastStatisticsFetchTime = cacheTime;
        } else {
          // 缓存过期，清除
          await _clearStatisticsLocalCache();
        }
      }
    } catch (e) {
      // 加载失败时清除缓存
      await _clearStatisticsLocalCache();
    }
  }

  /// 清除产品本地缓存
  Future<void> _clearProductsLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_productsCacheKey);
      await prefs.remove(_productsCacheTimeKey);
    } catch (e) {
      // 清除失败时静默处理
    }
  }

  /// 清除统计本地缓存
  Future<void> _clearStatisticsLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_statisticsCacheKey);
      await prefs.remove(_statisticsCacheTimeKey);
    } catch (e) {
      // 清除失败时静默处理
    }
  }

  /// 清除所有缓存
  Future<void> clearCache() async {
    _cachedProducts = null;
    _cachedStatistics = null;
    _lastProductsFetchTime = null;
    _lastStatisticsFetchTime = null;
    _lastError = null;
    _isLoadingProducts = false;
    _isLoadingStatistics = false;

    await Future.wait([
      _clearProductsLocalCache(),
      _clearStatisticsLocalCache(),
    ]);

    notifyListeners();
  }

  /// 预加载产品数据（应用启动时调用）
  Future<void> preloadProductData() async {
    if (_productService.checkDoctorPermission()) {
      // 先加载本地缓存
      await Future.wait([
        _loadProductsFromLocalCache(),
        _loadStatisticsFromLocalCache(),
      ]);

      // 后台刷新最新数据
      _backgroundRefreshProducts();
      _backgroundRefreshStatistics();
    }
  }

  /// 获取产品数量统计
  Map<String, int> getProductCountByStatus() {
    if (_cachedProducts == null) return {};

    final counts = <String, int>{};
    for (final product in _cachedProducts!) {
      final statusKey = product.status.toString();
      counts[statusKey] = (counts[statusKey] ?? 0) + 1;
    }

    return counts;
  }

  /// 检查是否有完整的产品数据
  bool hasCompleteProductData() {
    return _cachedProducts != null && _cachedStatistics != null;
  }
}
