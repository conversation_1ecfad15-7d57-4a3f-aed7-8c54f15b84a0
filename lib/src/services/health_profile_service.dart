import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api/api_config.dart';
import '../models/health_profile_model.dart';
import 'auth_service.dart';

/// 健康档案API服务
class HealthProfileService {
  // 单例模式
  static final HealthProfileService _instance = HealthProfileService._internal();
  factory HealthProfileService() => _instance;
  HealthProfileService._internal();

  final AuthService _authService = AuthService();

  /// 获取认证头
  Map<String, String> _getAuthHeaders() {
    final token = _authService.currentUser?.token;
    if (token == null) {
      throw Exception('用户未登录');
    }

    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  /// 获取健康档案
  Future<HealthProfileModel?> getHealthProfile() async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/applet/v1/health-profile'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return HealthProfileModel.fromJson(responseData);
      } else if (response.statusCode == 404) {
        // 健康档案不存在，返回null
        return null;
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        final responseData = json.decode(response.body);
        throw Exception(responseData['detail'] ?? '获取健康档案失败');
      }
    } on FormatException {
      throw Exception('服务器响应格式错误');
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('网络请求失败，请检查网络连接');
    }
  }

  /// 创建健康档案
  Future<HealthProfileModel> createHealthProfile(HealthProfileModel profile) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/applet/v1/health-profile'),
        headers: _getAuthHeaders(),
        body: json.encode(profile.toJson()),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return HealthProfileModel.fromJson(responseData);
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else if (response.statusCode == 422) {
        final responseData = json.decode(response.body);
        throw Exception('数据验证失败: ${responseData['detail']}');
      } else {
        final responseData = json.decode(response.body);
        throw Exception(responseData['detail'] ?? '创建健康档案失败');
      }
    } on FormatException {
      throw Exception('服务器响应格式错误');
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('网络请求失败，请检查网络连接');
    }
  }

  /// 更新健康档案（完整更新）
  Future<HealthProfileModel> updateHealthProfile(HealthProfileModel profile) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/applet/v1/health-profile'),
        headers: _getAuthHeaders(),
        body: json.encode(profile.toJson()),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return HealthProfileModel.fromJson(responseData);
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else if (response.statusCode == 422) {
        final responseData = json.decode(response.body);
        throw Exception('数据验证失败: ${responseData['detail']}');
      } else {
        final responseData = json.decode(response.body);
        throw Exception(responseData['detail'] ?? '更新健康档案失败');
      }
    } on FormatException {
      throw Exception('服务器响应格式错误');
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('网络请求失败，请检查网络连接');
    }
  }

  /// 部分更新健康档案
  Future<HealthProfileModel> patchHealthProfile(Map<String, dynamic> updates) async {
    try {
      final response = await http.patch(
        Uri.parse('${ApiConfig.baseUrl}/applet/v1/health-profile'),
        headers: _getAuthHeaders(),
        body: json.encode(updates),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return HealthProfileModel.fromJson(responseData);
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else if (response.statusCode == 404) {
        throw Exception('健康档案不存在');
      } else if (response.statusCode == 422) {
        final responseData = json.decode(response.body);
        throw Exception('数据验证失败: ${responseData['detail']}');
      } else {
        final responseData = json.decode(response.body);
        throw Exception(responseData['detail'] ?? '更新健康档案失败');
      }
    } on FormatException {
      throw Exception('服务器响应格式错误');
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('网络请求失败，请检查网络连接');
    }
  }

  /// 删除健康档案
  Future<void> deleteHealthProfile() async {
    try {
      final response = await http.delete(
        Uri.parse('${ApiConfig.baseUrl}/applet/v1/health-profile'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        return; // 成功删除
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else if (response.statusCode == 404) {
        throw Exception('健康档案不存在');
      } else {
        final responseData = json.decode(response.body);
        throw Exception(responseData['detail'] ?? '删除健康档案失败');
      }
    } on FormatException {
      throw Exception('服务器响应格式错误');
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('网络请求失败，请检查网络连接');
    }
  }

  /// 创建或更新健康档案（智能判断）
  Future<HealthProfileModel> saveHealthProfile(HealthProfileModel profile) async {
    // 先尝试获取现有档案
    final existingProfile = await getHealthProfile();
    
    if (existingProfile == null) {
      // 不存在，创建新档案
      return await createHealthProfile(profile);
    } else {
      // 存在，更新档案
      return await updateHealthProfile(profile);
    }
  }
}
