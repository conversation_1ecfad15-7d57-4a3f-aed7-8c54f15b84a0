import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_product_model.dart';
import 'user_product_service.dart';

/// 用户端产品缓存管理服务 - 提供产品数据的缓存机制和后台加载
class UserProductCacheService extends ChangeNotifier {
  static final UserProductCacheService _instance =
      UserProductCacheService._internal();

  /// 获取用户端产品缓存服务单例实例
  factory UserProductCacheService() => _instance;

  UserProductCacheService._internal();

  /// 缓存键名
  static const String _productsCacheKey = 'user_products_cache';
  static const String _productsCacheTimeKey = 'user_products_cache_time';
  static const String _categoriesCacheKey = 'user_categories_cache';
  static const String _categoriesCacheTimeKey = 'user_categories_cache_time';

  /// 缓存有效期（小时）
  static const int _cacheValidityHours = 4; // 用户端产品数据4小时过期

  /// 服务实例
  final UserProductService _productService = UserProductService();

  /// 内存缓存
  List<UserProductModel>? _cachedProducts;
  List<ProductCategoryModel>? _cachedCategories;
  DateTime? _lastProductsFetchTime;
  DateTime? _lastCategoriesFetchTime;
  bool _isLoadingProducts = false;
  bool _isLoadingCategories = false;
  String? _lastError;

  /// 获取当前缓存的产品列表
  List<UserProductModel>? get cachedProducts => _cachedProducts;

  /// 获取当前缓存的分类列表
  List<ProductCategoryModel>? get cachedCategories => _cachedCategories;

  /// 是否正在加载产品
  bool get isLoadingProducts => _isLoadingProducts;

  /// 是否正在加载分类
  bool get isLoadingCategories => _isLoadingCategories;

  /// 最后的错误信息
  String? get lastError => _lastError;

  /// 是否有缓存的产品数据
  bool get hasCachedProducts =>
      _cachedProducts != null && _cachedProducts!.isNotEmpty;

  /// 是否有缓存的分类数据
  bool get hasCachedCategories =>
      _cachedCategories != null && _cachedCategories!.isNotEmpty;

  /// 初始化缓存服务
  Future<void> initialize() async {
    await Future.wait([
      _loadProductsFromLocalCache(),
      _loadCategoriesFromLocalCache(),
    ]);
  }

  /// 同步获取缓存的产品列表（如果有的话）
  List<UserProductModel> getCachedProductsSync() {
    return _cachedProducts ?? [];
  }

  /// 同步获取缓存的分类列表（如果有的话）
  List<ProductCategoryModel> getCachedCategoriesSync() {
    return _cachedCategories ?? [];
  }

  /// 获取产品列表（优先使用缓存，后台刷新）
  Future<List<UserProductModel>> getProducts({
    bool forceRefresh = false,
    int pageSize = 1000,
  }) async {
    // 如果不是强制刷新且有内存缓存，直接返回
    if (!forceRefresh && _cachedProducts != null) {
      // 后台检查是否需要刷新
      _backgroundRefreshProducts(pageSize: pageSize);
      return _cachedProducts!;
    }

    // 如果没有缓存或需要强制刷新，立即加载
    return await _loadProducts(pageSize: pageSize);
  }

  /// 获取分类列表（优先使用缓存，后台刷新）
  Future<List<ProductCategoryModel>> getCategories({
    bool forceRefresh = false,
  }) async {
    // 如果不是强制刷新且有内存缓存，直接返回
    if (!forceRefresh && _cachedCategories != null) {
      // 后台检查是否需要刷新
      _backgroundRefreshCategories();
      return _cachedCategories!;
    }

    // 如果没有缓存或需要强制刷新，立即加载
    return await _loadCategories();
  }

  /// 立即加载产品列表
  Future<List<UserProductModel>> _loadProducts({int pageSize = 1000}) async {
    if (_isLoadingProducts) return _cachedProducts ?? [];

    _isLoadingProducts = true;
    _lastError = null;
    notifyListeners();

    try {
      final response = await _productService.getProducts(pageSize: pageSize);
      final products = response.products;

      // 更新缓存
      _cachedProducts = products;
      _lastProductsFetchTime = DateTime.now();

      // 保存到本地缓存
      await _saveProductsToLocalCache(products);

      _isLoadingProducts = false;
      notifyListeners();

      return products;
    } catch (e) {
      _lastError = e.toString();
      _isLoadingProducts = false;
      notifyListeners();

      // 如果加载失败，返回现有缓存
      return _cachedProducts ?? [];
    }
  }

  /// 立即加载分类列表
  Future<List<ProductCategoryModel>> _loadCategories() async {
    if (_isLoadingCategories) return _cachedCategories ?? [];

    _isLoadingCategories = true;
    _lastError = null;
    notifyListeners();

    try {
      final categories = await _productService.getCategories();

      // 更新缓存
      _cachedCategories = categories;
      _lastCategoriesFetchTime = DateTime.now();

      // 保存到本地缓存
      await _saveCategoriesToLocalCache(categories);

      _isLoadingCategories = false;
      notifyListeners();

      return categories;
    } catch (e) {
      _lastError = e.toString();
      _isLoadingCategories = false;
      notifyListeners();

      // 如果加载失败，返回现有缓存
      return _cachedCategories ?? [];
    }
  }

  /// 后台刷新产品列表（不阻塞UI）
  void _backgroundRefreshProducts({int pageSize = 1000}) {
    // 检查是否需要刷新
    if (!_shouldRefreshProductsCache()) return;

    // 异步后台刷新
    Future.microtask(() async {
      try {
        final response = await _productService.getProducts(pageSize: pageSize);
        final products = response.products;

        // 只有数据真正变化时才更新缓存和通知UI
        if (_hasProductsDataChanged(products)) {
          _cachedProducts = products;
          _lastProductsFetchTime = DateTime.now();
          await _saveProductsToLocalCache(products);
          notifyListeners();
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
        _lastError = e.toString();
      }
    });
  }

  /// 后台刷新分类列表（不阻塞UI）
  void _backgroundRefreshCategories() {
    // 检查是否需要刷新
    if (!_shouldRefreshCategoriesCache()) return;

    // 异步后台刷新
    Future.microtask(() async {
      try {
        final categories = await _productService.getCategories();

        // 只有数据真正变化时才更新缓存和通知UI
        if (_hasCategoriesDataChanged(categories)) {
          _cachedCategories = categories;
          _lastCategoriesFetchTime = DateTime.now();
          await _saveCategoriesToLocalCache(categories);
          notifyListeners();
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
        _lastError = e.toString();
      }
    });
  }

  /// 检查是否需要刷新产品缓存
  bool _shouldRefreshProductsCache() {
    if (_lastProductsFetchTime == null) return true;

    final now = DateTime.now();
    final difference = now.difference(_lastProductsFetchTime!);
    return difference.inHours >= _cacheValidityHours;
  }

  /// 检查是否需要刷新分类缓存
  bool _shouldRefreshCategoriesCache() {
    if (_lastCategoriesFetchTime == null) return true;

    final now = DateTime.now();
    final difference = now.difference(_lastCategoriesFetchTime!);
    return difference.inHours >= _cacheValidityHours;
  }

  /// 检查产品数据是否发生变化
  bool _hasProductsDataChanged(List<UserProductModel>? newProducts) {
    if (_cachedProducts == null && newProducts == null) return false;
    if (_cachedProducts == null || newProducts == null) return true;
    if (_cachedProducts!.length != newProducts.length) return true;

    // 比较产品ID和更新时间
    for (int i = 0; i < _cachedProducts!.length; i++) {
      if (_cachedProducts![i].id != newProducts[i].id ||
          _cachedProducts![i].updatedAt != newProducts[i].updatedAt) {
        return true;
      }
    }

    return false;
  }

  /// 检查分类数据是否发生变化
  bool _hasCategoriesDataChanged(List<ProductCategoryModel>? newCategories) {
    if (_cachedCategories == null && newCategories == null) return false;
    if (_cachedCategories == null || newCategories == null) return true;
    if (_cachedCategories!.length != newCategories.length) return true;

    // 比较分类名称
    for (int i = 0; i < _cachedCategories!.length; i++) {
      if (_cachedCategories![i].name != newCategories[i].name) {
        return true;
      }
    }

    return false;
  }

  /// 保存产品到本地缓存
  Future<void> _saveProductsToLocalCache(
    List<UserProductModel> products,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(products.map((p) => p.toJson()).toList());
      await prefs.setString(_productsCacheKey, jsonString);
      await prefs.setString(
        _productsCacheTimeKey,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 保存分类到本地缓存
  Future<void> _saveCategoriesToLocalCache(
    List<ProductCategoryModel> categories,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(
        categories.map((c) => c.toJson()).toList(),
      );
      await prefs.setString(_categoriesCacheKey, jsonString);
      await prefs.setString(
        _categoriesCacheTimeKey,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 从本地缓存加载产品
  Future<void> _loadProductsFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_productsCacheKey);
      final cacheTimeString = prefs.getString(_productsCacheTimeKey);

      if (cachedJson != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        // 检查缓存是否过期
        if (now.difference(cacheTime).inHours < _cacheValidityHours) {
          final List<dynamic> jsonList = json.decode(cachedJson);
          _cachedProducts = jsonList
              .map((json) => UserProductModel.fromJson(json))
              .toList();
          _lastProductsFetchTime = cacheTime;
        } else {
          // 缓存过期，清除
          await _clearProductsLocalCache();
        }
      }
    } catch (e) {
      // 加载失败时清除缓存
      await _clearProductsLocalCache();
    }
  }

  /// 从本地缓存加载分类
  Future<void> _loadCategoriesFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_categoriesCacheKey);
      final cacheTimeString = prefs.getString(_categoriesCacheTimeKey);

      if (cachedJson != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        // 检查缓存是否过期
        if (now.difference(cacheTime).inHours < _cacheValidityHours) {
          final List<dynamic> jsonList = json.decode(cachedJson);
          _cachedCategories = jsonList
              .map((json) => ProductCategoryModel.fromJson(json))
              .toList();
          _lastCategoriesFetchTime = cacheTime;
        } else {
          // 缓存过期，清除
          await _clearCategoriesLocalCache();
        }
      }
    } catch (e) {
      // 加载失败时清除缓存
      await _clearCategoriesLocalCache();
    }
  }

  /// 清除产品本地缓存
  Future<void> _clearProductsLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_productsCacheKey);
      await prefs.remove(_productsCacheTimeKey);
    } catch (e) {
      // 清除失败时静默处理
    }
  }

  /// 清除分类本地缓存
  Future<void> _clearCategoriesLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_categoriesCacheKey);
      await prefs.remove(_categoriesCacheTimeKey);
    } catch (e) {
      // 清除失败时静默处理
    }
  }

  /// 清除所有缓存
  Future<void> clearCache() async {
    _cachedProducts = null;
    _cachedCategories = null;
    _lastProductsFetchTime = null;
    _lastCategoriesFetchTime = null;
    _lastError = null;
    _isLoadingProducts = false;
    _isLoadingCategories = false;

    await Future.wait([
      _clearProductsLocalCache(),
      _clearCategoriesLocalCache(),
    ]);

    notifyListeners();
  }

  /// 预加载产品数据（应用启动时调用）
  Future<void> preloadProductData() async {
    // 先加载本地缓存
    await Future.wait([
      _loadProductsFromLocalCache(),
      _loadCategoriesFromLocalCache(),
    ]);

    // 后台刷新最新数据
    _backgroundRefreshProducts();
    _backgroundRefreshCategories();
  }

  /// 检查是否有完整的产品数据
  bool hasCompleteProductData() {
    return _cachedProducts != null && _cachedCategories != null;
  }
}
