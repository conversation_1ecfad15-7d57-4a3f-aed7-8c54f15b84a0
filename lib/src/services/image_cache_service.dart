import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:path_provider/path_provider.dart';

/// 图片缓存管理服务
class ImageCacheService {
  static final ImageCacheService _instance = ImageCacheService._internal();
  factory ImageCacheService() => _instance;
  ImageCacheService._internal();

  /// 初始化缓存配置
  static Future<void> initialize() async {
    // 设置缓存配置
    await _configureCacheManager();

    // 清理过期缓存
    await _cleanExpiredCache();

    assert(() {
      // ignore: avoid_print
      print('📸 图片缓存服务初始化完成');
      return true;
    }());
  }

  /// 配置缓存管理器
  static Future<void> _configureCacheManager() async {
    try {
      // 获取缓存目录
      final cacheDir = await getTemporaryDirectory();
      final imageCacheDir = Directory('${cacheDir.path}/image_cache');

      // 确保缓存目录存在
      if (!await imageCacheDir.exists()) {
        await imageCacheDir.create(recursive: true);
      }

      assert(() {
        // ignore: avoid_print
        print('📁 图片缓存目录: ${imageCacheDir.path}');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('⚠️ 配置缓存管理器失败: $e');
        return true;
      }());
    }
  }

  /// 清理过期缓存
  static Future<void> _cleanExpiredCache() async {
    try {
      // 清理超过7天的缓存
      final cacheDir = await getTemporaryDirectory();
      final imageCacheDir = Directory('${cacheDir.path}/image_cache');

      if (await imageCacheDir.exists()) {
        final now = DateTime.now();
        final files = await imageCacheDir.list().toList();

        for (final file in files) {
          if (file is File) {
            final stat = await file.stat();
            final age = now.difference(stat.modified);

            // 删除超过7天的文件
            if (age.inDays > 7) {
              await file.delete();
              assert(() {
                // ignore: avoid_print
                print('🗑️ 删除过期缓存文件: ${file.path}');
                return true;
              }());
            }
          }
        }
      }
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('⚠️ 清理过期缓存失败: $e');
        return true;
      }());
    }
  }

  /// 预加载图片
  static Future<void> preloadImage(
    String imageUrl, {
    Map<String, String>? headers,
  }) async {
    try {
      if (imageUrl.isEmpty) return;

      // 使用CachedNetworkImageProvider预加载
      final imageProvider = CachedNetworkImageProvider(
        imageUrl,
        headers: headers,
      );

      // 预加载到内存
      await precacheImage(
        imageProvider,
        NavigationService.navigatorKey.currentContext!,
      );

      assert(() {
        // ignore: avoid_print
        print('📸 预加载图片成功: $imageUrl');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('⚠️ 预加载图片失败: $imageUrl, 错误: $e');
        return true;
      }());
    }
  }

  /// 批量预加载图片
  static Future<void> preloadImages(
    List<String> imageUrls, {
    Map<String, String>? headers,
    int maxConcurrent = 3,
  }) async {
    if (imageUrls.isEmpty) return;

    assert(() {
      // ignore: avoid_print
      print('📸 开始批量预加载 ${imageUrls.length} 张图片...');
      return true;
    }());

    // 分批处理，避免同时加载太多图片
    for (int i = 0; i < imageUrls.length; i += maxConcurrent) {
      final batch = imageUrls.skip(i).take(maxConcurrent);
      final futures = batch.map((url) => preloadImage(url, headers: headers));

      try {
        await Future.wait(futures, eagerError: false);
      } catch (e) {
        assert(() {
          // ignore: avoid_print
          print('⚠️ 批量预加载部分失败: $e');
          return true;
        }());
      }

      // 短暂延迟，避免过度占用资源
      await Future.delayed(const Duration(milliseconds: 100));
    }

    assert(() {
      // ignore: avoid_print
      print('✅ 批量预加载完成');
      return true;
    }());
  }

  /// 清除特定图片缓存
  static Future<void> evictImage(String imageUrl) async {
    try {
      if (imageUrl.isEmpty) return;

      final imageProvider = CachedNetworkImageProvider(imageUrl);
      await imageProvider.evict();

      assert(() {
        // ignore: avoid_print
        print('🗑️ 清除图片缓存: $imageUrl');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('⚠️ 清除图片缓存失败: $imageUrl, 错误: $e');
        return true;
      }());
    }
  }

  /// 清除所有图片缓存
  static Future<void> clearAllCache() async {
    try {
      // 清除内存缓存
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      // 清除磁盘缓存
      await DefaultCacheManager().emptyCache();

      assert(() {
        // ignore: avoid_print
        print('🗑️ 已清除所有图片缓存');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('⚠️ 清除所有缓存失败: $e');
        return true;
      }());
    }
  }

  /// 获取缓存大小
  static Future<int> getCacheSize() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final imageCacheDir = Directory('${cacheDir.path}/image_cache');

      if (!await imageCacheDir.exists()) {
        return 0;
      }

      int totalSize = 0;
      final files = await imageCacheDir.list(recursive: true).toList();

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('⚠️ 获取缓存大小失败: $e');
        return true;
      }());
      return 0;
    }
  }

  /// 格式化缓存大小
  static String formatCacheSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}

/// 导航服务 - 用于获取当前上下文
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();
}
