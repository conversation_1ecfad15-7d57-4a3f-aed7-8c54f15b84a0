import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 主题管理服务 - 管理应用的主题模式（浅色/暗色）
class ThemeService extends ChangeNotifier {
  static final ThemeService _instance = ThemeService._internal();

  /// 获取主题服务单例实例
  factory ThemeService() => _instance;

  ThemeService._internal();

  /// 主题持久化键名
  static const String _themePreferenceKey = 'app_theme_mode';
  static const String _userManuallySelectedKey = 'user_manually_selected_theme';

  /// 当前主题模式
  ThemeMode _themeMode = ThemeMode.system;

  /// 用户是否手动选择了主题（如果是，则不跟随系统变化）
  bool _userManuallySelected = false;

  /// 获取当前主题模式
  ThemeMode get themeMode => _themeMode;

  /// 是否为暗色模式
  bool get isDarkMode {
    return _themeMode == ThemeMode.dark;
  }

  /// 是否为浅色模式
  bool get isLightMode {
    return _themeMode == ThemeMode.light;
  }

  /// 是否跟随系统主题
  bool get isSystemMode {
    return _themeMode == ThemeMode.system;
  }

  /// 用户是否手动选择了主题
  bool get isUserManuallySelected {
    return _userManuallySelected;
  }

  /// 初始化主题服务
  Future<void> initialize() async {
    await _loadThemePreference();
  }

  /// 从持久化存储加载主题设置
  Future<void> _loadThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 读取用户是否手动设置了主题
      _userManuallySelected = prefs.getBool(_userManuallySelectedKey) ?? false;

      // 如果用户手动设置了主题，则加载保存的主题设置
      if (_userManuallySelected) {
        final themeModeString = prefs.getString(_themePreferenceKey);
        if (themeModeString != null) {
          if (themeModeString == ThemeMode.light.name) {
            _themeMode = ThemeMode.light;
          } else if (themeModeString == ThemeMode.dark.name) {
            _themeMode = ThemeMode.dark;
          } else {
            _themeMode = ThemeMode.system;
          }
        }
      } else {
        // 如果用户没有手动设置，则默认使用系统主题
        _themeMode = ThemeMode.system;
      }
    } catch (e) {
      // 出错时默认使用系统主题
      _themeMode = ThemeMode.system;
      _userManuallySelected = false;
    }
  }

  /// 保存主题设置到持久化存储
  Future<void> _saveThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themePreferenceKey, _themeMode.name);
      await prefs.setBool(_userManuallySelectedKey, _userManuallySelected);
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 切换到浅色模式
  Future<void> setLightMode() async {
    _userManuallySelected = true;
    await _setThemeMode(ThemeMode.light);
  }

  /// 切换到暗色模式
  Future<void> setDarkMode() async {
    _userManuallySelected = true;
    await _setThemeMode(ThemeMode.dark);
  }

  /// 跟随系统主题
  Future<void> setSystemMode() async {
    _userManuallySelected = false;
    await _setThemeMode(ThemeMode.system);
  }

  /// 切换暗色模式开关（在暗色和浅色之间切换）
  Future<void> toggleDarkMode(bool isDark) async {
    _userManuallySelected = true;
    if (isDark) {
      await setDarkMode();
    } else {
      await setLightMode();
    }
  }

  /// 设置主题模式
  Future<void> _setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode &&
        (mode != ThemeMode.system || !_userManuallySelected)) {
      return;
    }

    _themeMode = mode;
    await _saveThemePreference();
    notifyListeners();
  }

  /// 根据当前系统主题判断是否为暗色模式
  bool isDarkModeActive(BuildContext context) {
    switch (_themeMode) {
      case ThemeMode.light:
        return false;
      case ThemeMode.dark:
        return true;
      case ThemeMode.system:
        return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }
  }

  /// 获取主题模式的显示名称
  String getThemeModeDisplayName() {
    switch (_themeMode) {
      case ThemeMode.light:
        return '浅色模式';
      case ThemeMode.dark:
        return '暗色模式';
      case ThemeMode.system:
        return '跟随系统';
    }
  }
}
