import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/doctor_model.dart';
import 'doctor_interaction_service.dart';
import 'auth_service.dart';

/// 用户互动缓存管理服务 - 提供点赞和收藏数据的缓存机制和后台加载
class UserInteractionCacheService extends ChangeNotifier {
  static final UserInteractionCacheService _instance = UserInteractionCacheService._internal();
  
  /// 获取用户互动缓存服务单例实例
  factory UserInteractionCacheService() => _instance;
  
  UserInteractionCacheService._internal();

  /// 缓存键名
  static const String _likedDoctorsCacheKey = 'liked_doctors_cache';
  static const String _likedDoctorsCacheTimeKey = 'liked_doctors_cache_time';
  static const String _favoriteDoctorsCacheKey = 'favorite_doctors_cache';
  static const String _favoriteDoctorsCacheTimeKey = 'favorite_doctors_cache_time';
  
  /// 缓存有效期（小时）
  static const int _cacheValidityHours = 2; // 互动数据2小时过期

  /// 服务实例
  final DoctorInteractionService _interactionService = DoctorInteractionService();
  final AuthService _authService = AuthService();

  /// 内存缓存
  List<DoctorModel>? _cachedLikedDoctors;
  List<DoctorModel>? _cachedFavoriteDoctors;
  DateTime? _lastLikedFetchTime;
  DateTime? _lastFavoriteFetchTime;
  bool _isLoadingLiked = false;
  bool _isLoadingFavorite = false;
  String? _lastError;

  /// 获取当前缓存的点赞医生列表
  List<DoctorModel>? get cachedLikedDoctors => _cachedLikedDoctors;

  /// 获取当前缓存的收藏医生列表
  List<DoctorModel>? get cachedFavoriteDoctors => _cachedFavoriteDoctors;

  /// 是否正在加载点赞数据
  bool get isLoadingLiked => _isLoadingLiked;

  /// 是否正在加载收藏数据
  bool get isLoadingFavorite => _isLoadingFavorite;

  /// 最后的错误信息
  String? get lastError => _lastError;

  /// 是否有缓存的点赞数据
  bool get hasCachedLikedData => _cachedLikedDoctors != null;

  /// 是否有缓存的收藏数据
  bool get hasCachedFavoriteData => _cachedFavoriteDoctors != null;

  /// 初始化缓存服务
  Future<void> initialize() async {
    await Future.wait([
      _loadLikedFromLocalCache(),
      _loadFavoriteFromLocalCache(),
    ]);
  }

  /// 同步获取缓存的点赞医生列表（如果有的话）
  List<DoctorModel> getCachedLikedDoctorsSync() {
    return _cachedLikedDoctors ?? [];
  }

  /// 同步获取缓存的收藏医生列表（如果有的话）
  List<DoctorModel> getCachedFavoriteDoctorsSync() {
    return _cachedFavoriteDoctors ?? [];
  }

  /// 获取点赞医生列表（优先使用缓存，后台刷新）
  Future<List<DoctorModel>> getLikedDoctors({bool forceRefresh = false}) async {
    // 如果用户未登录，清除缓存并返回空列表
    if (_authService.currentUser == null) {
      await clearCache();
      return [];
    }

    // 如果不是强制刷新且有内存缓存，直接返回
    if (!forceRefresh && _cachedLikedDoctors != null) {
      // 后台检查是否需要刷新
      _backgroundRefreshLiked();
      return _cachedLikedDoctors!;
    }

    // 如果没有缓存或需要强制刷新，立即加载
    return await _loadLikedDoctors();
  }

  /// 获取收藏医生列表（优先使用缓存，后台刷新）
  Future<List<DoctorModel>> getFavoriteDoctors({bool forceRefresh = false}) async {
    // 如果用户未登录，清除缓存并返回空列表
    if (_authService.currentUser == null) {
      await clearCache();
      return [];
    }

    // 如果不是强制刷新且有内存缓存，直接返回
    if (!forceRefresh && _cachedFavoriteDoctors != null) {
      // 后台检查是否需要刷新
      _backgroundRefreshFavorite();
      return _cachedFavoriteDoctors!;
    }

    // 如果没有缓存或需要强制刷新，立即加载
    return await _loadFavoriteDoctors();
  }

  /// 立即加载点赞医生列表
  Future<List<DoctorModel>> _loadLikedDoctors() async {
    if (_isLoadingLiked) return _cachedLikedDoctors ?? [];

    _isLoadingLiked = true;
    _lastError = null;
    notifyListeners();

    try {
      final doctors = await _interactionService.getLikedDoctors();
      
      // 更新缓存
      _cachedLikedDoctors = doctors;
      _lastLikedFetchTime = DateTime.now();
      
      // 保存到本地缓存
      await _saveLikedToLocalCache(doctors);
      
      _isLoadingLiked = false;
      notifyListeners();
      
      return doctors;
    } catch (e) {
      _lastError = e.toString();
      _isLoadingLiked = false;
      notifyListeners();
      
      // 如果加载失败，返回现有缓存
      return _cachedLikedDoctors ?? [];
    }
  }

  /// 立即加载收藏医生列表
  Future<List<DoctorModel>> _loadFavoriteDoctors() async {
    if (_isLoadingFavorite) return _cachedFavoriteDoctors ?? [];

    _isLoadingFavorite = true;
    _lastError = null;
    notifyListeners();

    try {
      final doctors = await _interactionService.getFavoriteDoctors();
      
      // 更新缓存
      _cachedFavoriteDoctors = doctors;
      _lastFavoriteFetchTime = DateTime.now();
      
      // 保存到本地缓存
      await _saveFavoriteToLocalCache(doctors);
      
      _isLoadingFavorite = false;
      notifyListeners();
      
      return doctors;
    } catch (e) {
      _lastError = e.toString();
      _isLoadingFavorite = false;
      notifyListeners();
      
      // 如果加载失败，返回现有缓存
      return _cachedFavoriteDoctors ?? [];
    }
  }

  /// 后台刷新点赞数据（不阻塞UI）
  void _backgroundRefreshLiked() {
    // 检查是否需要刷新
    if (!_shouldRefreshLikedCache()) return;

    // 异步后台刷新
    Future.microtask(() async {
      try {
        final doctors = await _interactionService.getLikedDoctors();
        
        // 只有数据真正变化时才更新缓存和通知UI
        if (_hasLikedDataChanged(doctors)) {
          _cachedLikedDoctors = doctors;
          _lastLikedFetchTime = DateTime.now();
          await _saveLikedToLocalCache(doctors);
          notifyListeners();
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
        _lastError = e.toString();
      }
    });
  }

  /// 后台刷新收藏数据（不阻塞UI）
  void _backgroundRefreshFavorite() {
    // 检查是否需要刷新
    if (!_shouldRefreshFavoriteCache()) return;

    // 异步后台刷新
    Future.microtask(() async {
      try {
        final doctors = await _interactionService.getFavoriteDoctors();
        
        // 只有数据真正变化时才更新缓存和通知UI
        if (_hasFavoriteDataChanged(doctors)) {
          _cachedFavoriteDoctors = doctors;
          _lastFavoriteFetchTime = DateTime.now();
          await _saveFavoriteToLocalCache(doctors);
          notifyListeners();
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
        _lastError = e.toString();
      }
    });
  }

  /// 检查是否需要刷新点赞缓存
  bool _shouldRefreshLikedCache() {
    if (_lastLikedFetchTime == null) return true;
    
    final now = DateTime.now();
    final difference = now.difference(_lastLikedFetchTime!);
    return difference.inHours >= _cacheValidityHours;
  }

  /// 检查是否需要刷新收藏缓存
  bool _shouldRefreshFavoriteCache() {
    if (_lastFavoriteFetchTime == null) return true;
    
    final now = DateTime.now();
    final difference = now.difference(_lastFavoriteFetchTime!);
    return difference.inHours >= _cacheValidityHours;
  }

  /// 检查点赞数据是否发生变化
  bool _hasLikedDataChanged(List<DoctorModel>? newDoctors) {
    if (_cachedLikedDoctors == null && newDoctors == null) return false;
    if (_cachedLikedDoctors == null || newDoctors == null) return true;
    if (_cachedLikedDoctors!.length != newDoctors.length) return true;
    
    // 比较医生ID
    for (int i = 0; i < _cachedLikedDoctors!.length; i++) {
      if (_cachedLikedDoctors![i].id != newDoctors[i].id) {
        return true;
      }
    }
    
    return false;
  }

  /// 检查收藏数据是否发生变化
  bool _hasFavoriteDataChanged(List<DoctorModel>? newDoctors) {
    if (_cachedFavoriteDoctors == null && newDoctors == null) return false;
    if (_cachedFavoriteDoctors == null || newDoctors == null) return true;
    if (_cachedFavoriteDoctors!.length != newDoctors.length) return true;
    
    // 比较医生ID
    for (int i = 0; i < _cachedFavoriteDoctors!.length; i++) {
      if (_cachedFavoriteDoctors![i].id != newDoctors[i].id) {
        return true;
      }
    }
    
    return false;
  }

  /// 保存点赞数据到本地缓存
  Future<void> _saveLikedToLocalCache(List<DoctorModel> doctors) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(doctors.map((d) => d.toJson()).toList());
      await prefs.setString(_likedDoctorsCacheKey, jsonString);
      await prefs.setString(_likedDoctorsCacheTimeKey, DateTime.now().toIso8601String());
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 保存收藏数据到本地缓存
  Future<void> _saveFavoriteToLocalCache(List<DoctorModel> doctors) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(doctors.map((d) => d.toJson()).toList());
      await prefs.setString(_favoriteDoctorsCacheKey, jsonString);
      await prefs.setString(_favoriteDoctorsCacheTimeKey, DateTime.now().toIso8601String());
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 从本地缓存加载点赞数据
  Future<void> _loadLikedFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_likedDoctorsCacheKey);
      final cacheTimeString = prefs.getString(_likedDoctorsCacheTimeKey);
      
      if (cachedJson != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();
        
        // 检查缓存是否过期
        if (now.difference(cacheTime).inHours < _cacheValidityHours) {
          final List<dynamic> jsonList = json.decode(cachedJson);
          _cachedLikedDoctors = jsonList.map((json) => DoctorModel.fromJson(json)).toList();
          _lastLikedFetchTime = cacheTime;
        } else {
          // 缓存过期，清除
          await _clearLikedLocalCache();
        }
      }
    } catch (e) {
      // 加载失败时清除缓存
      await _clearLikedLocalCache();
    }
  }

  /// 从本地缓存加载收藏数据
  Future<void> _loadFavoriteFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_favoriteDoctorsCacheKey);
      final cacheTimeString = prefs.getString(_favoriteDoctorsCacheTimeKey);
      
      if (cachedJson != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();
        
        // 检查缓存是否过期
        if (now.difference(cacheTime).inHours < _cacheValidityHours) {
          final List<dynamic> jsonList = json.decode(cachedJson);
          _cachedFavoriteDoctors = jsonList.map((json) => DoctorModel.fromJson(json)).toList();
          _lastFavoriteFetchTime = cacheTime;
        } else {
          // 缓存过期，清除
          await _clearFavoriteLocalCache();
        }
      }
    } catch (e) {
      // 加载失败时清除缓存
      await _clearFavoriteLocalCache();
    }
  }

  /// 清除点赞本地缓存
  Future<void> _clearLikedLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_likedDoctorsCacheKey);
      await prefs.remove(_likedDoctorsCacheTimeKey);
    } catch (e) {
      // 清除失败时静默处理
    }
  }

  /// 清除收藏本地缓存
  Future<void> _clearFavoriteLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_favoriteDoctorsCacheKey);
      await prefs.remove(_favoriteDoctorsCacheTimeKey);
    } catch (e) {
      // 清除失败时静默处理
    }
  }

  /// 清除所有缓存
  Future<void> clearCache() async {
    _cachedLikedDoctors = null;
    _cachedFavoriteDoctors = null;
    _lastLikedFetchTime = null;
    _lastFavoriteFetchTime = null;
    _lastError = null;
    _isLoadingLiked = false;
    _isLoadingFavorite = false;
    
    await Future.wait([
      _clearLikedLocalCache(),
      _clearFavoriteLocalCache(),
    ]);
    
    notifyListeners();
  }

  /// 预加载互动数据（应用启动时调用）
  Future<void> preloadInteractionData() async {
    if (_authService.currentUser != null) {
      // 先加载本地缓存
      await Future.wait([
        _loadLikedFromLocalCache(),
        _loadFavoriteFromLocalCache(),
      ]);
      
      // 后台刷新最新数据
      _backgroundRefreshLiked();
      _backgroundRefreshFavorite();
    }
  }

  /// 获取点赞医生数量
  int getLikedDoctorsCount() {
    return _cachedLikedDoctors?.length ?? 0;
  }

  /// 获取收藏医生数量
  int getFavoriteDoctorsCount() {
    return _cachedFavoriteDoctors?.length ?? 0;
  }

  /// 检查是否有完整的互动数据
  bool hasCompleteInteractionData() {
    return _cachedLikedDoctors != null && _cachedFavoriteDoctors != null;
  }
}
