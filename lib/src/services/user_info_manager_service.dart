import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile_model.dart';
import '../services/auth_service.dart';
import '../services/avatar_manager_service.dart';
import '../services/user_profile_service.dart';

/// 用户信息管理服务
/// 负责用户完整信息的获取、本地持久化存储、状态管理和自动更新
/// 包括会员到期检测、头像管理集成等功能
class UserInfoManagerService extends ChangeNotifier {
  // 单例模式
  static final UserInfoManagerService _instance =
      UserInfoManagerService._internal();
  factory UserInfoManagerService() => _instance;
  UserInfoManagerService._internal();

  // 依赖的服务
  final AuthService _authService = AuthService();
  final AvatarManagerService _avatarManagerService = AvatarManagerService();
  final UserProfileService _userProfileService = UserProfileService();

  // 当前用户完整信息
  UserProfileModel? _currentUserProfile;

  // 加载状态
  bool _isLoading = false;

  // 自动检查会员到期的定时器
  Timer? _vipExpiryCheckTimer;

  // SharedPreferences key
  static const String _userProfileKey = 'user_profile_data';
  static const String _lastUpdateTimeKey = 'user_profile_last_update';

  /// 获取当前用户完整资料
  UserProfileModel? get currentUserProfile => _currentUserProfile;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 是否有本地用户数据
  bool get hasLocalUserData => _currentUserProfile != null;

  /// 当前用户是否为VIP
  bool get isVip => _currentUserProfile?.vip == true;

  /// VIP是否已过期
  bool get isVipExpired => _currentUserProfile?.isVipExpired ?? true;

  /// 获取VIP到期时间
  DateTime? get vipExpiryDate => _currentUserProfile?.vipExpiryDate;

  /// 初始化用户信息管理器
  Future<void> initialize() async {
    try {
      // 从本地恢复用户信息
      await _loadUserProfileFromLocal();

      // 启动会员到期检查
      _startVipExpiryCheck();

      assert(() {
        // ignore: avoid_print
        print('🔧 UserInfoManagerService 初始化完成');
        // ignore: avoid_print
        print('- 本地用户数据: ${_currentUserProfile != null ? "存在" : "不存在"}');
        // ignore: avoid_print
        print('- VIP状态: ${isVip ? "VIP用户" : "普通用户"}');
        if (isVip) {
          // ignore: avoid_print
          print('- VIP到期时间: $vipExpiryDate');
          // ignore: avoid_print
          print('- VIP是否过期: $isVipExpired');
        }
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ UserInfoManagerService 初始化失败: $e');
        return true;
      }());
    }
  }

  /// 用户登录后获取并持久化完整用户信息
  Future<void> handleUserLogin({bool isBackgroundFetch = false}) async {
    if (!_authService.isLoggedIn) {
      assert(() {
        // ignore: avoid_print
        print('⚠️ 用户未登录，无法获取用户信息');
        return true;
      }());
      return;
    }

    try {
      _isLoading = true;
      notifyListeners();

      assert(() {
        // ignore: avoid_print
        print(isBackgroundFetch ? '🔄 后台获取用户完整信息...' : '🔄 开始获取用户完整信息...');
        return true;
      }());

      // 调用 GET /applet/v1/app/profile 接口获取完整用户信息
      final userProfile = await _userProfileService.getUserProfile();

      // 保存到本地持久化存储
      await _saveUserProfileToLocal(userProfile);

      // 更新内存中的用户信息
      _currentUserProfile = userProfile;

      // 处理头像下载和保存
      if (userProfile.avatar != null && userProfile.avatar!.isNotEmpty) {
        try {
          await _avatarManagerService.handleLoginAvatar(userProfile.avatar);
        } catch (e) {
          assert(() {
            // ignore: avoid_print
            print('⚠️ 头像处理失败，但不影响登录流程: $e');
            return true;
          }());
        }
      }

      // 启动会员到期检查
      _startVipExpiryCheck();

      assert(() {
        // ignore: avoid_print
        print('✅ 用户信息获取并持久化成功');
        // ignore: avoid_print
        print('- 用户ID: ${userProfile.id}');
        // ignore: avoid_print
        print('- 昵称: ${userProfile.nickname}');
        // ignore: avoid_print
        print('- 手机号: ${userProfile.phone}');
        // ignore: avoid_print
        print('- VIP状态: ${userProfile.vip}');
        // ignore: avoid_print
        print('- 头像: ${userProfile.avatar}');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ 获取用户信息失败: $e');
        return true;
      }());

      // 如果是后台获取失败，不抛出异常，避免影响用户体验
      if (isBackgroundFetch) {
        assert(() {
          // ignore: avoid_print
          print('🔧 后台获取失败，但不影响用户正常使用');
          return true;
        }());
        return;
      }

      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 刷新用户信息（从服务器重新获取）
  Future<void> refreshUserInfo() async {
    if (!_authService.isLoggedIn) {
      return;
    }

    try {
      _isLoading = true;
      notifyListeners();

      assert(() {
        // ignore: avoid_print
        print('🔄 手动刷新用户信息...');
        return true;
      }());

      final userProfile = await _userProfileService.getUserProfile();
      await _saveUserProfileToLocal(userProfile);

      // 检查头像是否有变化
      if (_currentUserProfile?.avatar != userProfile.avatar) {
        if (userProfile.avatar != null && userProfile.avatar!.isNotEmpty) {
          try {
            await _avatarManagerService.handleLoginAvatar(userProfile.avatar);
          } catch (e) {
            assert(() {
              // ignore: avoid_print
              print('⚠️ 头像更新失败: $e');
              return true;
            }());
          }
        } else {
          // 如果新的头像为空，清除本地头像
          await _avatarManagerService.clearLocalAvatar();
        }
      }

      _currentUserProfile = userProfile;

      assert(() {
        // ignore: avoid_print
        print('✅ 用户信息刷新成功');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ 刷新用户信息失败: $e');
        return true;
      }());
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 处理头像上传成功后的本地更新
  Future<void> handleAvatarUploadSuccess(String avatarFileName) async {
    try {
      // 使用 AvatarManagerService 处理头像更新
      await _avatarManagerService.handleAvatarUploadSuccess(avatarFileName);

      // 更新本地存储的用户资料中的头像字段
      if (_currentUserProfile != null) {
        final updatedProfile = _currentUserProfile!.copyWith(
          avatar: avatarFileName,
        );

        _currentUserProfile = updatedProfile;
        await _saveUserProfileToLocal(updatedProfile);

        notifyListeners();

        assert(() {
          // ignore: avoid_print
          print('✅ 用户资料中的头像字段已更新: $avatarFileName');
          return true;
        }());
      }
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ 处理头像上传成功后更新失败: $e');
        return true;
      }());
      rethrow;
    }
  }

  /// 用户退出登录时清除所有本地数据
  Future<void> handleUserLogout() async {
    try {
      assert(() {
        // ignore: avoid_print
        print('🔄 清除用户信息和头像...');
        return true;
      }());

      // 停止会员到期检查
      _stopVipExpiryCheck();

      // 清除本地持久化的用户信息
      await _clearUserProfileFromLocal();

      // 清除头像
      await _avatarManagerService.clearLocalAvatar();

      // 清除内存中的用户信息
      _currentUserProfile = null;

      notifyListeners();

      assert(() {
        // ignore: avoid_print
        print('✅ 用户信息和头像已清除');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ 清除用户信息失败: $e');
        return true;
      }());
    }
  }

  /// 检查会员是否到期，如果到期则自动刷新用户信息
  Future<void> _checkVipExpiry() async {
    if (!_authService.isLoggedIn || _currentUserProfile == null) {
      return;
    }

    final profile = _currentUserProfile!;

    // 只有VIP用户才需要检查到期
    if (profile.vip != true || profile.vipData == null) {
      return;
    }

    try {
      final isExpired = profile.isVipExpired;

      if (isExpired) {
        assert(() {
          // ignore: avoid_print
          print('⚠️ 检测到VIP已过期，自动刷新用户信息...');
          return true;
        }());

        await refreshUserInfo();
      }
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ 检查VIP到期状态失败: $e');
        return true;
      }());
    }
  }

  /// 启动会员到期检查定时器
  void _startVipExpiryCheck() {
    _stopVipExpiryCheck(); // 先停止现有的定时器

    // 每30分钟检查一次会员到期状态
    _vipExpiryCheckTimer = Timer.periodic(
      const Duration(minutes: 30),
      (timer) => _checkVipExpiry(),
    );

    assert(() {
      // ignore: avoid_print
      print('⏰ VIP到期检查定时器已启动');
      return true;
    }());
  }

  /// 停止会员到期检查定时器
  void _stopVipExpiryCheck() {
    _vipExpiryCheckTimer?.cancel();
    _vipExpiryCheckTimer = null;
  }

  /// 从本地存储加载用户资料
  Future<void> _loadUserProfileFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = prefs.getString(_userProfileKey);

      if (profileJson != null && profileJson.isNotEmpty) {
        final profileData = json.decode(profileJson) as Map<String, dynamic>;
        _currentUserProfile = UserProfileModel.fromJson(profileData);

        assert(() {
          // ignore: avoid_print
          print('📱 从本地加载用户资料成功');
          return true;
        }());
      }
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ 从本地加载用户资料失败: $e');
        return true;
      }());
      // 如果加载失败，清除可能损坏的数据
      await _clearUserProfileFromLocal();
    }
  }

  /// 保存用户资料到本地存储
  Future<void> _saveUserProfileToLocal(UserProfileModel profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = json.encode(profile.toJson());

      await Future.wait([
        prefs.setString(_userProfileKey, profileJson),
        prefs.setInt(_lastUpdateTimeKey, DateTime.now().millisecondsSinceEpoch),
      ]);

      assert(() {
        // ignore: avoid_print
        print('💾 用户资料已保存到本地');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ 保存用户资料到本地失败: $e');
        return true;
      }());
      rethrow;
    }
  }

  /// 清除本地存储的用户资料
  Future<void> _clearUserProfileFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await Future.wait([
        prefs.remove(_userProfileKey),
        prefs.remove(_lastUpdateTimeKey),
      ]);

      assert(() {
        // ignore: avoid_print
        print('🗑️ 本地用户资料已清除');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ 清除本地用户资料失败: $e');
        return true;
      }());
    }
  }

  /// 获取本地数据的最后更新时间
  Future<DateTime?> getLastUpdateTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastUpdateTimeKey);
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ 获取最后更新时间失败: $e');
        return true;
      }());
    }
    return null;
  }

  /// 检查本地数据是否需要更新（超过指定时间间隔）
  Future<bool> shouldUpdateProfile({
    Duration maxAge = const Duration(hours: 1),
  }) async {
    final lastUpdate = await getLastUpdateTime();
    if (lastUpdate == null) return true;

    return DateTime.now().difference(lastUpdate) > maxAge;
  }

  /// 强制从服务器更新用户信息（不管本地数据是否过期）
  Future<void> forceUpdateProfile() async {
    await refreshUserInfo();
  }

  @override
  void dispose() {
    _stopVipExpiryCheck();
    super.dispose();
  }
}
