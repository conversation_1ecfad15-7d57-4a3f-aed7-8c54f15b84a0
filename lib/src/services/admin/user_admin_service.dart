import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../config/api/api_config.dart';
import '../../models/admin_user_model.dart';
import '../../services/auth_service.dart';

/// 管理员用户管理服务
class UserAdminService {
  /// 获取当前用户token
  String _getCurrentUserToken() {
    final currentUser = AuthService().currentUser;
    if (currentUser == null || currentUser.token.isEmpty) {
      throw Exception('未登录');
    }
    return currentUser.token;
  }

  /// 获取用户列表
  Future<UserListResponse> getUsers({
    int page = 1,
    int pageSize = 10,
    String? keyword,
    int? status,
    int? sex,
    int? isAdmin,
    int? isDoctor,
    int? isReferrer,
    int? registerSource,
    String? startDate,
    String? endDate,
    String sortField = 'created_at',
    String sortOrder = 'desc',
  }) async {
    try {
      final token = _getCurrentUserToken();

      final queryParams = <String, String>{
        'page': page.toString(),
        'page_size': pageSize.toString(),
        'sort_field': sortField,
        'sort_order': sortOrder,
      };

      if (keyword != null && keyword.isNotEmpty) {
        queryParams['keyword'] = keyword;
      }
      if (status != null) queryParams['status'] = status.toString();
      if (sex != null) queryParams['sex'] = sex.toString();
      if (isAdmin != null) queryParams['is_admin'] = isAdmin.toString();
      if (isDoctor != null) queryParams['is_doctor'] = isDoctor.toString();
      if (isReferrer != null)
        queryParams['is_referrer'] = isReferrer.toString();
      if (registerSource != null) {
        queryParams['register_source'] = registerSource.toString();
      }
      if (startDate != null && startDate.isNotEmpty) {
        queryParams['start_date'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        queryParams['end_date'] = endDate;
      }

      final uri = Uri.parse(
        ApiConfig.getAdminUsersUrl,
      ).replace(queryParameters: queryParams);

      print('UserAdminService: 获取用户列表，URL: $uri');

      final response = await http
          .get(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 用户列表API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          return UserListResponse.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? '获取用户列表失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else {
        throw Exception('获取用户列表失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 获取用户列表失败: $e');
      rethrow;
    }
  }

  /// 获取用户详细信息
  Future<AdminUserModel> getUserDetail(int userId) async {
    try {
      final token = _getCurrentUserToken();

      final uri = Uri.parse(ApiConfig.getAdminUserDetailUrl(userId));

      print('UserAdminService: 获取用户详情，ID: $userId');

      final response = await http
          .get(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 用户详情API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          return AdminUserModel.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? '获取用户详情失败');
        }
      } else if (response.statusCode == 404) {
        throw Exception('用户不存在');
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else {
        throw Exception('获取用户详情失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 获取用户详情失败: $e');
      rethrow;
    }
  }

  /// 更新用户基础信息
  Future<void> updateUser(
    int userId, {
    String? nickname,
    String? phone,
    int? sex,
    String? birthday,
    bool? status,
  }) async {
    try {
      final token = _getCurrentUserToken();

      final requestBody = <String, dynamic>{};
      if (nickname != null) requestBody['nickname'] = nickname;
      if (phone != null) requestBody['phone'] = phone;
      if (sex != null) requestBody['sex'] = sex;
      if (birthday != null) requestBody['birthday'] = birthday;
      if (status != null) requestBody['status'] = status;

      final uri = Uri.parse(ApiConfig.updateAdminUserUrl(userId));

      print('UserAdminService: 更新用户信息，ID: $userId, 数据: $requestBody');

      final response = await http
          .put(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: json.encode(requestBody),
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 更新用户信息API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '更新用户信息失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('用户不存在');
      } else {
        throw Exception('更新用户信息失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 更新用户信息失败: $e');
      rethrow;
    }
  }

  /// 更新用户状态
  Future<void> updateUserStatus(int userId, bool status) async {
    try {
      final token = _getCurrentUserToken();

      final requestBody = {'status': status};
      final uri = Uri.parse(ApiConfig.updateAdminUserStatusUrl(userId));

      print('UserAdminService: 更新用户状态，ID: $userId, 状态: $status');

      final response = await http
          .put(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: json.encode(requestBody),
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 更新用户状态API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '更新用户状态失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('用户不存在');
      } else {
        throw Exception('更新用户状态失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 更新用户状态失败: $e');
      rethrow;
    }
  }

  /// 调整用户余额
  Future<void> adjustUserBalance(
    int userId,
    double amount,
    String reason,
  ) async {
    try {
      final token = _getCurrentUserToken();

      final requestBody = {'amount': amount, 'reason': reason};

      final uri = Uri.parse(ApiConfig.adjustAdminUserBalanceUrl(userId));

      print('UserAdminService: 调整用户余额，ID: $userId, 金额: $amount, 原因: $reason');

      final response = await http
          .put(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: json.encode(requestBody),
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 调整用户余额API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '调整用户余额失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('用户不存在');
      } else {
        throw Exception('调整用户余额失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 调整用户余额失败: $e');
      rethrow;
    }
  }

  /// 获取用户统计数据
  Future<UserStatisticsModel> getUserStatistics() async {
    try {
      final token = _getCurrentUserToken();

      final uri = Uri.parse(ApiConfig.getAdminUserStatisticsUrl);

      print('UserAdminService: 获取用户统计数据');

      final response = await http
          .get(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 获取用户统计数据API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          return UserStatisticsModel.fromJson(jsonData['data']);
        } else {
          throw Exception(jsonData['message'] ?? '获取用户统计数据失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else {
        throw Exception('获取用户统计数据失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 获取用户统计数据失败: $e');
      rethrow;
    }
  }

  /// 更新用户信息
  Future<void> updateUserInfo(
    int userId, {
    String? nickname,
    String? phone,
    String? birthday,
    int? gender,
  }) async {
    try {
      final token = _getCurrentUserToken();

      final requestBody = <String, dynamic>{};
      if (nickname != null) requestBody['nickname'] = nickname;
      if (phone != null) requestBody['phone'] = phone;
      if (birthday != null) requestBody['birthday'] = birthday;
      if (gender != null) requestBody['sex'] = gender;

      final uri = Uri.parse(ApiConfig.updateAdminUserUrl(userId));

      print('UserAdminService: 更新用户信息，ID: $userId, 数据: $requestBody');

      final response = await http
          .put(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: json.encode(requestBody),
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 更新用户信息API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '更新用户信息失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('用户不存在');
      } else {
        throw Exception('更新用户信息失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 更新用户信息失败: $e');
      rethrow;
    }
  }

  /// 调整用户积分
  Future<void> adjustUserIntegral(int userId, int amount, String reason) async {
    try {
      final token = _getCurrentUserToken();

      final requestBody = {'amount': amount, 'reason': reason};

      final uri = Uri.parse(ApiConfig.adjustAdminUserIntegralUrl(userId));

      print('UserAdminService: 调整用户积分，ID: $userId, 积分: $amount, 原因: $reason');

      final response = await http
          .put(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: json.encode(requestBody),
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 调整用户积分API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '调整用户积分失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('用户不存在');
      } else {
        throw Exception('调整用户积分失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 调整用户积分失败: $e');
      rethrow;
    }
  }

  /// 更新用户角色
  Future<void> updateUserRole(
    int userId, {
    bool? isAdmin,
    bool? isDoctor,
    bool? isReferrer,
    int? referrerLevel,
  }) async {
    try {
      final token = _getCurrentUserToken();

      final requestBody = <String, dynamic>{};
      if (isAdmin != null) requestBody['is_admin'] = isAdmin;
      if (isDoctor != null) requestBody['is_doctor'] = isDoctor;
      if (isReferrer != null) requestBody['is_referrer'] = isReferrer;
      if (referrerLevel != null) requestBody['referrer_level'] = referrerLevel;

      final uri = Uri.parse(ApiConfig.updateAdminUserRoleUrl(userId));

      print('UserAdminService: 更新用户角色，ID: $userId, 数据: $requestBody');

      final response = await http
          .put(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: json.encode(requestBody),
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 更新用户角色API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '更新用户角色失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('用户不存在');
      } else {
        throw Exception('更新用户角色失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 更新用户角色失败: $e');
      rethrow;
    }
  }

  /// 重置用户密码
  Future<void> resetUserPassword(int userId, String newPassword) async {
    try {
      final token = _getCurrentUserToken();

      final requestBody = {'new_password': newPassword};
      final uri = Uri.parse(ApiConfig.resetAdminUserPasswordUrl(userId));

      print('UserAdminService: 重置用户密码，ID: $userId');

      final response = await http
          .post(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: json.encode(requestBody),
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 重置用户密码API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '重置用户密码失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('用户不存在');
      } else {
        throw Exception('重置用户密码失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 重置用户密码失败: $e');
      rethrow;
    }
  }

  /// 获取用户登录令牌列表
  Future<List<UserTokenModel>> getUserTokens(int userId) async {
    try {
      final token = _getCurrentUserToken();

      final uri = Uri.parse(ApiConfig.getAdminUserTokensUrl(userId));

      print('UserAdminService: 获取用户登录令牌列表，ID: $userId');

      final response = await http
          .get(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 获取用户登录令牌列表API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          final List<dynamic> tokensJson = jsonData['data'];
          return tokensJson
              .map((json) => UserTokenModel.fromJson(json))
              .toList();
        } else {
          throw Exception(jsonData['message'] ?? '获取用户登录令牌列表失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('用户不存在');
      } else {
        throw Exception('获取用户登录令牌列表失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 获取用户登录令牌列表失败: $e');
      rethrow;
    }
  }

  /// 清除用户所有登录令牌
  Future<void> clearUserAllTokens(int userId) async {
    try {
      final token = _getCurrentUserToken();

      final uri = Uri.parse(ApiConfig.clearAdminUserAllTokensUrl(userId));

      print('UserAdminService: 清除用户所有登录令牌，ID: $userId');

      final response = await http
          .delete(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 清除用户所有登录令牌API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '清除用户所有登录令牌失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('用户不存在');
      } else {
        throw Exception('清除用户所有登录令牌失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 清除用户所有登录令牌失败: $e');
      rethrow;
    }
  }

  /// 清除用户指定登录令牌
  Future<void> clearUserToken(int userId, int tokenId) async {
    try {
      final token = _getCurrentUserToken();

      final uri = Uri.parse(ApiConfig.clearAdminUserTokenUrl(userId, tokenId));

      print('UserAdminService: 清除用户指定登录令牌，用户ID: $userId, 令牌ID: $tokenId');

      final response = await http
          .delete(
            uri,
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
          )
          .timeout(const Duration(seconds: 10));

      print('UserAdminService: 清除用户指定登录令牌API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        if (jsonData['code'] != 200) {
          throw Exception(jsonData['message'] ?? '清除用户指定登录令牌失败');
        }
      } else if (response.statusCode == 401) {
        throw Exception('权限不足，请重新登录');
      } else if (response.statusCode == 403) {
        throw Exception('无管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('用户或令牌不存在');
      } else {
        throw Exception('清除用户指定登录令牌失败: ${response.statusCode}');
      }
    } catch (e) {
      print('UserAdminService: 清除用户指定登录令牌失败: $e');
      rethrow;
    }
  }
}
