import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../config/api/api_config.dart';
import '../../models/doctor_product_model.dart';
import '../../services/auth_service.dart';

/// 管理员产品审核服务
class AdminProductService {
  static final AdminProductService _instance = AdminProductService._internal();
  factory AdminProductService() => _instance;
  AdminProductService._internal();

  /// 获取认证头
  Map<String, String> _getAuthHeaders() {
    final user = AuthService().currentUser;
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${user?.token ?? ''}',
    };
  }

  /// 检查用户是否有管理员权限
  bool checkAdminPermission() {
    final user = AuthService().currentUser;
    return user != null && user.isAdmin == true;
  }

  /// 获取所有产品列表（管理员）
  Future<List<DoctorProductModel>> getAllProducts({
    int? status,
    int? doctorId,
  }) async {
    try {
      final params = <String, String>{};

      // 后端返回包含所有语言的多语言对象，不需要lang参数
      if (status != null) params['status'] = status.toString();
      if (doctorId != null) params['doctor_id'] = doctorId.toString();

      final uri = Uri.parse(ApiConfig.getAdminProductsUrl);
      final finalUri = uri.replace(queryParameters: params);
      String url = finalUri.toString();

      print('AdminProductService: 请求URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: _getAuthHeaders(),
      );

      print('AdminProductService: 获取产品列表响应状态码: ${response.statusCode}');
      print('AdminProductService: 获取产品列表响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        final products = jsonList
            .map((json) => DoctorProductModel.fromJson(json))
            .toList();
        print('AdminProductService: 成功获取 ${products.length} 个产品');
        return products;
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else if (response.statusCode == 401) {
        throw Exception('未登录，请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取产品列表失败');
      }
    } catch (e) {
      print('AdminProductService: 获取产品列表失败: $e');
      rethrow;
    }
  }

  /// 获取待审核产品列表
  Future<List<DoctorProductModel>> getPendingProducts() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getPendingAdminProductsUrl),
        headers: _getAuthHeaders(),
      );

      print('AdminProductService: 获取待审核产品响应状态码: ${response.statusCode}');
      print('AdminProductService: 获取待审核产品响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        final products = jsonList
            .map((json) => DoctorProductModel.fromJson(json))
            .toList();
        print('AdminProductService: 成功获取 ${products.length} 个待审核产品');
        return products;
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取待审核产品失败');
      }
    } catch (e) {
      print('AdminProductService: 获取待审核产品失败: $e');
      rethrow;
    }
  }

  /// 获取产品详情
  Future<DoctorProductModel> getProductById(int productId) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getAdminProductDetailUrl(productId)),
        headers: _getAuthHeaders(),
      );

      print('AdminProductService: 获取产品详情响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return DoctorProductModel.fromJson(jsonData);
      } else if (response.statusCode == 404) {
        throw Exception('产品不存在');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取产品详情失败');
      }
    } catch (e) {
      print('AdminProductService: 获取产品详情失败: $e');
      rethrow;
    }
  }

  /// 审核产品
  Future<DoctorProductModel> reviewProduct(
    int productId,
    int status, {
    String? reviewNote,
  }) async {
    try {
      final requestData = {
        'status': status,
        if (reviewNote != null) 'admin_review_note': reviewNote,
      };

      final response = await http.put(
        Uri.parse(ApiConfig.reviewAdminProductUrl(productId)),
        headers: _getAuthHeaders(),
        body: json.encode(requestData),
      );

      print('AdminProductService: 审核产品响应状态码: ${response.statusCode}');
      print('AdminProductService: 审核产品响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return DoctorProductModel.fromJson(jsonData);
      } else if (response.statusCode == 404) {
        throw Exception('产品不存在');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '审核产品失败');
      }
    } catch (e) {
      print('AdminProductService: 审核产品失败: $e');
      rethrow;
    }
  }

  /// 批量审核产品
  Future<List<DoctorProductModel>> batchReviewProducts(
    List<int> productIds,
    int status, {
    String? reviewNote,
  }) async {
    try {
      final requestData = {
        'product_ids': productIds,
        'status': status,
        if (reviewNote != null) 'admin_review_note': reviewNote,
      };

      final response = await http.post(
        Uri.parse(ApiConfig.batchReviewAdminProductsUrl),
        headers: _getAuthHeaders(),
        body: json.encode(requestData),
      );

      print('AdminProductService: 批量审核产品响应状态码: ${response.statusCode}');
      print('AdminProductService: 批量审核产品响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        final products = jsonList
            .map((json) => DoctorProductModel.fromJson(json))
            .toList();
        print('AdminProductService: 成功批量审核 ${products.length} 个产品');
        return products;
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '批量审核产品失败');
      }
    } catch (e) {
      print('AdminProductService: 批量审核产品失败: $e');
      rethrow;
    }
  }

  /// 获取产品统计
  Future<ProductStatisticsModel> getStatistics() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getAdminProductStatisticsUrl),
        headers: _getAuthHeaders(),
      );

      print('AdminProductService: 获取统计响应状态码: ${response.statusCode}');
      print('AdminProductService: 获取统计响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = response.body;
        if (responseBody.isEmpty) {
          print('AdminProductService: 统计响应体为空，返回默认统计');
          return const ProductStatisticsModel();
        }

        try {
          final jsonData = json.decode(responseBody);
          return ProductStatisticsModel.fromJson(jsonData);
        } catch (e) {
          print('AdminProductService: 解析统计数据失败: $e');
          print('AdminProductService: 统计数据: $responseBody');
          return const ProductStatisticsModel();
        }
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取统计信息失败');
      }
    } catch (e) {
      print('AdminProductService: 获取统计失败: $e');
      rethrow;
    }
  }

  /// 获取有产品的医生列表
  Future<List<Map<String, dynamic>>> getDoctorsList() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getAdminProductDoctorsUrl),
        headers: _getAuthHeaders(),
      );

      print('AdminProductService: 获取医生列表响应状态码: ${response.statusCode}');
      print('AdminProductService: 获取医生列表响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        final doctors = jsonList.cast<Map<String, dynamic>>();
        print('AdminProductService: 成功获取 ${doctors.length} 个医生');
        return doctors;
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取医生列表失败');
      }
    } catch (e) {
      print('AdminProductService: 获取医生列表失败: $e');
      rethrow;
    }
  }

  /// 获取指定医生的产品
  Future<List<DoctorProductModel>> getProductsByDoctor(int doctorId) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getAdminProductsByDoctorUrl(doctorId)),
        headers: _getAuthHeaders(),
      );

      print('AdminProductService: 获取医生产品响应状态码: ${response.statusCode}');
      print('AdminProductService: 获取医生产品响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        final products = jsonList
            .map((json) => DoctorProductModel.fromJson(json))
            .toList();
        print('AdminProductService: 成功获取医生 ${products.length} 个产品');
        return products;
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取医生产品失败');
      }
    } catch (e) {
      print('AdminProductService: 获取医生产品失败: $e');
      rethrow;
    }
  }
}
