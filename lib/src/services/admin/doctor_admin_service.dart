import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import '../../config/api/api_config.dart';
import '../../models/doctor_model.dart';
import '../../services/auth_service.dart';

/// 医生管理服务 - 管理员专用
class DoctorAdminService {
  static final DoctorAdminService _instance = DoctorAdminService._internal();
  factory DoctorAdminService() => _instance;
  DoctorAdminService._internal();

  /// 获取认证头
  Map<String, String> _getAuthHeaders() {
    final user = AuthService().currentUser;
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${user?.token ?? ''}',
    };
  }

  /// 创建简单的多语言对象格式
  Map<String, dynamic> _createSimpleMultiLangText(Map<String, String> data) {
    final result = <String, dynamic>{};
    final zhText = data['zh']?.trim() ?? '';
    final enText = data['en']?.trim() ?? '';
    final ugText = data['ug']?.trim() ?? '';

    // 只添加非空的语言字段，但至少要有中文
    if (zhText.isNotEmpty) result['zh'] = zhText;
    if (enText.isNotEmpty) result['en'] = enText;
    if (ugText.isNotEmpty) result['ug'] = ugText;

    // 如果中文为空但其他语言有内容，仍然添加中文字段
    if (!result.containsKey('zh') && result.isNotEmpty) {
      result['zh'] = zhText; // 可能为空字符串
    }

    // 如果完全没有内容，添加空的中文字段
    if (result.isEmpty) {
      result['zh'] = '';
    }

    return result;
  }

  /// 获取所有医生列表（管理员）
  Future<List<DoctorModel>> getAllDoctors() async {
    try {
      // 后端返回包含所有语言的多语言对象，不需要lang参数
      final response = await http.get(
        Uri.parse(ApiConfig.getAdminDoctorsUrl),
        headers: _getAuthHeaders(),
      );

      print('DoctorAdminService: 获取医生列表响应状态码: ${response.statusCode}');
      print('DoctorAdminService: 获取医生列表响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        // 管理员接口不需要语言过滤，使用默认解析
        final doctors = jsonList
            .map((json) => DoctorModel.fromJson(json))
            .toList();
        print('DoctorAdminService: 成功获取 ${doctors.length} 个医生');
        return doctors;
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else if (response.statusCode == 401) {
        throw Exception('未登录，请先登录');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取医生列表失败');
      }
    } catch (e) {
      print('DoctorAdminService: 获取医生列表失败: $e');
      rethrow;
    }
  }

  /// 获取指定医生信息
  Future<DoctorModel> getDoctorById(int doctorId) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getAdminDoctorDetailUrl(doctorId)),
        headers: _getAuthHeaders(),
      );

      print('DoctorAdminService: 获取医生详情响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return DoctorModel.fromJson(jsonData);
      } else if (response.statusCode == 404) {
        throw Exception('医生不存在');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取医生信息失败');
      }
    } catch (e) {
      print('DoctorAdminService: 获取医生详情失败: $e');
      rethrow;
    }
  }

  /// 获取指定医生的原始JSON数据（用于编辑页面获取多语言信息）
  Future<Map<String, dynamic>> getDoctorRawDataById(int doctorId) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getAdminDoctorDetailUrl(doctorId)),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData as Map<String, dynamic>;
      } else if (response.statusCode == 404) {
        throw Exception('医生不存在');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取医生信息失败');
      }
    } catch (e) {
      print('DoctorAdminService: 获取医生原始数据失败: $e');
      rethrow;
    }
  }

  /// 使用多语言数据创建医生
  Future<DoctorModel> createDoctorWithMultiLangData({
    required Map<String, String> nameData,
    required Map<String, String> specialtyData,
    required Map<String, String> specialtiesData,
    required Map<String, String> descriptionData,
    required Map<String, String> detailedInfoData,
    required String systemPrompt,
    String? avatarUrl,
    String? llmModelName,
    bool isActive = true,
    int yearsOfExperience = 0,
    double rating = 0.0,
    String? digitalHumanUrl,
    String? phone,
    String? address,
  }) async {
    try {
      // 验证name字段不能为空
      if (nameData['zh']?.trim().isEmpty != false) {
        throw Exception('医生姓名（中文）不能为空');
      }

      print('DoctorAdminService: nameData输入 = $nameData');
      final nameMultiLang = _createSimpleMultiLangText(nameData);
      print('DoctorAdminService: name多语言格式 = $nameMultiLang');

      // 测试最简单的格式 - 只发送必需字段
      final requestData = <String, dynamic>{'name': nameMultiLang};

      // 只有当字段不为空时才添加
      if (specialtyData['zh']?.isNotEmpty == true ||
          specialtyData['en']?.isNotEmpty == true ||
          specialtyData['ug']?.isNotEmpty == true) {
        requestData['specialty'] = _createSimpleMultiLangText(specialtyData);
      }

      if (specialtiesData['zh']?.isNotEmpty == true ||
          specialtiesData['en']?.isNotEmpty == true ||
          specialtiesData['ug']?.isNotEmpty == true) {
        requestData['specialties'] = _createSimpleMultiLangText(
          specialtiesData,
        );
      }

      if (descriptionData['zh']?.isNotEmpty == true ||
          descriptionData['en']?.isNotEmpty == true ||
          descriptionData['ug']?.isNotEmpty == true) {
        requestData['description'] = _createSimpleMultiLangText(
          descriptionData,
        );
      }

      if (detailedInfoData['zh']?.isNotEmpty == true ||
          detailedInfoData['en']?.isNotEmpty == true ||
          detailedInfoData['ug']?.isNotEmpty == true) {
        requestData['detailed_info'] = _createSimpleMultiLangText(
          detailedInfoData,
        );
      }

      if (systemPrompt.isNotEmpty) {
        requestData['system_prompt'] = systemPrompt;
      }

      // 添加其他字段
      if (avatarUrl?.isNotEmpty == true) requestData['avatar_url'] = avatarUrl;
      if (llmModelName?.isNotEmpty == true) {
        requestData['llm_model_name'] = llmModelName;
      }
      requestData['is_active'] = isActive;
      if (yearsOfExperience > 0) {
        requestData['years_of_experience'] = yearsOfExperience;
      }
      if (rating > 0) {
        requestData['rating'] = rating;
      }
      if (digitalHumanUrl?.isNotEmpty == true) {
        requestData['digital_human_url'] = digitalHumanUrl;
      }
      if (phone?.isNotEmpty == true) requestData['phone'] = phone;
      if (address?.isNotEmpty == true) requestData['address'] = address;

      // 移除null值
      requestData.removeWhere((key, value) => value == null);

      print('DoctorAdminService: 发送的请求数据: ${json.encode(requestData)}');

      final response = await http.post(
        Uri.parse(ApiConfig.createAdminDoctorUrl),
        headers: _getAuthHeaders(),
        body: json.encode(requestData),
      );

      print('DoctorAdminService: 创建医生响应状态码: ${response.statusCode}');
      print('DoctorAdminService: 创建医生响应内容: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        return DoctorModel.fromJson(jsonData);
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '创建医生失败');
      }
    } catch (e) {
      print('DoctorAdminService: 创建医生失败: $e');
      rethrow;
    }
  }

  /// 使用多语言数据更新医生
  Future<DoctorModel> updateDoctorWithMultiLangData({
    required int doctorId,
    Map<String, String>? nameData,
    Map<String, String>? specialtyData,
    Map<String, String>? specialtiesData,
    Map<String, String>? descriptionData,
    Map<String, String>? detailedInfoData,
    String? systemPrompt,
    String? avatarUrl,
    String? llmModelName,
    bool? isActive,
    int? yearsOfExperience,
    double? rating,
    String? digitalHumanUrl,
    String? phone,
    String? address,
  }) async {
    try {
      final requestData = <String, dynamic>{};

      // 添加多语言字段
      if (nameData != null) {
        requestData['name'] = _createSimpleMultiLangText(nameData);
      }
      if (specialtyData != null) {
        requestData['specialty'] = _createSimpleMultiLangText(specialtyData);
      }
      if (specialtiesData != null) {
        requestData['specialties'] = _createSimpleMultiLangText(
          specialtiesData,
        );
      }
      if (descriptionData != null) {
        requestData['description'] = _createSimpleMultiLangText(
          descriptionData,
        );
      }
      if (detailedInfoData != null) {
        requestData['detailed_info'] = _createSimpleMultiLangText(
          detailedInfoData,
        );
      }
      if (systemPrompt != null && systemPrompt.isNotEmpty) {
        requestData['system_prompt'] = systemPrompt;
      }

      // 添加其他字段
      if (avatarUrl?.isNotEmpty == true) requestData['avatar_url'] = avatarUrl;
      if (llmModelName?.isNotEmpty == true) {
        requestData['llm_model_name'] = llmModelName;
      }
      if (isActive != null) {
        requestData['is_active'] = isActive;
      }
      if (yearsOfExperience != null) {
        requestData['years_of_experience'] = yearsOfExperience;
      }
      if (rating != null) {
        requestData['rating'] = rating;
      }
      if (digitalHumanUrl?.isNotEmpty == true) {
        requestData['digital_human_url'] = digitalHumanUrl;
      }
      if (phone?.isNotEmpty == true) requestData['phone'] = phone;
      if (address?.isNotEmpty == true) requestData['address'] = address;

      // 移除null值
      requestData.removeWhere((key, value) => value == null);

      print('DoctorAdminService: 更新医生请求数据: ${json.encode(requestData)}');

      final response = await http.put(
        Uri.parse(ApiConfig.updateAdminDoctorUrl(doctorId)),
        headers: _getAuthHeaders(),
        body: json.encode(requestData),
      );

      print('DoctorAdminService: 更新医生响应状态码: ${response.statusCode}');
      print('DoctorAdminService: 更新医生响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return DoctorModel.fromJson(jsonData);
      } else if (response.statusCode == 404) {
        throw Exception('医生不存在');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '更新医生信息失败');
      }
    } catch (e) {
      print('DoctorAdminService: 更新医生失败: $e');
      rethrow;
    }
  }

  /// 上传医生头像
  Future<String> uploadDoctorAvatar(File imageFile) async {
    try {
      final user = AuthService().currentUser;
      final request = http.MultipartRequest(
        'POST',
        Uri.parse(ApiConfig.uploadAdminDoctorAvatarUrl),
      );

      // 添加认证头
      request.headers['Authorization'] = 'Bearer ${user?.token ?? ''}';

      // 添加文件
      final fileExtension = imageFile.path.split('.').last.toLowerCase();
      MediaType? mediaType;

      switch (fileExtension) {
        case 'jpg':
        case 'jpeg':
          mediaType = MediaType('image', 'jpeg');
          break;
        case 'png':
          mediaType = MediaType('image', 'png');
          break;
        case 'gif':
          mediaType = MediaType('image', 'gif');
          break;
        default:
          mediaType = MediaType('image', 'jpeg');
      }

      request.files.add(
        await http.MultipartFile.fromPath(
          'avatar',
          imageFile.path,
          contentType: mediaType,
        ),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('DoctorAdminService: 上传头像响应状态码: ${response.statusCode}');
      print('DoctorAdminService: 上传头像响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        if (jsonData['code'] == 200) {
          return jsonData['data']['avatar_url'];
        } else {
          throw Exception(jsonData['message'] ?? '上传头像失败');
        }
      } else if (response.statusCode == 400) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '文件格式或大小错误');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '上传头像失败');
      }
    } catch (e) {
      print('DoctorAdminService: 上传头像失败: $e');
      rethrow;
    }
  }
}
