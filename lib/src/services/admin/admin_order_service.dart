import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../config/api/api_config.dart';
import '../../models/doctor_product_model.dart';
import '../auth_service.dart';
import '../language_service.dart';

/// 管理员订单服务
class AdminOrderService {
  /// 获取认证头部
  Map<String, String> _getAuthHeaders({bool needAuth = true}) {
    final headers = {'Content-Type': 'application/json'};

    if (needAuth) {
      final user = AuthService().currentUser;
      if (user?.token != null) {
        headers['Authorization'] = 'Bearer ${user!.token}';
      }
    }

    return headers;
  }

  /// 获取所有订单
  Future<List<ProductOrderModel>> getAllOrders({
    int? payStatus,
    int? orderStatus,
    int? doctorId,
    int? userId,
    int? productId,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      // 构建查询参数
      final queryParams = <String, String>{};
      if (payStatus != null) queryParams['pay_status'] = payStatus.toString();
      if (orderStatus != null) {
        queryParams['order_status'] = orderStatus.toString();
      }
      if (doctorId != null) queryParams['doctor_id'] = doctorId.toString();
      if (userId != null) queryParams['user_id'] = userId.toString();
      if (productId != null) queryParams['product_id'] = productId.toString();
      queryParams['page'] = page.toString();
      queryParams['page_size'] = pageSize.toString();

      final uri = Uri.parse(
        ApiConfig.getAdminAllOrdersUrl,
      ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await http.get(uri, headers: _getAuthHeaders());

      print('AdminOrderService: 获取所有订单响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        final orders = jsonList
            .map((json) => ProductOrderModel.fromJson(json))
            .toList();
        print('AdminOrderService: 成功获取 ${orders.length} 个订单');
        return orders;
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取订单列表失败');
      }
    } catch (e) {
      print('AdminOrderService: 获取订单列表失败: $e');
      rethrow;
    }
  }

  /// 获取订单统计信息
  Future<OrderStatisticsModel> getStatistics() async {
    try {
      // 添加语言参数
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getAdminOrderStatisticsUrl,
      ).replace(queryParameters: {'lang': languageCode});

      final response = await http.get(uri, headers: _getAuthHeaders());

      print('AdminOrderService: 获取统计信息响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return OrderStatisticsModel.fromJson(jsonData);
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取统计信息失败');
      }
    } catch (e) {
      print('AdminOrderService: 获取统计信息失败: $e');
      rethrow;
    }
  }

  /// 获取医生列表
  Future<List<Map<String, dynamic>>> getDoctorsList() async {
    try {
      // 获取当前语言设置
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getDoctorsUrl,
      ).replace(queryParameters: {'lang': languageCode});

      final response = await http.get(uri, headers: _getAuthHeaders());

      print('AdminOrderService: 获取医生列表响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        return jsonList.cast<Map<String, dynamic>>();
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取医生列表失败');
      }
    } catch (e) {
      print('AdminOrderService: 获取医生列表失败: $e');
      rethrow;
    }
  }

  /// 更新订单状态
  Future<void> updateOrderStatus(
    int orderId,
    int status, {
    String? note,
  }) async {
    try {
      final uri = Uri.parse(
        ApiConfig.updateAdminOrderStatusUrl(orderId),
      ).replace(queryParameters: {'order_status': status.toString()});

      final response = await http.put(uri, headers: _getAuthHeaders());

      print('AdminOrderService: 更新订单状态响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('AdminOrderService: 订单状态更新成功');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('订单不存在');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '更新订单状态失败');
      }
    } catch (e) {
      print('AdminOrderService: 更新订单状态失败: $e');
      rethrow;
    }
  }

  /// 批量更新订单状态（逐个调用单个更新接口）
  Future<void> batchUpdateOrderStatus(
    List<int> orderIds,
    int status, {
    String? note,
  }) async {
    try {
      // 由于API没有提供批量更新接口，我们逐个更新
      for (final orderId in orderIds) {
        await updateOrderStatus(orderId, status, note: note);
      }
      print('AdminOrderService: 批量更新订单状态成功');
    } catch (e) {
      print('AdminOrderService: 批量更新订单状态失败: $e');
      rethrow;
    }
  }

  /// 删除订单
  Future<void> deleteOrder(int orderId) async {
    try {
      final response = await http.delete(
        Uri.parse(ApiConfig.deleteAdminOrderUrl(orderId)),
        headers: _getAuthHeaders(),
      );

      print('AdminOrderService: 删除订单响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('AdminOrderService: 订单删除成功');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('订单不存在');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '删除订单失败');
      }
    } catch (e) {
      print('AdminOrderService: 删除订单失败: $e');
      rethrow;
    }
  }

  /// 获取指定订单详情
  Future<ProductOrderModel> getOrderDetail(int orderId) async {
    try {
      // 添加语言参数
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getAdminOrderDetailUrl(orderId),
      ).replace(queryParameters: {'lang': languageCode});

      final response = await http.get(uri, headers: _getAuthHeaders());

      print('AdminOrderService: 获取订单详情响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ProductOrderModel.fromJson(jsonData);
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('订单不存在');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取订单详情失败');
      }
    } catch (e) {
      print('AdminOrderService: 获取订单详情失败: $e');
      rethrow;
    }
  }

  /// 更新支付状态
  Future<void> updatePayStatus(int orderId, int payStatus) async {
    try {
      final uri = Uri.parse(
        ApiConfig.updateAdminPayStatusUrl(orderId),
      ).replace(queryParameters: {'pay_status': payStatus.toString()});

      final response = await http.put(uri, headers: _getAuthHeaders());

      print('AdminOrderService: 更新支付状态响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('AdminOrderService: 支付状态更新成功');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('订单不存在');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '更新支付状态失败');
      }
    } catch (e) {
      print('AdminOrderService: 更新支付状态失败: $e');
      rethrow;
    }
  }

  /// 管理员代发货
  Future<void> adminShipOrder({
    required int orderId,
    required String trackingNumber,
    String? shippingCompany,
    String? shippingNote,
  }) async {
    try {
      final requestBody = {
        'tracking_number': trackingNumber,
        if (shippingCompany != null && shippingCompany.isNotEmpty)
          'shipping_company': shippingCompany,
        if (shippingNote != null && shippingNote.isNotEmpty)
          'shipping_note': shippingNote,
      };

      final response = await http.post(
        Uri.parse(ApiConfig.adminShipOrderUrl(orderId)),
        headers: _getAuthHeaders(),
        body: json.encode(requestBody),
      );

      print('AdminOrderService: 管理员代发货响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('AdminOrderService: 管理员代发货成功');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要管理员权限');
      } else if (response.statusCode == 404) {
        throw Exception('订单不存在');
      } else if (response.statusCode == 400) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '发货失败，请检查订单状态');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '管理员代发货失败');
      }
    } catch (e) {
      print('AdminOrderService: 管理员代发货失败: $e');
      rethrow;
    }
  }

  /// 检查管理员权限
  bool checkAdminPermission() {
    final user = AuthService().currentUser;
    return user != null && user.isAdmin == true;
  }
}

/// 订单统计信息模型
class OrderStatisticsModel {
  final int totalOrders;
  final double totalSales;
  final int pendingPayment;
  final int pendingShipment;
  final int shipped;
  final int completed;
  final int cancelled;
  final int todayOrders;
  final double todaySales;

  const OrderStatisticsModel({
    this.totalOrders = 0,
    this.totalSales = 0.0,
    this.pendingPayment = 0,
    this.pendingShipment = 0,
    this.shipped = 0,
    this.completed = 0,
    this.cancelled = 0,
    this.todayOrders = 0,
    this.todaySales = 0.0,
  });

  factory OrderStatisticsModel.fromJson(Map<String, dynamic> json) {
    final orderStatusStats = json['order_status_stats'] ?? {};
    final salesStats = json['sales_stats'] ?? {};

    return OrderStatisticsModel(
      totalOrders: json['total_orders'] ?? 0,
      totalSales: _parseDouble(salesStats['total_sales']),
      pendingPayment: orderStatusStats['pending_payment'] ?? 0,
      pendingShipment: orderStatusStats['pending_shipment'] ?? 0,
      shipped: orderStatusStats['shipped'] ?? 0,
      completed: orderStatusStats['completed'] ?? 0,
      cancelled: orderStatusStats['cancelled'] ?? 0,
      todayOrders: salesStats['today_orders'] ?? 0,
      todaySales: _parseDouble(salesStats['today_sales']),
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }
}
