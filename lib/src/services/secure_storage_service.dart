import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';

/// 安全存储服务 - 管理敏感数据的加密存储
class SecureStorageService {
  // 单例模式
  static final SecureStorageService _instance =
      SecureStorageService._internal();

  factory SecureStorageService() => _instance;

  SecureStorageService._internal();

  // 安全存储实例
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(),
    iOptions: IOSOptions(
      // 设置iOS钥匙串的可访问性
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // 存储键名
  static const String _keyUserId = 'user_id';
  static const String _keyUsername = 'username';
  static const String _keyPhone = 'phone';
  static const String _keyToken = 'token';
  static const String _keyLoginTime = 'login_time';
  static const String _keyTokenExpiry = 'token_expiry';
  static const String _keyVip = 'vip';
  static const String _keyVipData = 'vip_data';

  /// 保存用户登录凭证
  Future<void> saveUserCredentials(
    UserModel user, {
    Duration? tokenExpiry,
  }) async {
    try {
      final loginTime = DateTime.now().millisecondsSinceEpoch.toString();

      // 计算token过期时间（默认30天）
      final expiryTime = DateTime.now()
          .add(tokenExpiry ?? const Duration(days: 30))
          .millisecondsSinceEpoch
          .toString();

      await Future.wait([
        _storage.write(key: _keyUserId, value: user.id.toString()),
        _storage.write(key: _keyUsername, value: user.username),
        _storage.write(key: _keyPhone, value: user.phone),
        _storage.write(key: _keyToken, value: user.token),
        _storage.write(key: _keyLoginTime, value: loginTime),
        _storage.write(key: _keyTokenExpiry, value: expiryTime),
        _storage.write(key: _keyVip, value: user.vip?.toString() ?? 'false'),
        _storage.write(
          key: _keyVipData,
          value: user.vipData != null
              ? jsonEncode(user.vipData!.toJson())
              : null,
        ),
      ]);
    } catch (e) {
      throw Exception('保存用户凭证失败: $e');
    }
  }

  /// 获取保存的用户凭证
  Future<UserModel?> getSavedUserCredentials() async {
    try {
      final values = await Future.wait([
        _storage.read(key: _keyUserId),
        _storage.read(key: _keyUsername),
        _storage.read(key: _keyPhone),
        _storage.read(key: _keyToken),
        _storage.read(key: _keyTokenExpiry),
        _storage.read(key: _keyVip),
        _storage.read(key: _keyVipData),
      ]);

      final userId = values[0];
      final username = values[1];
      final phone = values[2];
      final token = values[3];
      final expiryTime = values[4];
      final vipString = values[5];
      final vipDataString = values[6];

      // 检查必要字段是否存在
      if (userId == null ||
          username == null ||
          phone == null ||
          token == null) {
        return null;
      }

      // 检查token是否过期
      if (expiryTime != null) {
        final expiry = DateTime.fromMillisecondsSinceEpoch(
          int.parse(expiryTime),
        );
        if (DateTime.now().isAfter(expiry)) {
          // Token已过期，清除凭证
          await clearUserCredentials();
          return null;
        }
      }

      // 解析VIP信息
      bool? vip;
      if (vipString != null) {
        vip = vipString.toLowerCase() == 'true';
      }

      VipData? vipData;
      if (vipDataString != null) {
        try {
          final vipDataJson = jsonDecode(vipDataString);
          vipData = VipData.fromJson(vipDataJson);
        } catch (e) {
          // VIP数据解析失败，忽略
        }
      }

      return UserModel(
        id: int.parse(userId),
        username: username,
        phone: phone,
        token: token,
        vip: vip,
        vipData: vipData,
      );
    } catch (e) {
      // 出现错误时清除可能损坏的数据
      await clearUserCredentials();
      return null;
    }
  }

  /// 检查是否存在有效的用户凭证
  Future<bool> hasValidCredentials() async {
    final user = await getSavedUserCredentials();
    return user != null;
  }

  /// 清除用户凭证
  Future<void> clearUserCredentials() async {
    try {
      await Future.wait([
        _storage.delete(key: _keyUserId),
        _storage.delete(key: _keyUsername),
        _storage.delete(key: _keyPhone),
        _storage.delete(key: _keyToken),
        _storage.delete(key: _keyLoginTime),
        _storage.delete(key: _keyTokenExpiry),
        _storage.delete(key: _keyVip),
        _storage.delete(key: _keyVipData),
      ]);
    } catch (e) {
      throw Exception('清除用户凭证失败: $e');
    }
  }

  /// 更新token过期时间
  Future<void> refreshTokenExpiry({Duration? newExpiry}) async {
    try {
      final expiryTime = DateTime.now()
          .add(newExpiry ?? const Duration(days: 30))
          .millisecondsSinceEpoch
          .toString();

      await _storage.write(key: _keyTokenExpiry, value: expiryTime);
    } catch (e) {
      throw Exception('更新token过期时间失败: $e');
    }
  }

  /// 获取登录时间
  Future<DateTime?> getLoginTime() async {
    try {
      final loginTimeStr = await _storage.read(key: _keyLoginTime);
      if (loginTimeStr != null) {
        return DateTime.fromMillisecondsSinceEpoch(int.parse(loginTimeStr));
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 获取token过期时间
  Future<DateTime?> getTokenExpiryTime() async {
    try {
      final expiryTimeStr = await _storage.read(key: _keyTokenExpiry);
      if (expiryTimeStr != null) {
        return DateTime.fromMillisecondsSinceEpoch(int.parse(expiryTimeStr));
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
