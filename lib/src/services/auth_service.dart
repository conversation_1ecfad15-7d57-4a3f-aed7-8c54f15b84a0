import '../models/user_model.dart';
import 'secure_storage_service.dart';
import 'user_info_manager_service.dart';

/// 认证服务 - 管理用户登录状态
class AuthService {
  // 单例模式
  static final AuthService _instance = AuthService._internal();

  factory AuthService() => _instance;

  AuthService._internal();

  // 当前登录用户
  UserModel? _currentUser;
  // 用户登录状态
  bool _isLoggedIn = false;

  // 安全存储服务
  final _secureStorage = SecureStorageService();

  /// 获取当前用户
  UserModel? get currentUser => _currentUser;

  /// 获取登录状态
  bool get isLoggedIn => _isLoggedIn;

  /// 应用启动时初始化认证状态
  Future<void> initialize() async {
    try {
      // 尝试从安全存储中恢复用户凭证
      final savedUser = await _secureStorage.getSavedUserCredentials();
      if (savedUser != null) {
        // 自动恢复登录状态
        _currentUser = savedUser;
        _isLoggedIn = true;

        // 用户状态已恢复，现在获取最新的用户信息（包括doctor_id和is_doctor）
        await _refreshUserInfoAfterRestore();
      }
    } catch (e) {
      // 如果恢复失败，确保状态清理
      await _clearLoginState();
    }
  }

  /// 用户登录
  Future<void> login(UserModel user, {Duration? tokenExpiry}) async {
    try {
      // 更新内存状态
      _currentUser = user;
      _isLoggedIn = true;

      // 用户登录成功

      // 持久化存储用户凭证
      await _secureStorage.saveUserCredentials(user, tokenExpiry: tokenExpiry);

      // 登录成功后，通过回调通知获取完整用户信息
      // 为了避免循环依赖，这个调用将在更高层进行
    } catch (e) {
      // 登录失败时清理状态
      await _clearLoginState();
      throw Exception('登录状态保存失败: $e');
    }
  }

  /// 退出登录
  Future<void> logout() async {
    try {
      // 清除本地存储的用户凭证
      await _secureStorage.clearUserCredentials();
    } catch (e) {
      // 即使清除存储失败，也要清理内存状态
      // 在生产环境中应该使用日志框架而不是print
      // print('清除存储凭证时出错: $e');
    } finally {
      // 清理内存状态
      await _clearLoginState();

      // 退出登录成功后，需要在更高层清除用户信息和头像
      // 为了避免循环依赖，这个调用将在调用方进行
    }
  }

  /// 检查是否有有效的保存凭证
  Future<bool> hasValidSavedCredentials() async {
    return await _secureStorage.hasValidCredentials();
  }

  /// 刷新token过期时间
  Future<void> refreshTokenExpiry({Duration? newExpiry}) async {
    if (_isLoggedIn) {
      await _secureStorage.refreshTokenExpiry(newExpiry: newExpiry);
    }
  }

  /// 获取用户登录时间
  Future<DateTime?> getUserLoginTime() async {
    return await _secureStorage.getLoginTime();
  }

  /// 获取token过期时间
  Future<DateTime?> getTokenExpiryTime() async {
    return await _secureStorage.getTokenExpiryTime();
  }

  /// 清理登录状态（内部方法）
  Future<void> _clearLoginState() async {
    _currentUser = null;
    _isLoggedIn = false;

    // 用户状态已清理
  }

  /// 验证当前登录状态是否仍然有效
  Future<bool> validateCurrentSession() async {
    if (!_isLoggedIn || _currentUser == null) {
      return false;
    }

    // 检查存储中的凭证是否仍然有效
    final isValid = await _secureStorage.hasValidCredentials();
    if (!isValid) {
      // 如果存储中的凭证无效，清理当前状态
      await _clearLoginState();
      return false;
    }

    return true;
  }

  /// 验证token有效性（简单的接口调用测试）
  Future<bool> validateTokenWithServer() async {
    if (!_isLoggedIn || _currentUser == null) {
      return false;
    }

    try {
      // 可以调用一个简单的API接口来验证token
      // 这里暂时只检查本地存储的有效性
      return await _secureStorage.hasValidCredentials();
    } catch (e) {
      // 验证失败，自动退出登录
      await logout();
      return false;
    }
  }

  /// 在恢复用户状态后刷新用户信息
  /// 这确保了应用启动时能获取到最新的doctor_id和is_doctor信息
  /// 改为异步执行，不阻塞应用启动
  Future<void> _refreshUserInfoAfterRestore() async {
    // 异步执行用户信息刷新，不阻塞应用启动
    _refreshUserInfoInBackground();
  }

  /// 在后台异步刷新用户信息，不阻塞应用启动
  void _refreshUserInfoInBackground() {
    // 使用Future.microtask确保在下一个事件循环中执行
    Future.microtask(() async {
      try {
        // 使用UserInfoManagerService来获取最新的用户信息
        final userInfoManager = UserInfoManagerService();
        await userInfoManager.handleUserLogin(isBackgroundFetch: true);

        // 获取最新的用户资料
        final userProfile = userInfoManager.currentUserProfile;
        if (userProfile != null && _currentUser != null) {
          // 更新AuthService中的用户信息，特别是doctor_id和is_doctor字段
          _currentUser = _currentUser!.copyWith(
            isDoctor: userProfile.isDoctor,
            doctorId: userProfile.doctorId,
          );

          // 保存更新后的用户信息到本地存储
          await _secureStorage.saveUserCredentials(_currentUser!);
        }
      } catch (e) {
        // 如果获取用户信息失败，不影响应用启动，只是记录错误
        print('AuthService: 后台刷新用户信息失败: $e');
      }
    });
  }
}
