import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api/api_config.dart';
import '../models/doctor_product_model.dart';
import '../services/auth_service.dart';
import '../services/language_service.dart';

/// 医生产品管理服务
class DoctorProductService {
  static final DoctorProductService _instance =
      DoctorProductService._internal();
  factory DoctorProductService() => _instance;
  DoctorProductService._internal();

  /// 获取认证头
  Map<String, String> _getAuthHeaders() {
    final user = AuthService().currentUser;
    if (user == null) {
      throw Exception('用户未登录');
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${user.token}',
    };
  }

  /// 创建简单的多语言对象格式
  Map<String, dynamic> _createSimpleMultiLangText(Map<String, String> data) {
    final result = <String, dynamic>{};
    final zhText = data['zh']?.trim() ?? '';
    final enText = data['en']?.trim() ?? '';
    final ugText = data['ug']?.trim() ?? '';

    // 只添加非空的语言字段，但至少要有中文
    if (zhText.isNotEmpty) result['zh'] = zhText;
    if (enText.isNotEmpty) result['en'] = enText;
    if (ugText.isNotEmpty) result['ug'] = ugText;

    // 如果中文为空但其他语言有内容，仍然添加中文字段
    if (!result.containsKey('zh') && result.isNotEmpty) {
      result['zh'] = zhText; // 可能为空字符串
    }

    // 如果完全没有内容，添加空的中文字段
    if (result.isEmpty) {
      result['zh'] = '';
    }

    return result;
  }

  /// 使用多语言数据创建产品
  Future<DoctorProductModel> createProductWithMultiLangData({
    required Map<String, String> nameData,
    Map<String, String>? descriptionData,
    Map<String, String>? detailedDescriptionData,
    Map<String, String>? manufacturerData,
    Map<String, String>? categoryData,
    required double price,
    double? originalPrice,
    required int inventoryCount,
    String? mainImageUrl,
    List<String>? imageUrls,
  }) async {
    try {
      final requestData = <String, dynamic>{
        'name': _createSimpleMultiLangText(nameData),
        'price': price,
        'inventory_count': inventoryCount,
      };

      // 添加可选的多语言字段
      if (descriptionData != null) {
        requestData['description'] = _createSimpleMultiLangText(
          descriptionData,
        );
      }
      if (detailedDescriptionData != null) {
        requestData['detailed_description'] = _createSimpleMultiLangText(
          detailedDescriptionData,
        );
      }
      if (manufacturerData != null) {
        requestData['manufacturer'] = _createSimpleMultiLangText(
          manufacturerData,
        );
      }
      if (categoryData != null) {
        requestData['category'] = _createSimpleMultiLangText(categoryData);
      }

      // 添加其他字段
      if (originalPrice != null) requestData['original_price'] = originalPrice;
      if (mainImageUrl?.isNotEmpty == true) {
        requestData['main_image_url'] = mainImageUrl;
      }
      if (imageUrls?.isNotEmpty == true) requestData['image_urls'] = imageUrls;

      print('DoctorProductService: 创建产品请求数据: ${json.encode(requestData)}');

      final response = await http.post(
        Uri.parse(ApiConfig.createDoctorProductUrl),
        headers: _getAuthHeaders(),
        body: json.encode(requestData),
      );

      print('DoctorProductService: 创建产品响应状态码: ${response.statusCode}');
      print('DoctorProductService: 创建产品响应内容: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonData = json.decode(response.body);
        return DoctorProductModel.fromJson(jsonData);
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要医生权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '创建产品失败');
      }
    } catch (e) {
      print('DoctorProductService: 创建产品失败: $e');
      rethrow;
    }
  }

  /// 获取产品列表
  Future<List<DoctorProductModel>> getProducts({int? status}) async {
    try {
      // 构建查询参数
      final queryParams = <String, String>{};

      // 医生端不需要lang参数，后端返回完整多语言数据
      if (status != null) {
        queryParams['status'] = status.toString();
      }

      final uri = Uri.parse(ApiConfig.getDoctorProductsUrl);
      final finalUri = uri.replace(queryParameters: queryParams);
      String url = finalUri.toString();

      print('DoctorProductService: 请求URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: _getAuthHeaders(),
      );

      print('DoctorProductService: 获取产品列表响应状态码: ${response.statusCode}');
      print('DoctorProductService: 获取产品列表响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = response.body;
        if (responseBody.isEmpty) {
          print('DoctorProductService: 响应体为空，返回空列表');
          return [];
        }

        final List<dynamic> jsonList = json.decode(responseBody);
        final products = <DoctorProductModel>[];

        for (int i = 0; i < jsonList.length; i++) {
          try {
            final product = DoctorProductModel.fromJson(jsonList[i]);
            products.add(product);
          } catch (e) {
            print('DoctorProductService: 解析第$i个产品失败: $e');
            print('DoctorProductService: 产品数据: ${jsonList[i]}');
            // 继续处理其他产品，不中断整个流程
          }
        }

        print('DoctorProductService: 成功获取 ${products.length} 个产品');
        return products;
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要医生权限');
      } else {
        try {
          final errorData = json.decode(response.body);
          throw Exception(errorData['detail'] ?? '获取产品列表失败');
        } catch (e) {
          throw Exception('获取产品列表失败，状态码: ${response.statusCode}');
        }
      }
    } catch (e) {
      print('DoctorProductService: 获取产品列表失败: $e');
      if (e.toString().contains('NoSuchMethodError')) {
        print('DoctorProductService: NoSuchMethodError详情: $e');
        // 返回空列表而不是抛出异常，避免页面崩溃
        return [];
      }
      rethrow;
    }
  }

  /// 获取产品详情
  Future<DoctorProductModel> getProductById(int productId) async {
    try {
      // 添加语言参数
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getDoctorProductDetailUrl(productId),
      ).replace(queryParameters: {'lang': languageCode});

      final response = await http.get(uri, headers: _getAuthHeaders());

      print('DoctorProductService: 获取产品详情响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return DoctorProductModel.fromJson(jsonData);
      } else if (response.statusCode == 404) {
        throw Exception('产品不存在');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要医生权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取产品信息失败');
      }
    } catch (e) {
      print('DoctorProductService: 获取产品详情失败: $e');
      rethrow;
    }
  }

  /// 获取产品的原始JSON数据（用于编辑页面获取多语言信息）
  Future<Map<String, dynamic>> getProductRawDataById(int productId) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getDoctorProductDetailUrl(productId)),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return jsonData as Map<String, dynamic>;
      } else if (response.statusCode == 404) {
        throw Exception('产品不存在');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要医生权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取产品信息失败');
      }
    } catch (e) {
      print('DoctorProductService: 获取产品原始数据失败: $e');
      rethrow;
    }
  }

  /// 使用多语言数据更新产品
  Future<DoctorProductModel> updateProductWithMultiLangData({
    required int productId,
    Map<String, String>? nameData,
    Map<String, String>? descriptionData,
    Map<String, String>? detailedDescriptionData,
    Map<String, String>? manufacturerData,
    Map<String, String>? categoryData,
    double? price,
    double? originalPrice,
    int? inventoryCount,
    String? mainImageUrl,
    List<String>? imageUrls,
  }) async {
    try {
      final requestData = <String, dynamic>{};

      // 添加多语言字段
      if (nameData != null) {
        requestData['name'] = _createSimpleMultiLangText(nameData);
      }
      if (descriptionData != null) {
        requestData['description'] = _createSimpleMultiLangText(
          descriptionData,
        );
      }
      if (detailedDescriptionData != null) {
        requestData['detailed_description'] = _createSimpleMultiLangText(
          detailedDescriptionData,
        );
      }
      if (manufacturerData != null) {
        requestData['manufacturer'] = _createSimpleMultiLangText(
          manufacturerData,
        );
      }
      if (categoryData != null) {
        requestData['category'] = _createSimpleMultiLangText(categoryData);
      }

      // 添加其他字段
      if (price != null) requestData['price'] = price;
      if (originalPrice != null) requestData['original_price'] = originalPrice;
      if (inventoryCount != null) {
        requestData['inventory_count'] = inventoryCount;
      }
      if (mainImageUrl?.isNotEmpty == true) {
        requestData['main_image_url'] = mainImageUrl;
      }
      if (imageUrls?.isNotEmpty == true) requestData['image_urls'] = imageUrls;

      // 移除null值
      requestData.removeWhere((key, value) => value == null);

      print('DoctorProductService: 更新产品请求数据: ${json.encode(requestData)}');

      final response = await http.put(
        Uri.parse(ApiConfig.updateDoctorProductUrl(productId)),
        headers: _getAuthHeaders(),
        body: json.encode(requestData),
      );

      print('DoctorProductService: 更新产品响应状态码: ${response.statusCode}');
      print('DoctorProductService: 更新产品响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return DoctorProductModel.fromJson(jsonData);
      } else if (response.statusCode == 404) {
        throw Exception('产品不存在');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要医生权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '更新产品失败');
      }
    } catch (e) {
      print('DoctorProductService: 更新产品失败: $e');
      rethrow;
    }
  }

  /// 更新产品（旧版本，保持向后兼容）
  Future<DoctorProductModel> updateProduct(
    int productId,
    Map<String, dynamic> updateData,
  ) async {
    try {
      // 移除null值
      updateData.removeWhere((key, value) => value == null);

      final response = await http.put(
        Uri.parse(ApiConfig.updateDoctorProductUrl(productId)),
        headers: _getAuthHeaders(),
        body: json.encode(updateData),
      );

      print('DoctorProductService: 更新产品响应状态码: ${response.statusCode}');
      print('DoctorProductService: 更新产品响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return DoctorProductModel.fromJson(jsonData);
      } else if (response.statusCode == 404) {
        throw Exception('产品不存在');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要医生权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '更新产品信息失败');
      }
    } catch (e) {
      print('DoctorProductService: 更新产品失败: $e');
      rethrow;
    }
  }

  /// 删除产品
  Future<void> deleteProduct(int productId) async {
    try {
      final response = await http.delete(
        Uri.parse(ApiConfig.deleteDoctorProductUrl(productId)),
        headers: _getAuthHeaders(),
      );

      print('DoctorProductService: 删除产品响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        // 删除成功
        return;
      } else if (response.statusCode == 404) {
        throw Exception('产品不存在');
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要医生权限');
      } else if (response.statusCode == 400) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '删除失败，可能有未完成的订单');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '删除产品失败');
      }
    } catch (e) {
      print('DoctorProductService: 删除产品失败: $e');
      rethrow;
    }
  }

  /// 获取产品统计
  Future<ProductStatisticsModel> getStatistics() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getDoctorProductStatisticsUrl),
        headers: _getAuthHeaders(),
      );

      print('DoctorProductService: 获取统计响应状态码: ${response.statusCode}');
      print('DoctorProductService: 获取统计响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = response.body;
        if (responseBody.isEmpty) {
          print('DoctorProductService: 统计响应体为空，返回默认统计');
          return const ProductStatisticsModel();
        }

        try {
          final jsonData = json.decode(responseBody);
          return ProductStatisticsModel.fromJson(jsonData);
        } catch (e) {
          print('DoctorProductService: 解析统计数据失败: $e');
          print('DoctorProductService: 统计数据: $responseBody');
          return const ProductStatisticsModel();
        }
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要医生权限');
      } else {
        try {
          final errorData = json.decode(response.body);
          throw Exception(errorData['detail'] ?? '获取统计信息失败');
        } catch (e) {
          throw Exception('获取统计信息失败，状态码: ${response.statusCode}');
        }
      }
    } catch (e) {
      print('DoctorProductService: 获取统计失败: $e');
      if (e.toString().contains('NoSuchMethodError')) {
        print('DoctorProductService: 统计NoSuchMethodError详情: $e');
        // 返回默认统计而不是抛出异常
        return const ProductStatisticsModel();
      }
      rethrow;
    }
  }

  /// 获取订单列表
  Future<List<ProductOrderModel>> getOrders({int? productId}) async {
    try {
      String url = ApiConfig.getDoctorProductOrdersUrl;
      if (productId != null) {
        url += '?product_id=$productId';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: _getAuthHeaders(),
      );

      print('DoctorProductService: 获取订单列表响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        final orders = jsonList
            .map((json) => ProductOrderModel.fromJson(json))
            .toList();
        print('DoctorProductService: 成功获取 ${orders.length} 个订单');
        return orders;
      } else if (response.statusCode == 403) {
        throw Exception('权限不足，需要医生权限');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['detail'] ?? '获取订单列表失败');
      }
    } catch (e) {
      print('DoctorProductService: 获取订单列表失败: $e');
      rethrow;
    }
  }

  /// 检查用户是否有医生权限
  bool checkDoctorPermission() {
    final user = AuthService().currentUser;
    return user != null && user.isDoctor == true && user.doctorId != null;
  }
}
