import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cart_model.dart';
import 'cart_service.dart';
import 'auth_service.dart';

/// 购物车缓存管理服务 - 提供购物车数据的缓存机制和后台加载
class CartCacheService extends ChangeNotifier {
  static final CartCacheService _instance = CartCacheService._internal();

  /// 获取购物车缓存服务单例实例
  factory CartCacheService() => _instance;

  CartCacheService._internal();

  /// 缓存键名
  static const String _cartCacheKey = 'cart_cache';
  static const String _cartCacheTimeKey = 'cart_cache_time';

  /// 缓存有效期（分钟）- 购物车数据变化频繁，30分钟过期
  static const int _cacheValidityMinutes = 30;

  /// 服务实例
  final CartService _cartService = CartService();
  final AuthService _authService = AuthService();

  /// 内存缓存
  CartListResponseModel? _cachedCart;
  DateTime? _lastFetchTime;
  bool _isLoading = false;
  String? _lastError;

  /// 获取当前缓存的购物车数据
  CartListResponseModel? get cachedCart => _cachedCart;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 最后的错误信息
  String? get lastError => _lastError;

  /// 是否有缓存数据
  bool get hasCachedData => _cachedCart != null;

  /// 初始化缓存服务
  Future<void> initialize() async {
    await _loadFromLocalCache();
  }

  /// 同步获取缓存的购物车数据（如果有的话）
  CartListResponseModel? getCachedCartSync() {
    return _cachedCart;
  }

  /// 获取购物车数据（优先使用缓存，后台刷新）
  Future<CartListResponseModel?> getCartData({
    bool forceRefresh = false,
  }) async {
    // 如果用户未登录，清除缓存并返回null
    if (_authService.currentUser == null) {
      await clearCache();
      return null;
    }

    // 如果不是强制刷新且有内存缓存，直接返回
    if (!forceRefresh && _cachedCart != null) {
      // 后台检查是否需要刷新
      _backgroundRefresh();
      return _cachedCart;
    }

    // 如果没有缓存或需要强制刷新，立即加载
    return await _loadCartData();
  }

  /// 立即加载购物车数据
  Future<CartListResponseModel?> _loadCartData() async {
    if (_isLoading) return _cachedCart;

    _isLoading = true;
    _lastError = null;
    notifyListeners();

    try {
      final cartData = await _cartService.getCartList();

      // 更新缓存
      _cachedCart = cartData;
      _lastFetchTime = DateTime.now();

      // 保存到本地缓存
      await _saveToLocalCache(cartData);

      _isLoading = false;
      notifyListeners();

      return cartData;
    } catch (e) {
      _lastError = e.toString();
      _isLoading = false;
      notifyListeners();

      // 如果加载失败，返回现有缓存
      return _cachedCart;
    }
  }

  /// 后台刷新（不阻塞UI）
  void _backgroundRefresh() {
    // 检查是否需要刷新
    if (!_shouldRefreshCache()) return;

    // 异步后台刷新
    Future.microtask(() async {
      try {
        final cartData = await _cartService.getCartList();

        // 只有数据真正变化时才更新缓存和通知UI
        if (_hasDataChanged(cartData)) {
          _cachedCart = cartData;
          _lastFetchTime = DateTime.now();
          await _saveToLocalCache(cartData);
          notifyListeners();
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
        _lastError = e.toString();
      }
    });
  }

  /// 检查是否需要刷新缓存
  bool _shouldRefreshCache() {
    if (_lastFetchTime == null) return true;

    final now = DateTime.now();
    final difference = now.difference(_lastFetchTime!);
    return difference.inMinutes >= _cacheValidityMinutes;
  }

  /// 检查数据是否发生变化
  bool _hasDataChanged(CartListResponseModel? newCart) {
    if (_cachedCart == null && newCart == null) return false;
    if (_cachedCart == null || newCart == null) return true;

    // 比较商品数量和统计信息
    if (_cachedCart!.items.length != newCart.items.length) {
      return true;
    }
    if (_cachedCart!.statistics.totalAmount != newCart.statistics.totalAmount) {
      return true;
    }
    if (_cachedCart!.statistics.totalQuantity !=
        newCart.statistics.totalQuantity) {
      return true;
    }

    // 比较商品ID和数量
    for (int i = 0; i < _cachedCart!.items.length; i++) {
      if (_cachedCart!.items[i].id != newCart.items[i].id ||
          _cachedCart!.items[i].quantity != newCart.items[i].quantity) {
        return true;
      }
    }

    return false;
  }

  /// 保存到本地缓存
  Future<void> _saveToLocalCache(CartListResponseModel? cart) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (cart != null) {
        final jsonString = json.encode(cart.toJson());
        await prefs.setString(_cartCacheKey, jsonString);
        await prefs.setString(
          _cartCacheTimeKey,
          DateTime.now().toIso8601String(),
        );
      } else {
        await prefs.remove(_cartCacheKey);
        await prefs.remove(_cartCacheTimeKey);
      }
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 从本地缓存加载
  Future<void> _loadFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_cartCacheKey);
      final cacheTimeString = prefs.getString(_cartCacheTimeKey);

      if (cachedJson != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        // 检查缓存是否过期
        if (now.difference(cacheTime).inMinutes < _cacheValidityMinutes) {
          final cartJson = json.decode(cachedJson);
          _cachedCart = CartListResponseModel.fromJson(cartJson);
          _lastFetchTime = cacheTime;
        } else {
          // 缓存过期，清除
          await _clearLocalCache();
        }
      }
    } catch (e) {
      // 加载失败时清除缓存
      await _clearLocalCache();
    }
  }

  /// 清除本地缓存
  Future<void> _clearLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cartCacheKey);
      await prefs.remove(_cartCacheTimeKey);
    } catch (e) {
      // 清除失败时静默处理
    }
  }

  /// 更新购物车项目后刷新缓存
  Future<void> updateCartItem({
    required int cartId,
    int? quantity,
    bool? selected,
  }) async {
    try {
      await _cartService.updateCartItem(
        cartId: cartId,
        quantity: quantity,
        selected: selected,
      );

      // 更新成功后，重新加载最新数据
      await _loadCartData();
    } catch (e) {
      _lastError = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  /// 删除购物车项目后刷新缓存
  Future<void> deleteCartItem(int cartId) async {
    try {
      await _cartService.deleteCartItem(cartId);

      // 删除成功后，重新加载最新数据
      await _loadCartData();
    } catch (e) {
      _lastError = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  /// 批量删除购物车项目后刷新缓存
  Future<void> deleteSelectedItems(List<int> cartIds) async {
    try {
      await _cartService.batchDeleteCartItems(cartIds);

      // 删除成功后，重新加载最新数据
      await _loadCartData();
    } catch (e) {
      _lastError = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  /// 清除所有缓存
  Future<void> clearCache() async {
    _cachedCart = null;
    _lastFetchTime = null;
    _lastError = null;
    _isLoading = false;

    await _clearLocalCache();
    notifyListeners();
  }

  /// 预加载购物车数据（应用启动时调用）
  Future<void> preloadCartData() async {
    if (_authService.currentUser != null) {
      // 先加载本地缓存
      await _loadFromLocalCache();

      // 后台刷新最新数据
      _backgroundRefresh();
    }
  }

  /// 获取购物车商品数量
  int getCartItemCount() {
    return _cachedCart?.statistics.totalQuantity ?? 0;
  }

  /// 获取购物车总金额
  double getCartTotalAmount() {
    return _cachedCart?.statistics.totalAmount ?? 0.0;
  }

  /// 检查是否有完整的购物车数据
  bool hasCompleteCartData() {
    return _cachedCart != null;
  }
}
