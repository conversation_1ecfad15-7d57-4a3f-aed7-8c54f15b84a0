import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_product_model.dart';
import 'user_product_service.dart';
import 'auth_service.dart';

/// 订单缓存管理服务 - 提供订单数据的缓存机制和后台加载
class OrderCacheService extends ChangeNotifier {
  static final OrderCacheService _instance = OrderCacheService._internal();

  /// 获取订单缓存服务单例实例
  factory OrderCacheService() => _instance;

  OrderCacheService._internal();

  /// 缓存键名
  static const String _ordersCacheKey = 'orders_cache';
  static const String _ordersCacheTimeKey = 'orders_cache_time';

  /// 缓存有效期（小时）
  static const int _cacheValidityHours = 1; // 订单数据1小时过期

  /// 服务实例
  final UserProductService _productService = UserProductService();
  final AuthService _authService = AuthService();

  /// 内存缓存
  List<ProductOrderModel>? _cachedOrders;
  DateTime? _lastFetchTime;
  bool _isLoading = false;
  String? _lastError;

  /// 获取当前缓存的订单列表
  List<ProductOrderModel>? get cachedOrders => _cachedOrders;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 最后的错误信息
  String? get lastError => _lastError;

  /// 是否有缓存数据
  bool get hasCachedData => _cachedOrders != null && _cachedOrders!.isNotEmpty;

  /// 初始化缓存服务
  Future<void> initialize() async {
    await _loadFromLocalCache();
  }

  /// 同步获取缓存的订单列表（如果有的话）
  List<ProductOrderModel> getCachedOrdersSync() {
    return _cachedOrders ?? [];
  }

  /// 获取订单列表（优先使用缓存，后台刷新）
  Future<List<ProductOrderModel>> getOrders({bool forceRefresh = false}) async {
    // 如果用户未登录，清除缓存并返回空列表
    if (_authService.currentUser == null) {
      await clearCache();
      return [];
    }

    // 如果不是强制刷新且有内存缓存，直接返回
    if (!forceRefresh && _cachedOrders != null) {
      // 后台检查是否需要刷新
      _backgroundRefresh();
      return _cachedOrders!;
    }

    // 如果没有缓存或需要强制刷新，立即加载
    return await _loadOrders();
  }

  /// 根据状态过滤订单
  List<ProductOrderModel> getOrdersByStatus(int? status) {
    if (_cachedOrders == null) return [];

    if (status == null) {
      return _cachedOrders!; // 返回全部订单
    }

    return _cachedOrders!
        .where((order) => order.orderStatus == status)
        .toList();
  }

  /// 立即加载订单列表
  Future<List<ProductOrderModel>> _loadOrders() async {
    if (_isLoading) return _cachedOrders ?? [];

    _isLoading = true;
    _lastError = null;
    notifyListeners();

    try {
      final orders = await _productService.getMyOrders();

      // 更新缓存
      _cachedOrders = orders;
      _lastFetchTime = DateTime.now();

      // 保存到本地缓存
      await _saveToLocalCache(orders);

      _isLoading = false;
      notifyListeners();

      return orders;
    } catch (e) {
      _lastError = e.toString();
      _isLoading = false;
      notifyListeners();

      // 如果加载失败，返回现有缓存
      return _cachedOrders ?? [];
    }
  }

  /// 后台刷新（不阻塞UI）
  void _backgroundRefresh() {
    // 检查是否需要刷新
    if (!_shouldRefreshCache()) return;

    // 异步后台刷新
    Future.microtask(() async {
      try {
        final orders = await _productService.getMyOrders();

        // 只有数据真正变化时才更新缓存和通知UI
        if (_hasDataChanged(orders)) {
          _cachedOrders = orders;
          _lastFetchTime = DateTime.now();
          await _saveToLocalCache(orders);
          notifyListeners();
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
        _lastError = e.toString();
      }
    });
  }

  /// 检查是否需要刷新缓存
  bool _shouldRefreshCache() {
    if (_lastFetchTime == null) return true;

    final now = DateTime.now();
    final difference = now.difference(_lastFetchTime!);
    return difference.inHours >= _cacheValidityHours;
  }

  /// 检查数据是否发生变化
  bool _hasDataChanged(List<ProductOrderModel>? newOrders) {
    if (_cachedOrders == null && newOrders == null) return false;
    if (_cachedOrders == null || newOrders == null) return true;
    if (_cachedOrders!.length != newOrders.length) return true;

    // 比较订单ID和状态
    for (int i = 0; i < _cachedOrders!.length; i++) {
      if (_cachedOrders![i].id != newOrders[i].id ||
          _cachedOrders![i].orderStatus != newOrders[i].orderStatus ||
          _cachedOrders![i].createdAt != newOrders[i].createdAt) {
        return true;
      }
    }

    return false;
  }

  /// 保存到本地缓存
  Future<void> _saveToLocalCache(List<ProductOrderModel> orders) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(orders.map((o) => o.toJson()).toList());
      await prefs.setString(_ordersCacheKey, jsonString);
      await prefs.setString(
        _ordersCacheTimeKey,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 从本地缓存加载
  Future<void> _loadFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_ordersCacheKey);
      final cacheTimeString = prefs.getString(_ordersCacheTimeKey);

      if (cachedJson != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        // 检查缓存是否过期
        if (now.difference(cacheTime).inHours < _cacheValidityHours) {
          final List<dynamic> jsonList = json.decode(cachedJson);
          _cachedOrders = jsonList
              .map((json) => ProductOrderModel.fromJson(json))
              .toList();
          _lastFetchTime = cacheTime;
        } else {
          // 缓存过期，清除
          await _clearLocalCache();
        }
      }
    } catch (e) {
      // 加载失败时清除缓存
      await _clearLocalCache();
    }
  }

  /// 清除本地缓存
  Future<void> _clearLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_ordersCacheKey);
      await prefs.remove(_ordersCacheTimeKey);
    } catch (e) {
      // 清除失败时静默处理
    }
  }

  /// 更新订单状态后刷新缓存
  Future<void> updateOrderStatus(String orderId, int newStatus) async {
    try {
      // 这里应该调用更新订单状态的API
      // await _productService.updateOrderStatus(orderId, newStatus);

      // 更新成功后，重新加载最新数据
      await _loadOrders();
    } catch (e) {
      _lastError = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  /// 取消订单后刷新缓存
  Future<void> cancelOrder(String orderId) async {
    try {
      // 这里应该调用取消订单的API
      // await _productService.cancelOrder(orderId);

      // 取消成功后，重新加载最新数据
      await _loadOrders();
    } catch (e) {
      _lastError = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  /// 确认收货后刷新缓存
  Future<void> confirmReceived(String orderId) async {
    try {
      // 这里应该调用确认收货的API
      // await _productService.confirmReceived(orderId);

      // 确认成功后，重新加载最新数据
      await _loadOrders();
    } catch (e) {
      _lastError = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  /// 清除所有缓存
  Future<void> clearCache() async {
    _cachedOrders = null;
    _lastFetchTime = null;
    _lastError = null;
    _isLoading = false;

    await _clearLocalCache();
    notifyListeners();
  }

  /// 预加载订单数据（应用启动时调用）
  Future<void> preloadOrderData() async {
    if (_authService.currentUser != null) {
      // 先加载本地缓存
      await _loadFromLocalCache();

      // 后台刷新最新数据
      _backgroundRefresh();
    }
  }

  /// 获取不同状态的订单数量
  Map<int, int> getOrderCountByStatus() {
    if (_cachedOrders == null) return {};

    final counts = <int, int>{};
    for (final order in _cachedOrders!) {
      counts[order.orderStatus] = (counts[order.orderStatus] ?? 0) + 1;
    }

    return counts;
  }

  /// 获取待付款订单数量
  int getPendingPaymentCount() {
    if (_cachedOrders == null) return 0;
    return _cachedOrders!
        .where((order) => order.orderStatus == 1)
        .length; // 假设1是待付款状态
  }

  /// 获取待发货订单数量
  int getPendingShipmentCount() {
    if (_cachedOrders == null) return 0;
    return _cachedOrders!
        .where((order) => order.orderStatus == 2)
        .length; // 假设2是待发货状态
  }

  /// 获取待收货订单数量
  int getPendingReceiptCount() {
    if (_cachedOrders == null) return 0;
    return _cachedOrders!
        .where((order) => order.orderStatus == 3)
        .length; // 假设3是待收货状态
  }

  /// 检查是否有完整的订单数据
  bool hasCompleteOrderData() {
    return _cachedOrders != null;
  }
}
