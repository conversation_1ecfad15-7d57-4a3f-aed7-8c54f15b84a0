import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 字体大小档位枚举
enum FontSizeLevel { small, medium, large }

/// 字体大小管理服务 - 管理应用的全局字体大小和textScaleFactor
class FontSizeService extends ChangeNotifier {
  static final FontSizeService _instance = FontSizeService._internal();

  /// 获取字体大小服务单例实例
  factory FontSizeService() => _instance;

  FontSizeService._internal();

  /// 字体大小档位持久化键名
  static const String _fontSizeLevelPreferenceKey = 'app_font_size_level';

  /// textScaleFactor 值映射
  static const Map<FontSizeLevel, double> _textScaleFactorMap = {
    FontSizeLevel.small: 0.85,
    FontSizeLevel.medium: 1.0,
    FontSizeLevel.large: 1.15,
  };

  /// 当前字体大小档位
  FontSizeLevel _currentLevel = FontSizeLevel.medium;

  /// 获取当前字体大小档位
  FontSizeLevel get currentLevel => _currentLevel;

  /// 获取当前textScaleFactor
  double get textScaleFactor => _textScaleFactorMap[_currentLevel] ?? 1.0;

  /// 获取当前档位的显示名称（仅用于调试）
  String get currentLevelDisplayName {
    switch (_currentLevel) {
      case FontSizeLevel.small:
        return 'Small';
      case FontSizeLevel.medium:
        return 'Medium';
      case FontSizeLevel.large:
        return 'Large';
    }
  }

  /// 初始化字体大小服务
  Future<void> initialize() async {
    await _loadFontSizeLevelPreference();
  }

  /// 从持久化存储加载字体大小档位设置
  Future<void> _loadFontSizeLevelPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final levelIndex = prefs.getInt(_fontSizeLevelPreferenceKey);

      if (levelIndex != null &&
          levelIndex >= 0 &&
          levelIndex < FontSizeLevel.values.length) {
        _currentLevel = FontSizeLevel.values[levelIndex];
      } else {
        _currentLevel = FontSizeLevel.medium; // 默认中档
      }
    } catch (e) {
      // 出错时使用默认档位
      _currentLevel = FontSizeLevel.medium;
    }
  }

  /// 保存字体大小档位设置到持久化存储
  Future<void> _saveFontSizeLevelPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_fontSizeLevelPreferenceKey, _currentLevel.index);
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 设置字体大小档位
  Future<void> setFontSizeLevel(FontSizeLevel level) async {
    if (_currentLevel == level) {
      return;
    }

    _currentLevel = level;
    await _saveFontSizeLevelPreference();
    notifyListeners();
  }

  /// 重置到默认字体大小档位（中档）
  Future<void> resetToDefault() async {
    await setFontSizeLevel(FontSizeLevel.medium);
  }

  /// 获取相对于字体大小的间距值
  /// 基于当前档位的textScaleFactor计算相对间距
  double getRelativeSpacing(double baseSpacing) {
    return baseSpacing * textScaleFactor;
  }

  /// 获取相对于字体大小的图标尺寸
  /// 基于当前档位的textScaleFactor计算相对图标大小
  double getRelativeIconSize(double baseIconSize) {
    return baseIconSize * textScaleFactor;
  }

  /// 获取相对于字体大小的最小高度
  /// 基于当前档位的textScaleFactor计算相对最小高度
  double getRelativeMinHeight(double baseMinHeight) {
    return baseMinHeight * textScaleFactor;
  }

  /// 获取基于当前字体主题的相对尺寸
  /// 这个方法允许基于实际字体大小计算相对尺寸
  double getRelativeSizeFromTextTheme(
    BuildContext context, {
    required double multiplier,
    double fallbackBaseFontSize = 14.0,
  }) {
    final textTheme = Theme.of(context).textTheme;
    final baseFontSize = textTheme.bodyMedium?.fontSize ?? fallbackBaseFontSize;
    return baseFontSize * multiplier;
  }

  /// 获取按钮的相对垂直内边距
  double getButtonVerticalPadding(BuildContext context) {
    return getRelativeSizeFromTextTheme(context, multiplier: 0.6);
  }

  /// 获取按钮的相对水平内边距
  double getButtonHorizontalPadding(BuildContext context) {
    return getRelativeSizeFromTextTheme(context, multiplier: 1.0);
  }

  /// 获取列表项的相对最小高度
  double getListItemMinHeight(BuildContext context) {
    return getRelativeSizeFromTextTheme(
      context,
      multiplier: 3.0,
      fallbackBaseFontSize: 16.0,
    );
  }

  /// 获取卡片的相对内边距
  double getCardPadding(BuildContext context) {
    return getRelativeSizeFromTextTheme(context, multiplier: 1.0);
  }

  /// 为全局MediaQuery提供修改后的MediaQueryData
  /// 这是核心方法，用于在MaterialApp的builder中修改textScaler
  MediaQueryData getModifiedMediaQueryData(MediaQueryData originalData) {
    return originalData.copyWith(
      textScaler: TextScaler.linear(textScaleFactor),
    );
  }
}
