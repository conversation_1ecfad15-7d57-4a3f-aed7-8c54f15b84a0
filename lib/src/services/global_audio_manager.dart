import 'package:audioplayers/audioplayers.dart';

/// 全局音频管理器 - 统一管理所有页面的音频播放
class GlobalAudioManager {
  static final GlobalAudioManager _instance = GlobalAudioManager._internal();
  factory GlobalAudioManager() => _instance;
  GlobalAudioManager._internal();

  // 存储所有活跃的音频播放器
  final Map<String, AudioPlayer> _audioPlayers = {};
  final Set<String> _playingAudios = {};

  /// 注册音频播放器
  void registerAudioPlayer(String key, AudioPlayer player) {
    _audioPlayers[key] = player;
  }

  /// 注销音频播放器
  void unregisterAudioPlayer(String key) {
    final player = _audioPlayers.remove(key);
    if (player != null) {
      player.stop();
      player.dispose();
    }
    _playingAudios.remove(key);
  }

  /// 开始播放音频
  void startPlaying(String key) {
    _playingAudios.add(key);
  }

  /// 停止播放音频
  void stopPlaying(String key) {
    _playingAudios.remove(key);
    final player = _audioPlayers[key];
    if (player != null) {
      player.stop();
    }
  }

  /// 停止所有音频播放
  void stopAllAudio() {
    for (final player in _audioPlayers.values) {
      player.stop();
    }
    _playingAudios.clear();
  }

  /// 停止除指定key外的其他音频播放
  void stopOtherAudio(String excludeKey) {
    for (final entry in _audioPlayers.entries) {
      if (entry.key != excludeKey) {
        entry.value.stop();
        _playingAudios.remove(entry.key);
      }
    }
  }

  /// 检查是否有音频正在播放
  bool get hasPlayingAudio => _playingAudios.isNotEmpty;

  /// 获取正在播放的音频数量
  int get playingAudioCount => _playingAudios.length;

  /// 清理所有资源
  void dispose() {
    for (final player in _audioPlayers.values) {
      player.stop();
      player.dispose();
    }
    _audioPlayers.clear();
    _playingAudios.clear();
  }
}
