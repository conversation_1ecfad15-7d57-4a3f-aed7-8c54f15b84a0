import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import '../config/api/api_config.dart';
import '../models/doctor_model.dart';
import '../services/auth_service.dart';
import '../services/http_client_service.dart';
import 'language_service.dart';

/// 医生互动服务 - 处理点赞收藏相关功能
class DoctorInteractionService {
  final AuthService _authService = AuthService();
  final HttpClientService _httpClient = HttpClientService();

  /// 获取认证请求头
  Map<String, String> _getAuthHeaders() {
    final token = _authService.currentUser?.token;
    if (token == null || token.isEmpty) {
      throw Exception('用户未登录，请先登录');
    }

    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  /// 点赞医生
  Future<void> likeDoctor(int doctorId, {BuildContext? context}) async {
    try {
      final url = ApiConfig.likeDoctorUrl(doctorId);
      final response = await _httpClient.post(url, context: context);

      final Map<String, dynamic> jsonData = jsonDecode(response.body);
      if (response.statusCode == 200) {
        if (jsonData['success'] != true) {
          throw Exception(jsonData['message'] ?? '点赞失败');
        }
      } else if (response.statusCode == 400) {
        throw Exception(jsonData['detail'] ?? '您已经点赞过这位医生了');
      } else if (response.statusCode == 404) {
        throw Exception('医生不存在');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('点赞医生异常: $e');
    }
  }

  /// 取消点赞医生
  Future<void> unlikeDoctor(int doctorId, {BuildContext? context}) async {
    try {
      final url = ApiConfig.unlikeDoctorUrl(doctorId);
      final response = await _httpClient.delete(url, context: context);

      final Map<String, dynamic> jsonData = jsonDecode(response.body);
      if (response.statusCode == 200) {
        if (jsonData['success'] != true) {
          throw Exception(jsonData['message'] ?? '取消点赞失败');
        }
      } else if (response.statusCode == 404) {
        throw Exception('医生不存在');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('取消点赞医生异常: $e');
    }
  }

  /// 收藏医生
  Future<void> favoriteDoctor(int doctorId, {BuildContext? context}) async {
    try {
      final url = ApiConfig.favoriteDoctorUrl(doctorId);
      final response = await _httpClient.post(url, context: context);

      final Map<String, dynamic> jsonData = jsonDecode(response.body);
      if (response.statusCode == 200) {
        if (jsonData['success'] != true) {
          throw Exception(jsonData['message'] ?? '收藏失败');
        }
      } else if (response.statusCode == 400) {
        throw Exception(jsonData['detail'] ?? '您已经收藏过这位医生了');
      } else if (response.statusCode == 404) {
        throw Exception('医生不存在');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('收藏医生异常: $e');
    }
  }

  /// 取消收藏医生
  Future<void> unfavoriteDoctor(int doctorId, {BuildContext? context}) async {
    try {
      final url = ApiConfig.unfavoriteDoctorUrl(doctorId);
      final response = await _httpClient.delete(url, context: context);

      final Map<String, dynamic> jsonData = jsonDecode(response.body);
      if (response.statusCode == 200) {
        if (jsonData['success'] != true) {
          throw Exception(jsonData['message'] ?? '取消收藏失败');
        }
      } else if (response.statusCode == 404) {
        throw Exception('医生不存在');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('取消收藏医生异常: $e');
    }
  }

  /// 获取医生互动状态
  Future<Map<String, dynamic>> getDoctorStatus(
    int doctorId, {
    BuildContext? context,
  }) async {
    try {
      final url = ApiConfig.getDoctorStatusUrl(doctorId);
      final response = await _httpClient.get(url, context: context);

      final Map<String, dynamic> jsonData = jsonDecode(response.body);
      return jsonData;
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('获取医生状态异常: $e');
    }
  }

  /// 获取用户收藏的医生列表
  Future<List<DoctorModel>> getFavoriteDoctors({BuildContext? context}) async {
    try {
      // 获取当前语言
      final currentLanguage = LanguageService().getCurrentLanguageCode();
      print('🌐 DoctorInteractionService: 获取收藏医生，当前语言: $currentLanguage');
      final url = '${ApiConfig.getFavoriteDoctorsUrl}?lang=$currentLanguage';
      print('🌐 DoctorInteractionService: 请求URL: $url');

      // 使用新的HTTP客户端，自动处理token过期
      final response = await _httpClient.get(url, context: context);

      final List<dynamic> jsonList = jsonDecode(response.body);
      print('🌐 DoctorInteractionService: 收藏医生API响应: ${response.body}');
      final doctors = jsonList
          .map(
            (json) => DoctorModel.fromJson(
              json as Map<String, dynamic>,
              currentLanguage,
            ),
          )
          .toList();

      // 打印第一个医生的信息用于调试
      if (doctors.isNotEmpty) {
        print(
          '🌐 DoctorInteractionService: 第一个收藏医生解析结果: ${doctors.first.name}',
        );
      }

      return doctors;
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('获取收藏医生列表异常: $e');
    }
  }

  /// 获取用户点赞的医生列表
  Future<List<DoctorModel>> getLikedDoctors({BuildContext? context}) async {
    try {
      // 获取当前语言
      final currentLanguage = LanguageService().getCurrentLanguageCode();
      print('🌐 DoctorInteractionService: 获取点赞医生，当前语言: $currentLanguage');
      final url = '${ApiConfig.getLikedDoctorsUrl}?lang=$currentLanguage';
      print('🌐 DoctorInteractionService: 请求URL: $url');

      // 使用新的HTTP客户端，自动处理token过期
      final response = await _httpClient.get(url, context: context);

      final List<dynamic> jsonList = jsonDecode(response.body);
      print('🌐 DoctorInteractionService: 点赞医生API响应: ${response.body}');
      final doctors = jsonList
          .map(
            (json) => DoctorModel.fromJson(
              json as Map<String, dynamic>,
              currentLanguage,
            ),
          )
          .toList();

      // 打印第一个医生的信息用于调试
      if (doctors.isNotEmpty) {
        print(
          '🌐 DoctorInteractionService: 第一个点赞医生解析结果: ${doctors.first.name}',
        );
      }

      return doctors;
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('获取点赞医生列表异常: $e');
    }
  }

  /// 获取包含互动状态的医生列表
  Future<List<DoctorModel>> getDoctorsWithInteraction() async {
    try {
      final uri = Uri.parse(ApiConfig.getDoctorsWithInteractionUrl);
      final response = await http.get(uri, headers: _getAuthHeaders());

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = jsonDecode(response.body);
        return jsonList
            .map((json) => DoctorModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('获取医生列表异常: $e');
    }
  }
}
