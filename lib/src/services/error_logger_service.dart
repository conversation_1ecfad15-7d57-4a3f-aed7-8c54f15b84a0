import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

/// 错误日志模型
class ErrorLogEntry {
  final DateTime timestamp;
  final String errorType;
  final String message;
  final String? stackTrace;
  final Map<String, dynamic>? context;
  final String severity; // info, warning, error, critical

  ErrorLogEntry({
    required this.timestamp,
    required this.errorType,
    required this.message,
    this.stackTrace,
    this.context,
    this.severity = 'error',
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'errorType': errorType,
      'message': message,
      'stackTrace': stackTrace,
      'context': context,
      'severity': severity,
    };
  }

  factory ErrorLogEntry.fromJson(Map<String, dynamic> json) {
    return ErrorLogEntry(
      timestamp: DateTime.parse(json['timestamp']),
      errorType: json['errorType'],
      message: json['message'],
      stackTrace: json['stackTrace'],
      context: json['context'],
      severity: json['severity'] ?? 'error',
    );
  }

  @override
  String toString() {
    return '[${timestamp.toIso8601String()}] [$severity] $errorType: $message';
  }
}

/// 错误日志收集服务
class ErrorLoggerService {
  static final ErrorLoggerService _instance = ErrorLoggerService._internal();
  factory ErrorLoggerService() => _instance;
  ErrorLoggerService._internal();

  static const int _maxLogEntries = 100; // 最多保存100条错误日志
  static const int _maxLogFileSizeMB = 5; // 最大日志文件5MB

  List<ErrorLogEntry> _errorLogs = [];
  File? _logFile;
  bool _initialized = false;

  /// 初始化错误日志服务
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // 获取应用文档目录
      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/error_logs');

      // 确保日志目录存在
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }

      _logFile = File('${logDir.path}/app_errors.log');

      // 加载现有的错误日志
      await _loadExistingLogs();

      // 设置全局错误捕获
      _setupGlobalErrorHandling();

      _initialized = true;
      await logInfo('ErrorLoggerService', '错误日志服务初始化成功');
    } catch (e) {
      debugPrint('错误日志服务初始化失败: $e');
    }
  }

  /// 设置全局错误处理
  void _setupGlobalErrorHandling() {
    // 捕获Flutter框架错误
    FlutterError.onError = (FlutterErrorDetails details) {
      logError(
        'FlutterError',
        details.exception.toString(),
        stackTrace: details.stack.toString(),
        context: {
          'library': details.library ?? 'unknown',
          'context': details.context?.toString() ?? 'unknown',
        },
      );
    };

    // 捕获平台错误
    PlatformDispatcher.instance.onError = (error, stack) {
      logError('PlatformError', error.toString(), stackTrace: stack.toString());
      return true;
    };
  }

  /// 记录错误日志
  Future<void> logError(
    String errorType,
    String message, {
    String? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    await _addLogEntry(
      ErrorLogEntry(
        timestamp: DateTime.now(),
        errorType: errorType,
        message: message,
        stackTrace: stackTrace,
        context: context,
        severity: 'error',
      ),
    );
  }

  /// 记录警告日志
  Future<void> logWarning(
    String warningType,
    String message, {
    Map<String, dynamic>? context,
  }) async {
    await _addLogEntry(
      ErrorLogEntry(
        timestamp: DateTime.now(),
        errorType: warningType,
        message: message,
        context: context,
        severity: 'warning',
      ),
    );
  }

  /// 记录信息日志
  Future<void> logInfo(
    String infoType,
    String message, {
    Map<String, dynamic>? context,
  }) async {
    await _addLogEntry(
      ErrorLogEntry(
        timestamp: DateTime.now(),
        errorType: infoType,
        message: message,
        context: context,
        severity: 'info',
      ),
    );
  }

  /// 记录关键错误
  Future<void> logCritical(
    String errorType,
    String message, {
    String? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    await _addLogEntry(
      ErrorLogEntry(
        timestamp: DateTime.now(),
        errorType: errorType,
        message: message,
        stackTrace: stackTrace,
        context: context,
        severity: 'critical',
      ),
    );
  }

  /// 添加日志条目
  Future<void> _addLogEntry(ErrorLogEntry entry) async {
    if (!_initialized) return;

    try {
      // 添加到内存列表
      _errorLogs.add(entry);

      // 保持最大条目数限制
      if (_errorLogs.length > _maxLogEntries) {
        _errorLogs = _errorLogs.sublist(_errorLogs.length - _maxLogEntries);
      }

      // 写入文件
      await _writeToFile(entry);

      // 在debug模式下打印到控制台
      if (kDebugMode) {
        debugPrint('ErrorLog: $entry');
      }
    } catch (e) {
      debugPrint('写入错误日志失败: $e');
    }
  }

  /// 写入文件
  Future<void> _writeToFile(ErrorLogEntry entry) async {
    if (_logFile == null) return;

    try {
      // 检查文件大小
      if (await _logFile!.exists()) {
        final fileSize = await _logFile!.length();
        if (fileSize > _maxLogFileSizeMB * 1024 * 1024) {
          // 文件太大，备份并创建新文件
          await _rotateLogFile();
        }
      }

      // 追加写入
      final logLine = '${jsonEncode(entry.toJson())}\n';
      await _logFile!.writeAsString(logLine, mode: FileMode.append);
    } catch (e) {
      debugPrint('写入日志文件失败: $e');
    }
  }

  /// 轮转日志文件
  Future<void> _rotateLogFile() async {
    if (_logFile == null || !await _logFile!.exists()) return;

    try {
      final directory = _logFile!.parent;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final backupFile = File('${directory.path}/app_errors_$timestamp.log');

      // 移动当前文件为备份
      await _logFile!.rename(backupFile.path);

      // 创建新的日志文件
      _logFile = File('${directory.path}/app_errors.log');

      // 清理旧的备份文件（保留最近5个）
      await _cleanupOldLogFiles(directory);
    } catch (e) {
      debugPrint('轮转日志文件失败: $e');
    }
  }

  /// 清理旧的日志文件
  Future<void> _cleanupOldLogFiles(Directory directory) async {
    try {
      final files = await directory
          .list()
          .where((entity) {
            return entity is File &&
                entity.path.contains('app_errors_') &&
                entity.path.endsWith('.log');
          })
          .cast<File>()
          .toList();

      // 按修改时间排序
      files.sort((a, b) {
        return b.lastModifiedSync().compareTo(a.lastModifiedSync());
      });

      // 删除超过5个的旧文件
      if (files.length > 5) {
        for (int i = 5; i < files.length; i++) {
          await files[i].delete();
        }
      }
    } catch (e) {
      debugPrint('清理旧日志文件失败: $e');
    }
  }

  /// 加载现有日志
  Future<void> _loadExistingLogs() async {
    if (_logFile == null || !await _logFile!.exists()) return;

    try {
      final content = await _logFile!.readAsString();
      final lines = content.split('\n').where((line) => line.trim().isNotEmpty);

      _errorLogs = [];
      for (final line in lines) {
        try {
          final json = jsonDecode(line) as Map<String, dynamic>;
          _errorLogs.add(ErrorLogEntry.fromJson(json));
        } catch (e) {
          // 忽略无法解析的行
        }
      }

      // 保持最新的条目
      if (_errorLogs.length > _maxLogEntries) {
        _errorLogs = _errorLogs.sublist(_errorLogs.length - _maxLogEntries);
      }
    } catch (e) {
      debugPrint('加载现有日志失败: $e');
    }
  }

  /// 获取所有错误日志
  List<ErrorLogEntry> getAllLogs() {
    return List.unmodifiable(_errorLogs);
  }

  /// 获取最近的错误日志
  List<ErrorLogEntry> getRecentLogs({int limit = 10}) {
    final logs = _errorLogs.reversed.take(limit).toList();
    return logs.reversed.toList();
  }

  /// 获取指定严重程度的日志
  List<ErrorLogEntry> getLogsBySeverity(String severity) {
    return _errorLogs.where((log) => log.severity == severity).toList();
  }

  /// 获取最近的错误（不包括info级别）
  List<ErrorLogEntry> getRecentErrors({int limit = 5}) {
    return _errorLogs
        .where((log) => log.severity != 'info')
        .toList()
        .reversed
        .take(limit)
        .toList();
  }

  /// 获取日志统计信息
  Map<String, int> getLogStatistics() {
    final stats = <String, int>{
      'total': _errorLogs.length,
      'critical': 0,
      'error': 0,
      'warning': 0,
      'info': 0,
    };

    for (final log in _errorLogs) {
      stats[log.severity] = (stats[log.severity] ?? 0) + 1;
    }

    return stats;
  }

  /// 清除所有日志
  Future<void> clearAllLogs() async {
    try {
      _errorLogs.clear();
      if (_logFile != null && await _logFile!.exists()) {
        await _logFile!.delete();
      }
      await logInfo('ErrorLoggerService', '所有错误日志已清除');
    } catch (e) {
      debugPrint('清除日志失败: $e');
    }
  }

  /// 导出日志为字符串
  String exportLogsAsString({int? limit}) {
    final logs = limit != null ? getRecentLogs(limit: limit) : _errorLogs;
    final buffer = StringBuffer();

    buffer.writeln('======== 错误日志导出 ========');
    buffer.writeln('导出时间: ${DateTime.now().toIso8601String()}');
    buffer.writeln('日志条目数: ${logs.length}');
    buffer.writeln();

    for (final log in logs) {
      buffer.writeln('时间: ${log.timestamp.toIso8601String()}');
      buffer.writeln('类型: ${log.errorType}');
      buffer.writeln('严重程度: ${log.severity}');
      buffer.writeln('消息: ${log.message}');

      if (log.context != null && log.context!.isNotEmpty) {
        buffer.writeln('上下文: ${jsonEncode(log.context)}');
      }

      if (log.stackTrace != null) {
        buffer.writeln('堆栈追踪:');
        buffer.writeln(log.stackTrace);
      }

      buffer.writeln('----------------------------------------');
    }

    return buffer.toString();
  }

  /// 获取日志文件路径（用于调试）
  String? get logFilePath => _logFile?.path;

  /// 服务是否已初始化
  bool get isInitialized => _initialized;
}
