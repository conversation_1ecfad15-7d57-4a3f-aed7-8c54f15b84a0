import 'dart:io';
import 'package:flutter/widgets.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import '../config/api/api_config.dart';
import 'auth_service.dart';

/// 头像管理服务
/// 负责头像的本地存储、下载、缓存和状态管理
class AvatarManagerService extends ChangeNotifier {
  // 单例模式
  static final AvatarManagerService _instance =
      AvatarManagerService._internal();
  factory AvatarManagerService() => _instance;
  AvatarManagerService._internal();

  final AuthService _authService = AuthService();

  // 当前头像文件路径
  String? _currentAvatarPath;

  // 是否正在加载头像
  bool _isLoading = false;

  // 头像版本号 - 用于强制UI刷新
  int _avatarVersion = 0;

  // 头像最后更新时间戳
  DateTime? _lastUpdateTime;

  // 头像文件名
  static const String _avatarFileName = 'user_avatar.jpg';

  /// 获取当前头像文件路径
  String? get currentAvatarPath => _currentAvatarPath;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 获取头像版本号 - 用于强制UI刷新
  int get avatarVersion => _avatarVersion;

  /// 获取头像最后更新时间
  DateTime? get lastUpdateTime => _lastUpdateTime;

  /// 获取头像缓存键 - 组合路径和版本号
  String? get avatarCacheKey => _currentAvatarPath != null
      ? '${_currentAvatarPath}_v${_avatarVersion}_${_lastUpdateTime?.millisecondsSinceEpoch ?? 0}'
      : null;

  /// 获取应用文档目录中的头像文件路径
  Future<String> get _avatarFilePath async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/$_avatarFileName';
  }

  /// 更新头像版本号并清除图片缓存
  /// 这将强制所有使用该头像的Widget重新加载图片
  void _updateAvatarVersion() {
    _avatarVersion++;
    _lastUpdateTime = DateTime.now();

    // 清除特定文件的图片缓存
    if (_currentAvatarPath != null) {
      try {
        final imageProvider = FileImage(File(_currentAvatarPath!));
        imageProvider.evict();

        assert(() {
          // ignore: avoid_print
          print('🔄 头像版本已更新: v$_avatarVersion, 时间: $_lastUpdateTime');
          // ignore: avoid_print
          print('📸 已清除头像图片缓存: $_currentAvatarPath');
          return true;
        }());
      } catch (e) {
        assert(() {
          // ignore: avoid_print
          print('⚠️ 清除图片缓存失败: $e');
          return true;
        }());
      }
    }

    // 通知所有监听器重新构建
    notifyListeners();
  }

  /// 初始化头像管理器
  /// 检查本地是否已存在头像文件
  Future<void> initialize() async {
    try {
      final filePath = await _avatarFilePath;
      final file = File(filePath);

      if (await file.exists()) {
        _currentAvatarPath = filePath;

        // 在调试模式下打印信息
        assert(() {
          // ignore: avoid_print
          print('发现本地头像文件: $filePath');
          return true;
        }());
      } else {
        _currentAvatarPath = null;

        assert(() {
          // ignore: avoid_print
          print('本地未找到头像文件，将使用默认头像');
          return true;
        }());
      }

      notifyListeners();
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('初始化头像管理器失败: $e');
        return true;
      }());
    }
  }

  /// 保存头像到本地
  /// [imageData] 图片字节数据
  Future<void> saveAvatarToLocal(List<int> imageData) async {
    try {
      _isLoading = true;
      notifyListeners();

      final filePath = await _avatarFilePath;
      final file = File(filePath);

      // 确保目录存在
      await file.parent.create(recursive: true);

      // 写入图片数据
      await file.writeAsBytes(imageData);

      _currentAvatarPath = filePath;

      // 更新版本号，强制UI刷新
      _updateAvatarVersion();

      assert(() {
        // ignore: avoid_print
        print('头像保存成功: $filePath, 大小: ${imageData.length} bytes');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('保存头像失败: $e');
        return true;
      }());
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 从文件保存头像到本地
  /// [imageFile] 图片文件
  Future<void> saveAvatarFromFile(File imageFile) async {
    try {
      final imageData = await imageFile.readAsBytes();
      await saveAvatarToLocal(imageData);
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('从文件保存头像失败: $e');
        return true;
      }());
      rethrow;
    }
  }

  /// 从网络下载并保存头像
  /// [avatarUrl] 头像URL
  Future<void> downloadAndSaveAvatar(String avatarUrl) async {
    try {
      _isLoading = true;
      notifyListeners();

      assert(() {
        // ignore: avoid_print
        print('开始下载头像: $avatarUrl');
        return true;
      }());

      // 添加认证头
      final headers = <String, String>{};
      final token = _authService.currentUser?.token;
      if (token != null) {
        headers['Authorization'] = token;
      }

      final response = await http.get(Uri.parse(avatarUrl), headers: headers);

      if (response.statusCode == 200) {
        await saveAvatarToLocal(response.bodyBytes);

        assert(() {
          // ignore: avoid_print
          print('头像下载并保存成功');
          return true;
        }());
      } else {
        throw Exception('下载头像失败: HTTP ${response.statusCode}');
      }
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('下载头像失败: $e');
        return true;
      }());
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 处理用户登录后的头像设置
  /// [avatarUrl] 从登录接口获取的头像URL
  Future<void> handleLoginAvatar(String? avatarUrl) async {
    if (avatarUrl == null || avatarUrl.isEmpty) {
      return;
    }

    try {
      // 使用API配置构造完整的头像URL
      String fullAvatarUrl;
      if (avatarUrl.startsWith('http')) {
        fullAvatarUrl = avatarUrl;
      } else {
        fullAvatarUrl = ApiConfig.buildAvatarUrl(avatarUrl);
      }

      await downloadAndSaveAvatar(fullAvatarUrl);
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('处理登录头像失败: $e');
        return true;
      }());
      // 登录时头像下载失败不应该影响登录流程
    }
  }

  /// 处理头像上传成功后的更新
  /// [avatarFileName] 上传成功后服务器返回的头像文件名
  Future<void> handleAvatarUploadSuccess(String avatarFileName) async {
    try {
      // 使用API配置构造完整的头像URL
      String fullAvatarUrl;
      if (avatarFileName.startsWith('http')) {
        fullAvatarUrl = avatarFileName;
      } else {
        fullAvatarUrl = ApiConfig.buildAvatarUrl(avatarFileName);
      }

      await downloadAndSaveAvatar(fullAvatarUrl);

      // 确保UI立即刷新显示新头像
      await updateAvatarWithCacheBusting();

      assert(() {
        // ignore: avoid_print
        print('✅ 头像上传成功处理完成，UI已刷新');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('处理头像上传成功后更新失败: $e');
        return true;
      }());
      rethrow;
    }
  }

  /// 清除本地头像
  Future<void> clearLocalAvatar() async {
    try {
      final filePath = await _avatarFilePath;
      final file = File(filePath);

      if (await file.exists()) {
        await file.delete();

        assert(() {
          // ignore: avoid_print
          print('本地头像文件已删除');
          return true;
        }());
      }

      _currentAvatarPath = null;

      // 更新版本号，确保UI立即清除头像显示
      _updateAvatarVersion();
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('清除本地头像失败: $e');
        return true;
      }());
    }
  }

  /// 检查本地头像是否存在
  Future<bool> hasLocalAvatar() async {
    try {
      final filePath = await _avatarFilePath;
      return await File(filePath).exists();
    } catch (e) {
      return false;
    }
  }

  /// 强制刷新头像状态
  /// 用于头像上传成功后立即更新所有监听的组件
  Future<void> forceRefresh() async {
    try {
      final filePath = await _avatarFilePath;
      final file = File(filePath);

      if (await file.exists()) {
        _currentAvatarPath = filePath;

        assert(() {
          // ignore: avoid_print
          print('强制刷新头像状态: $filePath');
          return true;
        }());
      } else {
        _currentAvatarPath = null;

        assert(() {
          // ignore: avoid_print
          print('强制刷新头像状态: 文件不存在，使用默认头像');
          return true;
        }());
      }

      notifyListeners();
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('强制刷新头像状态失败: $e');
        return true;
      }());
    }
  }

  /// 更新头像文件并通知界面刷新
  /// 使用版本控制机制强制重新加载图片
  Future<void> updateAvatarWithCacheBusting() async {
    await forceRefresh();

    // 强制更新版本号，确保所有UI立即刷新
    _updateAvatarVersion();
  }

  /// 强制刷新头像显示
  /// 当知道头像文件已更新但UI未刷新时调用
  void forceRefreshAvatar() {
    _updateAvatarVersion();
  }

  /// 从本地文件直接更新头像
  /// 当本地已有新的头像文件时调用此方法
  Future<void> updateFromLocalFile(File avatarFile) async {
    try {
      await saveAvatarFromFile(avatarFile);

      assert(() {
        // ignore: avoid_print
        print('✅ 从本地文件更新头像成功，UI已刷新');
        return true;
      }());
    } catch (e) {
      assert(() {
        // ignore: avoid_print
        print('❌ 从本地文件更新头像失败: $e');
        return true;
      }());
      rethrow;
    }
  }
}
