import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

/// 定位服务类
class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  /// 当前位置信息
  Position? _currentPosition;
  String? _currentAddress;
  String? _currentProvince;
  String? _currentCity;
  String? _currentArea;

  /// 获取当前位置信息
  Position? get currentPosition => _currentPosition;
  String? get currentAddress => _currentAddress;
  String? get currentProvince => _currentProvince;
  String? get currentCity => _currentCity;
  String? get currentArea => _currentArea;

  /// 检查定位权限
  Future<bool> checkLocationPermission() async {
    try {
      // 检查定位服务是否开启
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return false;
      }

      // 检查权限状态
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return false;
      }

      return true;
    } catch (e) {
      print('LocationService: 检查定位权限失败: $e');
      return false;
    }
  }

  /// 获取当前位置
  Future<bool> getCurrentLocation() async {
    try {
      print('LocationService: 开始检查定位权限...');

      // 检查定位服务是否开启
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      print('LocationService: 定位服务是否开启: $serviceEnabled');
      if (!serviceEnabled) {
        print('LocationService: 定位服务未开启，请在设置中开启GPS');
        return false;
      }

      // 检查权限状态
      LocationPermission permission = await Geolocator.checkPermission();
      print('LocationService: 当前权限状态: $permission');

      if (permission == LocationPermission.denied) {
        print('LocationService: 权限被拒绝，尝试请求权限...');
        permission = await Geolocator.requestPermission();
        print('LocationService: 请求权限后的状态: $permission');
        if (permission == LocationPermission.denied) {
          print('LocationService: 用户拒绝了定位权限');
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        print('LocationService: 定位权限被永久拒绝，需要手动在设置中开启');
        return false;
      }

      print('LocationService: 权限检查通过，开始获取当前位置...');

      // 首先尝试高精度定位
      try {
        print('LocationService: 尝试高精度GPS定位...');
        _currentPosition = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
          timeLimit: const Duration(seconds: 10),
        );
        print('LocationService: 高精度定位成功');
      } catch (e) {
        print('LocationService: 高精度定位失败: $e，尝试网络定位...');

        // 降级到网络定位
        try {
          _currentPosition = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.medium,
            timeLimit: const Duration(seconds: 15),
          );
          print('LocationService: 网络定位成功');
        } catch (e2) {
          print('LocationService: 网络定位也失败: $e2，尝试低精度定位...');

          // 最后尝试低精度定位
          _currentPosition = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.low,
            timeLimit: const Duration(seconds: 20),
          );
          print('LocationService: 低精度定位成功');
        }
      }

      print(
        'LocationService: 获取到位置坐标: ${_currentPosition!.latitude}, ${_currentPosition!.longitude}',
      );

      // 进行逆地理编码
      await _reverseGeocode();

      return true;
    } catch (e) {
      print('LocationService: 获取当前位置失败: $e');
      print('LocationService: 错误类型: ${e.runtimeType}');
      return false;
    }
  }

  /// 逆地理编码 - 将坐标转换为地址
  Future<void> _reverseGeocode() async {
    if (_currentPosition == null) return;

    try {
      print('LocationService: 开始逆地理编码...');

      List<Placemark> placemarks = await placemarkFromCoordinates(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        localeIdentifier: 'zh_CN',
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks.first;

        // 提取地址信息
        _currentProvince = place.administrativeArea ?? '';
        _currentCity = place.locality ?? place.subAdministrativeArea ?? '';
        _currentArea = place.subLocality ?? '';

        // 组合完整地址
        List<String> addressParts = [];
        if (_currentProvince?.isNotEmpty == true) {
          addressParts.add(_currentProvince!);
        }
        if (_currentCity?.isNotEmpty == true) addressParts.add(_currentCity!);
        if (_currentArea?.isNotEmpty == true) addressParts.add(_currentArea!);

        _currentAddress = addressParts.join(' ');

        print('LocationService: 逆地理编码成功');
        print('- 省份: $_currentProvince');
        print('- 城市: $_currentCity');
        print('- 区域: $_currentArea');
        print('- 完整地址: $_currentAddress');
      } else {
        print('LocationService: 逆地理编码未找到地址信息');
      }
    } catch (e) {
      print('LocationService: 逆地理编码失败: $e');
    }
  }

  /// 清除当前位置信息
  void clearCurrentLocation() {
    _currentPosition = null;
    _currentAddress = null;
    _currentProvince = null;
    _currentCity = null;
    _currentArea = null;
  }

  /// 获取格式化的当前地址
  String getFormattedCurrentAddress() {
    if (_currentAddress?.isNotEmpty == true) {
      return _currentAddress!;
    }
    return '';
  }

  /// 检查是否有当前位置信息
  bool hasCurrentLocation() {
    return _currentPosition != null && _currentAddress?.isNotEmpty == true;
  }

  /// 打开系统定位设置
  Future<void> openLocationSettings() async {
    try {
      await Geolocator.openLocationSettings();
    } catch (e) {
      print('LocationService: 打开定位设置失败: $e');
    }
  }

  /// 打开应用权限设置
  Future<void> openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      print('LocationService: 打开应用设置失败: $e');
    }
  }
}
