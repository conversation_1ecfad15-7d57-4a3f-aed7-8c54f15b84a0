import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api/api_config.dart';
import '../services/auth_service.dart';

/// 产品图片上传服务
class ProductImageService {
  /// 获取认证头
  Map<String, String> _getAuthHeaders() {
    final token = AuthService().currentUser?.token;
    return {'Authorization': 'Bearer $token'};
  }

  /// 单张图片上传
  Future<String> uploadImage(File imageFile) async {
    try {
      final uri = Uri.parse(ApiConfig.uploadProductImageUrl);
      final request = http.MultipartRequest('POST', uri);

      // 添加认证头
      request.headers.addAll(_getAuthHeaders());

      // 添加图片文件
      request.files.add(
        await http.MultipartFile.fromPath('image', imageFile.path),
      );

      print('ProductImageService: 上传单张图片到: $uri');
      print('ProductImageService: 图片路径: ${imageFile.path}');

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      print('ProductImageService: 单张图片上传响应状态码: ${response.statusCode}');
      print('ProductImageService: 单张图片上传响应内容: $responseBody');

      if (response.statusCode == 200) {
        final result = json.decode(responseBody);
        if (result['code'] == 200) {
          return result['data']['image_url'] as String;
        } else {
          throw Exception(result['message'] ?? '图片上传失败');
        }
      } else {
        final result = json.decode(responseBody);
        throw Exception(result['message'] ?? '图片上传失败');
      }
    } catch (e) {
      print('ProductImageService: 单张图片上传失败: $e');
      rethrow;
    }
  }

  /// 批量图片上传
  Future<List<String>> uploadImages(List<File> imageFiles) async {
    try {
      final uri = Uri.parse(ApiConfig.uploadProductImagesUrl);
      final request = http.MultipartRequest('POST', uri);

      // 添加认证头
      request.headers.addAll(_getAuthHeaders());

      // 添加图片文件
      for (final file in imageFiles) {
        request.files.add(
          await http.MultipartFile.fromPath('images', file.path),
        );
      }

      print('ProductImageService: 批量上传图片到: $uri');
      print('ProductImageService: 图片数量: ${imageFiles.length}');

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      print('ProductImageService: 批量图片上传响应状态码: ${response.statusCode}');
      print('ProductImageService: 批量图片上传响应内容: $responseBody');

      if (response.statusCode == 200) {
        final result = json.decode(responseBody);
        if (result['code'] == 200) {
          final uploadedImages = result['data']['uploaded_images'] as List;
          return uploadedImages
              .map((img) => img['image_url'] as String)
              .toList();
        } else {
          throw Exception(result['message'] ?? '批量图片上传失败');
        }
      } else {
        final result = json.decode(responseBody);
        throw Exception(result['message'] ?? '批量图片上传失败');
      }
    } catch (e) {
      print('ProductImageService: 批量图片上传失败: $e');
      rethrow;
    }
  }

  /// 上传产品主图
  Future<String?> uploadMainImage(File? imageFile) async {
    if (imageFile == null) return null;
    return await uploadImage(imageFile);
  }

  /// 上传产品详情图片
  Future<List<String>> uploadDetailImages(List<File> imageFiles) async {
    if (imageFiles.isEmpty) return [];
    return await uploadImages(imageFiles);
  }
}
