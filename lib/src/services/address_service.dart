import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/address_model.dart';
import '../config/api/api_config.dart';
import '../services/auth_service.dart';

/// 地址服务
class AddressService {
  static final AddressService _instance = AddressService._internal();
  factory AddressService() => _instance;
  AddressService._internal();

  final AuthService _authService = AuthService();

  /// 获取请求头
  Map<String, String> get _headers {
    final token = _authService.currentUser?.token ?? '';
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  /// 创建地址
  Future<AddressModel> createAddress(AddressRequestModel request) async {
    try {
      final url = Uri.parse(ApiConfig.createAddressUrl);

      final response = await http.post(
        url,
        headers: _headers,
        body: json.encode(request.toJson()),
      );

      print('AddressService: 创建地址请求 - ${response.statusCode}');
      print('AddressService: 请求体 - ${json.encode(request.toJson())}');
      print('AddressService: 响应数据 - ${response.body}');

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (responseData['data'] != null) {
          print('AddressService: 创建地址成功');
          return AddressModel.fromJson(responseData['data']);
        } else {
          throw Exception('响应数据中缺少data字段');
        }
      } else {
        // 处理错误响应
        String errorMessage = responseData['message'] ?? '创建地址失败';
        print('AddressService: 创建地址失败 - $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      print('AddressService: 创建地址失败 - $e');
      rethrow;
    }
  }

  /// 获取地址列表
  Future<List<AddressModel>> getAddressList() async {
    try {
      final url = Uri.parse(ApiConfig.getAddressListUrl);

      final response = await http.get(url, headers: _headers);

      print('AddressService: 获取地址列表 - ${response.statusCode}');
      print('AddressService: 地址列表响应 - ${response.body}');

      final responseData = json.decode(response.body);

      if (response.statusCode == 200) {
        if (responseData['data'] != null &&
            responseData['data']['addresses'] != null) {
          final addressesData = responseData['data']['addresses'] as List;

          print('AddressService: 获取到 ${addressesData.length} 个地址');
          print(
            'AddressService: 第一个地址数据 - ${addressesData.isNotEmpty ? addressesData[0] : "无"}',
          );

          return addressesData
              .map((json) => AddressModel.fromJson(json))
              .toList();
        } else {
          return []; // 返回空列表
        }
      } else {
        String errorMessage = responseData['message'] ?? '获取地址列表失败';
        throw Exception(errorMessage);
      }
    } catch (e) {
      print('AddressService: 获取地址列表失败 - $e');
      rethrow;
    }
  }

  /// 获取简化地址列表
  Future<List<SimpleAddressModel>> getSimpleAddressList() async {
    try {
      final url = Uri.parse(ApiConfig.getSimpleAddressListUrl);

      final response = await http.get(url, headers: _headers);

      print('AddressService: 获取简化地址列表 - ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final addressesData = responseData['data'] as List;

        return addressesData
            .map((json) => SimpleAddressModel.fromJson(json))
            .toList();
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取地址列表失败');
      }
    } catch (e) {
      print('AddressService: 获取简化地址列表失败 - $e');
      rethrow;
    }
  }

  /// 获取地址详情
  Future<AddressModel> getAddressDetail(int addressId) async {
    try {
      final url = Uri.parse(ApiConfig.getAddressDetailUrl(addressId));

      final response = await http.get(url, headers: _headers);

      print('AddressService: 获取地址详情 - ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return AddressModel.fromJson(responseData['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取地址详情失败');
      }
    } catch (e) {
      print('AddressService: 获取地址详情失败 - $e');
      rethrow;
    }
  }

  /// 更新地址
  Future<AddressModel> updateAddress(
    int addressId,
    AddressRequestModel request,
  ) async {
    try {
      final url = Uri.parse(ApiConfig.updateAddressUrl(addressId));

      final response = await http.put(
        url,
        headers: _headers,
        body: json.encode(request.toJson()),
      );

      print('AddressService: 更新地址 - ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        print('AddressService: 更新地址成功');
        return AddressModel.fromJson(responseData['data']);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '更新地址失败');
      }
    } catch (e) {
      print('AddressService: 更新地址失败 - $e');
      rethrow;
    }
  }

  /// 删除地址
  Future<void> deleteAddress(int addressId) async {
    try {
      final url = Uri.parse(ApiConfig.deleteAddressUrl(addressId));

      final response = await http.delete(url, headers: _headers);

      print('AddressService: 删除地址 - ${response.statusCode}');

      if (response.statusCode == 200) {
        print('AddressService: 删除地址成功');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '删除地址失败');
      }
    } catch (e) {
      print('AddressService: 删除地址失败 - $e');
      rethrow;
    }
  }

  /// 设置默认地址
  Future<void> setDefaultAddress(int addressId) async {
    try {
      final url = Uri.parse(ApiConfig.setDefaultAddressUrl);

      final response = await http.post(
        url,
        headers: _headers,
        body: json.encode({'address_id': addressId}),
      );

      print('AddressService: 设置默认地址 - ${response.statusCode}');

      if (response.statusCode == 200) {
        print('AddressService: 设置默认地址成功');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '设置默认地址失败');
      }
    } catch (e) {
      print('AddressService: 设置默认地址失败 - $e');
      rethrow;
    }
  }

  /// 获取默认地址
  Future<AddressModel?> getDefaultAddress() async {
    try {
      final url = Uri.parse(ApiConfig.getDefaultAddressUrl);

      final response = await http.get(url, headers: _headers);

      print('AddressService: 获取默认地址 - ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['data'] != null) {
          return AddressModel.fromJson(responseData['data']);
        }
        return null;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '获取默认地址失败');
      }
    } catch (e) {
      print('AddressService: 获取默认地址失败 - $e');
      rethrow;
    }
  }
}
