import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart' as http_parser;
import 'package:flutter/foundation.dart';
import '../config/api/api_config.dart';
import '../utils/auth_helper.dart';

/// 语音转文字服务结果
class SpeechToTextResult {
  final String text;
  final String filename;
  final String audioFormat;
  final int audioSize;
  final bool success;
  final String? errorMessage;

  const SpeechToTextResult({
    required this.text,
    required this.filename,
    required this.audioFormat,
    required this.audioSize,
    required this.success,
    this.errorMessage,
  });

  /// 成功结果
  factory SpeechToTextResult.success({
    required String text,
    required String filename,
    required String audioFormat,
    required int audioSize,
  }) {
    return SpeechToTextResult(
      text: text,
      filename: filename,
      audioFormat: audioFormat,
      audioSize: audioSize,
      success: true,
    );
  }

  /// 失败结果
  factory SpeechToTextResult.error(String errorMessage) {
    return SpeechToTextResult(
      text: '',
      filename: '',
      audioFormat: '',
      audioSize: 0,
      success: false,
      errorMessage: errorMessage,
    );
  }

  /// 从JSON创建对象
  factory SpeechToTextResult.fromJson(Map<String, dynamic> json) {
    return SpeechToTextResult(
      text: json['text'] ?? '',
      filename: json['filename'] ?? '',
      audioFormat: json['audio_format'] ?? '',
      audioSize: json['audio_size'] ?? 0,
      success: true,
    );
  }
}

/// 语音转文字服务
class SpeechToTextService {
  /// 将音频文件转换为文字
  /// [audioFile] - 音频文件，支持wav/pcm/amr/m4a格式
  /// 返回识别的文字内容
  static Future<SpeechToTextResult> convertSpeechToText({
    required File audioFile,
  }) async {
    try {
      // 检查用户是否已登录
      if (!AuthHelper.isLoggedIn()) {
        if (kDebugMode) {
          debugPrint('🎤 STT: 用户未登录');
        }
        return SpeechToTextResult.error('用户未登录');
      }

      // 检查音频文件是否存在
      if (!await audioFile.exists()) {
        if (kDebugMode) {
          debugPrint('🎤 STT: 音频文件不存在: ${audioFile.path}');
        }
        return SpeechToTextResult.error('音频文件不存在');
      }

      // 检查文件大小（最大10MB）
      final fileSize = await audioFile.length();
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (fileSize > maxSize) {
        if (kDebugMode) {
          debugPrint('🎤 STT: 音频文件过大: ${fileSize}bytes > ${maxSize}bytes');
        }
        return SpeechToTextResult.error('音频文件过大，最大支持10MB');
      }

      // 检查文件是否为空
      if (fileSize == 0) {
        if (kDebugMode) {
          debugPrint('🎤 STT: 音频文件为空: ${audioFile.path}');
        }
        return SpeechToTextResult.error('音频文件为空，请重新录制');
      }

      // 检查文件是否太小（小于100字节可能是无效录音）
      if (fileSize < 100) {
        if (kDebugMode) {
          debugPrint('🎤 STT: 音频文件过小: ${fileSize}bytes < 100bytes');
        }
        return SpeechToTextResult.error('音频文件过小，请重新录制');
      }

      // 详细的文件信息日志
      if (kDebugMode) {
        debugPrint('🎤 STT: 音频文件详细信息');
        debugPrint('- 文件路径: ${audioFile.path}');
        debugPrint(
          '- 文件大小: ${fileSize}bytes (${(fileSize / 1024).toStringAsFixed(2)}KB)',
        );
        debugPrint('- 文件存在: ${await audioFile.exists()}');

        // 尝试读取文件头部信息
        try {
          final bytes = await audioFile.readAsBytes();
          final header = bytes
              .take(16)
              .map((b) => b.toRadixString(16).padLeft(2, '0'))
              .join(' ');
          debugPrint('- 文件头部: $header');
        } catch (e) {
          debugPrint('- 无法读取文件头部: $e');
        }
      }

      // 获取用户token
      final token = AuthHelper.getToken();
      if (token == null) {
        return SpeechToTextResult.error('获取用户token失败');
      }

      // 使用API配置
      final apiUrl = ApiConfig.speechToTextUrl;

      if (kDebugMode) {
        debugPrint('🎤 STT: 发送语音转文字请求');
        debugPrint('- API端点: $apiUrl');
        debugPrint('- 音频文件: ${audioFile.path}');
        debugPrint('- 文件大小: ${fileSize}bytes');
      }

      // 创建multipart请求
      final request = http.MultipartRequest('POST', Uri.parse(apiUrl));

      // 添加请求头
      request.headers.addAll({'Authorization': 'Bearer $token'});

      // 添加音频文件
      final audioStream = http.ByteStream(audioFile.openRead());
      final fileName = audioFile.path.split('/').last;

      // 确定MIME类型
      String? mimeType;
      if (fileName.toLowerCase().endsWith('.wav')) {
        mimeType = 'audio/wav';
      } else if (fileName.toLowerCase().endsWith('.m4a')) {
        mimeType = 'audio/m4a';
      } else if (fileName.toLowerCase().endsWith('.amr')) {
        mimeType = 'audio/amr';
      } else if (fileName.toLowerCase().endsWith('.pcm')) {
        mimeType = 'audio/pcm';
      } else {
        mimeType = 'audio/m4a'; // 默认使用m4a
      }

      final audioMultipart = http.MultipartFile(
        'audio',
        audioStream,
        fileSize,
        filename: fileName,
        contentType: http_parser.MediaType.parse(mimeType),
      );
      request.files.add(audioMultipart);

      if (kDebugMode) {
        debugPrint('🎤 STT: 音频文件信息');
        debugPrint('- 文件名: $fileName');
        debugPrint('- MIME类型: $mimeType');
        debugPrint('- 文件大小: ${fileSize}bytes');
      }

      // 发送请求
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (kDebugMode) {
        debugPrint('🎤 STT: 响应状态码: ${response.statusCode}');
        debugPrint('🎤 STT: 响应头: ${response.headers}');
        debugPrint('🎤 STT: 响应内容: ${response.body}');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (jsonData['code'] == 200) {
          final data = jsonData['data'] as Map<String, dynamic>;

          if (kDebugMode) {
            debugPrint('✅ STT: 语音识别成功');
            debugPrint('- 识别文字: ${data['text']}');
          }

          return SpeechToTextResult.fromJson(data);
        } else {
          // API返回错误
          final errorMessage = jsonData['message'] ?? '语音识别失败';
          final errorCode = jsonData['code'];
          if (kDebugMode) {
            debugPrint('❌ STT: API返回错误');
            debugPrint('- 错误代码: $errorCode');
            debugPrint('- 错误信息: $errorMessage');
            debugPrint('- 完整响应: $jsonData');
          }
          return SpeechToTextResult.error(errorMessage);
        }
      } else if (response.statusCode == 401) {
        if (kDebugMode) {
          debugPrint('❌ STT: 身份验证失败');
        }
        return SpeechToTextResult.error('登录已过期，请重新登录');
      } else {
        if (kDebugMode) {
          debugPrint('❌ STT: 网络请求失败: ${response.statusCode}');
          debugPrint('❌ STT: 响应内容: ${response.body}');
        }
        return SpeechToTextResult.error('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ STT: 语音转文字异常: $e');
      }
      return SpeechToTextResult.error('语音转文字异常: $e');
    }
  }
}
