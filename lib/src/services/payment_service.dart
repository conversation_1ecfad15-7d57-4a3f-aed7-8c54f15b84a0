import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api/api_config.dart';
import '../models/payment_model.dart';
import '../services/auth_service.dart';
import 'wechat_pay_service.dart';

/// 支付服务类 - 处理产品订单支付相关功能
class PaymentService {
  final WeChatPayService _wechatPayService = WeChatPayService();

  /// 获取认证头部
  Map<String, String> _getAuthHeaders({bool needAuth = true}) {
    final headers = <String, String>{'Content-Type': 'application/json'};

    if (needAuth) {
      final user = AuthService().currentUser;
      if (user?.token != null) {
        headers['Authorization'] = 'Bearer ${user!.token}';
      }
    }

    return headers;
  }

  /// 创建订单支付
  ///
  /// [orderId] 产品订单ID
  /// [paymentData] 支付请求数据
  ///
  /// 返回支付创建响应，包含微信支付所需参数
  Future<ProductOrderPaymentOut> createOrderPayment({
    required int orderId,
    required ProductOrderPaymentIn paymentData,
  }) async {
    try {
      // 验证支付数据
      if (!paymentData.isValid()) {
        throw PaymentException(
          type: PaymentErrorType.invalidOrder,
          message: 'jsapi支付时openid不能为空',
        );
      }

      final response = await http.post(
        Uri.parse(ApiConfig.createOrderPaymentUrl(orderId)),
        headers: _getAuthHeaders(needAuth: true),
        body: json.encode(paymentData.toJson()),
      );

      print('PaymentService: 创建支付响应状态码: ${response.statusCode}');
      print('PaymentService: 创建支付响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ProductOrderPaymentOut.fromJson(jsonData);
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['detail'] ?? '创建支付失败';

        throw PaymentException.fromStatusCode(
          response.statusCode,
          errorMessage,
        );
      }
    } catch (e) {
      print('PaymentService: 创建支付失败: $e');
      if (e is PaymentException) {
        rethrow;
      }
      throw PaymentException(
        type: PaymentErrorType.networkError,
        message: '网络请求失败: $e',
      );
    }
  }

  /// 查询订单支付状态
  ///
  /// [orderId] 产品订单ID
  ///
  /// 返回支付状态信息
  Future<ProductOrderPaymentStatusOut> getOrderPaymentStatus({
    required int orderId,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getOrderPaymentStatusUrl(orderId)),
        headers: _getAuthHeaders(needAuth: true),
      );

      print('PaymentService: 查询支付状态响应状态码: ${response.statusCode}');
      print('PaymentService: 查询支付状态响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ProductOrderPaymentStatusOut.fromJson(jsonData);
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['detail'] ?? '查询支付状态失败';

        throw PaymentException.fromStatusCode(
          response.statusCode,
          errorMessage,
        );
      }
    } catch (e) {
      print('PaymentService: 查询支付状态失败: $e');
      if (e is PaymentException) {
        rethrow;
      }
      throw PaymentException(
        type: PaymentErrorType.networkError,
        message: '网络请求失败: $e',
      );
    }
  }

  /// 同步订单支付状态
  ///
  /// [orderId] 产品订单ID
  ///
  /// 从微信支付平台主动同步订单支付状态，用于处理回调失败等异常情况
  Future<PaymentSyncResponse> syncOrderPaymentStatus({
    required int orderId,
  }) async {
    try {
      final response = await http.put(
        Uri.parse(ApiConfig.syncOrderPaymentStatusUrl(orderId)),
        headers: _getAuthHeaders(needAuth: true),
      );

      print('PaymentService: 同步支付状态响应状态码: ${response.statusCode}');
      print('PaymentService: 同步支付状态响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return PaymentSyncResponse.fromJson(jsonData);
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['detail'] ?? '同步支付状态失败';

        throw PaymentException.fromStatusCode(
          response.statusCode,
          errorMessage,
        );
      }
    } catch (e) {
      print('PaymentService: 同步支付状态失败: $e');
      if (e is PaymentException) {
        rethrow;
      }
      throw PaymentException(
        type: PaymentErrorType.syncFailed,
        message: '网络请求失败: $e',
      );
    }
  }

  /// 轮询支付状态
  ///
  /// [orderId] 产品订单ID
  /// [maxAttempts] 最大轮询次数，默认30次
  /// [intervalSeconds] 轮询间隔秒数，默认2秒
  ///
  /// 返回最终的支付状态，如果超时则返回最后一次查询结果
  Future<ProductOrderPaymentStatusOut> pollPaymentStatus({
    required int orderId,
    int maxAttempts = 30,
    int intervalSeconds = 2,
  }) async {
    for (int attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        final status = await getOrderPaymentStatus(orderId: orderId);

        // 如果已支付或订单状态不是待支付，停止轮询
        if (status.isPaid || status.orderStatus != 0) {
          return status;
        }

        // 如果不是最后一次尝试，等待后继续
        if (attempt < maxAttempts - 1) {
          await Future.delayed(Duration(seconds: intervalSeconds));
        } else {
          // 最后一次尝试，返回结果
          return status;
        }
      } catch (e) {
        print('PaymentService: 轮询支付状态失败 (第${attempt + 1}次): $e');

        // 如果是最后一次尝试，抛出异常
        if (attempt == maxAttempts - 1) {
          rethrow;
        }

        // 等待后重试
        await Future.delayed(Duration(seconds: intervalSeconds));
      }
    }

    // 理论上不会到达这里，但为了类型安全
    throw PaymentException(type: PaymentErrorType.unknown, message: '轮询支付状态超时');
  }

  /// 处理支付结果
  ///
  /// [result] 支付结果
  /// [orderId] 订单ID
  ///
  /// 根据支付结果进行相应处理
  Future<ProductOrderPaymentStatusOut?> handlePaymentResult({
    required PaymentResult result,
    required int orderId,
  }) async {
    switch (result) {
      case PaymentResult.success:
        // 支付成功，查询最新状态
        try {
          return await getOrderPaymentStatus(orderId: orderId);
        } catch (e) {
          print('PaymentService: 支付成功后查询状态失败: $e');
          // 尝试同步状态
          try {
            await syncOrderPaymentStatus(orderId: orderId);
            return await getOrderPaymentStatus(orderId: orderId);
          } catch (syncError) {
            print('PaymentService: 同步支付状态失败: $syncError');
            rethrow;
          }
        }

      case PaymentResult.failed:
        // 支付失败，可以查询状态确认
        try {
          return await getOrderPaymentStatus(orderId: orderId);
        } catch (e) {
          print('PaymentService: 支付失败后查询状态失败: $e');
          return null;
        }

      case PaymentResult.cancelled:
        // 用户取消支付，不需要特殊处理
        return null;

      case PaymentResult.unknown:
        // 未知状态，尝试查询
        try {
          return await getOrderPaymentStatus(orderId: orderId);
        } catch (e) {
          print('PaymentService: 未知状态查询失败: $e');
          return null;
        }
    }
  }

  /// 创建APP支付请求
  ///
  /// [orderId] 订单ID
  ///
  /// 创建APP支付请求的便捷方法
  Future<ProductOrderPaymentOut> createAppPayment({
    required int orderId,
  }) async {
    final paymentData = ProductOrderPaymentIn(payType: 'app');
    return await createOrderPayment(orderId: orderId, paymentData: paymentData);
  }

  /// 创建小程序支付请求
  ///
  /// [orderId] 订单ID
  /// [openid] 微信用户openid
  ///
  /// 创建小程序支付请求的便捷方法
  Future<ProductOrderPaymentOut> createMiniProgramPayment({
    required int orderId,
    required String openid,
  }) async {
    final paymentData = ProductOrderPaymentIn(payType: 'jsapi', openid: openid);
    return await createOrderPayment(orderId: orderId, paymentData: paymentData);
  }

  /// 使用微信支付进行APP支付
  ///
  /// [orderId] 订单ID
  ///
  /// 创建支付请求并调用微信支付SDK
  Future<PaymentResult> payWithWeChatApp({required int orderId}) async {
    try {
      // 1. 创建支付请求
      final paymentResult = await createAppPayment(orderId: orderId);

      // 2. 获取APP支付参数
      final appPayParams = paymentResult.appPayParams;
      if (appPayParams == null) {
        throw PaymentException(
          type: PaymentErrorType.invalidOrder,
          message: '支付参数格式错误',
        );
      }

      // 3. 调用微信支付SDK
      final wechatResult = await _wechatPayService.payWithApp(appPayParams);

      return wechatResult;
    } catch (e) {
      print('PaymentService: 微信支付失败: $e');
      rethrow;
    }
  }

  /// 初始化微信支付SDK
  ///
  /// [appId] 微信开放平台AppID
  /// [universalLink] iOS Universal Link
  Future<bool> initWeChatPay({
    required String appId,
    String? universalLink,
  }) async {
    try {
      return await _wechatPayService.initWeChatSDK(
        appId: appId,
        universalLink: universalLink,
      );
    } catch (e) {
      print('PaymentService: 初始化微信支付失败: $e');
      return false;
    }
  }

  /// 检查微信支付是否可用
  Future<bool> isWeChatPayAvailable() async {
    try {
      return await _wechatPayService.isWeChatPaySupported();
    } catch (e) {
      print('PaymentService: 检查微信支付可用性失败: $e');
      return false;
    }
  }
}
