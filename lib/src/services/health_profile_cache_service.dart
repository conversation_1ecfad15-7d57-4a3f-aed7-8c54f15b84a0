import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/health_profile_model.dart';
import 'health_profile_service.dart';
import 'auth_service.dart';

/// 健康档案缓存管理服务 - 提供缓存机制和后台加载
class HealthProfileCacheService extends ChangeNotifier {
  static final HealthProfileCacheService _instance =
      HealthProfileCacheService._internal();

  /// 获取健康档案缓存服务单例实例
  factory HealthProfileCacheService() => _instance;

  HealthProfileCacheService._internal();

  /// 缓存键名
  static const String _cacheKey = 'health_profile_cache';
  static const String _cacheTimeKey = 'health_profile_cache_time';

  /// 缓存有效期（小时）
  static const int _cacheValidityHours = 24;

  /// 服务实例
  final HealthProfileService _healthService = HealthProfileService();
  final AuthService _authService = AuthService();

  /// 内存缓存
  HealthProfileModel? _cachedProfile;
  DateTime? _lastFetchTime;
  bool _isLoading = false;
  String? _lastError;

  /// 获取当前缓存的健康档案
  HealthProfileModel? get cachedProfile => _cachedProfile;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 最后的错误信息
  String? get lastError => _lastError;

  /// 是否有缓存数据
  bool get hasCachedData => _cachedProfile != null;

  /// 初始化缓存服务
  Future<void> initialize() async {
    await _loadFromLocalCache();
  }

  /// 同步获取缓存的健康档案（如果有的话）
  HealthProfileModel? getCachedHealthProfileSync() {
    return _cachedProfile;
  }

  /// 获取健康档案（优先使用缓存，后台刷新）
  Future<HealthProfileModel?> getHealthProfile({
    bool forceRefresh = false,
  }) async {
    // 如果用户未登录，清除缓存并返回null
    if (_authService.currentUser == null) {
      await clearCache();
      return null;
    }

    // 如果不是强制刷新且有内存缓存，直接返回
    if (!forceRefresh && _cachedProfile != null) {
      // 后台检查是否需要刷新
      _backgroundRefresh();
      return _cachedProfile;
    }

    // 如果没有缓存或需要强制刷新，立即加载
    return await _loadHealthProfile();
  }

  /// 立即加载健康档案
  Future<HealthProfileModel?> _loadHealthProfile() async {
    if (_isLoading) return _cachedProfile;

    _isLoading = true;
    _lastError = null;
    notifyListeners();

    try {
      final profile = await _healthService.getHealthProfile();

      // 更新缓存
      _cachedProfile = profile;
      _lastFetchTime = DateTime.now();

      // 保存到本地缓存
      await _saveToLocalCache(profile);

      _isLoading = false;
      notifyListeners();

      return profile;
    } catch (e) {
      _lastError = e.toString();
      _isLoading = false;
      notifyListeners();

      // 如果加载失败，返回现有缓存
      return _cachedProfile;
    }
  }

  /// 后台刷新（不阻塞UI）
  void _backgroundRefresh() {
    // 检查是否需要刷新
    if (!_shouldRefreshCache()) return;

    // 异步后台刷新
    Future.microtask(() async {
      try {
        final profile = await _healthService.getHealthProfile();

        // 只有数据真正变化时才更新缓存和通知UI
        if (_hasDataChanged(profile)) {
          _cachedProfile = profile;
          _lastFetchTime = DateTime.now();
          await _saveToLocalCache(profile);
          notifyListeners();
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
        _lastError = e.toString();
      }
    });
  }

  /// 检查是否需要刷新缓存
  bool _shouldRefreshCache() {
    if (_lastFetchTime == null) return true;

    final now = DateTime.now();
    final difference = now.difference(_lastFetchTime!);
    return difference.inHours >= _cacheValidityHours;
  }

  /// 检查数据是否发生变化
  bool _hasDataChanged(HealthProfileModel? newProfile) {
    if (_cachedProfile == null && newProfile == null) return false;
    if (_cachedProfile == null || newProfile == null) return true;

    // 比较关键字段
    return _cachedProfile!.height != newProfile.height ||
        _cachedProfile!.weight != newProfile.weight ||
        _cachedProfile!.bloodType != newProfile.bloodType ||
        _cachedProfile!.updatedAt != newProfile.updatedAt;
  }

  /// 保存到本地缓存
  Future<void> _saveToLocalCache(HealthProfileModel? profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (profile != null) {
        final jsonString = json.encode(profile.toJson());
        await prefs.setString(_cacheKey, jsonString);
        await prefs.setString(_cacheTimeKey, DateTime.now().toIso8601String());
      } else {
        await prefs.remove(_cacheKey);
        await prefs.remove(_cacheTimeKey);
      }
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 从本地缓存加载
  Future<void> _loadFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_cacheKey);
      final cacheTimeString = prefs.getString(_cacheTimeKey);

      if (cachedJson != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        // 检查缓存是否过期
        if (now.difference(cacheTime).inHours < _cacheValidityHours) {
          final profileJson = json.decode(cachedJson);
          _cachedProfile = HealthProfileModel.fromJson(profileJson);
          _lastFetchTime = cacheTime;
        } else {
          // 缓存过期，清除
          await _clearLocalCache();
        }
      }
    } catch (e) {
      // 加载失败时清除缓存
      await _clearLocalCache();
    }
  }

  /// 清除本地缓存
  Future<void> _clearLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimeKey);
    } catch (e) {
      // 清除失败时静默处理
    }
  }

  /// 更新健康档案缓存
  Future<HealthProfileModel?> updateHealthProfile(
    HealthProfileModel profile,
  ) async {
    _isLoading = true;
    _lastError = null;
    notifyListeners();

    try {
      final updatedProfile = await _healthService.saveHealthProfile(profile);

      // 更新缓存
      _cachedProfile = updatedProfile;
      _lastFetchTime = DateTime.now();

      // 保存到本地缓存
      await _saveToLocalCache(updatedProfile);

      _isLoading = false;
      notifyListeners();

      return updatedProfile;
    } catch (e) {
      _lastError = e.toString();
      _isLoading = false;
      notifyListeners();

      rethrow;
    }
  }

  /// 清除所有缓存
  Future<void> clearCache() async {
    _cachedProfile = null;
    _lastFetchTime = null;
    _lastError = null;
    _isLoading = false;

    await _clearLocalCache();
    notifyListeners();
  }

  /// 预加载健康档案（应用启动时调用）
  Future<void> preloadHealthProfile() async {
    if (_authService.currentUser != null) {
      // 先加载本地缓存
      await _loadFromLocalCache();

      // 后台刷新最新数据
      _backgroundRefresh();
    }
  }

  /// 获取默认的健康指标数据（用于显示占位符）
  Map<String, String> getDefaultMetrics() {
    return {
      'weight': _cachedProfile?.weight?.toStringAsFixed(1) ?? '--',
      'height': _cachedProfile?.height?.toStringAsFixed(0) ?? '--',
      'bloodType': _cachedProfile?.bloodType ?? '--',
    };
  }

  /// 检查是否有完整的基础健康数据
  bool hasBasicHealthData() {
    return _cachedProfile != null &&
        _cachedProfile!.weight != null &&
        _cachedProfile!.height != null &&
        _cachedProfile!.bloodType != null;
  }
}
