import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile_model.dart';
import '../models/health_profile_model.dart';
import 'user_profile_service.dart';
import 'health_profile_cache_service.dart';
import 'auth_service.dart';

/// 用户资料缓存管理服务 - 提供用户基本资料和健康档案的统一缓存
class UserProfileCacheService extends ChangeNotifier {
  static final UserProfileCacheService _instance =
      UserProfileCacheService._internal();

  /// 获取用户资料缓存服务单例实例
  factory UserProfileCacheService() => _instance;

  UserProfileCacheService._internal();

  /// 缓存键名
  static const String _userProfileCacheKey = 'user_profile_cache';
  static const String _userProfileCacheTimeKey = 'user_profile_cache_time';

  /// 缓存有效期（小时）
  static const int _cacheValidityHours = 12;

  /// 服务实例
  final UserProfileService _userProfileService = UserProfileService();
  final HealthProfileCacheService _healthCacheService =
      HealthProfileCacheService();
  final AuthService _authService = AuthService();

  /// 内存缓存
  UserProfileModel? _cachedUserProfile;
  DateTime? _lastFetchTime;
  bool _isLoading = false;
  String? _lastError;

  /// 获取当前缓存的用户资料
  UserProfileModel? get cachedUserProfile => _cachedUserProfile;

  /// 获取当前缓存的健康档案
  HealthProfileModel? get cachedHealthProfile =>
      _healthCacheService.cachedProfile;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 最后的错误信息
  String? get lastError => _lastError;

  /// 是否有缓存数据
  bool get hasCachedData => _cachedUserProfile != null;

  /// 初始化缓存服务
  Future<void> initialize() async {
    await _loadFromLocalCache();
  }

  /// 获取用户资料（优先使用缓存，后台刷新）
  Future<UserProfileModel?> getUserProfile({bool forceRefresh = false}) async {
    // 如果用户未登录，清除缓存并返回null
    if (_authService.currentUser == null) {
      await clearCache();
      return null;
    }

    // 如果不是强制刷新且有内存缓存，直接返回
    if (!forceRefresh && _cachedUserProfile != null) {
      // 后台检查是否需要刷新
      _backgroundRefresh();
      return _cachedUserProfile;
    }

    // 如果没有缓存或需要强制刷新，立即加载
    return await _loadUserProfile();
  }

  /// 立即加载用户资料
  Future<UserProfileModel?> _loadUserProfile() async {
    if (_isLoading) return _cachedUserProfile;

    _isLoading = true;
    _lastError = null;
    notifyListeners();

    try {
      final profile = await _userProfileService.getUserProfile();

      // 更新缓存
      _cachedUserProfile = profile;
      _lastFetchTime = DateTime.now();

      // 保存到本地缓存
      await _saveToLocalCache(profile);

      _isLoading = false;
      notifyListeners();

      return profile;
    } catch (e) {
      _lastError = e.toString();
      _isLoading = false;
      notifyListeners();

      // 如果加载失败，返回现有缓存
      return _cachedUserProfile;
    }
  }

  /// 后台刷新（不阻塞UI）
  void _backgroundRefresh() {
    // 检查是否需要刷新
    if (!_shouldRefreshCache()) return;

    // 异步后台刷新
    Future.microtask(() async {
      try {
        final profile = await _userProfileService.getUserProfile();

        // 只有数据真正变化时才更新缓存和通知UI
        if (_hasDataChanged(profile)) {
          _cachedUserProfile = profile;
          _lastFetchTime = DateTime.now();
          await _saveToLocalCache(profile);
          notifyListeners();
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
        _lastError = e.toString();
      }
    });
  }

  /// 检查是否需要刷新缓存
  bool _shouldRefreshCache() {
    if (_lastFetchTime == null) return true;

    final now = DateTime.now();
    final difference = now.difference(_lastFetchTime!);
    return difference.inHours >= _cacheValidityHours;
  }

  /// 检查数据是否发生变化
  bool _hasDataChanged(UserProfileModel? newProfile) {
    if (_cachedUserProfile == null && newProfile == null) return false;
    if (_cachedUserProfile == null || newProfile == null) return true;

    // 比较关键字段
    return _cachedUserProfile!.nickname != newProfile.nickname ||
        _cachedUserProfile!.sex != newProfile.sex ||
        _cachedUserProfile!.birthday != newProfile.birthday ||
        _cachedUserProfile!.avatar != newProfile.avatar;
  }

  /// 保存到本地缓存
  Future<void> _saveToLocalCache(UserProfileModel? profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (profile != null) {
        final jsonString = json.encode(profile.toJson());
        await prefs.setString(_userProfileCacheKey, jsonString);
        await prefs.setString(
          _userProfileCacheTimeKey,
          DateTime.now().toIso8601String(),
        );
      } else {
        await prefs.remove(_userProfileCacheKey);
        await prefs.remove(_userProfileCacheTimeKey);
      }
    } catch (e) {
      // 保存失败时静默处理
    }
  }

  /// 从本地缓存加载
  Future<void> _loadFromLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_userProfileCacheKey);
      final cacheTimeString = prefs.getString(_userProfileCacheTimeKey);

      if (cachedJson != null && cacheTimeString != null) {
        final cacheTime = DateTime.parse(cacheTimeString);
        final now = DateTime.now();

        // 检查缓存是否过期
        if (now.difference(cacheTime).inHours < _cacheValidityHours) {
          final profileJson = json.decode(cachedJson);
          _cachedUserProfile = UserProfileModel.fromJson(profileJson);
          _lastFetchTime = cacheTime;
        } else {
          // 缓存过期，清除
          await _clearLocalCache();
        }
      }
    } catch (e) {
      // 加载失败时清除缓存
      await _clearLocalCache();
    }
  }

  /// 清除本地缓存
  Future<void> _clearLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userProfileCacheKey);
      await prefs.remove(_userProfileCacheTimeKey);
    } catch (e) {
      // 清除失败时静默处理
    }
  }

  /// 更新用户资料缓存
  Future<void> updateUserProfile({
    String? nickname,
    int? sex,
    String? birthday,
    String? avatar,
  }) async {
    _isLoading = true;
    _lastError = null;
    notifyListeners();

    try {
      await _userProfileService.updateUserProfile(
        nickname: nickname,
        sex: sex,
        birthday: birthday,
        avatar: avatar,
      );

      // 更新成功后，重新加载最新数据
      await _loadUserProfile();
    } catch (e) {
      _lastError = e.toString();
      _isLoading = false;
      notifyListeners();

      rethrow;
    }
  }

  /// 获取完整的编辑数据（用户资料 + 健康档案）
  Future<Map<String, dynamic>> getEditData({bool forceRefresh = false}) async {
    // 并行获取用户资料和健康档案
    final results = await Future.wait([
      getUserProfile(forceRefresh: forceRefresh),
      _healthCacheService.getHealthProfile(forceRefresh: forceRefresh),
    ]);

    return {'userProfile': results[0], 'healthProfile': results[1]};
  }

  /// 清除所有缓存
  Future<void> clearCache() async {
    _cachedUserProfile = null;
    _lastFetchTime = null;
    _lastError = null;
    _isLoading = false;

    await _clearLocalCache();
    notifyListeners();
  }

  /// 预加载用户资料（应用启动时调用）
  Future<void> preloadUserProfile() async {
    if (_authService.currentUser != null) {
      // 先加载本地缓存
      await _loadFromLocalCache();

      // 后台刷新最新数据
      _backgroundRefresh();
    }
  }

  /// 检查是否有完整的用户数据
  bool hasCompleteUserData() {
    return _cachedUserProfile != null &&
        _cachedUserProfile!.nickname.isNotEmpty;
  }

  /// 获取用户显示名称
  String getUserDisplayName() {
    return _cachedUserProfile?.nickname ?? '用户';
  }

  /// 获取用户性别显示文本
  String getUserGenderText() {
    if (_cachedUserProfile?.sex == null) return '未设置';
    switch (_cachedUserProfile!.sex) {
      case 1:
        return '男';
      case 2:
        return '女';
      default:
        return '未设置';
    }
  }

  /// 获取用户生日显示文本
  String getUserBirthdayText() {
    return _cachedUserProfile?.birthday ?? '未设置';
  }
}
