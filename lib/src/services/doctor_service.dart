import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api/api_config.dart';
import '../models/doctor_model.dart';
import '../services/language_service.dart';

/// 医生服务 - 管理医生数据的获取和缓存
class DoctorService {
  static final DoctorService _instance = DoctorService._internal();
  factory DoctorService() => _instance;
  DoctorService._internal();

  static const String _cacheKey = 'cached_doctors';
  static const String _cacheTimeKey = 'doctors_cache_time';
  static const int _cacheValidityHours = 24; // 缓存有效期24小时

  List<DoctorModel>? _cachedDoctors;
  DateTime? _lastFetchTime;

  /// 同步获取缓存的医生列表（如果有的话）
  List<DoctorModel> getCachedDoctorsSync() {
    return _cachedDoctors ?? [];
  }

  /// 检查是否有缓存的医生数据
  bool hasCachedDoctors() {
    return _cachedDoctors != null && _cachedDoctors!.isNotEmpty;
  }

  /// 获取医生列表（应用启动时总是检查API更新，后续调用使用缓存）
  Future<List<DoctorModel>> getDoctors({bool forceRefresh = false}) async {
    print('DoctorService: 开始获取医生数据, forceRefresh=$forceRefresh');

    // 如果不是强制刷新且有内存缓存，直接返回（用于页面间快速切换）
    if (!forceRefresh &&
        _cachedDoctors != null &&
        _cachedDoctors!.isNotEmpty &&
        _lastFetchTime != null) {
      print('DoctorService: 从内存缓存返回 ${_cachedDoctors!.length} 个医生');
      return _cachedDoctors!;
    }

    // 先获取本地缓存作为备用
    final cachedDoctors = await _getCachedDoctors();
    print('DoctorService: 本地缓存中有 ${cachedDoctors.length} 个医生');

    // 总是尝试从API获取最新数据
    print('DoctorService: 尝试从API获取最新医生数据');
    try {
      final apiDoctors = await _fetchDoctorsFromApi();
      print('DoctorService: API返回 ${apiDoctors.length} 个医生');

      if (apiDoctors.isNotEmpty) {
        // 比较API数据和本地缓存是否有差异
        final hasChanges = _hasDataChanged(cachedDoctors, apiDoctors);

        if (hasChanges) {
          print('DoctorService: 检测到数据变化，更新本地缓存');
          await _cacheDoctors(apiDoctors);
        } else {
          print('DoctorService: 数据无变化，保持现有缓存');
        }

        // 更新内存缓存和时间戳
        _cachedDoctors = apiDoctors;
        _lastFetchTime = DateTime.now();
        return apiDoctors;
      }
    } catch (e) {
      print('DoctorService: 从API获取医生数据失败: $e');
    }

    // 如果API失败但有本地缓存，使用本地缓存
    if (cachedDoctors.isNotEmpty) {
      print('DoctorService: API失败，使用本地缓存 ${cachedDoctors.length} 个医生');
      _cachedDoctors = cachedDoctors;
      return cachedDoctors;
    }

    // 如果所有方法都失败，返回空列表
    print('DoctorService: 所有方法都失败，返回空列表');
    return [];
  }

  /// 检查API数据和本地缓存是否有变化
  bool _hasDataChanged(List<DoctorModel> cached, List<DoctorModel> api) {
    if (cached.length != api.length) {
      return true;
    }

    // 简单比较：检查每个医生的基本信息是否相同
    for (int i = 0; i < cached.length; i++) {
      final cachedDoctor = cached[i];
      final apiDoctor = api[i];

      if (cachedDoctor.id != apiDoctor.id ||
          cachedDoctor.name != apiDoctor.name ||
          cachedDoctor.specialty != apiDoctor.specialty ||
          cachedDoctor.avatarUrl != apiDoctor.avatarUrl ||
          cachedDoctor.description != apiDoctor.description) {
        return true;
      }
    }

    return false;
  }

  /// 从API获取医生列表
  Future<List<DoctorModel>> _fetchDoctorsFromApi() async {
    // 获取当前语言设置
    final languageCode = LanguageService().getCurrentLanguageCode();
    final uri = Uri.parse(
      ApiConfig.getDoctorsUrl,
    ).replace(queryParameters: {'lang': languageCode});

    print('DoctorService: 发送API请求到 $uri (语言: $languageCode)');

    final response = await http
        .get(uri, headers: {'Content-Type': 'application/json'})
        .timeout(
          const Duration(seconds: 10), // 设置10秒超时
          onTimeout: () {
            print('DoctorService: API请求超时');
            throw Exception('网络请求超时，请检查网络连接');
          },
        );

    print('DoctorService: API响应状态码: ${response.statusCode}');
    print('DoctorService: API响应内容: ${response.body}');

    if (response.statusCode == 200) {
      final List<dynamic> jsonList = json.decode(response.body);
      final doctors = jsonList
          .map((json) => DoctorModel.fromJson(json, languageCode))
          .toList();
      print('DoctorService: 成功解析 ${doctors.length} 个医生数据');

      // 打印每个医生的头像URL用于调试
      for (int i = 0; i < doctors.length; i++) {
        print(
          'DoctorService: 医生${i + 1} - ${doctors[i].name}, 头像URL: ${doctors[i].avatarUrl}',
        );
        print(
          'DoctorService: 医生${i + 1} - 完整头像URL: ${doctors[i].fullAvatarUrl}',
        );
      }

      return doctors;
    } else {
      throw Exception('获取医生列表失败: ${response.statusCode}, 响应: ${response.body}');
    }
  }

  /// 缓存医生数据到本地
  Future<void> _cacheDoctors(List<DoctorModel> doctors) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonList = doctors.map((doctor) => doctor.toJson()).toList();
    await prefs.setString(_cacheKey, json.encode(jsonList));
    await prefs.setInt(_cacheTimeKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// 从本地缓存获取医生数据
  Future<List<DoctorModel>> _getCachedDoctors() async {
    final prefs = await SharedPreferences.getInstance();
    final cachedJson = prefs.getString(_cacheKey);

    if (cachedJson != null) {
      try {
        final List<dynamic> jsonList = json.decode(cachedJson);
        // 获取当前语言代码用于解析缓存数据
        final languageCode = LanguageService().getCurrentLanguageCode();
        return jsonList
            .map((json) => DoctorModel.fromJson(json, languageCode))
            .toList();
      } catch (e) {
        // 缓存数据格式错误，清除缓存
        await _clearCache();
        return [];
      }
    }

    return [];
  }

  /// 检查是否需要刷新缓存
  bool _shouldRefreshCache() {
    if (_lastFetchTime == null) {
      // 检查本地缓存时间
      return _shouldRefreshLocalCache();
    }

    final now = DateTime.now();
    final difference = now.difference(_lastFetchTime!);
    return difference.inHours >= _cacheValidityHours;
  }

  /// 检查本地缓存是否过期
  bool _shouldRefreshLocalCache() {
    return true; // 简化处理，总是检查API
  }

  /// 清除所有缓存
  Future<void> _clearCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_cacheKey);
    await prefs.remove(_cacheTimeKey);
    _cachedDoctors = null;
    _lastFetchTime = null;
  }

  /// 清除缓存（公共方法，用于语言切换时调用）
  Future<void> clearCache() async {
    await _clearCache();
    print('DoctorService: 缓存已清除，下次调用将重新从API获取数据');
  }

  /// 强制刷新医生列表
  Future<List<DoctorModel>> refreshDoctors() async {
    return await getDoctors(forceRefresh: true);
  }

  /// 根据ID获取医生信息（从缓存）
  DoctorModel? getDoctorById(int id) {
    if (_cachedDoctors == null) return null;

    try {
      return _cachedDoctors!.firstWhere((doctor) => doctor.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取包含互动状态的医生列表
  Future<List<DoctorModel>> getDoctorsWithInteraction() async {
    try {
      // 获取当前语言设置
      final languageCode = LanguageService().getCurrentLanguageCode();
      final uri = Uri.parse(
        ApiConfig.getDoctorsWithInteractionUrl,
      ).replace(queryParameters: {'lang': languageCode});

      print('DoctorService: 获取包含互动状态的医生列表，语言: $languageCode');
      print('DoctorService: 请求URL: $uri');

      final response = await http
          .get(uri, headers: {'Content-Type': 'application/json'})
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw Exception('网络请求超时，请检查网络连接');
            },
          );

      print('DoctorService: 互动状态API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonList = json.decode(response.body);
        final doctors = jsonList
            .map((json) => DoctorModel.fromJson(json, languageCode))
            .toList();
        print('DoctorService: 成功获取 ${doctors.length} 个包含互动状态的医生');
        return doctors;
      } else {
        throw Exception('获取医生互动状态失败: ${response.statusCode}');
      }
    } catch (e) {
      print('DoctorService: 获取医生互动状态失败: $e');
      rethrow;
    }
  }

  /// 根据ID获取包含互动状态的医生详细信息
  Future<DoctorModel?> getDoctorWithInteractionById(int doctorId) async {
    try {
      final doctors = await getDoctorsWithInteraction();
      return doctors.firstWhere(
        (doctor) => doctor.id == doctorId,
        orElse: () => throw Exception('医生不存在'),
      );
    } catch (e) {
      print('DoctorService: 获取医生互动详情失败: $e');
      return null;
    }
  }

  /// 搜索医生
  Future<List<DoctorModel>> searchDoctors(String query) async {
    // 确保有最新的医生数据
    final allDoctors = await getDoctors();

    if (query.isEmpty) {
      return allDoctors;
    }

    final lowercaseQuery = query.toLowerCase();
    return allDoctors.where((doctor) {
      return doctor.name.toLowerCase().contains(lowercaseQuery) ||
          (doctor.specialty?.toLowerCase().contains(lowercaseQuery) ?? false) ||
          (doctor.description?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  /// 获取缓存状态信息
  Map<String, dynamic> getCacheInfo() {
    return {
      'hasCachedData': _cachedDoctors != null,
      'cachedCount': _cachedDoctors?.length ?? 0,
      'lastFetchTime': _lastFetchTime?.toIso8601String(),
      'shouldRefresh': _shouldRefreshCache(),
    };
  }
}
