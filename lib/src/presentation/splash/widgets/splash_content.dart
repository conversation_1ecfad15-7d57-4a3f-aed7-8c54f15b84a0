import 'package:flutter/material.dart';

/// 启动页内容组件 - 健康助手主题，响应式设计
class SplashContent extends StatelessWidget {
  const SplashContent({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取屏幕尺寸信息
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;
    final shortestSide = screenSize.shortestSide;

    // 设备类型识别
    final bool isTablet = shortestSide >= 600;

    // 响应式尺寸计算 - 使用完整屏幕
    final containerWidth = screenWidth;
    final containerHeight = screenHeight;

    // 字体大小响应式计算
    final appDavaFontSize = isTablet ? 56.0 : screenWidth * 0.13;
    final chineseFontSize = isTablet ? 50.0 : screenWidth * 0.12;
    final uyghurFontSize = isTablet ? 32.0 : screenWidth * 0.075;

    // Logo尺寸响应式计算
    final logoWidth = isTablet ? 150.0 : screenWidth * 0.35;
    final logoHeight = isTablet ? 80.0 : screenWidth * 0.18;

    return Container(
      width: containerWidth,
      height: containerHeight,
      decoration: const BoxDecoration(
        color: Color(0xFF12B768), // 纯色背景 #12B768
      ),
      child: Stack(
        children: [
          // Logo PNG - 响应式定位
          Positioned(
            left: (containerWidth - logoWidth) / 2,
            top: containerHeight * 0.35,
            child: Image.asset(
              'assets/svg/splash/logo.png',
              width: logoWidth,
              height: logoHeight,
              fit: BoxFit.contain,
            ),
          ),

          // APP DAVA 文字
          Positioned(
            left: 0,
            right: 0,
            top: containerHeight * 0.51,
            child: Text(
              'APP DAVA',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: appDavaFontSize,
                fontFamily: 'League Spartan',
                fontWeight: FontWeight.w100,
              ),
            ),
          ),

          // 健康助理 中文文字
          Positioned(
            left: 0,
            right: 0,
            top: containerHeight * 0.57,
            child: Text(
              '健康助理',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: chineseFontSize,
                fontFamily: 'League Spartan',
                fontWeight: FontWeight.w100,
              ),
            ),
          ),

          // 维吾尔语文字
          Positioned(
            left: 0,
            right: 0,
            top: containerHeight * 0.64,
            child: Text(
              'ئەپداۋا ئېئايى',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: uyghurFontSize,
                fontFamily: 'UKIJTor',
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
