import 'dart:async';
import 'package:flutter/material.dart';
import '../../../config/routes/app_routes.dart';
import '../widgets/splash_content.dart';

/// 启动页屏幕 - 展示一段时间后自动跳转到主页
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // 启动计时器，2秒后自动跳转到主页
    Timer(const Duration(seconds: 2), () {
      if (mounted) AppRoutes.navigateToHome(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // 防止从启动页返回到系统桌面
      child: const Scaffold(
        // 移除SafeArea，确保内容能够填充到屏幕边缘
        body: SplashContent(),
      ),
    );
  }
}
