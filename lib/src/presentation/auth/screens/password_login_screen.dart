import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../config/routes/app_routes.dart';
import '../../../config/api/api_config.dart';
import '../../../models/user_model.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_info_manager_service.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../config/themes/app_colors.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 密码登录页面
class PasswordLoginScreen extends StatefulWidget {
  const PasswordLoginScreen({super.key});

  @override
  State<PasswordLoginScreen> createState() => _PasswordLoginScreenState();
}

class _PasswordLoginScreenState extends State<PasswordLoginScreen> {
  // 手机号控制器
  final TextEditingController _phoneController = TextEditingController();
  // 密码控制器
  final TextEditingController _passwordController = TextEditingController();
  // 当前输入焦点
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  // 密码可见性
  bool _isPasswordVisible = false;

  @override
  void dispose() {
    _phoneFocusNode.dispose();
    _passwordFocusNode.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // 处理登录按钮点击
  Future<void> _handleLogin() async {
    // 关闭键盘
    FocusScope.of(context).unfocus();

    // 验证手机号
    if (_phoneController.text.isEmpty) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseEnterPhoneNumber,
      );
      return;
    }

    if (_phoneController.text.length != 11) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseEnterCorrectPhoneNumber,
      );
      return;
    }

    // 验证密码
    if (_passwordController.text.isEmpty) {
      ToastUtil.show(context, AppLocalizations.of(context).pleaseEnterPassword);
      return;
    }

    if (_passwordController.text.length < 6) {
      ToastUtil.show(context, AppLocalizations.of(context).passwordMinLength);
      return;
    }

    // 显示加载中提示
    ToastUtil.show(context, AppLocalizations.of(context).loggingIn);

    try {
      // 发送登录请求
      final response = await http.post(
        Uri.parse(ApiConfig.passwordLoginUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'phone': _phoneController.text,
          'password': _passwordController.text,
        }),
      );

      if (!mounted) return;

      // 解析响应
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        try {
          // 登录成功，创建用户模型
          final userData = responseData['data'];
          if (userData != null) {
            final user = UserModel.fromJson(userData);

            // 保存用户状态
            await AuthService().login(user);

            // 获取完整用户信息
            try {
              await UserInfoManagerService().handleUserLogin();
            } catch (e) {
              // 即使用户信息获取失败，也不应影响登录成功状态
              assert(() {
                // ignore: avoid_print
                print('⚠️ 登录后获取用户信息失败: $e');
                return true;
              }());
            }

            // 检查组件是否仍然挂载
            if (!mounted) return;

            // 显示登录成功信息
            ToastUtil.show(
              context,
              AppLocalizations.of(context).loginSuccessful,
            );

            // 登录成功后导航到主页
            AppRoutes.navigateToHome(context);
          } else {
            // 数据为空的情况
            ToastUtil.show(
              context,
              AppLocalizations.of(context).loginSuccessButNoData,
            );
          }
        } catch (e) {
          // 数据处理异常
          ToastUtil.show(
            context,
            AppLocalizations.of(context).dataProcessingError(e.toString()),
          );
        }
      } else {
        // 登录失败
        _handleLoginError(responseData);
      }
    } catch (e) {
      if (!mounted) return;
      // 异常处理
      ToastUtil.show(
        context,
        AppLocalizations.of(context).loginProcessError(e.toString()),
      );
    }
  }

  // 处理登录错误
  void _handleLoginError(Map<String, dynamic> responseData) {
    final message = responseData['message'] ?? "未知错误";

    if (message.contains('密码错误')) {
      ToastUtil.show(context, AppLocalizations.of(context).passwordIncorrect);
      _passwordController.clear();
      _passwordFocusNode.requestFocus();
    } else if (message.contains('用户不存在') || message.contains('未注册')) {
      ToastUtil.show(context, AppLocalizations.of(context).phoneNotRegistered);
      // 延迟2秒跳转到注册页
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          AppRoutes.navigateToRegister(context);
        }
      });
    } else {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).loginFailedWithMessage(message),
      );
    }
  }

  // 处理注册按钮点击
  void _handleRegister() {
    // 导航到注册页面
    AppRoutes.navigateToRegister(context);
  }

  // 处理验证码登录按钮点击
  void _handleSmsLogin() {
    // 返回上一个页面(验证码登录页)
    Navigator.pop(context);
  }

  // 处理忘记密码按钮点击
  void _handleForgotPassword() {
    // 导航到密码重置页面
    AppRoutes.navigateToPasswordReset(context);
  }

  // 处理关闭按钮点击
  void _handleClose() {
    // 返回到上一个页面（登录页）
    Navigator.of(context).pop();
  }

  // 处理返回键
  void _handleBackPressed() {
    // 返回到上一个页面（登录页）
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final accentColor = AppColors.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBackPressed();
        }
      },
      child: GestureDetector(
        // 点击空白区域关闭键盘
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
          backgroundColor: ThemeHelper.getBackground(context),
          // 防止键盘弹出时调整内容大小
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              // 顶部装饰性背景
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: size.width,
                  height: size.height * 0.35,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                      colors: isDarkMode
                          ? [
                              accentColor.withValues(alpha: 0.08),
                              accentColor.withValues(alpha: 0.15),
                            ]
                          : [
                              accentColor.withValues(alpha: 0.05),
                              accentColor.withValues(alpha: 0.1),
                            ],
                    ),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(60),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: 50,
                        right: 40,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.white.withValues(alpha: 0.15)
                                : Colors.white.withValues(alpha: 0.4),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 120,
                        right: 120,
                        child: Container(
                          width: 70,
                          height: 70,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.white.withValues(alpha: 0.08)
                                : Colors.white.withValues(alpha: 0.2),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 底部装饰性波浪
              Positioned(
                bottom: 0,
                left: 0,
                child: ClipPath(
                  clipper: WaveClipper(),
                  child: Container(
                    width: size.width,
                    height: size.height * 0.35,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.bottomLeft,
                        end: Alignment.topRight,
                        colors: isDarkMode
                            ? [
                                accentColor.withValues(alpha: 0.08),
                                accentColor.withValues(alpha: 0.12),
                              ]
                            : [
                                accentColor.withValues(alpha: 0.05),
                                accentColor.withValues(alpha: 0.08),
                              ],
                      ),
                    ),
                  ),
                ),
              ),

              // 装饰性圆形元素
              Positioned(
                bottom: 120,
                right: -20,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? accentColor.withValues(alpha: 0.08)
                        : accentColor.withValues(alpha: 0.04),
                    border: Border.all(
                      color: isDarkMode
                          ? accentColor.withValues(alpha: 0.15)
                          : accentColor.withValues(alpha: 0.1),
                    ),
                  ),
                ),
              ),

              Positioned(
                bottom: 70,
                right: 60,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? accentColor.withValues(alpha: 0.12)
                        : accentColor.withValues(alpha: 0.07),
                  ),
                ),
              ),

              // 安全区域内容
              SafeArea(
                child: Stack(
                  children: [
                    // 关闭按钮
                    Positioned(
                      top: 16,
                      left: 16,
                      child: InkWell(
                        onTap: _handleClose,
                        borderRadius: BorderRadius.circular(30),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: ThemeHelper.getCardBackground(context),
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: isDarkMode
                                    ? Colors.black.withValues(alpha: 0.3)
                                    : Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Icon(
                              Icons.arrow_back_ios_new_rounded,
                              color: ThemeHelper.getTextPrimary(context),
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // 主内容区 - 仿照注册页面布局，顶部固定，表单可滚动
                    Positioned.fill(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 固定的标题区域
                            const SizedBox(height: 80),
                            Text(
                              AppLocalizations.of(context).passwordLogin,
                              style: TextStyle(
                                fontSize: 26,
                                fontWeight: FontWeight.bold,
                                color: ThemeHelper.getTextPrimary(context),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              AppLocalizations.of(
                                context,
                              ).passwordLoginSubtitle,
                              style: TextStyle(
                                fontSize: 15,
                                color: ThemeHelper.getTextSecondary(context),
                              ),
                            ),
                            const SizedBox(height: 30),

                            // 可滚动的表单区域
                            Expanded(
                              child: SingleChildScrollView(
                                physics: const BouncingScrollPhysics(),
                                child: Column(
                                  children: [
                                    // 手机号输入框
                                    buildInputField(
                                      icon: Icons.phone_android_rounded,
                                      hintText: AppLocalizations.of(
                                        context,
                                      ).phoneNumberHint,
                                      controller: _phoneController,
                                      focusNode: _phoneFocusNode,
                                      keyboardType: TextInputType.phone,
                                      accentColor: accentColor,
                                      maxLength: 11,
                                    ),

                                    const SizedBox(height: 20),

                                    // 密码输入框
                                    buildPasswordField(
                                      controller: _passwordController,
                                      focusNode: _passwordFocusNode,
                                      accentColor: accentColor,
                                    ),

                                    // 忘记密码按钮
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: TextButton(
                                        onPressed: _handleForgotPassword,
                                        style: TextButton.styleFrom(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                        ),
                                        child: Text(
                                          AppLocalizations.of(
                                            context,
                                          ).forgotPassword,
                                          style: TextStyle(
                                            color: accentColor,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 15),

                                    // 登录按钮
                                    Container(
                                      height: 56,
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(16),
                                        boxShadow: [
                                          BoxShadow(
                                            color: accentColor.withValues(
                                              alpha: 0.3,
                                            ),
                                            spreadRadius: 1,
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: ElevatedButton(
                                        onPressed: _handleLogin,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: accentColor,
                                          foregroundColor: Colors.white,
                                          elevation: 0,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                          ),
                                        ),
                                        child: Text(
                                          AppLocalizations.of(context).login,
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w600,
                                            letterSpacing: 2,
                                          ),
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    // 切换到验证码登录和注册按钮
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        TextButton(
                                          onPressed: _handleSmsLogin,
                                          style: TextButton.styleFrom(
                                            foregroundColor:
                                                Colors.grey.shade700,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 8,
                                            ),
                                          ),
                                          child: Text(
                                            AppLocalizations.of(
                                              context,
                                            ).smsLogin,
                                            style: TextStyle(
                                              fontSize: 15,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                        Text(
                                          '|',
                                          style: TextStyle(
                                            color: Colors.grey.shade400,
                                            fontSize: 15,
                                          ),
                                        ),
                                        TextButton(
                                          onPressed: _handleRegister,
                                          style: TextButton.styleFrom(
                                            foregroundColor:
                                                Colors.grey.shade700,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 8,
                                            ),
                                          ),
                                          child: Text(
                                            AppLocalizations.of(
                                              context,
                                            ).registerAccount,
                                            style: TextStyle(
                                              fontSize: 15,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 40),

                                    // 其他登录方式
                                    Center(
                                      child: Text(
                                        AppLocalizations.of(
                                          context,
                                        ).orOtherLoginMethods,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey.shade500,
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 20),

                                    // 第三方登录图标
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        buildSocialButton(
                                          icon: Icons.wechat,
                                          color: const Color(0xFF07C160),
                                          onTap: () {},
                                        ),
                                        const SizedBox(width: 30),
                                        buildSocialButton(
                                          icon: Icons.account_circle,
                                          color: AppColors.primary,
                                          onTap: () {},
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 30),

                                    // 底部信息
                                    Center(
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                          bottom: 20,
                                        ),
                                        child: Text(
                                          AppLocalizations.of(
                                            context,
                                          ).loginAgreement,
                                          style: TextStyle(
                                            color: ThemeHelper.getTextSecondary(
                                              context,
                                            ),
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建输入框
  Widget buildInputField({
    required IconData icon,
    required String hintText,
    required TextEditingController controller,
    required FocusNode focusNode,
    required TextInputType keyboardType,
    required Color accentColor,
    bool obscureText = false,
    int? maxLength,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.08),
            spreadRadius: isDarkMode ? 0 : 1,
            blurRadius: isDarkMode ? 4 : 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        obscureText: obscureText,
        keyboardType: keyboardType,
        maxLength: maxLength,
        style: TextStyle(
          color: ThemeHelper.getTextPrimary(context),
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          counterText: '',
          hintStyle: TextStyle(
            color: ThemeHelper.getTextHint(context),
            fontSize: 16,
          ),
          prefixIcon: Icon(icon, color: ThemeHelper.getTextHint(context)),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 18,
          ),
          border: InputBorder.none,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: accentColor, width: 1.5),
          ),
        ),
      ),
    );
  }

  // 构建密码输入框
  Widget buildPasswordField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required Color accentColor,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.08),
            spreadRadius: isDarkMode ? 0 : 1,
            blurRadius: isDarkMode ? 4 : 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        obscureText: !_isPasswordVisible,
        keyboardType: TextInputType.visiblePassword,
        style: TextStyle(
          color: ThemeHelper.getTextPrimary(context),
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: AppLocalizations.of(context).passwordHint,
          hintStyle: TextStyle(
            color: ThemeHelper.getTextHint(context),
            fontSize: 16,
          ),
          prefixIcon: Icon(
            Icons.lock_outline,
            color: ThemeHelper.getTextHint(context),
          ),
          suffixIcon: IconButton(
            icon: Icon(
              _isPasswordVisible
                  ? Icons.visibility_off_outlined
                  : Icons.visibility_outlined,
              color: ThemeHelper.getTextHint(context),
            ),
            onPressed: () {
              setState(() {
                _isPasswordVisible = !_isPasswordVisible;
              });
            },
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 18,
          ),
          border: InputBorder.none,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: accentColor, width: 1.5),
          ),
        ),
      ),
    );
  }

  // 构建社交媒体登录按钮
  Widget buildSocialButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(30),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: ThemeHelper.getCardBackground(context),
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.black.withValues(alpha: 0.3)
                  : color.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Icon(
            icon,
            color: ThemeHelper.getTextPrimary(context),
            size: 40,
          ),
        ),
      ),
    );
  }
}

/// 自定义波浪形状裁剪器
class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height * 0.7);

    final firstControlPoint = Offset(size.width * 0.25, size.height * 0.5);
    final firstEndPoint = Offset(size.width * 0.5, size.height * 0.7);
    path.quadraticBezierTo(
      firstControlPoint.dx,
      firstControlPoint.dy,
      firstEndPoint.dx,
      firstEndPoint.dy,
    );

    final secondControlPoint = Offset(size.width * 0.75, size.height * 0.9);
    final secondEndPoint = Offset(size.width, size.height * 0.7);
    path.quadraticBezierTo(
      secondControlPoint.dx,
      secondControlPoint.dy,
      secondEndPoint.dx,
      secondEndPoint.dy,
    );

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}
