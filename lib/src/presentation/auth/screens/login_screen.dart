import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../../generated/l10n/app_localizations.dart';
import '../../../config/routes/app_routes.dart';
import '../../../config/api/api_config.dart';
import '../../../models/user_model.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_info_manager_service.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../config/themes/app_colors.dart';
import '../widgets/login_form.dart';

/// 登录页面
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  // 手机号控制器
  final TextEditingController _phoneController = TextEditingController();
  // 验证码控制器
  final TextEditingController _verifyCodeController = TextEditingController();
  // 当前输入焦点
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _verifyCodeFocusNode = FocusNode();
  // 验证码状态
  bool _isGettingCode = false;
  int _countDown = 60;

  @override
  void dispose() {
    _phoneFocusNode.dispose();
    _verifyCodeFocusNode.dispose();
    _phoneController.dispose();
    _verifyCodeController.dispose();
    super.dispose();
  }

  // 处理获取验证码按钮
  Future<void> _handleGetVerificationCode() async {
    if (_phoneController.text.isEmpty) {
      ToastUtil.show(context, AppLocalizations.of(context).enterPhoneNumber);
      return;
    }

    if (_phoneController.text.length != 11) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).enterCorrectPhoneNumber,
      );
      return;
    }

    // 设置正在获取验证码状态
    setState(() {
      _isGettingCode = true;
    });

    try {
      // 发送登录验证码请求
      final response = await http.post(
        Uri.parse(ApiConfig.sendLoginSmsCodeUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({'phone': _phoneController.text}),
      );

      if (!mounted) return;

      // 解析响应
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        // 成功发送验证码
        ToastUtil.show(
          context,
          AppLocalizations.of(context).verificationCodeSent,
        );
        // 开始倒计时
        _startCountDown();
      } else {
        // 发送失败
        setState(() {
          _isGettingCode = false;
        });
        final message = responseData['message'] ?? "未知错误";
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).sendFailed}: $message',
        );
      }
    } catch (e) {
      // 异常处理
      if (!mounted) return;
      setState(() {
        _isGettingCode = false;
      });
      ToastUtil.show(
        context,
        AppLocalizations.of(context).sendVerificationCodeError(e.toString()),
      );
    }
  }

  // 开始倒计时
  void _startCountDown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;

      if (_countDown > 0) {
        setState(() {
          _countDown--;
        });
        _startCountDown();
      } else {
        setState(() {
          _isGettingCode = false;
          _countDown = 60;
        });
      }
    });
  }

  // 处理登录按钮点击
  Future<void> _handleLogin() async {
    // 关闭键盘
    FocusScope.of(context).unfocus();

    // 验证手机号
    if (_phoneController.text.isEmpty) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseEnterPhoneNumber,
      );
      return;
    }

    if (_phoneController.text.length != 11) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseEnterCorrectPhoneNumber,
      );
      return;
    }

    // 验证验证码
    if (_verifyCodeController.text.length != 6) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).enterCorrectVerificationCode,
      );
      return;
    }

    // 显示加载中提示
    ToastUtil.show(context, AppLocalizations.of(context).loggingIn);

    try {
      // 发送登录请求
      final response = await http.post(
        Uri.parse(ApiConfig.smsLoginUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'phone': _phoneController.text,
          'code': _verifyCodeController.text,
        }),
      );

      if (!mounted) return;

      // 解析响应
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        try {
          // 登录成功，创建用户模型
          final userData = responseData['data'];
          if (userData != null) {
            final user = UserModel.fromJson(userData);

            // 保存用户状态
            await AuthService().login(user);

            // 获取完整用户信息
            try {
              await UserInfoManagerService().handleUserLogin();
            } catch (e) {
              // 即使用户信息获取失败，也不应影响登录成功状态
              assert(() {
                // ignore: avoid_print
                print('⚠️ 登录后获取用户信息失败: $e');
                return true;
              }());
            }

            // 检查组件是否仍然挂载
            if (!mounted) return;

            // 显示登录成功信息
            ToastUtil.show(
              context,
              AppLocalizations.of(context).loginSuccessful,
            );

            // 登录成功后导航到主页
            AppRoutes.navigateToHome(context);
          } else {
            // 数据为空的情况
            ToastUtil.show(
              context,
              AppLocalizations.of(context).loginSuccessButNoUserData,
            );
          }
        } catch (e) {
          // 数据处理异常
          ToastUtil.show(
            context,
            AppLocalizations.of(context).processingUserDataError(e.toString()),
          );
        }
      } else {
        // 登录失败
        if (responseData['code'] == 404 ||
            (responseData['message'] != null &&
                responseData['message'].toString().contains('未注册'))) {
          // 用户未注册的情况
          ToastUtil.show(
            context,
            AppLocalizations.of(context).userNotRegisteredRedirecting,
          );

          // 等待Snackbar显示一会后跳转到注册页面
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              AppRoutes.navigateToRegister(context);
            }
          });
        } else {
          // 其他登录失败情况
          ToastUtil.show(
            context,
            '${AppLocalizations.of(context).loginFailed}: ${responseData['message'] ?? "未知错误"}',
          );
        }
      }
    } catch (e) {
      if (!mounted) return;
      // 异常处理
      ToastUtil.show(
        context,
        AppLocalizations.of(context).loginProcessError(e.toString()),
      );
    }
  }

  // 处理注册按钮点击
  void _handleRegister() {
    // 导航到注册页面
    AppRoutes.navigateToRegister(context);
  }

  // 处理密码登录按钮点击
  void _handlePasswordLogin() {
    // 导航到密码登录页面
    AppRoutes.navigateToPasswordLogin(context);
  }

  // 处理关闭按钮点击
  void _handleClose() {
    _navigateBack();
  }

  // 处理返回键
  void _handleBackPressed() {
    _navigateBack();
  }

  // 统一的返回处理逻辑
  void _navigateBack() {
    // 检查是否可以返回到上一个页面
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    } else {
      // 如果没有上一个页面，跳转到主页
      AppRoutes.navigateToHome(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final accentColor = AppColors.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBackPressed();
        }
      },
      child: GestureDetector(
        // 点击空白区域关闭键盘
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
          backgroundColor: ThemeHelper.getBackground(context),
          // 防止键盘弹出时调整内容大小
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              // 顶部装饰性背景
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: size.width,
                  height: size.height * 0.35,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                      colors: isDarkMode
                          ? [
                              accentColor.withValues(alpha: 0.08),
                              accentColor.withValues(alpha: 0.15),
                            ]
                          : [
                              accentColor.withValues(alpha: 0.05),
                              accentColor.withValues(alpha: 0.1),
                            ],
                    ),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(60),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: 50,
                        right: 40,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.white.withValues(alpha: 0.15)
                                : Colors.white.withValues(alpha: 0.4),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 120,
                        right: 120,
                        child: Container(
                          width: 70,
                          height: 70,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.white.withValues(alpha: 0.08)
                                : Colors.white.withValues(alpha: 0.2),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 底部装饰性波浪
              Positioned(
                bottom: 0,
                left: 0,
                child: ClipPath(
                  clipper: WaveClipper(),
                  child: Container(
                    width: size.width,
                    height: size.height * 0.35,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.bottomLeft,
                        end: Alignment.topRight,
                        colors: isDarkMode
                            ? [
                                accentColor.withValues(alpha: 0.08),
                                accentColor.withValues(alpha: 0.12),
                              ]
                            : [
                                accentColor.withValues(alpha: 0.05),
                                accentColor.withValues(alpha: 0.08),
                              ],
                      ),
                    ),
                  ),
                ),
              ),

              // 装饰性圆形元素
              Positioned(
                bottom: 120,
                right: -20,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? accentColor.withValues(alpha: 0.08)
                        : accentColor.withValues(alpha: 0.04),
                    border: Border.all(
                      color: isDarkMode
                          ? accentColor.withValues(alpha: 0.15)
                          : accentColor.withValues(alpha: 0.1),
                    ),
                  ),
                ),
              ),

              Positioned(
                bottom: 70,
                right: 60,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? accentColor.withValues(alpha: 0.12)
                        : accentColor.withValues(alpha: 0.07),
                  ),
                ),
              ),

              // 安全区域内容
              SafeArea(
                child: Stack(
                  children: [
                    // 关闭按钮
                    Positioned(
                      top: 16,
                      left: 16,
                      child: InkWell(
                        onTap: _handleClose,
                        borderRadius: BorderRadius.circular(30),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: ThemeHelper.getCardBackground(context),
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: isDarkMode
                                    ? Colors.black.withValues(alpha: 0.3)
                                    : Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Icon(
                              Icons.arrow_back_ios_new_rounded,
                              color: ThemeHelper.getTextPrimary(context),
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // 主内容区 - 顶部固定标题，下方可滚动表单
                    Positioned.fill(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 固定的标题区域
                            const SizedBox(height: 80),
                            Text(
                              AppLocalizations.of(context).welcomeBackTitle,
                              style: TextStyle(
                                fontSize: 26,
                                fontWeight: FontWeight.bold,
                                color: ThemeHelper.getTextPrimary(context),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              AppLocalizations.of(
                                context,
                              ).loginWithPhoneSubtitle,
                              style: TextStyle(
                                fontSize: 15,
                                color: ThemeHelper.getTextSecondary(context),
                              ),
                            ),
                            const SizedBox(height: 30),

                            // 可滚动的表单区域
                            Expanded(
                              child: SingleChildScrollView(
                                physics: const BouncingScrollPhysics(),
                                child: Column(
                                  children: [
                                    // 登录表单
                                    LoginFormWithController(
                                      phoneController: _phoneController,
                                      verifyCodeController:
                                          _verifyCodeController,
                                      phoneFocusNode: _phoneFocusNode,
                                      verifyCodeFocusNode: _verifyCodeFocusNode,
                                      onLoginPressed: _handleLogin,
                                      onRegisterPressed: _handleRegister,
                                      onPasswordLoginPressed:
                                          _handlePasswordLogin,
                                      isGettingCode: _isGettingCode,
                                      countDown: _countDown,
                                      onGetCode: _handleGetVerificationCode,
                                    ),

                                    const SizedBox(height: 40),

                                    // 底部信息
                                    Center(
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                          bottom: 20,
                                        ),
                                        child: Text(
                                          AppLocalizations.of(
                                            context,
                                          ).loginAgreementText,
                                          style: TextStyle(
                                            color: ThemeHelper.getTextSecondary(
                                              context,
                                            ),
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 自定义波浪形状裁剪器
class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height * 0.7);

    final firstControlPoint = Offset(size.width * 0.25, size.height * 0.5);
    final firstEndPoint = Offset(size.width * 0.5, size.height * 0.7);
    path.quadraticBezierTo(
      firstControlPoint.dx,
      firstControlPoint.dy,
      firstEndPoint.dx,
      firstEndPoint.dy,
    );

    final secondControlPoint = Offset(size.width * 0.75, size.height * 0.9);
    final secondEndPoint = Offset(size.width, size.height * 0.7);
    path.quadraticBezierTo(
      secondControlPoint.dx,
      secondControlPoint.dy,
      secondEndPoint.dx,
      secondEndPoint.dy,
    );

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}
