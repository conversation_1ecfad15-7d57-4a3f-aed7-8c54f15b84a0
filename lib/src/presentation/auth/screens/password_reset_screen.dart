import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../config/api/api_config.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../config/themes/app_colors.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 密码重置页面
class PasswordResetScreen extends StatefulWidget {
  const PasswordResetScreen({super.key});

  @override
  State<PasswordResetScreen> createState() => _PasswordResetScreenState();
}

class _PasswordResetScreenState extends State<PasswordResetScreen> {
  // 表单控制器
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  // 焦点节点
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _codeFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _confirmPasswordFocusNode = FocusNode();

  // 密码可见性
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  // 验证码发送状态
  bool _isCodeButtonEnabled = true;
  int _countdown = 0;
  Timer? _timer;

  // 提交按钮加载状态
  bool _isSubmitting = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _phoneFocusNode.dispose();
    _codeFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    _timer?.cancel();
    super.dispose();
  }

  /// 验证手机号格式
  bool _validatePhone(String phone) {
    return phone.isNotEmpty &&
        phone.length == 11 &&
        RegExp(r'^\d+$').hasMatch(phone);
  }

  /// 开始倒计时
  void _startCountdown() {
    setState(() {
      _isCodeButtonEnabled = false;
      _countdown = 60;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdown > 0) {
          _countdown--;
        } else {
          _isCodeButtonEnabled = true;
          timer.cancel();
        }
      });
    });
  }

  /// 发送验证码
  Future<void> _sendVerificationCode() async {
    final phone = _phoneController.text.trim();

    if (!_validatePhone(phone)) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).enterCorrectPhoneNumber,
      );
      return;
    }

    try {
      // 显示加载提示
      ToastUtil.show(
        context,
        AppLocalizations.of(context).sendingVerificationCode,
      );

      // 发送重置密码验证码请求
      final response = await http.post(
        Uri.parse(ApiConfig.sendForgotPasswordCodeUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({'phone': phone}),
      );

      if (!mounted) return;

      // 解析响应
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        // 发送成功
        ToastUtil.show(
          context,
          AppLocalizations.of(context).verificationCodeSentToPhone,
        );
        _startCountdown();
      } else {
        // 发送失败
        final message =
            responseData['message'] ??
            AppLocalizations.of(context).sendVerificationCodeFailed;
        ToastUtil.show(context, message);
      }
    } catch (e) {
      if (!mounted) return;
      ToastUtil.show(
        context,
        AppLocalizations.of(context).networkConnectionFailed,
      );
    }
  }

  /// 提交密码重置
  Future<void> _submitReset() async {
    // 关闭键盘
    FocusScope.of(context).unfocus();

    final phone = _phoneController.text.trim();
    final code = _codeController.text.trim();
    final password = _passwordController.text;
    final confirmPassword = _confirmPasswordController.text;

    // 表单验证
    if (!_validatePhone(phone)) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).enterCorrectPhoneNumber,
      );
      return;
    }

    if (code.isEmpty) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).enterVerificationCode,
      );
      return;
    }

    if (code.length != 6) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).verificationCodeSixDigits,
      );
      return;
    }

    if (password.isEmpty) {
      ToastUtil.show(context, AppLocalizations.of(context).enterNewPassword);
      return;
    }

    if (password.length < 6) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).passwordMinSixCharacters,
      );
      return;
    }

    if (confirmPassword != password) {
      ToastUtil.show(context, AppLocalizations.of(context).passwordsDoNotMatch);
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // 显示加载提示
      ToastUtil.show(
        context,
        AppLocalizations.of(context).resettingPasswordLoading,
      );

      final response = await http.post(
        Uri.parse(ApiConfig.forgotPasswordUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'phone': phone,
          'code': code,
          'new_password': password,
        }),
      );

      if (!mounted) return;

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        // 重置成功
        ToastUtil.show(
          context,
          AppLocalizations.of(context).passwordResetSuccess,
        );

        // 延迟1秒后返回登录页面
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            Navigator.of(context).pop();
          }
        });
      } else {
        // 重置失败
        final message =
            responseData['message'] ??
            AppLocalizations.of(context).passwordResetFailed;
        ToastUtil.show(context, message);
      }
    } catch (e) {
      if (!mounted) return;
      ToastUtil.show(
        context,
        AppLocalizations.of(context).networkConnectionFailedRetry,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  /// 处理返回
  void _handleBack() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final accentColor = AppColors.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBack();
        }
      },
      child: GestureDetector(
        // 点击空白区域关闭键盘
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
          backgroundColor: ThemeHelper.getBackground(context),
          // 防止键盘弹出时调整内容大小
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              // 顶部装饰性背景
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: size.width,
                  height: size.height * 0.35,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                      colors: isDarkMode
                          ? [
                              accentColor.withValues(alpha: 0.08),
                              accentColor.withValues(alpha: 0.15),
                            ]
                          : [
                              accentColor.withValues(alpha: 0.05),
                              accentColor.withValues(alpha: 0.1),
                            ],
                    ),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(60),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: 50,
                        right: 40,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.white.withValues(alpha: 0.15)
                                : Colors.white.withValues(alpha: 0.4),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 120,
                        right: 120,
                        child: Container(
                          width: 70,
                          height: 70,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.white.withValues(alpha: 0.08)
                                : Colors.white.withValues(alpha: 0.2),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 底部装饰性波浪
              Positioned(
                bottom: 0,
                left: 0,
                child: ClipPath(
                  clipper: WaveClipper(),
                  child: Container(
                    width: size.width,
                    height: size.height * 0.35,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.bottomLeft,
                        end: Alignment.topRight,
                        colors: isDarkMode
                            ? [
                                accentColor.withValues(alpha: 0.08),
                                accentColor.withValues(alpha: 0.12),
                              ]
                            : [
                                accentColor.withValues(alpha: 0.05),
                                accentColor.withValues(alpha: 0.08),
                              ],
                      ),
                    ),
                  ),
                ),
              ),

              // 装饰性圆形元素
              Positioned(
                bottom: 120,
                right: -20,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? accentColor.withValues(alpha: 0.08)
                        : accentColor.withValues(alpha: 0.04),
                    border: Border.all(
                      color: isDarkMode
                          ? accentColor.withValues(alpha: 0.15)
                          : accentColor.withValues(alpha: 0.1),
                    ),
                  ),
                ),
              ),

              Positioned(
                bottom: 70,
                right: 60,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? accentColor.withValues(alpha: 0.12)
                        : accentColor.withValues(alpha: 0.07),
                  ),
                ),
              ),

              // 安全区域内容
              SafeArea(
                child: Stack(
                  children: [
                    // 返回按钮
                    Positioned(
                      top: 16,
                      left: 16,
                      child: InkWell(
                        onTap: _handleBack,
                        borderRadius: BorderRadius.circular(30),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: ThemeHelper.getCardBackground(context),
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: isDarkMode
                                    ? Colors.black.withValues(alpha: 0.3)
                                    : Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Icon(
                              Icons.arrow_back_ios_new_rounded,
                              color: ThemeHelper.getTextPrimary(context),
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // 主内容区
                    Positioned.fill(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 固定的标题区域
                            const SizedBox(height: 80),
                            Text(
                              AppLocalizations.of(context).resetPasswordTitle,
                              style: TextStyle(
                                fontSize: 26,
                                fontWeight: FontWeight.bold,
                                color: ThemeHelper.getTextPrimary(context),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              AppLocalizations.of(
                                context,
                              ).resetPasswordDescription,
                              style: TextStyle(
                                fontSize: 15,
                                color: ThemeHelper.getTextSecondary(context),
                              ),
                            ),
                            const SizedBox(height: 30),

                            // 可滚动的表单区域
                            Expanded(
                              child: SingleChildScrollView(
                                physics: const BouncingScrollPhysics(),
                                child: Column(
                                  children: [
                                    // 手机号输入框
                                    _buildInputField(
                                      icon: Icons.phone_android_rounded,
                                      hintText: AppLocalizations.of(
                                        context,
                                      ).phoneNumberHint,
                                      controller: _phoneController,
                                      focusNode: _phoneFocusNode,
                                      keyboardType: TextInputType.phone,
                                      accentColor: accentColor,
                                      maxLength: 11,
                                    ),

                                    const SizedBox(height: 20),

                                    // 验证码输入框和发送按钮
                                    Row(
                                      children: [
                                        Expanded(
                                          child: _buildInputField(
                                            icon: Icons.message_rounded,
                                            hintText: AppLocalizations.of(
                                              context,
                                            ).verificationCodeHint,
                                            controller: _codeController,
                                            focusNode: _codeFocusNode,
                                            keyboardType: TextInputType.number,
                                            accentColor: accentColor,
                                            maxLength: 6,
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Container(
                                          height: 56,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                            boxShadow: _isCodeButtonEnabled
                                                ? [
                                                    BoxShadow(
                                                      color: accentColor
                                                          .withValues(
                                                            alpha: 0.2,
                                                          ),
                                                      spreadRadius: 1,
                                                      blurRadius: 6,
                                                      offset: const Offset(
                                                        0,
                                                        2,
                                                      ),
                                                    ),
                                                  ]
                                                : [],
                                          ),
                                          child: ElevatedButton(
                                            onPressed: _isCodeButtonEnabled
                                                ? _sendVerificationCode
                                                : null,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  _isCodeButtonEnabled
                                                  ? accentColor
                                                  : Colors.grey.shade400,
                                              foregroundColor: Colors.white,
                                              elevation: 0,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 16,
                                                  ),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                              ),
                                            ),
                                            child: Text(
                                              _isCodeButtonEnabled
                                                  ? AppLocalizations.of(
                                                      context,
                                                    ).getVerificationCodeButton
                                                  : AppLocalizations.of(
                                                      context,
                                                    ).resendCountdown(
                                                      _countdown,
                                                    ),
                                              style: const TextStyle(
                                                fontSize: 13,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 20),

                                    // 新密码输入框
                                    _buildPasswordField(
                                      hintText: AppLocalizations.of(
                                        context,
                                      ).enterNewPassword,
                                      controller: _passwordController,
                                      focusNode: _passwordFocusNode,
                                      accentColor: accentColor,
                                      isVisible: _isPasswordVisible,
                                      onVisibilityToggle: () {
                                        setState(() {
                                          _isPasswordVisible =
                                              !_isPasswordVisible;
                                        });
                                      },
                                    ),

                                    const SizedBox(height: 20),

                                    // 确认密码输入框
                                    _buildPasswordField(
                                      hintText: AppLocalizations.of(
                                        context,
                                      ).enterNewPasswordAgain,
                                      controller: _confirmPasswordController,
                                      focusNode: _confirmPasswordFocusNode,
                                      accentColor: accentColor,
                                      isVisible: _isConfirmPasswordVisible,
                                      onVisibilityToggle: () {
                                        setState(() {
                                          _isConfirmPasswordVisible =
                                              !_isConfirmPasswordVisible;
                                        });
                                      },
                                    ),

                                    const SizedBox(height: 30),

                                    // 确认重置按钮
                                    Container(
                                      height: 56,
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(16),
                                        boxShadow: [
                                          BoxShadow(
                                            color: accentColor.withValues(
                                              alpha: 0.3,
                                            ),
                                            spreadRadius: 1,
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: ElevatedButton(
                                        onPressed: _isSubmitting
                                            ? null
                                            : _submitReset,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: accentColor,
                                          foregroundColor: Colors.white,
                                          elevation: 0,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                          ),
                                        ),
                                        child: _isSubmitting
                                            ? const SizedBox(
                                                width: 20,
                                                height: 20,
                                                child: CircularProgressIndicator(
                                                  valueColor:
                                                      AlwaysStoppedAnimation<
                                                        Color
                                                      >(Colors.white),
                                                  strokeWidth: 2,
                                                ),
                                              )
                                            : Text(
                                                AppLocalizations.of(
                                                  context,
                                                ).confirmReset,
                                                style: const TextStyle(
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.w600,
                                                  letterSpacing: 2,
                                                ),
                                              ),
                                      ),
                                    ),

                                    const SizedBox(height: 30),

                                    // 底部提示信息
                                    Center(
                                      child: Padding(
                                        padding: const EdgeInsets.only(
                                          bottom: 20,
                                        ),
                                        child: Text(
                                          AppLocalizations.of(
                                            context,
                                          ).contactCustomerService,
                                          style: TextStyle(
                                            color: ThemeHelper.getTextSecondary(
                                              context,
                                            ),
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建普通输入框
  Widget _buildInputField({
    required IconData icon,
    required String hintText,
    required TextEditingController controller,
    required FocusNode focusNode,
    required TextInputType keyboardType,
    required Color accentColor,
    int? maxLength,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.08),
            spreadRadius: isDarkMode ? 0 : 1,
            blurRadius: isDarkMode ? 4 : 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        keyboardType: keyboardType,
        maxLength: maxLength,
        style: TextStyle(
          color: ThemeHelper.getTextPrimary(context),
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          counterText: '',
          hintStyle: TextStyle(
            color: ThemeHelper.getTextHint(context),
            fontSize: 16,
          ),
          prefixIcon: Icon(icon, color: ThemeHelper.getTextHint(context)),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 18,
          ),
          border: InputBorder.none,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: accentColor, width: 1.5),
          ),
        ),
      ),
    );
  }

  /// 构建密码输入框
  Widget _buildPasswordField({
    required String hintText,
    required TextEditingController controller,
    required FocusNode focusNode,
    required Color accentColor,
    required bool isVisible,
    required VoidCallback onVisibilityToggle,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.08),
            spreadRadius: isDarkMode ? 0 : 1,
            blurRadius: isDarkMode ? 4 : 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        obscureText: !isVisible,
        keyboardType: TextInputType.visiblePassword,
        style: TextStyle(
          color: ThemeHelper.getTextPrimary(context),
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(
            color: ThemeHelper.getTextHint(context),
            fontSize: 16,
          ),
          prefixIcon: Icon(
            Icons.lock_outline,
            color: ThemeHelper.getTextHint(context),
          ),
          suffixIcon: IconButton(
            icon: Icon(
              isVisible
                  ? Icons.visibility_off_outlined
                  : Icons.visibility_outlined,
              color: ThemeHelper.getTextHint(context),
            ),
            onPressed: onVisibilityToggle,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 18,
          ),
          border: InputBorder.none,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: accentColor, width: 1.5),
          ),
        ),
      ),
    );
  }
}

/// 自定义波浪形状裁剪器（与登录页面保持一致）
class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height * 0.7);

    final firstControlPoint = Offset(size.width * 0.25, size.height * 0.5);
    final firstEndPoint = Offset(size.width * 0.5, size.height * 0.7);
    path.quadraticBezierTo(
      firstControlPoint.dx,
      firstControlPoint.dy,
      firstEndPoint.dx,
      firstEndPoint.dy,
    );

    final secondControlPoint = Offset(size.width * 0.75, size.height * 0.9);
    final secondEndPoint = Offset(size.width, size.height * 0.7);
    path.quadraticBezierTo(
      secondControlPoint.dx,
      secondControlPoint.dy,
      secondEndPoint.dx,
      secondEndPoint.dy,
    );

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}
