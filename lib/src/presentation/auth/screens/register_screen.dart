import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../config/routes/app_routes.dart';
import '../../../config/api/api_config.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../config/themes/app_colors.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../models/user_model.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_info_manager_service.dart';

/// 注册页面
class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  // 用户名控制器
  final TextEditingController _usernameController = TextEditingController();
  // 手机号控制器
  final TextEditingController _phoneController = TextEditingController();
  // 验证码控制器
  final TextEditingController _verifyCodeController = TextEditingController();
  // 密码控制器
  final TextEditingController _passwordController = TextEditingController();
  // 确认密码控制器
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  // 当前输入焦点
  final FocusNode _usernameFocusNode = FocusNode();
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _verifyCodeFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _confirmPasswordFocusNode = FocusNode();

  // 验证码状态
  bool _isGettingCode = false;
  int _countDown = 60;

  // 是否同意协议
  bool _agreedToTerms = false;

  @override
  void dispose() {
    _usernameFocusNode.dispose();
    _phoneFocusNode.dispose();
    _verifyCodeFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();

    _usernameController.dispose();
    _phoneController.dispose();
    _verifyCodeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // 处理获取验证码按钮
  Future<void> _handleGetVerificationCode() async {
    if (_phoneController.text.isEmpty) {
      ToastUtil.show(context, AppLocalizations.of(context).enterPhoneNumber);
      return;
    }

    if (_phoneController.text.length != 11) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).enterCorrectPhoneNumber,
      );
      return;
    }

    // 设置正在获取验证码状态
    setState(() {
      _isGettingCode = true;
    });

    try {
      // 发送注册验证码请求
      final response = await http.post(
        Uri.parse(ApiConfig.sendRegisterSmsCodeUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({'phone': _phoneController.text}),
      );

      if (!mounted) return;

      // 解析响应
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        // 成功发送验证码
        ToastUtil.show(
          context,
          AppLocalizations.of(context).verificationCodeSentToPhone,
        );
        // 开始倒计时
        _startCountDown();
      } else {
        // 发送失败
        setState(() {
          _isGettingCode = false;
        });
        final message = responseData['message'] ?? "未知错误";
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).sendFailed}: $message',
        );
      }
    } catch (e) {
      // 异常处理
      if (!mounted) return;
      setState(() {
        _isGettingCode = false;
      });
      ToastUtil.show(
        context,
        AppLocalizations.of(context).sendVerificationCodeError(e.toString()),
      );
    }
  }

  // 开始倒计时
  void _startCountDown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;

      if (_countDown > 0) {
        setState(() {
          _countDown--;
        });
        _startCountDown();
      } else {
        setState(() {
          _isGettingCode = false;
          _countDown = 60;
        });
      }
    });
  }

  // 处理注册按钮点击
  Future<void> _handleRegister() async {
    // 关闭键盘
    FocusScope.of(context).unfocus();

    // 验证用户名
    if (_usernameController.text.isEmpty) {
      ToastUtil.show(context, AppLocalizations.of(context).enterUsername);
      return;
    }

    // 验证手机号
    if (_phoneController.text.isEmpty) {
      ToastUtil.show(context, AppLocalizations.of(context).enterPhoneNumber);
      return;
    }

    if (_phoneController.text.length != 11) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).enterCorrectPhoneNumber,
      );
      return;
    }

    // 验证验证码
    if (_verifyCodeController.text.length != 6) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).enterCorrectVerificationCode,
      );
      return;
    }

    // 验证密码
    if (_passwordController.text.length < 6) {
      ToastUtil.show(context, AppLocalizations.of(context).passwordMinLength6);
      return;
    }

    // 验证确认密码
    if (_passwordController.text != _confirmPasswordController.text) {
      ToastUtil.show(context, AppLocalizations.of(context).passwordsNotMatch);
      return;
    }

    // 验证是否同意协议
    if (!_agreedToTerms) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).agreeToUserAgreement,
      );
      return;
    }

    // 执行注册操作
    ToastUtil.show(context, AppLocalizations.of(context).registering);

    try {
      // 发送注册请求
      final response = await http.post(
        Uri.parse(ApiConfig.registerUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': _usernameController.text,
          'phone': _phoneController.text,
          'password': _passwordController.text,
          'sms_code': _verifyCodeController.text,
        }),
      );

      if (!mounted) return;

      // 解析响应
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200 && responseData['code'] == 200) {
        // 注册成功，显示成功提示
        ToastUtil.show(context, AppLocalizations.of(context).registerSuccess);

        // 注册成功后直接调用登录API来获取完整的登录信息
        await _autoLoginAfterRegister();
      } else {
        // 注册失败
        setState(() {
          // 根据不同错误类型提供更具体的反馈
          final message = responseData['message'] ?? "未知错误";

          // 根据错误消息类型提供不同处理
          if (message.contains('手机号格式不正确')) {
            ToastUtil.show(
              context,
              AppLocalizations.of(context).phoneFormatIncorrect,
            );
            _phoneFocusNode.requestFocus(); // 自动聚焦到手机号输入框
          } else if (message.contains('短信验证码错误') || message.contains('已过期')) {
            ToastUtil.show(
              context,
              AppLocalizations.of(context).verificationCodeIncorrectOrExpired,
            );
            _verifyCodeFocusNode.requestFocus(); // 聚焦到验证码输入框
            // 清空验证码输入框
            _verifyCodeController.clear();
          } else if (message.contains('用户名已存在')) {
            ToastUtil.show(
              context,
              AppLocalizations.of(context).usernameAlreadyExists,
            );
            _usernameController.selection = TextSelection(
              baseOffset: 0,
              extentOffset: _usernameController.text.length,
            ); // 选中全部文本
            _usernameFocusNode.requestFocus(); // 聚焦到用户名输入框
          } else if (message.contains('手机号已注册')) {
            ToastUtil.show(
              context,
              AppLocalizations.of(context).phoneAlreadyRegistered,
            );
            // 2秒后跳转到登录页
            Future.delayed(const Duration(seconds: 2), () {
              if (mounted) {
                Navigator.pop(context);
              }
            });
          } else {
            // 通用错误
            ToastUtil.show(context, '注册失败: $message');
          }
        });
      }
    } catch (e) {
      if (!mounted) return;
      // 异常处理
      ToastUtil.show(context, '注册过程中出错: $e');
    }
  }

  // 注册成功后自动登录
  Future<void> _autoLoginAfterRegister() async {
    try {
      assert(() {
        // ignore: avoid_print
        print('🔄 注册成功，开始自动登录...');
        return true;
      }());

      // 使用手机号和密码自动登录
      final response = await http.post(
        Uri.parse(ApiConfig.passwordLoginUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'phone': _phoneController.text,
          'password': _passwordController.text,
        }),
      );

      if (!mounted) return;

      final responseData = jsonDecode(response.body);

      assert(() {
        // ignore: avoid_print
        print('🔄 自动登录API响应: ${response.statusCode}, ${responseData['code']}');
        return true;
      }());

      if (response.statusCode == 200 && responseData['code'] == 200) {
        try {
          // 登录成功，创建用户模型
          final userData = responseData['data'];
          if (userData != null) {
            final user = UserModel.fromJson(userData);

            assert(() {
              // ignore: avoid_print
              print(
                '✅ 自动登录成功，用户ID: ${user.id}, Token: ${user.token.substring(0, 20)}...',
              );
              return true;
            }());

            // 保存用户状态
            await AuthService().login(user);

            // 获取完整用户信息
            try {
              await UserInfoManagerService().handleUserLogin();
              assert(() {
                // ignore: avoid_print
                print('✅ 用户信息获取成功');
                return true;
              }());
            } catch (e) {
              // 即使用户信息获取失败，也不应影响登录成功状态
              assert(() {
                // ignore: avoid_print
                print('⚠️ 自动登录后获取用户信息失败: $e');
                return true;
              }());
            }

            // 显示登录成功提示
            if (mounted) {
              ToastUtil.show(
                context,
                AppLocalizations.of(context).loginSuccessful,
              );
            }

            // 延迟一下再跳转，让用户看到成功提示
            Future.delayed(const Duration(seconds: 1), () {
              if (mounted) {
                // 自动登录成功后导航到主页
                AppRoutes.navigateToHome(context);
              }
            });
          } else {
            // 登录数据为空
            assert(() {
              // ignore: avoid_print
              print('❌ 自动登录失败：登录数据为空');
              return true;
            }());
            _showAutoLoginFailedAndNavigateToLogin('登录数据为空');
          }
        } catch (e) {
          // 数据处理异常
          assert(() {
            // ignore: avoid_print
            print('❌ 自动登录数据处理失败: $e');
            return true;
          }());
          _showAutoLoginFailedAndNavigateToLogin('数据处理失败: $e');
        }
      } else {
        // 自动登录失败
        final message = responseData['message'] ?? '未知错误';
        assert(() {
          // ignore: avoid_print
          print('❌ 自动登录失败: $message');
          return true;
        }());
        _showAutoLoginFailedAndNavigateToLogin('自动登录失败: $message');
      }
    } catch (e) {
      // 自动登录异常
      assert(() {
        // ignore: avoid_print
        print('❌ 自动登录异常: $e');
        return true;
      }());
      _showAutoLoginFailedAndNavigateToLogin('网络错误: $e');
    }
  }

  // 显示自动登录失败提示并跳转到登录页面
  void _showAutoLoginFailedAndNavigateToLogin(String errorMessage) {
    if (mounted) {
      ToastUtil.show(context, '自动登录失败，请手动登录');
    }

    // 延迟跳转到登录页面
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.pop(context);
      }
    });
  }

  // 处理直接登录按钮点击
  void _handleDirectLogin() {
    // 导航回登录页面
    Navigator.pop(context);
  }

  // 处理关闭按钮点击
  void _handleClose() {
    // 返回到上一个页面（登录页）
    Navigator.of(context).pop();
  }

  // 处理返回键
  void _handleBackPressed() {
    // 返回到上一个页面（登录页）
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final accentColor = AppColors.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBackPressed();
        }
      },
      child: GestureDetector(
        // 点击空白区域关闭键盘
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
          backgroundColor: ThemeHelper.getBackground(context),
          // 防止键盘弹出时调整内容大小
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              // 顶部装饰性背景
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: size.width,
                  height: size.height * 0.35,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                      colors: isDarkMode
                          ? [
                              accentColor.withValues(alpha: 0.08),
                              accentColor.withValues(alpha: 0.15),
                            ]
                          : [
                              accentColor.withValues(alpha: 0.05),
                              accentColor.withValues(alpha: 0.1),
                            ],
                    ),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(60),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: 50,
                        right: 40,
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.white.withValues(alpha: 0.15)
                                : Colors.white.withValues(alpha: 0.4),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 120,
                        right: 120,
                        child: Container(
                          width: 70,
                          height: 70,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.white.withValues(alpha: 0.08)
                                : Colors.white.withValues(alpha: 0.2),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 底部装饰性波浪
              Positioned(
                bottom: 0,
                left: 0,
                child: ClipPath(
                  clipper: WaveClipper(),
                  child: Container(
                    width: size.width,
                    height: size.height * 0.35,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.bottomLeft,
                        end: Alignment.topRight,
                        colors: isDarkMode
                            ? [
                                accentColor.withValues(alpha: 0.08),
                                accentColor.withValues(alpha: 0.12),
                              ]
                            : [
                                accentColor.withValues(alpha: 0.05),
                                accentColor.withValues(alpha: 0.08),
                              ],
                      ),
                    ),
                  ),
                ),
              ),

              // 装饰性圆形元素
              Positioned(
                bottom: 120,
                right: -20,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? accentColor.withValues(alpha: 0.08)
                        : accentColor.withValues(alpha: 0.04),
                    border: Border.all(
                      color: isDarkMode
                          ? accentColor.withValues(alpha: 0.15)
                          : accentColor.withValues(alpha: 0.1),
                    ),
                  ),
                ),
              ),

              Positioned(
                bottom: 70,
                right: 60,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? accentColor.withValues(alpha: 0.12)
                        : accentColor.withValues(alpha: 0.07),
                  ),
                ),
              ),

              // 安全区域内容
              SafeArea(
                child: Stack(
                  children: [
                    // 关闭按钮
                    Positioned(
                      top: 16,
                      left: 16,
                      child: InkWell(
                        onTap: _handleClose,
                        borderRadius: BorderRadius.circular(30),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: ThemeHelper.getCardBackground(context),
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: isDarkMode
                                    ? Colors.black.withValues(alpha: 0.3)
                                    : Colors.black.withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Icon(
                              Icons.arrow_back_ios_new_rounded,
                              color: ThemeHelper.getTextPrimary(context),
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // 主内容区
                    Positioned.fill(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 标题区域
                            const SizedBox(height: 80),
                            Text(
                              AppLocalizations.of(context).registerTitle,
                              style: TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color: ThemeHelper.getTextPrimary(context),
                              ),
                            ),
                            const SizedBox(height: 30),

                            // 注册表单
                            Expanded(
                              child: SingleChildScrollView(
                                physics: const BouncingScrollPhysics(),
                                child: Column(
                                  children: [
                                    // 用户名输入框
                                    _buildInputField(
                                      icon: Icons.person_outline,
                                      hintText: AppLocalizations.of(
                                        context,
                                      ).usernameHint,
                                      controller: _usernameController,
                                      focusNode: _usernameFocusNode,
                                      keyboardType: TextInputType.text,
                                      accentColor: accentColor,
                                    ),

                                    const SizedBox(height: 16),

                                    // 手机号输入框
                                    _buildInputField(
                                      icon: Icons.phone_android_rounded,
                                      hintText: AppLocalizations.of(
                                        context,
                                      ).phoneNumberHint,
                                      controller: _phoneController,
                                      focusNode: _phoneFocusNode,
                                      keyboardType: TextInputType.number,
                                      accentColor: accentColor,
                                      maxLength: 11,
                                    ),

                                    const SizedBox(height: 16),

                                    // 验证码输入框和按钮
                                    Row(
                                      children: [
                                        Expanded(
                                          child: _buildInputField(
                                            icon: Icons.vpn_key_outlined,
                                            hintText: AppLocalizations.of(
                                              context,
                                            ).verificationCodeHint,
                                            controller: _verifyCodeController,
                                            focusNode: _verifyCodeFocusNode,
                                            keyboardType: TextInputType.number,
                                            accentColor: accentColor,
                                            maxLength: 6,
                                          ),
                                        ),

                                        const SizedBox(width: 16),

                                        // 获取验证码按钮
                                        Container(
                                          height: 56,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.grey.withValues(
                                                  alpha: 0.08,
                                                ),
                                                spreadRadius: 1,
                                                blurRadius: 8,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                          child: ElevatedButton(
                                            onPressed: _isGettingCode
                                                ? null
                                                : _handleGetVerificationCode,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: _isGettingCode
                                                  ? (isDarkMode
                                                        ? Colors.grey.shade800
                                                        : Colors.grey.shade200)
                                                  : accentColor,
                                              foregroundColor: _isGettingCode
                                                  ? (isDarkMode
                                                        ? Colors.grey.shade400
                                                        : Colors.grey.shade500)
                                                  : Colors.white,
                                              elevation: 0,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 16,
                                                  ),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                              ),
                                              minimumSize: const Size(120, 56),
                                            ),
                                            child: Text(
                                              _isGettingCode
                                                  ? AppLocalizations.of(
                                                      context,
                                                    ).countdownSeconds(
                                                      _countDown,
                                                    )
                                                  : AppLocalizations.of(
                                                      context,
                                                    ).getCodeButton,
                                              style: const TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 16),

                                    // 密码输入框
                                    _buildInputField(
                                      icon: Icons.lock_outline_rounded,
                                      hintText: AppLocalizations.of(
                                        context,
                                      ).setPassword,
                                      controller: _passwordController,
                                      focusNode: _passwordFocusNode,
                                      keyboardType:
                                          TextInputType.visiblePassword,
                                      accentColor: accentColor,
                                      obscureText: true,
                                    ),

                                    const SizedBox(height: 16),

                                    // 确认密码输入框
                                    _buildInputField(
                                      icon: Icons.lock_outline_rounded,
                                      hintText: AppLocalizations.of(
                                        context,
                                      ).confirmPassword,
                                      controller: _confirmPasswordController,
                                      focusNode: _confirmPasswordFocusNode,
                                      keyboardType:
                                          TextInputType.visiblePassword,
                                      accentColor: accentColor,
                                      obscureText: true,
                                    ),

                                    const SizedBox(height: 25),

                                    // 构建协议同意行
                                    buildAgreementRow(),

                                    const SizedBox(height: 20),

                                    // 注册按钮
                                    Container(
                                      height: 56,
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(16),
                                        boxShadow: [
                                          BoxShadow(
                                            color: accentColor.withValues(
                                              alpha: 0.3,
                                            ),
                                            spreadRadius: 1,
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: ElevatedButton(
                                        onPressed: _handleRegister,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: accentColor,
                                          foregroundColor: Colors.white,
                                          elevation: 0,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                          ),
                                        ),
                                        child: Text(
                                          AppLocalizations.of(context).register,
                                          style: const TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w600,
                                            letterSpacing: 2,
                                          ),
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 16),

                                    // 直接登录
                                    Center(
                                      child: TextButton(
                                        onPressed: _handleDirectLogin,
                                        style: TextButton.styleFrom(
                                          foregroundColor:
                                              ThemeHelper.getTextSecondary(
                                                context,
                                              ),
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 8,
                                          ),
                                        ),
                                        child: Text(
                                          AppLocalizations.of(
                                            context,
                                          ).phoneNumberLogin,
                                          style: const TextStyle(
                                            fontSize: 15,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 20),

                                    // 其他登录方式
                                    Center(
                                      child: Text(
                                        AppLocalizations.of(
                                          context,
                                        ).orOtherLoginMethods,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: ThemeHelper.getTextSecondary(
                                            context,
                                          ),
                                        ),
                                      ),
                                    ),

                                    const SizedBox(height: 20),

                                    // 第三方登录图标
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        _buildSocialButton(
                                          icon: Icons.wechat,
                                          color: const Color(0xFF07C160),
                                          onTap: () {},
                                        ),
                                        const SizedBox(width: 30),
                                        _buildSocialButton(
                                          icon: Icons.account_circle,
                                          color: AppColors.primary,
                                          onTap: () {},
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 30),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建输入框
  Widget _buildInputField({
    required IconData icon,
    required String hintText,
    required TextEditingController controller,
    required FocusNode focusNode,
    required TextInputType keyboardType,
    required Color accentColor,
    bool obscureText = false,
    int? maxLength,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.08),
            spreadRadius: isDarkMode ? 0 : 1,
            blurRadius: isDarkMode ? 4 : 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        obscureText: obscureText,
        keyboardType: keyboardType,
        maxLength: maxLength,
        style: TextStyle(
          color: ThemeHelper.getTextPrimary(context),
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          counterText: '',
          hintStyle: TextStyle(
            color: ThemeHelper.getTextHint(context),
            fontSize: 16,
          ),
          prefixIcon: Icon(icon, color: ThemeHelper.getTextHint(context)),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 18,
          ),
          border: InputBorder.none,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: accentColor, width: 1.5),
          ),
        ),
      ),
    );
  }

  // 构建社交媒体登录按钮
  Widget _buildSocialButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(30),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: ThemeHelper.getCardBackground(context),
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.black.withValues(alpha: 0.3)
                  : color.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Icon(
            icon,
            color: ThemeHelper.getTextPrimary(context),
            size: 40,
          ),
        ),
      ),
    );
  }

  // 构建协议同意行
  Widget buildAgreementRow() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Transform.scale(
            scale: 1.1,
            child: Theme(
              data: ThemeData(
                checkboxTheme: CheckboxThemeData(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              child: Checkbox(
                value: _agreedToTerms,
                onChanged: (value) {
                  setState(() {
                    _agreedToTerms = value ?? false;
                  });
                },
                activeColor: AppColors.primary,
                checkColor: Colors.white,
              ),
            ),
          ),
          Expanded(
            child: RichText(
              text: TextSpan(
                style: TextStyle(
                  color: ThemeHelper.getTextPrimary(context),
                  fontSize: 13,
                ),
                children: [
                  TextSpan(text: AppLocalizations.of(context).iAgreeToThe),
                  const TextSpan(text: " "),
                  TextSpan(
                    text: AppLocalizations.of(
                      context,
                    ).userAgreementAndPrivacyPolicy,
                    style: TextStyle(color: AppColors.primary),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // 打开用户协议页面
                        ToastUtil.show(
                          context,
                          AppLocalizations.of(context).openUserAgreement,
                        );
                      },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 自定义波浪形状裁剪器
class WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height * 0.7);

    final firstControlPoint = Offset(size.width * 0.25, size.height * 0.5);
    final firstEndPoint = Offset(size.width * 0.5, size.height * 0.7);
    path.quadraticBezierTo(
      firstControlPoint.dx,
      firstControlPoint.dy,
      firstEndPoint.dx,
      firstEndPoint.dy,
    );

    final secondControlPoint = Offset(size.width * 0.75, size.height * 0.9);
    final secondEndPoint = Offset(size.width, size.height * 0.7);
    path.quadraticBezierTo(
      secondControlPoint.dx,
      secondControlPoint.dy,
      secondEndPoint.dx,
      secondEndPoint.dy,
    );

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}
