import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../config/themes/app_colors.dart';

/// 登录表单组件
class LoginForm extends StatefulWidget {
  final VoidCallback onLoginPressed;
  final VoidCallback onRegisterPressed;
  final VoidCallback onPasswordLoginPressed;

  const LoginForm({
    super.key,
    required this.onLoginPressed,
    required this.onRegisterPressed,
    required this.onPasswordLoginPressed,
  });

  @override
  State<LoginForm> createState() => _LoginFormState();
}

/// 带控制器的登录表单组件
class LoginFormWithController extends StatelessWidget {
  final TextEditingController phoneController;
  final TextEditingController verifyCodeController;
  final FocusNode phoneFocusNode;
  final FocusNode verifyCodeFocusNode;
  final VoidCallback onLoginPressed;
  final VoidCallback onRegisterPressed;
  final VoidCallback onPasswordLoginPressed;
  final bool isGettingCode;
  final int countDown;
  final VoidCallback onGetCode;

  const LoginFormWithController({
    super.key,
    required this.phoneController,
    required this.verifyCodeController,
    required this.phoneFocusNode,
    required this.verifyCodeFocusNode,
    required this.onLoginPressed,
    required this.onRegisterPressed,
    required this.onPasswordLoginPressed,
    this.isGettingCode = false,
    this.countDown = 60,
    this.onGetCode = _defaultOnGetCode,
  });

  static void _defaultOnGetCode() {}

  @override
  Widget build(BuildContext context) {
    final accentColor = AppColors.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 手机号输入框
        Container(
          decoration: BoxDecoration(
            color: ThemeHelper.getCardBackground(context),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withValues(alpha: 0.2)
                    : Colors.grey.withValues(alpha: 0.08),
                spreadRadius: isDarkMode ? 0 : 1,
                blurRadius: isDarkMode ? 4 : 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: phoneController,
            focusNode: phoneFocusNode,
            style: TextStyle(
              color: ThemeHelper.getTextPrimary(context),
              fontSize: 16,
            ),
            decoration: InputDecoration(
              hintText: AppLocalizations.of(context).phoneInputHint,
              hintStyle: TextStyle(
                color: ThemeHelper.getTextHint(context),
                fontSize: 16,
              ),
              prefixIcon: Icon(
                Icons.phone_android_rounded,
                color: ThemeHelper.getTextHint(context),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 18,
              ),
              border: InputBorder.none,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: accentColor, width: 1.5),
              ),
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(11),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // 验证码输入框和获取验证码按钮的行
        Row(
          children: [
            // 验证码输入框
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: ThemeHelper.getCardBackground(context),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: isDarkMode
                          ? Colors.black.withValues(alpha: 0.2)
                          : Colors.grey.withValues(alpha: 0.08),
                      spreadRadius: isDarkMode ? 0 : 1,
                      blurRadius: isDarkMode ? 4 : 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: verifyCodeController,
                  focusNode: verifyCodeFocusNode,
                  style: TextStyle(
                    color: ThemeHelper.getTextPrimary(context),
                    fontSize: 16,
                  ),
                  decoration: InputDecoration(
                    hintText: AppLocalizations.of(context).codeInputHint,
                    hintStyle: TextStyle(
                      color: ThemeHelper.getTextHint(context),
                      fontSize: 16,
                    ),
                    prefixIcon: Icon(
                      Icons.lock_outline_rounded,
                      color: ThemeHelper.getTextHint(context),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 18,
                    ),
                    border: InputBorder.none,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(
                        color: ThemeHelper.getBorder(context),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(color: accentColor, width: 1.5),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(6),
                  ],
                ),
              ),
            ),

            const SizedBox(width: 16),

            // 获取验证码按钮
            Container(
              height: 56,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.08),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: isGettingCode ? null : onGetCode,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isGettingCode
                      ? (isDarkMode
                            ? Colors.grey.shade800
                            : Colors.grey.shade200)
                      : accentColor,
                  foregroundColor: isGettingCode
                      ? (isDarkMode
                            ? Colors.grey.shade400
                            : Colors.grey.shade500)
                      : Colors.white,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  minimumSize: const Size(120, 56),
                ),
                child: Text(
                  isGettingCode
                      ? AppLocalizations.of(
                          context,
                        ).verificationCodeSentSeconds(countDown.toString())
                      : AppLocalizations.of(context).getVerificationCodeButton,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 40),

        // 登录按钮
        Container(
          height: 56,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: accentColor.withValues(alpha: 0.3),
                spreadRadius: 1,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: onLoginPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: accentColor,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: Text(
              AppLocalizations.of(context).loginButton,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                letterSpacing: 2,
              ),
            ),
          ),
        ),

        const SizedBox(height: 24),

        // 注册账号和密码登录按钮（分开放置）
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
              onPressed: onRegisterPressed,
              style: TextButton.styleFrom(
                foregroundColor: ThemeHelper.getTextSecondary(context),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              child: Text(
                AppLocalizations.of(context).registerAccountButton,
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            Container(
              height: 15,
              width: 1,
              color: ThemeHelper.getDivider(context),
              margin: const EdgeInsets.symmetric(horizontal: 16),
            ),

            TextButton(
              onPressed: onPasswordLoginPressed,
              style: TextButton.styleFrom(
                foregroundColor: ThemeHelper.getTextSecondary(context),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              child: Text(
                AppLocalizations.of(context).passwordLoginButton,
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _LoginFormState extends State<LoginForm> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _verifyCodeController = TextEditingController();
  bool _isGettingCode = false;
  int _countDown = 60;

  @override
  void dispose() {
    _phoneController.dispose();
    _verifyCodeController.dispose();
    super.dispose();
  }

  // 获取验证码
  void _getVerificationCode() {
    if (_phoneController.text.length != 11) {
      ToastUtil.show(context, '请输入正确的手机号');
      return;
    }

    // 这里添加获取验证码的逻辑
    setState(() {
      _isGettingCode = true;
    });

    // 模拟获取验证码的请求
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        ToastUtil.show(context, '验证码已发送');

        // 开始倒计时
        _startCountDown();
      }
    });
  }

  // 开始倒计时
  void _startCountDown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (_countDown > 0) {
        setState(() {
          _countDown--;
        });
        _startCountDown();
      } else {
        setState(() {
          _isGettingCode = false;
          _countDown = 60;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 24),

          // 标题文字
          const Text(
            '请输入您的手机号',
            style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: 40),

          // 手机号输入框
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: TextField(
              controller: _phoneController,
              decoration: const InputDecoration(
                hintText: '请输入手机号',
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                border: InputBorder.none,
              ),
              keyboardType: TextInputType.number, // 使用系统数字键盘
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(11),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 验证码输入框和获取验证码按钮的行
          Row(
            children: [
              // 验证码输入框
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.1),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _verifyCodeController,
                    decoration: const InputDecoration(
                      hintText: '请输入验证码',
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 16,
                      ),
                      border: InputBorder.none,
                    ),
                    keyboardType: TextInputType.number, // 使用系统数字键盘
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(6),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // 获取验证码按钮
              Container(
                width: 120,
                height: 52,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: TextButton(
                  onPressed: _isGettingCode ? null : _getVerificationCode,
                  child: Text(
                    _isGettingCode ? '$_countDown秒后重试' : '获取验证码',
                    style: TextStyle(
                      color: _isGettingCode ? Colors.grey : AppColors.primary,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 48),

          // 登录按钮
          SizedBox(
            height: 52,
            child: ElevatedButton(
              onPressed: widget.onLoginPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('登录', style: TextStyle(fontSize: 18)),
            ),
          ),

          const SizedBox(height: 24),

          // 注册账号和密码登录按钮（分开放置）
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton(
                onPressed: widget.onRegisterPressed,
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey.shade700,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                child: const Text(
                  '注册账号',
                  style: TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
                ),
              ),

              Container(
                height: 15,
                width: 1,
                color: Colors.grey.shade300,
                margin: const EdgeInsets.symmetric(horizontal: 16),
              ),

              TextButton(
                onPressed: widget.onPasswordLoginPressed,
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey.shade700,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                child: const Text(
                  '密码登录',
                  style: TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
