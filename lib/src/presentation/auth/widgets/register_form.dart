import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';

/// 注册表单组件
class RegisterForm extends StatefulWidget {
  final VoidCallback onRegisterPressed;
  final VoidCallback onDirectLoginPressed;
  final VoidCallback onGetCodePressed;
  final bool isGettingCode;
  final int countDown;

  const RegisterForm({
    super.key,
    required this.onRegisterPressed,
    required this.onDirectLoginPressed,
    required this.onGetCodePressed,
    this.isGettingCode = false,
    this.countDown = 60,
  });

  @override
  State<RegisterForm> createState() => _RegisterFormState();
}

class _RegisterFormState extends State<RegisterForm> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _verifyCodeController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  @override
  void dispose() {
    _usernameController.dispose();
    _phoneController.dispose();
    _verifyCodeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final accentColor = const Color(0xFF4A80F0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 用户名输入框
        _buildInputField(
          icon: Icons.person_outline,
          hintText: '请输入用户名',
          controller: _usernameController,
          keyboardType: TextInputType.text,
          accentColor: accentColor,
        ),

        const SizedBox(height: 16),

        // 手机号输入框
        _buildInputField(
          icon: Icons.phone_android_rounded,
          hintText: '请输入手机号',
          controller: _phoneController,
          keyboardType: TextInputType.number,
          accentColor: accentColor,
          maxLength: 11,
        ),

        const SizedBox(height: 16),

        // 验证码输入框和按钮
        Row(
          children: [
            Expanded(
              child: _buildInputField(
                icon: Icons.vpn_key_outlined,
                hintText: '请输入验证码',
                controller: _verifyCodeController,
                keyboardType: TextInputType.number,
                accentColor: accentColor,
                maxLength: 6,
              ),
            ),

            const SizedBox(width: 16),

            // 获取验证码按钮
            Container(
              height: 56,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.08),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: widget.isGettingCode
                    ? null
                    : widget.onGetCodePressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.isGettingCode
                      ? Colors.grey.shade200
                      : accentColor,
                  foregroundColor: widget.isGettingCode
                      ? Colors.grey.shade500
                      : Colors.white,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  minimumSize: const Size(120, 56),
                ),
                child: Text(
                  widget.isGettingCode ? '${widget.countDown}秒' : '获取验证码',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 密码输入框
        _buildInputField(
          icon: Icons.lock_outline_rounded,
          hintText: '设置密码',
          controller: _passwordController,
          keyboardType: TextInputType.visiblePassword,
          accentColor: accentColor,
          obscureText: true,
        ),

        const SizedBox(height: 16),

        // 确认密码输入框
        _buildInputField(
          icon: Icons.lock_outline_rounded,
          hintText: '重输密码',
          controller: _confirmPasswordController,
          keyboardType: TextInputType.visiblePassword,
          accentColor: accentColor,
          obscureText: true,
        ),

        const SizedBox(height: 40),

        // 注册按钮
        Container(
          height: 56,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: accentColor.withValues(alpha: 0.3),
                spreadRadius: 1,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: widget.onRegisterPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: accentColor,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: const Text(
              '注 册',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                letterSpacing: 2,
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // 直接登录
        Center(
          child: TextButton(
            onPressed: widget.onDirectLoginPressed,
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey.shade700,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: const Text(
              '直接登录',
              style: TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
            ),
          ),
        ),

        const SizedBox(height: 20),

        // 其他登录方式
        Center(
          child: Text(
            '或选其他登录方式',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ),

        const SizedBox(height: 20),

        // 第三方登录图标
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSocialButton(
              icon: Icons.wechat,
              color: const Color(0xFF07C160),
              onTap: () {},
            ),
            const SizedBox(width: 30),
            _buildSocialButton(
              icon: Icons.account_circle,
              color: AppColors.primary,
              onTap: () {},
            ),
          ],
        ),
      ],
    );
  }

  // 构建输入框
  Widget _buildInputField({
    required IconData icon,
    required String hintText,
    required TextEditingController controller,
    required TextInputType keyboardType,
    required Color accentColor,
    bool obscureText = false,
    int? maxLength,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        maxLength: maxLength,
        decoration: InputDecoration(
          hintText: hintText,
          counterText: '',
          hintStyle: TextStyle(color: Colors.grey.shade400, fontSize: 16),
          prefixIcon: Icon(icon, color: Colors.grey.shade400),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 18,
          ),
          border: InputBorder.none,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: Colors.grey.shade100),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(color: accentColor, width: 1.5),
          ),
        ),
      ),
    );
  }

  // 构建社交媒体登录按钮
  Widget _buildSocialButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(30),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(icon, color: color, size: 30),
      ),
    );
  }
}
