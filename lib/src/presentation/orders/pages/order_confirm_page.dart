import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:city_pickers/city_pickers.dart';
import '../../../models/user_product_model.dart';
import '../../../models/address_model.dart';
import '../../../services/user_product_service.dart';
import '../../../services/payment_service.dart';
import '../../../services/location_service.dart';
import '../../../services/address_service.dart';
import '../../../models/payment_model.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../config/themes/app_colors.dart';
import '../../../common/utils/font_util.dart';
import '../../address/pages/address_list_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 订单确认页面
class OrderConfirmPage extends StatefulWidget {
  final UserProductModel product;
  final int quantity;

  const OrderConfirmPage({super.key, required this.product, this.quantity = 1});

  @override
  State<OrderConfirmPage> createState() => _OrderConfirmPageState();
}

class _OrderConfirmPageState extends State<OrderConfirmPage> {
  final UserProductService _productService = UserProductService();
  final PaymentService _paymentService = PaymentService();
  final LocationService _locationService = LocationService();
  final AddressService _addressService = AddressService();

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _quantityController = TextEditingController();

  bool _isCreatingOrder = false;
  bool _isGettingLocation = false;

  // 商品数量（可变）
  late int _quantity;

  // 地址相关
  AddressModel? _selectedAddress;

  // 地区选择相关（手动输入时使用）
  String _selectedProvince = '';
  String _selectedCity = '';
  String _selectedArea = '';
  String get _selectedRegion {
    if (_selectedProvince.isEmpty) return '';
    String region = _selectedProvince;
    if (_selectedCity.isNotEmpty) region += ' $_selectedCity';
    if (_selectedArea.isNotEmpty) region += ' $_selectedArea';
    return region;
  }

  @override
  void initState() {
    super.initState();
    _quantity = widget.quantity;
    _quantityController.text = _quantity.toString();
    // 页面加载时加载默认地址
    _loadDefaultAddress();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _quantityController.dispose();
    super.dispose();
  }

  /// 加载默认地址
  Future<void> _loadDefaultAddress() async {
    try {
      // 先尝试获取地址列表，看看是否有地址
      final addresses = await _addressService.getAddressList();

      if (addresses.isNotEmpty && mounted) {
        // 只有当存在默认地址时才自动填写
        final defaultAddress = addresses
            .where((addr) => addr.isDefault)
            .firstOrNull;

        if (defaultAddress != null) {
          setState(() {
            _selectedAddress = defaultAddress;
            _nameController.text = defaultAddress.receiverName;
            _phoneController.text = defaultAddress.receiverPhone;
            _addressController.text = defaultAddress.detailedAddress;
            _selectedProvince = defaultAddress.province;
            _selectedCity = defaultAddress.city;
            _selectedArea = defaultAddress.district;
          });
        } else {
          // 有地址但没有默认地址，不自动填写任何信息
          // 只获取当前位置用于地区选择
          _initializeLocation();
        }
      } else {
        // 如果没有地址，只获取当前位置用于地区选择
        _initializeLocation();
      }
    } catch (e) {
      print('OrderConfirmPage: 加载地址失败: $e');
      // 如果获取地址失败，只获取当前位置用于地区选择
      _initializeLocation();
    }
  }

  /// 初始化定位
  Future<void> _initializeLocation() async {
    try {
      // 静默获取当前位置，不显示加载状态
      bool success = await _locationService.getCurrentLocation();

      if (success && _locationService.hasCurrentLocation()) {
        // 如果用户还没有选择地区，自动设置为当前位置
        if (_selectedRegion.isEmpty) {
          setState(() {
            _selectedProvince = _locationService.currentProvince ?? '';
            _selectedCity = _locationService.currentCity ?? '';
            _selectedArea = _locationService.currentArea ?? '';
          });
        }
      }
    } catch (e) {
      // 静默失败，不显示错误信息
      print('OrderConfirmPage: 初始化定位失败: $e');
    }
  }

  /// 计算总价
  double get _totalAmount => widget.product.price * _quantity;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).orderConfirmation,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ThemeHelper.getTextPrimary(context),
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(height: 1, color: ThemeHelper.getBorder(context)),
        ),
      ),
      body: Column(
        children: [
          // 可滚动内容
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 商品信息
                    _buildProductInfo(),
                    const SizedBox(height: 16),

                    // 收货信息
                    _buildShippingInfo(),
                    const SizedBox(height: 16),

                    // 订单金额
                    _buildOrderAmount(),

                    // 底部安全区域
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ),
          ),

          // 固定底部按钮
          _buildBottomActions(),
        ],
      ),
    );
  }

  /// 构建商品信息
  Widget _buildProductInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4,
                height: 16,
                decoration: BoxDecoration(
                  color: const Color(0xFF109D58),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).productInfo,
                style: TextStyle(
                  color: ThemeHelper.getTextPrimary(context),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // 商品图片
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFF0F0F0)),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: widget.product.fullMainImageUrl.isNotEmpty
                      ? Image.network(
                          widget.product.fullMainImageUrl,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultProductImage();
                          },
                        )
                      : _buildDefaultProductImage(),
                ),
              ),
              const SizedBox(width: 16),

              // 商品信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.product.name,
                      style: TextStyle(
                        color: ThemeHelper.getTextPrimary(context),
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '${AppLocalizations.of(context).doctorName}：${widget.product.doctorName}',
                      style: TextStyle(
                        color: ThemeHelper.getTextSecondary(context),
                        fontSize: 13,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          widget.product.formattedPrice,
                          style: const TextStyle(
                            color: Color(0xFF109D58),
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 数量选择器
          Row(
            children: [
              Text(
                '${AppLocalizations.of(context).quantity}：',
                style: TextStyle(
                  color: ThemeHelper.getTextSecondary(context),
                  fontSize: 14,
                ),
              ),
              const Spacer(),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFFE0E0E0)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 减少按钮
                    InkWell(
                      onTap: _quantity > 1
                          ? () {
                              setState(() {
                                _quantity--;
                              });
                            }
                          : null,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        child: Icon(
                          Icons.remove,
                          size: 18,
                          color: _quantity > 1
                              ? AppColors.primary
                              : ThemeHelper.getTextHint(context),
                        ),
                      ),
                    ),
                    // 数量输入
                    GestureDetector(
                      onTap: () => _showQuantityInputDialog(),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: Text(
                          _quantity.toString(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: ThemeHelper.getTextPrimary(context),
                          ),
                        ),
                      ),
                    ),
                    // 增加按钮
                    InkWell(
                      onTap: _quantity < 99
                          ? () {
                              setState(() {
                                _quantity++;
                              });
                            }
                          : null,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        child: Icon(
                          Icons.add,
                          size: 18,
                          color: _quantity < 99
                              ? AppColors.primary
                              : ThemeHelper.getTextHint(context),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 小计显示
          Row(
            children: [
              Text(
                '${AppLocalizations.of(context).subtotal}：',
                style: TextStyle(
                  color: ThemeHelper.getTextSecondary(context),
                  fontSize: 14,
                ),
              ),
              const Spacer(),
              Text(
                '¥${(_totalAmount).toStringAsFixed(2)}',
                style: const TextStyle(
                  color: Color(0xFF109D58),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建默认商品图片
  Widget _buildDefaultProductImage() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.image_not_supported_outlined,
        color: Color(0xFFCCCCCC),
        size: 32,
      ),
    );
  }

  /// 构建收货信息
  Widget _buildShippingInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4,
                height: 16,
                decoration: BoxDecoration(
                  color: const Color(0xFF109D58),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).shippingInfo,
                style: TextStyle(
                  color: ThemeHelper.getTextPrimary(context),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _goToAddressManagement,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        color: AppColors.primary,
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        AppLocalizations.of(context).addressManagement,
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // 收货人姓名
          _buildInputField(
            controller: _nameController,
            label: AppLocalizations.of(context).recipientName,
            hint: AppLocalizations.of(context).enterRecipientName,
            icon: Icons.person_outline,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return AppLocalizations.of(context).enterRecipientName;
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // 联系电话
          _buildInputField(
            controller: _phoneController,
            label: AppLocalizations.of(context).recipientPhone,
            hint: AppLocalizations.of(context).enterRecipientPhone,
            icon: Icons.phone_outlined,
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return AppLocalizations.of(context).enterRecipientPhone;
              }
              if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value.trim())) {
                return AppLocalizations.of(context).enterCorrectPhoneNumber;
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // 所在地区选择
          _buildRegionSelector(),
          const SizedBox(height: 16),

          // 详细地址
          _buildInputField(
            controller: _addressController,
            label: AppLocalizations.of(context).shippingAddress,
            hint: AppLocalizations.of(context).enterShippingAddress,
            icon: Icons.location_on_outlined,
            maxLines: 3,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return AppLocalizations.of(context).enterShippingAddress;
              }
              if (value.trim().length < 5) {
                return AppLocalizations.of(context).enterShippingAddress;
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// 构建输入框
  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: ThemeHelper.getTextPrimary(context),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: ThemeHelper.getTextHint(context),
              fontSize: 14,
            ),
            prefixIcon: Icon(
              icon,
              color: ThemeHelper.getTextSecondary(context),
              size: 20,
            ),
            filled: true,
            fillColor: ThemeHelper.getInputBackground(context),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: Color(0xFF109D58),
                width: 1.5,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFFF4757)),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: Color(0xFFFF4757),
                width: 1.5,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建地区选择器
  Widget _buildRegionSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              AppLocalizations.of(context).region,
              style: TextStyle(
                color: ThemeHelper.getTextPrimary(context),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            GestureDetector(
              onTap: _getCurrentLocationDirect,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: _isGettingLocation
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: _isGettingLocation
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: AppColors.primary,
                        ),
                      )
                    : Icon(
                        Icons.my_location,
                        color: AppColors.primary,
                        size: 16,
                      ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _showSystemCityPicker,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: ThemeHelper.getInputBackground(context),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: ThemeHelper.getBorder(context)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.location_city_outlined,
                  color: ThemeHelper.getTextSecondary(context),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedRegion.isEmpty
                        ? AppLocalizations.of(context).selectRegion
                        : _selectedRegion,
                    style: TextStyle(
                      color: _selectedRegion.isEmpty
                          ? ThemeHelper.getTextHint(context)
                          : ThemeHelper.getTextPrimary(context),
                      fontSize: 14,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: ThemeHelper.getTextHint(context),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
        if (_selectedRegion.isEmpty)
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: Text(
              AppLocalizations.of(context).pleaseSelectRegion,
              style: const TextStyle(color: Color(0xFFFF4757), fontSize: 12),
            ),
          ),
      ],
    );
  }

  /// 直接获取当前位置
  Future<void> _getCurrentLocationDirect() async {
    if (_isGettingLocation) return;

    setState(() {
      _isGettingLocation = true;
    });

    try {
      bool success = await _locationService.getCurrentLocation();

      if (success && _locationService.hasCurrentLocation()) {
        setState(() {
          _selectedProvince = _locationService.currentProvince ?? '';
          _selectedCity = _locationService.currentCity ?? '';
          _selectedArea = _locationService.currentArea ?? '';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGettingLocation = false;
        });
      }
    }
  }

  /// 显示系统城市选择器
  Future<void> _showSystemCityPicker() async {
    try {
      final result = await CityPickers.showCityPicker(context: context);

      if (result != null) {
        setState(() {
          _selectedProvince = result.provinceName ?? '';
          _selectedCity = result.cityName ?? '';
          _selectedArea = result.areaName ?? '';
        });
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '地区选择失败，请重试');
      }
    }
  }

  /// 构建订单金额
  Widget _buildOrderAmount() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4,
                height: 16,
                decoration: BoxDecoration(
                  color: const Color(0xFF109D58),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).orderAmount,
                style: TextStyle(
                  color: ThemeHelper.getTextPrimary(context),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 商品金额
          _buildAmountRow(
            AppLocalizations.of(context).productAmount,
            '¥${_totalAmount.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 12),

          // 运费
          _buildAmountRow(
            AppLocalizations.of(context).shippingFee,
            AppLocalizations.of(context).freeShipping,
            isHighlight: true,
          ),

          // 分割线
          Container(
            margin: const EdgeInsets.symmetric(vertical: 16),
            height: 1,
            color: const Color(0xFFF0F0F0),
          ),

          // 实付金额
          Row(
            children: [
              Text(
                AppLocalizations.of(context).totalPaidLabel,
                style: TextStyle(
                  color: ThemeHelper.getTextPrimary(context),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '¥${_totalAmount.toStringAsFixed(2)}',
                style: const TextStyle(
                  color: Color(0xFF109D58),
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建金额行
  Widget _buildAmountRow(
    String label,
    String value, {
    bool isHighlight = false,
  }) {
    return Row(
      children: [
        Text(
          label,
          style: TextStyle(
            color: ThemeHelper.getTextSecondary(context),
            fontSize: 14,
          ),
        ),
        const Spacer(),
        Text(
          value,
          style: TextStyle(
            color: isHighlight
                ? AppColors.primary
                : ThemeHelper.getTextPrimary(context),
            fontSize: 14,
            fontWeight: isHighlight ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        boxShadow: const [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 总价显示
            Expanded(
              flex: 1,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.of(context).totalAmount,
                    style: TextStyle(
                      color: ThemeHelper.getTextSecondary(context),
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    '¥${_totalAmount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      color: Color(0xFF109D58),
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),

            // 提交订单按钮
            Expanded(
              flex: 2,
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF109D58), Color(0xFF0E8A4D)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF109D58).withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: _isCreatingOrder ? null : _submitOrder,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                    elevation: 0,
                  ),
                  child: _isCreatingOrder
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : Text(
                          AppLocalizations.of(context).submitOrder,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 提交订单
  Future<void> _submitOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 验证地区选择（如果没有选择地址且手动输入地区为空）
    if (_selectedAddress == null && _selectedRegion.isEmpty) {
      ToastUtil.show(context, '请选择收货地址或手动输入地区信息');
      return;
    }

    setState(() {
      _isCreatingOrder = true;
    });

    try {
      // 组合完整地址
      String fullAddress;
      if (_selectedAddress != null) {
        // 使用选择的地址
        fullAddress = _selectedAddress!.fullAddress;
      } else {
        // 使用手动输入的地址
        fullAddress = '$_selectedRegion ${_addressController.text.trim()}';
      }

      // 创建订单
      final order = await _productService.createOrder(
        productId: widget.product.id,
        quantity: _quantity,
        shippingAddress: fullAddress,
        shippingPhone: _phoneController.text.trim(),
        shippingName: _nameController.text.trim(),
      );

      if (mounted) {
        // 订单创建成功，跳转到支付页面
        ToastUtil.show(
          context,
          AppLocalizations.of(context).orderCreatedSuccess,
        );

        // 创建支付请求
        _createPayment(order);
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).createOrderFailed(e.toString()),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreatingOrder = false;
        });
      }
    }
  }

  /// 创建支付
  Future<void> _createPayment(ProductOrderModel order) async {
    try {
      // 使用微信支付
      final paymentResult = await _paymentService.payWithWeChatApp(
        orderId: order.id,
      );

      if (mounted) {
        // 处理支付结果
        _handlePaymentResult(paymentResult, order);
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '支付失败：$e');
      }
    }
  }

  /// 处理支付结果
  void _handlePaymentResult(PaymentResult result, ProductOrderModel order) {
    String message;
    bool shouldNavigateBack = false;

    switch (result) {
      case PaymentResult.success:
        message = '支付成功！订单已提交';
        shouldNavigateBack = true;
        break;
      case PaymentResult.failed:
        message = '支付失败，请重试';
        break;
      case PaymentResult.cancelled:
        message = '支付已取消';
        break;
      case PaymentResult.unknown:
        message = '支付状态未知，请稍后查看订单状态';
        shouldNavigateBack = true;
        break;
    }

    // 显示支付结果
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          result == PaymentResult.success ? '支付成功' : '支付提示',
          style: FontUtil.createHeadingTextStyle(
            text: result == PaymentResult.success ? '支付成功' : '支付提示',
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context).orderNumberLabel(order.orderSn),
              style: FontUtil.createBodyTextStyle(
                text: AppLocalizations.of(
                  context,
                ).orderNumberLabel(order.orderSn),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${AppLocalizations.of(context).paymentAmount}：${order.formattedTotalAmount}',
              style: FontUtil.createBodyTextStyle(
                text:
                    '${AppLocalizations.of(context).paymentAmount}：${order.formattedTotalAmount}',
              ),
            ),
            const SizedBox(height: 16),
            Text(message, style: FontUtil.createBodyTextStyle(text: message)),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // 关闭对话框
              if (shouldNavigateBack) {
                Navigator.of(context).pop(); // 返回到产品详情页
                ToastUtil.show(context, message);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF109D58),
            ),
            child: Text(
              '确定',
              style: FontUtil.createButtonTextStyle(
                text: '确定',
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 跳转到地址管理页面
  Future<void> _goToAddressManagement() async {
    final result = await Navigator.push<AddressModel>(
      context,
      MaterialPageRoute(
        builder: (context) => const AddressListPage(isSelectMode: true),
      ),
    );

    if (result != null) {
      // 用户选择了地址，更新表单
      setState(() {
        _selectedAddress = result;
        _nameController.text = result.receiverName;
        _phoneController.text = result.receiverPhone;
        _addressController.text = result.detailedAddress;
        _selectedProvince = result.province;
        _selectedCity = result.city;
        _selectedArea = result.district;
      });
    }
  }

  /// 显示数量输入对话框
  void _showQuantityInputDialog() {
    _quantityController.text = _quantity.toString();
    _quantityController.selection = TextSelection.fromPosition(
      TextPosition(offset: _quantityController.text.length),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: ThemeHelper.getCardBackground(context),
        title: Text(
          AppLocalizations.of(context).selectQuantity,
          style: FontUtil.createHeadingTextStyle(
            text: AppLocalizations.of(context).selectQuantity,
          ),
        ),
        content: TextField(
          controller: _quantityController,
          keyboardType: TextInputType.number,
          autofocus: true,
          style: TextStyle(color: ThemeHelper.getTextPrimary(context)),
          decoration: InputDecoration(
            labelText: AppLocalizations.of(context).quantity,
            labelStyle: TextStyle(color: ThemeHelper.getTextSecondary(context)),
            hintText: AppLocalizations.of(context).quantityRange,
            hintStyle: TextStyle(color: ThemeHelper.getTextHint(context)),
            border: OutlineInputBorder(
              borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.primary),
            ),
          ),
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: TextStyle(color: ThemeHelper.getTextSecondary(context)),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final inputText = _quantityController.text.trim();
              if (inputText.isEmpty) {
                return;
              }

              final quantity = int.tryParse(inputText);
              if (quantity == null || quantity < 1 || quantity > 99) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(AppLocalizations.of(context).quantityRange),
                  ),
                );
                return;
              }

              setState(() {
                _quantity = quantity;
                _quantityController.text = quantity.toString();
              });
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary),
            child: Text(
              AppLocalizations.of(context).confirm,
              style: FontUtil.createBodyTextStyle(
                text: AppLocalizations.of(context).confirm,
              ).copyWith(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
