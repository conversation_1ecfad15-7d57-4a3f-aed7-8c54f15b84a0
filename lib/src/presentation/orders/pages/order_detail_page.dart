import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';

import '../../../models/user_product_model.dart';
import '../../../services/user_product_service.dart';

import '../../../utils/toast_util.dart';
import '../../shipping/pages/shipping_status_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 订单详情页面
class OrderDetailPage extends StatefulWidget {
  final ProductOrderModel order;

  const OrderDetailPage({super.key, required this.order});

  @override
  State<OrderDetailPage> createState() => _OrderDetailPageState();
}

class _OrderDetailPageState extends State<OrderDetailPage> {
  final UserProductService _productService = UserProductService();

  ProductOrderModel? _currentOrder;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentOrder = widget.order;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomActions(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        '订单详情',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ThemeHelper.getTextPrimary(context),
        ),
      ),
      backgroundColor: ThemeHelper.getCardBackground(context),
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: ThemeHelper.getTextPrimary(context),
        ),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    if (_currentOrder == null) {
      return Center(
        child: Text(AppLocalizations.of(context).orderInfoLoadFailedMessage),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 订单状态卡片
          _buildOrderStatusCard(),
          const SizedBox(height: 16),

          // 商品信息卡片
          _buildProductInfoCard(),
          const SizedBox(height: 16),

          // 订单信息卡片
          _buildOrderInfoCard(),
          const SizedBox(height: 16),

          // 收货信息卡片
          _buildShippingInfoCard(),

          // 如果有快递信息，显示物流信息卡片
          if (_currentOrder!.trackingNumber?.isNotEmpty == true) ...[
            const SizedBox(height: 16),
            _buildTrackingInfoCard(),
          ],

          const SizedBox(height: 16),

          // 费用明细卡片
          _buildPriceDetailCard(),

          // 底部留出空间给操作按钮
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  /// 构建订单状态卡片
  Widget _buildOrderStatusCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(_getStatusIcon(), color: _getStatusColor(), size: 24),
              const SizedBox(width: 12),
              Text(
                _currentOrder!.getOrderStatusText(context),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: _getStatusColor(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _getStatusDescription(),
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建商品信息卡片
  Widget _buildProductInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).productInfoTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 商品图片
              _buildProductImage(),
              const SizedBox(width: 16),
              // 商品信息
              Expanded(child: _buildProductDetails()),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建商品图片
  Widget _buildProductImage() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: _currentOrder!.productImage?.isNotEmpty == true
            ? Image.network(
                _currentOrder!.productImage!,
                width: 80,
                height: 80,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultProductImage();
                },
              )
            : _buildDefaultProductImage(),
      ),
    );
  }

  /// 构建默认商品图片
  Widget _buildDefaultProductImage() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.primary.withValues(alpha: 0.1),
      ),
      child: Icon(
        Icons.inventory_2_outlined,
        color: AppColors.primary,
        size: 32,
      ),
    );
  }

  /// 构建商品详情
  Widget _buildProductDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _currentOrder!.productName,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Text(
          '医生: ${_currentOrder!.doctorName}',
          style: TextStyle(
            fontSize: 14,
            color: ThemeHelper.getTextSecondary(context),
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Text(
              '¥${_currentOrder!.unitPrice.toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: const Color(0xFF27AE60),
              ),
            ),
            const Spacer(),
            Text(
              'x${_currentOrder!.quantity}',
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建订单信息卡片
  Widget _buildOrderInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).orderInfoTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            AppLocalizations.of(context).orderNumberFieldLabel,
            _currentOrder!.orderSn,
            canCopy: true,
          ),
          _buildInfoRow(
            AppLocalizations.of(context).orderTimeLabel,
            _formatDateTime(_currentOrder!.createdAt),
          ),
          if (_currentOrder!.payTime != null)
            _buildInfoRow(
              AppLocalizations.of(context).paymentTimeLabel,
              _formatDateTime(_currentOrder!.payTime!),
            ),
          if (_currentOrder!.shipTime != null)
            _buildInfoRow(
              AppLocalizations.of(context).shipmentTimeLabel,
              _formatDateTime(_currentOrder!.shipTime!),
            ),
          if (_currentOrder!.completeTime != null)
            _buildInfoRow(
              AppLocalizations.of(context).completionTimeLabel,
              _formatDateTime(_currentOrder!.completeTime!),
            ),
        ],
      ),
    );
  }

  /// 构建收货信息卡片
  Widget _buildShippingInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).shippingInfoTitle,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('收货人', _currentOrder!.shippingName),
          _buildInfoRow('联系电话', _currentOrder!.shippingPhone),
          _buildInfoRow('收货地址', _currentOrder!.shippingAddress),
        ],
      ),
    );
  }

  /// 构建物流信息卡片
  Widget _buildTrackingInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_shipping_outlined,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).trackingInfoTitle,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => _viewShippingStatus(),
                child: Text(
                  AppLocalizations.of(context).viewDetails,
                  style: TextStyle(color: AppColors.primary, fontSize: 14),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            AppLocalizations.of(context).trackingNumber,
            _currentOrder!.trackingNumber!,
            canCopy: true,
          ),
          if (_currentOrder!.shippingCompany?.isNotEmpty == true)
            _buildInfoRow(
              AppLocalizations.of(context).shippingCompanyLabel,
              _currentOrder!.shippingCompany!,
            ),
          if (_currentOrder!.shippingNote?.isNotEmpty == true)
            _buildInfoRow(
              AppLocalizations.of(context).shippingNoteLabel,
              _currentOrder!.shippingNote!,
            ),
        ],
      ),
    );
  }

  /// 构建费用明细卡片
  Widget _buildPriceDetailCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).priceDetailsTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 16),
          _buildPriceRow(
            AppLocalizations.of(context).productAmountLabel,
            '¥${(_currentOrder!.unitPrice * _currentOrder!.quantity).toStringAsFixed(2)}',
          ),
          _buildPriceRow(
            AppLocalizations.of(context).shippingFeeLabel,
            '¥0.00',
          ),
          const Divider(height: 24),
          _buildPriceRow(
            AppLocalizations.of(context).totalPaidLabel,
            _currentOrder!.formattedTotalAmount,
            isTotal: true,
          ),
        ],
      ),
    );
  }

  /// 构建底部操作按钮
  Widget? _buildBottomActions() {
    List<Widget> actions = [];

    // 根据订单状态显示不同的操作按钮
    if (_currentOrder!.orderStatus == 0) {
      // 待支付
      actions.addAll([
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => _cancelOrder(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(AppLocalizations.of(context).cancelOrder),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : () => _payOrder(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(AppLocalizations.of(context).payNow),
          ),
        ),
      ]);
    } else if (_currentOrder!.orderStatus >= 2 &&
        _currentOrder!.orderStatus != 4) {
      // 已发货或已完成，但不是已取消
      actions.add(
        Expanded(
          child: ElevatedButton(
            onPressed: () => _viewShippingStatus(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('查看物流'),
          ),
        ),
      );
    } else if (_currentOrder!.canCancel) {
      // 可以取消的订单
      actions.add(
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => _cancelOrder(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(AppLocalizations.of(context).cancelOrder),
          ),
        ),
      );
    }

    if (actions.isEmpty) {
      return null;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, -4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: SafeArea(child: Row(children: actions)),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value, {bool canCopy = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
          ),
          if (canCopy)
            IconButton(
              onPressed: () => _copyToClipboard(value),
              icon: Icon(Icons.copy, size: 16, color: AppColors.primary),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
        ],
      ),
    );
  }

  /// 构建价格行
  Widget _buildPriceRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
              color: isTotal
                  ? ThemeHelper.getTextPrimary(context)
                  : ThemeHelper.getTextSecondary(context),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? FontWeight.w700 : FontWeight.normal,
              color: isTotal
                  ? const Color(0xFF27AE60)
                  : ThemeHelper.getTextPrimary(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取状态图标
  IconData _getStatusIcon() {
    switch (_currentOrder!.orderStatus) {
      case 0:
        return Icons.payment_outlined;
      case 1:
        return Icons.inventory_outlined;
      case 2:
        return Icons.local_shipping_outlined;
      case 3:
        return Icons.check_circle_outline;
      case 4:
        return Icons.cancel_outlined;
      default:
        return Icons.help_outline;
    }
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (_currentOrder!.orderStatus) {
      case 0:
        return const Color(0xFFF39C12);
      case 1:
        return const Color(0xFF3498DB);
      case 2:
        return AppColors.primary;
      case 3:
        return const Color(0xFF27AE60);
      case 4:
        return const Color(0xFFE74C3C);
      default:
        return Colors.grey;
    }
  }

  /// 获取状态描述
  String _getStatusDescription() {
    switch (_currentOrder!.orderStatus) {
      case 0:
        return AppLocalizations.of(context).orderStatusPendingDescription;
      case 1:
        return AppLocalizations.of(context).orderStatusPreparingDescription;
      case 2:
        return AppLocalizations.of(context).orderStatusShippedUserDescription;
      case 3:
        return AppLocalizations.of(context).orderStatusCompletedUserDescription;
      case 4:
        return AppLocalizations.of(context).orderStatusCancelledUserDescription;
      default:
        return '';
    }
  }

  /// 查看物流状态
  void _viewShippingStatus() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            ShippingStatusPage(order: _currentOrder!, isDoctorView: false),
      ),
    );
  }

  /// 支付订单
  Future<void> _payOrder() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 简化支付逻辑，直接显示支付成功（实际项目中需要集成真实的支付SDK）
      await Future.delayed(const Duration(seconds: 2)); // 模拟支付过程

      if (mounted) {
        ToastUtil.show(context, '支付成功');
        Navigator.pop(context, true); // 返回并刷新订单列表
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '支付失败：$e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 取消订单
  Future<void> _cancelOrder() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).confirmCancel),
        content: Text(AppLocalizations.of(context).confirmCancelOrder),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(AppLocalizations.of(context).confirm),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _productService.cancelOrder(_currentOrder!.id);
        if (mounted) {
          ToastUtil.show(context, AppLocalizations.of(context).orderCancelled);
          Navigator.pop(context, true); // 返回并刷新订单列表
        }
      } catch (e) {
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).cancelOrderFailed(e.toString()),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  /// 复制到剪贴板
  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ToastUtil.show(context, '已复制到剪贴板');
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
