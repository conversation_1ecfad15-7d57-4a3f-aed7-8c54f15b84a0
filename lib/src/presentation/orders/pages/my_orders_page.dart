import 'package:flutter/material.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/responsive_util.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/user_product_model.dart';
import '../../../models/payment_model.dart';
import '../../../services/user_product_service.dart';
import '../../../services/wechat_payment_service.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/order_status_helper.dart';

import '../../../services/auth_service.dart';
import '../../shipping/pages/shipping_status_page.dart';
import 'order_detail_page.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../config/api/api_config.dart';

/// 我的订单页面
class MyOrdersPage extends StatefulWidget {
  const MyOrdersPage({super.key});

  @override
  State<MyOrdersPage> createState() => _MyOrdersPageState();
}

class _MyOrdersPageState extends State<MyOrdersPage>
    with SingleTickerProviderStateMixin {
  final UserProductService _productService = UserProductService();

  late TabController _tabController;
  List<ProductOrderModel> _allOrders = [];
  bool _isLoading = true;
  String? _errorMessage;

  // 订单状态标签 - 延迟初始化，避免在initState中访问context
  List<String>? _statusTabs;
  List<String> get statusTabs => _statusTabs ?? [];
  final List<int?> _statusValues = [null, 0, 1, 2, 3]; // null表示全部

  @override
  void initState() {
    super.initState();
    // 延迟到下一帧执行，确保context完全初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeTabsAndLoadData();
    });
  }

  /// 初始化标签页并加载数据
  void _initializeTabsAndLoadData() {
    if (!mounted) return;

    // 初始化状态标签
    _statusTabs = [
      AppLocalizations.of(context).orderStatusAll,
      AppLocalizations.of(context).orderStatusPending,
      AppLocalizations.of(context).orderStatusPendingShipment,
      AppLocalizations.of(context).orderStatusShipped,
      AppLocalizations.of(context).orderStatusCompleted,
    ];

    // 初始化TabController
    _tabController = TabController(length: _statusTabs!.length, vsync: this);

    // 加载订单数据
    _loadOrders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载订单列表
  Future<void> _loadOrders() async {
    // 调试：检查当前登录状态
    final authService = AuthService();
    print('MyOrdersPage: 检查登录状态');
    print('MyOrdersPage: isLoggedIn = ${authService.isLoggedIn}');
    print('MyOrdersPage: currentUser = ${authService.currentUser}');
    print('MyOrdersPage: token = ${authService.currentUser?.token}');

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      print('MyOrdersPage: 开始获取订单列表');
      final orders = await _productService.getMyOrders();
      print('MyOrdersPage: 成功获取 ${orders.length} 个订单');

      if (mounted) {
        setState(() {
          _allOrders = orders;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('MyOrdersPage: 获取订单失败: $e');
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  /// 根据状态过滤订单
  List<ProductOrderModel> _getFilteredOrders(int? status) {
    if (status == null) {
      return _allOrders; // 返回全部订单
    }
    return _allOrders.where((order) => order.orderStatus == status).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).myOrdersTitle,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ThemeHelper.getTextPrimary(context),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: const Color(0xFF109D58),
          unselectedLabelColor: ThemeHelper.getTextSecondary(context),
          indicatorColor: const Color(0xFF109D58),
          labelStyle: FontUtil.createTabLabelStyle(fontSize: 14),
          unselectedLabelStyle: FontUtil.createTabUnselectedLabelStyle(
            fontSize: 14,
          ),
          tabAlignment: TabAlignment.start, // 让标签从左边开始
          dividerColor: Colors.transparent, // 去掉白色分割线
          indicatorSize: TabBarIndicatorSize.label,
          tabs: statusTabs.map((status) => Tab(text: status)).toList(),
        ),
      ),
      body: _buildBody(),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    // 如果标签页还未初始化，显示加载指示器
    if (_statusTabs == null) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return _buildErrorView();
    }

    return TabBarView(
      controller: _tabController,
      children: _statusValues.map((status) {
        final filteredOrders = _getFilteredOrders(status);
        return _buildOrderList(filteredOrders);
      }).toList(),
    );
  }

  /// 构建错误视图
  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: ThemeHelper.getTextSecondary(context),
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadOrders,
            child: Text(AppLocalizations.of(context).retry),
          ),
        ],
      ),
    );
  }

  /// 构建订单列表
  Widget _buildOrderList(List<ProductOrderModel> orders) {
    if (orders.isEmpty) {
      return _buildEmptyView();
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: ListView.builder(
        padding: EdgeInsets.all(ResponsiveUtil.mediumSpacing(context)),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          return _buildOrderItem(orders[index]);
        },
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: ThemeHelper.getTextSecondary(context),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).noOrders,
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建订单项
  Widget _buildOrderItem(ProductOrderModel order) {
    return Container(
      margin: EdgeInsets.only(bottom: ResponsiveUtil.mediumSpacing(context)),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _viewOrderDetail(order),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 订单头部信息
            _buildOrderHeader(order),
            // 商品信息
            _buildProductInfo(order),
            // 订单底部操作
            _buildOrderActions(order),
          ],
        ),
      ),
    );
  }

  /// 构建订单头部
  Widget _buildOrderHeader(ProductOrderModel order) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 16, 20, 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF109D58).withValues(alpha: 0.05),
            const Color(0xFF109D58).withValues(alpha: 0.02),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).orderNumber,
                  style: TextStyle(
                    color: ThemeHelper.getTextSecondary(context),
                    fontSize: 11,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  order.orderSn,
                  style: TextStyle(
                    color: ThemeHelper.getTextPrimary(context),
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getStatusColor(order.orderStatus),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: _getStatusColor(
                    order.orderStatus,
                  ).withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              OrderStatusHelper.getOrderStatusText(context, order.orderStatus),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建商品信息
  Widget _buildProductInfo(ProductOrderModel order) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
      child: Row(
        children: [
          // 商品图片
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: order.productImage?.isNotEmpty == true
                  ? Image.network(
                      _getFullImageUrl(order.productImage!),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        print(
                          '订单图片加载失败: ${_getFullImageUrl(order.productImage!)}',
                        );
                        return _buildDefaultProductImage();
                      },
                    )
                  : _buildDefaultProductImage(),
            ),
          ),
          const SizedBox(width: 16),
          // 商品信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order.productName,
                  style: TextStyle(
                    color: ThemeHelper.getTextPrimary(context),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF109D58).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '${AppLocalizations.of(context).doctor}：${order.doctorName}',
                    style: const TextStyle(
                      color: Color(0xFF109D58),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[800]
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '×${order.quantity}',
                        style: TextStyle(
                          color: ThemeHelper.getTextSecondary(context),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          order.formattedTotalAmount,
                          style: const TextStyle(
                            color: Color(0xFF109D58),
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.access_time_rounded,
                              size: 12,
                              color: ThemeHelper.getTextSecondary(context),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _formatDate(order.createdAt),
                              style: TextStyle(
                                color: ThemeHelper.getTextSecondary(context),
                                fontSize: 11,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建订单操作按钮
  Widget _buildOrderActions(ProductOrderModel order) {
    // 检查是否有可操作的按钮
    bool hasActions =
        order.canCancel ||
        order.orderStatus == 0 ||
        (order.orderStatus >= 2 &&
            order.orderStatus != 4); // 已发货或已完成可以查看物流，但排除已取消

    if (!hasActions) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.fromLTRB(20, 12, 20, 20),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[700]!
                : Colors.grey[100]!,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Spacer(),
          if (order.orderStatus >= 2 && order.orderStatus != 4) ...[
            // 已发货或已完成，但不是已取消，显示查看物流按钮
            _buildActionButton(
              AppLocalizations.of(context).viewShipping,
              () => _viewShippingStatus(order),
              isOutlined: true,
            ),
            const SizedBox(width: 12),
          ],
          if (order.canCancel) ...[
            _buildActionButton(
              AppLocalizations.of(context).cancelOrder,
              () => _cancelOrder(order),
              isOutlined: true,
            ),
            const SizedBox(width: 12),
          ],
          if (order.orderStatus == 0) // 待支付
            _buildActionButton(
              AppLocalizations.of(context).payNow,
              () => _payOrder(order),
            ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(
    String text,
    VoidCallback onPressed, {
    bool isOutlined = false,
  }) {
    return SizedBox(
      height: 36,
      child: isOutlined
          ? OutlinedButton(
              onPressed: onPressed,
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[600]!
                      : Colors.grey[400]!,
                  width: 1.5,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(18),
                ),
                backgroundColor: ThemeHelper.getCardBackground(context),
              ),
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
            )
          : Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF109D58), Color(0xFF0D8A4A)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(18),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF109D58).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: onPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18),
                  ),
                  elevation: 0,
                  shadowColor: Colors.transparent,
                ),
                child: Text(
                  text,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
    );
  }

  /// 获取完整的图片URL
  String _getFullImageUrl(String imageUrl) {
    // 使用ApiConfig中的buildImageUrl方法来处理URL
    return ApiConfig.buildImageUrl(imageUrl);
  }

  /// 构建默认商品图片
  Widget _buildDefaultProductImage() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF109D58).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.medical_services,
        color: Color(0xFF109D58),
        size: 24,
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor(int status) {
    return OrderStatusHelper.getOrderStatusColor(status);
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// 取消订单
  Future<void> _cancelOrder(ProductOrderModel order) async {
    // 显示确认对话框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).confirmCancel),
        content: Text(AppLocalizations.of(context).confirmCancelOrder),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(AppLocalizations.of(context).confirm),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _productService.cancelOrder(order.id);
        if (mounted) {
          ToastUtil.show(context, AppLocalizations.of(context).orderCancelled);
          _loadOrders(); // 重新加载订单列表
        }
      } catch (e) {
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).cancelOrderFailed(e.toString()),
          );
        }
      }
    }
  }

  /// 查看订单详情
  void _viewOrderDetail(ProductOrderModel order) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => OrderDetailPage(order: order)),
    );

    // 如果从订单详情页面返回并且有变化，刷新订单列表
    if (result == true) {
      _loadOrders();
    }
  }

  /// 查看物流状态
  void _viewShippingStatus(ProductOrderModel order) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            ShippingStatusPage(order: order, isDoctorView: false),
      ),
    );
  }

  /// 支付订单
  Future<void> _payOrder(ProductOrderModel order) async {
    try {
      // 显示美化的支付确认对话框
      final confirmed = await _showPaymentConfirmDialog(order);

      if (confirmed == true && mounted) {
        // 显示支付处理中
        _showPaymentProcessingDialog(order);

        try {
          // 调用后端创建支付
          print(
            'MyOrdersPage: 开始创建支付，订单ID = ${order.id}, 订单号 = ${order.orderSn}',
          );
          final paymentResponse = await _productService.createOrderPayment(
            orderId: order.id,
            payType: 'app',
          );

          print('MyOrdersPage: 支付创建成功，订单号 = ${paymentResponse.orderSn}');

          // 关闭处理中对话框
          if (mounted) {
            Navigator.of(context).pop();
          }

          // 获取APP支付参数
          final appPayParams = paymentResponse.appPayParams;
          if (appPayParams == null) {
            throw Exception('获取支付参数失败');
          }

          // 初始化微信支付
          final wechatPayService = WechatPaymentService();
          final isInitialized = await wechatPayService.initialize();
          if (!isInitialized) {
            throw Exception('微信支付初始化失败，请检查是否安装微信');
          }

          // 发起微信支付
          print('MyOrdersPage: 发起微信支付');
          final paymentResult = await wechatPayService.pay(appPayParams);

          // 处理支付结果
          await _handlePaymentResult(order, paymentResult);
        } catch (e) {
          // 关闭处理中对话框
          if (mounted) {
            Navigator.of(context).pop();
          }

          print('MyOrdersPage: 支付过程出错: $e');
          if (mounted) {
            ToastUtil.show(context, '支付失败：$e');
          }
        }
      }
    } catch (e) {
      print('MyOrdersPage: 支付订单失败: $e');
      if (mounted) {
        ToastUtil.show(context, '支付失败：$e');
      }
    }
  }

  /// 处理支付结果
  Future<void> _handlePaymentResult(
    ProductOrderModel order,
    PaymentResult result,
  ) async {
    switch (result) {
      case PaymentResult.success:
        print('MyOrdersPage: 支付成功，开始同步支付状态');
        try {
          // 同步支付状态
          await _productService.syncOrderPaymentStatus(order.id);

          if (mounted) {
            _showPaymentSuccessDialog(order);
            // 刷新订单列表
            _loadOrders();
          }
        } catch (e) {
          print('MyOrdersPage: 同步支付状态失败: $e');
          if (mounted) {
            // 即使同步失败，也显示支付成功，因为微信支付已经成功
            _showPaymentSuccessDialog(order);
            _loadOrders();
          }
        }
        break;

      case PaymentResult.cancelled:
        print('MyOrdersPage: 用户取消支付');
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).paymentCancelled,
          );
        }
        break;

      case PaymentResult.failed:
        print('MyOrdersPage: 支付失败');
        if (mounted) {
          ToastUtil.show(context, '支付失败，请重试');
        }
        break;

      case PaymentResult.unknown:
        print('MyOrdersPage: 支付结果未知');
        if (mounted) {
          ToastUtil.show(context, '支付结果未知，请稍后查看订单状态');
          _loadOrders(); // 刷新订单列表查看最新状态
        }
        break;
    }
  }

  /// 显示支付确认对话框
  Future<bool?> _showPaymentConfirmDialog(ProductOrderModel order) {
    return showDialog<bool>(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: [
                Colors.white,
                const Color(0xFF109D58).withValues(alpha: 0.02),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF109D58), Color(0xFF0D8A4A)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(
                  Icons.payment_rounded,
                  color: Colors.white,
                  size: 30,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).confirmPayment,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF1A202C),
                ),
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    _buildPaymentInfoRow(
                      AppLocalizations.of(context).orderNumber,
                      order.orderSn,
                    ),
                    const SizedBox(height: 8),
                    _buildPaymentInfoRow(
                      AppLocalizations.of(context).product,
                      order.productName,
                    ),
                    const SizedBox(height: 8),
                    _buildPaymentInfoRow(
                      AppLocalizations.of(context).doctor,
                      order.doctorName,
                    ),
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Colors.grey[200]!),
                        ),
                      ),
                      child: Row(
                        children: [
                          Text(
                            AppLocalizations.of(context).paymentAmount,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF1A202C),
                            ),
                          ),
                          const Spacer(),
                          Text(
                            order.formattedTotalAmount,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w700,
                              color: Color(0xFF109D58),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey[400]!),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        AppLocalizations.of(context).cancel,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF109D58), Color(0xFF0D8A4A)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(
                              0xFF109D58,
                            ).withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                          shadowColor: Colors.transparent,
                        ),
                        child: Text(
                          AppLocalizations.of(context).confirmPaymentButton,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建支付信息行
  Widget _buildPaymentInfoRow(String label, String value) {
    return Row(
      children: [
        Text(label, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
        const Spacer(),
        Expanded(
          flex: 2,
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF1A202C),
            ),
            textAlign: TextAlign.right,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// 显示支付处理中对话框
  void _showPaymentProcessingDialog(ProductOrderModel order) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF109D58), Color(0xFF0D8A4A)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 3,
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                '支付处理中...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1A202C),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '请稍候，正在处理您的支付',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示支付成功对话框
  void _showPaymentSuccessDialog(ProductOrderModel order) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: [
                Colors.white,
                const Color(0xFF109D58).withValues(alpha: 0.05),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF109D58), Color(0xFF0D8A4A)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.check_rounded,
                  color: Colors.white,
                  size: 40,
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                '支付成功！',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF109D58),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                AppLocalizations.of(context).orderPaidSuccessfully,
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    _buildPaymentInfoRow(
                      AppLocalizations.of(context).orderNumber,
                      order.orderSn,
                    ),
                    const SizedBox(height: 8),
                    _buildPaymentInfoRow('商品', order.productName),
                    const SizedBox(height: 8),
                    _buildPaymentInfoRow(
                      AppLocalizations.of(context).paymentAmount,
                      order.formattedTotalAmount,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF109D58), Color(0xFF0D8A4A)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF109D58).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                      shadowColor: Colors.transparent,
                    ),
                    child: Text(
                      AppLocalizations.of(context).done,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
