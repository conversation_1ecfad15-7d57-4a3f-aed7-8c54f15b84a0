/// 用户资料信息模型 (包含分销员信息)
class UserProfile {
  /// 用户ID
  final int id;

  /// 昵称
  final String nickname;

  /// 手机号
  final String? phone;

  /// 性别 (1: 男, 2: 女)
  final int? sex;

  /// 头像URL
  final String? avatar;

  /// 生日
  final String? birthday;

  /// 分销码
  final String? disCode;

  /// 创建时间
  final String? createdAt;

  /// 最后登录时间
  final String? loginAt;

  /// 是否认证
  final bool auth;

  /// 是否VIP
  final bool vip;

  /// 免费翻译次数
  final int freeTCount;

  /// VIP数据
  final Map<String, dynamic>? vipData;

  /// 是否为分销员 (最重要的判断字段)
  final bool isReferrer;

  /// 分销员等级 (0: 普通用户, 1: 一级, 2: 二级, 3: 三级)
  final int referrerLevel;

  /// 分销员等级名称
  final String? levelName;

  /// 注册来源
  final int? registerSource;

  const UserProfile({
    required this.id,
    required this.nickname,
    this.phone,
    this.sex,
    this.avatar,
    this.birthday,
    this.disCode,
    this.createdAt,
    this.loginAt,
    required this.auth,
    required this.vip,
    required this.freeTCount,
    this.vipData,
    required this.isReferrer,
    required this.referrerLevel,
    this.levelName,
    this.registerSource,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] as int? ?? 0,
      nickname: json['nickname'] as String? ?? '',
      phone: json['phone'] as String?,
      sex: json['sex'] as int?,
      avatar: json['avatar'] as String?,
      birthday: json['birthday'] as String?,
      disCode: json['dis_code'] as String?,
      createdAt: json['created_at'] as String?,
      loginAt: json['login_at'] as String?,
      auth: json['auth'] as bool? ?? false,
      vip: json['vip'] as bool? ?? false,
      freeTCount: json['free_t_count'] as int? ?? 0,
      vipData: json['vip_data'] as Map<String, dynamic>?,
      isReferrer: json['is_referrer'] as bool? ?? false,
      referrerLevel: json['referrer_level'] as int? ?? 0,
      levelName: json['level_name'] as String?,
      registerSource: json['register_source'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nickname': nickname,
      'phone': phone,
      'sex': sex,
      'avatar': avatar,
      'birthday': birthday,
      'dis_code': disCode,
      'created_at': createdAt,
      'login_at': loginAt,
      'auth': auth,
      'vip': vip,
      'free_t_count': freeTCount,
      'vip_data': vipData,
      'is_referrer': isReferrer,
      'referrer_level': referrerLevel,
      'level_name': levelName,
      'register_source': registerSource,
    };
  }
}

/// 分销员收入信息模型
class DistributorMoneys {
  /// 当前余额
  final double money;

  /// 今日收入
  final double todayRevenue;

  /// 总收入
  final double totalRevenue;

  /// 提现中金额
  final double withdrawal;

  /// 已提现金额
  final double withdrawn;

  /// 分销佣金总收入
  final double distribution;

  const DistributorMoneys({
    required this.money,
    required this.todayRevenue,
    required this.totalRevenue,
    required this.withdrawal,
    required this.withdrawn,
    required this.distribution,
  });

  factory DistributorMoneys.fromJson(Map<String, dynamic> json) {
    return DistributorMoneys(
      money: (json['money'] as num?)?.toDouble() ?? 0.0,
      todayRevenue: (json['today_revenue'] as num?)?.toDouble() ?? 0.0,
      totalRevenue: (json['total_revenue'] as num?)?.toDouble() ?? 0.0,
      withdrawal: (json['withdrawal'] as num?)?.toDouble() ?? 0.0,
      withdrawn: (json['withdrawn'] as num?)?.toDouble() ?? 0.0,
      distribution: (json['distribution'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'money': money,
      'today_revenue': todayRevenue,
      'total_revenue': totalRevenue,
      'withdrawal': withdrawal,
      'withdrawn': withdrawn,
      'distribution': distribution,
    };
  }
}

/// 余额变动记录模型
class BalanceRecord {
  /// 变动金额
  final double money;

  /// 变动类型
  final String type;

  /// 来源名称
  final String sourceName;

  /// 备注
  final String remark;

  /// 创建时间
  final String createdAt;

  const BalanceRecord({
    required this.money,
    required this.type,
    required this.sourceName,
    required this.remark,
    required this.createdAt,
  });

  factory BalanceRecord.fromJson(Map<String, dynamic> json) {
    return BalanceRecord(
      money: (json['money'] as num?)?.toDouble() ?? 0.0,
      type: json['type'] as String? ?? '',
      sourceName: json['source_name'] as String? ?? '',
      remark: json['remark'] as String? ?? '',
      createdAt: json['created_at'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'money': money,
      'type': type,
      'source_name': sourceName,
      'remark': remark,
      'created_at': createdAt,
    };
  }
}

/// 下级用户信息模型
class LevelUser {
  /// 用户头像
  final String? avatar;

  /// 用户昵称
  final String nickname;

  /// 当前等级名称
  final String levelName;

  /// 用户ID
  final int userId;

  const LevelUser({
    this.avatar,
    required this.nickname,
    required this.levelName,
    required this.userId,
  });

  factory LevelUser.fromJson(Map<String, dynamic> json) {
    return LevelUser(
      avatar: json['avatar'] as String?,
      nickname: json['nickname'] as String? ?? '',
      levelName: json['level_name'] as String? ?? '',
      userId: json['user_id'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'avatar': avatar,
      'nickname': nickname,
      'level_name': levelName,
      'user_id': userId,
    };
  }
}

/// 分销海报模型
class DistributorPoster {
  /// 海报标题
  final String title;

  /// 海报缩略图
  final String thumb;

  /// 海报大图
  final String img;

  const DistributorPoster({
    required this.title,
    required this.thumb,
    required this.img,
  });

  factory DistributorPoster.fromJson(Map<String, dynamic> json) {
    return DistributorPoster(
      title: json['title'] as String? ?? '',
      thumb: json['thumb'] as String? ?? '',
      img: json['img'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'title': title, 'thumb': thumb, 'img': img};
  }
}

/// 分销等级模型
class DistributorLevel {
  /// 等级ID
  final int id;

  /// 等级名称
  final String name;

  const DistributorLevel({required this.id, required this.name});

  factory DistributorLevel.fromJson(Map<String, dynamic> json) {
    return DistributorLevel(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name};
  }
}
