import 'package:flutter/material.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../common/utils/font_util.dart';
import '../services/distribution_service.dart';
import '../screens/distributor_screen.dart';
import '../widgets/distribution_apply_dialog.dart';

/// 分销管理工具类
class DistributionHelper {
  /// 处理分销管理入口点击事件
  ///
  /// [context] - 上下文
  /// [token] - 用户token
  static Future<void> handleDistributionManagement(
    BuildContext context,
    String token,
  ) async {
    try {
      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // 获取用户资料（包含分销员状态）
      final result = await DistributionService.getUserProfile(token);

      // 关闭加载对话框
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (!context.mounted) return;

      if (result['success']) {
        final userProfile = result['data'];
        final isReferrer = userProfile?.isReferrer ?? false;

        if (isReferrer) {
          // 情况1: 已认证的分销员，直接进入分销员页面
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => DistributorScreen(token: token),
            ),
          );
        } else {
          // 情况2: 不是分销员，显示申请对话框
          _showDistributionApplyDialog(context, token, isReferrer);
        }
      } else {
        // 获取状态失败，显示错误提示
        _showErrorSnackBar(
          context,
          result['message'] ?? AppLocalizations.of(context).loadFailed,
        );
      }
    } catch (e) {
      // 关闭可能存在的加载对话框
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (context.mounted) {
        _showErrorSnackBar(context, AppLocalizations.of(context).networkError);
      }
    }
  }

  /// 显示分销员申请对话框
  static void _showDistributionApplyDialog(
    BuildContext context,
    String token,
    bool isReferrer,
  ) {
    String statusMessage;
    bool canApply;

    if (isReferrer) {
      // 已经是分销员，这种情况不应该出现，但防御性编程
      statusMessage = AppLocalizations.of(context).certifiedDistributor;
      canApply = false;
    } else {
      // 不是分销员，可以申请
      statusMessage = AppLocalizations.of(
        context,
      ).distributionAccessDeniedMessage;
      canApply = true;
    }

    if (canApply) {
      // 显示申请对话框
      showDialog(
        context: context,
        builder: (context) => DistributionApplyDialog(
          token: token,
          onApplySuccess: () {
            // 申请成功后的处理
            _showSuccessSnackBar(
              context,
              AppLocalizations.of(context).applicationSubmitted,
            );
          },
        ),
      );
    } else {
      // 显示状态提示对话框
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            AppLocalizations.of(context).reminderTitle,
            style: FontUtil.createHeadingTextStyle(
              text: AppLocalizations.of(context).reminderTitle,
            ),
          ),
          content: Text(
            statusMessage,
            style: FontUtil.createBodyTextStyle(text: statusMessage),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                AppLocalizations.of(context).confirm,
                style: FontUtil.createButtonTextStyle(
                  text: AppLocalizations.of(context).confirm,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  /// 显示成功提示
  static void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 显示错误提示
  static void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 获取分销员状态显示文本
  static String getDistributorStatusText(bool isReferrer, String? levelName) {
    if (isReferrer) {
      return levelName ?? '已认证分销员'; // 这个通常来自服务器数据
    } else {
      return '未申请'; // 这个通常来自服务器数据
    }
  }

  /// 获取分销员状态颜色
  static Color getDistributorStatusColor(bool isReferrer) {
    if (isReferrer) {
      return Colors.green;
    } else {
      return Colors.grey;
    }
  }
}
