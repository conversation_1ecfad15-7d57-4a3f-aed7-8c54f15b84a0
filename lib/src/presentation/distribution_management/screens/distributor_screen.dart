import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../common/utils/font_util.dart';
import '../models/distributor_models.dart';
import '../services/distribution_service.dart';
import '../widgets/income_overview_card.dart';
import '../widgets/balance_record_list.dart';
import '../widgets/level_user_list.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 分销员管理页面
class DistributorScreen extends StatefulWidget {
  /// 用户token
  final String token;

  const DistributorScreen({super.key, required this.token});

  @override
  State<DistributorScreen> createState() => _DistributorScreenState();
}

class _DistributorScreenState extends State<DistributorScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // 状态变量
  bool _isLoading = true;
  String? _errorMessage;

  // 数据变量
  UserProfile? _userProfile;
  DistributorMoneys? _distributorMoneys;
  List<BalanceRecord> _balanceRecords = [];
  List<LevelUser> _levelUsers = [];
  List<DistributorPoster> _posters = [];
  List<DistributorLevel> _availableLevels = [];

  // 用户列表相关
  bool _showAllLevels = false;

  // 余额记录分页
  int _balanceCurrentPage = 1;
  bool _balanceHasMore = true;
  bool _balanceLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 并行加载基础数据
      final results = await Future.wait([
        DistributionService.getUserProfile(widget.token),
        DistributionService.getDistributorMoneys(widget.token),
        DistributionService.getBalanceRecords(widget.token),
        DistributionService.getLevelUsers(widget.token),
        DistributionService.getDistributorPosters(widget.token),
        DistributionService.getDistributorLevels(),
      ]);

      if (mounted) {
        // 处理用户资料
        if (results[0]['success']) {
          _userProfile = results[0]['data'];
        }

        // 处理收入信息
        if (results[1]['success']) {
          _distributorMoneys = results[1]['data'];
        }

        // 处理余额记录
        if (results[2]['success']) {
          _balanceRecords = results[2]['data'];
          _balanceHasMore = results[2]['hasMore'] ?? false;
        }

        // 处理下级用户
        if (results[3]['success']) {
          _levelUsers = results[3]['data'];
        }

        // 处理海报
        if (results[4]['success']) {
          _posters = results[4]['data'];
        }

        // 处理分销等级
        if (results[5]['success']) {
          _availableLevels = results[5]['data'];
        }

        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = '${AppLocalizations.of(context).loadFailed}: $e';
        });
      }
    }
  }

  /// 刷新数据
  Future<void> _refreshData() async {
    await _loadInitialData();
  }

  /// 加载更多余额记录
  Future<void> _loadMoreBalanceRecords() async {
    if (_balanceLoading || !_balanceHasMore) return;

    setState(() {
      _balanceLoading = true;
    });

    try {
      final result = await DistributionService.getBalanceRecords(
        widget.token,
        page: _balanceCurrentPage + 1,
      );

      if (mounted && result['success']) {
        final newRecords = result['data'] as List<BalanceRecord>;
        setState(() {
          _balanceRecords.addAll(newRecords);
          _balanceCurrentPage++;
          _balanceHasMore = result['hasMore'] ?? false;
          _balanceLoading = false;
        });
      } else {
        setState(() {
          _balanceLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _balanceLoading = false;
        });
        _showErrorSnackBar(
          '${AppLocalizations.of(context).loadMoreFailed}: $e',
        );
      }
    }
  }

  /// 切换用户层级显示
  Future<void> _toggleUserLevel(bool showAll) async {
    setState(() {
      _showAllLevels = showAll;
    });

    try {
      final result = await DistributionService.getLevelUsers(
        widget.token,
        all: showAll,
      );

      if (mounted && result['success']) {
        setState(() {
          _levelUsers = result['data'];
        });
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(
          '${AppLocalizations.of(context).getUserListFailed}: $e',
        );
      }
    }
  }

  /// 修改用户等级
  Future<void> _updateUserLevel(LevelUser user, DistributorLevel level) async {
    try {
      final result = await DistributionService.updateDistributorLevel(
        widget.token,
        user.userId,
        level.id,
      );

      if (result['success']) {
        if (mounted) {
          _showSuccessSnackBar(AppLocalizations.of(context).levelUpdateSuccess);
        }
        // 重新加载用户列表
        _toggleUserLevel(_showAllLevels);
      } else {
        if (mounted) {
          _showErrorSnackBar(
            result['message'] ?? AppLocalizations.of(context).levelUpdateFailed,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('修改等级失败: $e');
      }
    }
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.success),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.error),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background(context),
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).distributionManagement),
        backgroundColor: AppColors.cardBackground(context),
        foregroundColor: AppColors.textPrimary(context),
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _refreshData),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary(context),
          indicatorColor: AppColors.primary,
          labelStyle: FontUtil.createTabLabelStyle(fontSize: 16),
          unselectedLabelStyle: FontUtil.createTabUnselectedLabelStyle(
            fontSize: 16,
          ),
          tabs: [
            Tab(text: AppLocalizations.of(context).overview),
            Tab(text: AppLocalizations.of(context).records),
            Tab(text: AppLocalizations.of(context).team),
            Tab(text: AppLocalizations.of(context).promotion),
          ],
        ),
      ),
      body: _isLoading
          ? _buildLoadingState()
          : _errorMessage != null
          ? _buildErrorState()
          : _buildContent(),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).loading,
            style: TextStyle(color: AppColors.textSecondary(context)),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            textAlign: TextAlign.center,
            style: TextStyle(color: AppColors.textSecondary(context)),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _refreshData,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: Text(AppLocalizations.of(context).retry),
          ),
        ],
      ),
    );
  }

  /// 构建主要内容
  Widget _buildContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        // 概览页面
        _buildOverviewTab(),
        // 记录页面
        _buildRecordsTab(),
        // 团队页面
        _buildTeamTab(),
        // 推广页面
        _buildPromotionTab(),
      ],
    );
  }

  /// 构建概览标签页
  Widget _buildOverviewTab() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            // 个人信息卡片
            if (_userProfile != null) _buildProfileCard(),
            // 收入概览卡片
            if (_distributorMoneys != null)
              IncomeOverviewCard(moneys: _distributorMoneys!),
            // 更多统计信息
            if (_distributorMoneys != null) _buildMoreStatsCard(),
          ],
        ),
      ),
    );
  }

  /// 构建个人信息卡片
  Widget _buildProfileCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.cardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.textHint(context).withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 头像
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              color: AppColors.primary.withValues(alpha: 0.1),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(30),
              child:
                  _userProfile!.avatar != null &&
                      _userProfile!.avatar!.isNotEmpty
                  ? Image.network(
                      _userProfile!.avatar!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.person,
                          color: AppColors.primary,
                          size: 30,
                        );
                      },
                    )
                  : Icon(Icons.person, color: AppColors.primary, size: 30),
            ),
          ),
          const SizedBox(width: 16),
          // 信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _userProfile!.nickname,
                  style: TextStyle(
                    fontSize: 18,
                    color: AppColors.textPrimary(context),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                if (_userProfile!.phone != null)
                  Text(
                    _userProfile!.phone!,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary(context),
                    ),
                  ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.success.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _userProfile!.levelName ??
                        AppLocalizations.of(context).certifiedDistributor,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.success,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建更多统计信息卡片
  Widget _buildMoreStatsCard() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.cardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.textHint(context).withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).fundsDetail,
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textPrimary(context),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  AppLocalizations.of(context).withdrawing,
                  '¥${_distributorMoneys!.withdrawal.toStringAsFixed(2)}',
                  AppColors.warning,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: AppColors.divider(context),
              ),
              Expanded(
                child: _buildStatItem(
                  AppLocalizations.of(context).withdrawn,
                  '¥${_distributorMoneys!.withdrawn.toStringAsFixed(2)}',
                  AppColors.success,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildStatItem(
            AppLocalizations.of(context).totalCommissionIncome,
            '¥${_distributorMoneys!.distribution.toStringAsFixed(2)}',
            AppColors.primary,
          ),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary(context),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// 构建记录标签页
  Widget _buildRecordsTab() {
    return BalanceRecordList(
      records: _balanceRecords,
      isLoading: _balanceLoading,
      hasMore: _balanceHasMore,
      onLoadMore: _loadMoreBalanceRecords,
    );
  }

  /// 构建团队标签页
  Widget _buildTeamTab() {
    return LevelUserList(
      users: _levelUsers,
      showAllLevels: _showAllLevels,
      onToggleLevel: _toggleUserLevel,
      availableLevels: _availableLevels,
      onUpdateLevel: _updateUserLevel,
    );
  }

  /// 构建推广标签页
  Widget _buildPromotionTab() {
    if (_posters.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_outlined,
              size: 64,
              color: AppColors.textHint(context),
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context).noPromotionPosters,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary(context),
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.75,
      ),
      itemCount: _posters.length,
      itemBuilder: (context, index) {
        final poster = _posters[index];
        return _buildPosterItem(poster);
      },
    );
  }

  /// 构建海报项
  Widget _buildPosterItem(DistributorPoster poster) {
    return GestureDetector(
      onTap: () => _showPosterDetail(poster),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.cardBackground(context),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.textHint(context).withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 海报图片
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                child: Image.network(
                  poster.thumb,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: AppColors.iconBackground(context),
                      child: Icon(
                        Icons.image,
                        color: AppColors.textHint(context),
                      ),
                    );
                  },
                ),
              ),
            ),
            // 海报标题
            Padding(
              padding: const EdgeInsets.all(12),
              child: Text(
                poster.title,
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.textPrimary(context),
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示海报详情
  void _showPosterDetail(DistributorPoster poster) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBackground(context),
        contentPadding: const EdgeInsets.all(16),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                poster.img,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 200,
                    height: 200,
                    color: AppColors.iconBackground(context),
                    child: Icon(
                      Icons.image,
                      color: AppColors.textHint(context),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            Text(
              poster.title,
              style: FontUtil.createSubheadingTextStyle(
                text: poster.title,
                fontSize: 16,
                color: AppColors.textPrimary(context),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppLocalizations.of(context).close,
              style: FontUtil.createButtonTextStyle(
                text: AppLocalizations.of(context).close,
                color: AppColors.textSecondary(context),
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: 实现保存或分享功能
              Navigator.of(context).pop();
              _showSuccessSnackBar(AppLocalizations.of(context).inDevelopment);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: Text(
              AppLocalizations.of(context).save,
              style: FontUtil.createButtonTextStyle(
                text: AppLocalizations.of(context).save,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
