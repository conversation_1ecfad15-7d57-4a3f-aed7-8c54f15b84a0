import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../config/api/api_config.dart';
import '../models/distributor_models.dart';

/// 分销管理服务类
class DistributionService {
  /// 获取请求头（包含token）
  static Map<String, String> _getHeaders(String token) {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  /// 申请成为分销员
  static Future<Map<String, dynamic>> applyDistributor(String token) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.applyDistributorUrl),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {'success': true, 'data': data};
      } else {
        return {'success': false, 'message': '申请失败: ${response.statusCode}'};
      }
    } catch (e) {
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  /// 获取用户资料（包含分销员状态）
  static Future<Map<String, dynamic>> getUserProfile(String token) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getUserProfileUrl),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200) {
          return {
            'success': true,
            'data': UserProfile.fromJson(data['data'] ?? {}),
          };
        } else {
          return {'success': false, 'message': data['message'] ?? '获取用户资料失败'};
        }
      } else {
        return {
          'success': false,
          'message': '获取用户资料失败: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  /// 获取分销员收入信息
  static Future<Map<String, dynamic>> getDistributorMoneys(String token) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getDistributorMoneysUrl),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200) {
          return {
            'success': true,
            'data': DistributorMoneys.fromJson(data['data'] ?? {}),
          };
        } else {
          return {'success': false, 'message': data['message'] ?? '获取收入信息失败'};
        }
      } else {
        return {
          'success': false,
          'message': '获取收入信息失败: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  /// 获取余额变动记录
  static Future<Map<String, dynamic>> getBalanceRecords(
    String token, {
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final uri = Uri.parse(ApiConfig.getBalanceRecordsUrl).replace(
        queryParameters: {
          'page': page.toString(),
          'pageSize': pageSize.toString(),
        },
      );

      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200) {
          final List<dynamic> recordsData = data['data'] ?? [];
          final records = recordsData
              .map((item) => BalanceRecord.fromJson(item))
              .toList();

          return {
            'success': true,
            'data': records,
            'hasMore': records.length >= pageSize,
          };
        } else {
          return {'success': false, 'message': data['message'] ?? '获取余额记录失败'};
        }
      } else {
        return {
          'success': false,
          'message': '获取余额记录失败: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  /// 获取下级用户列表
  static Future<Map<String, dynamic>> getLevelUsers(
    String token, {
    bool all = false,
  }) async {
    try {
      final uri = Uri.parse(
        ApiConfig.getLevelUsersUrl,
      ).replace(queryParameters: {'all': all.toString()});

      final response = await http.get(uri, headers: _getHeaders(token));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200) {
          final List<dynamic> usersData = data['data'] ?? [];
          final users = usersData
              .map((item) => LevelUser.fromJson(item))
              .toList();

          return {'success': true, 'data': users};
        } else {
          return {'success': false, 'message': data['message'] ?? '获取下级用户失败'};
        }
      } else {
        return {
          'success': false,
          'message': '获取下级用户失败: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  /// 修改下级分销等级
  static Future<Map<String, dynamic>> updateDistributorLevel(
    String token,
    int userId,
    int levelId,
  ) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConfig.updateDistributorLevelUrl),
        headers: _getHeaders(token),
        body: jsonEncode({'user_id': userId, 'level_id': levelId}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200) {
          return {'success': true, 'data': data['data']};
        } else {
          return {'success': false, 'message': data['message'] ?? '修改等级失败'};
        }
      } else {
        return {'success': false, 'message': '修改等级失败: ${response.statusCode}'};
      }
    } catch (e) {
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  /// 获取分销海报列表
  static Future<Map<String, dynamic>> getDistributorPosters(
    String token,
  ) async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getDistributorPostersUrl),
        headers: _getHeaders(token),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200) {
          final List<dynamic> postersData = data['data'] ?? [];
          final posters = postersData
              .map((item) => DistributorPoster.fromJson(item))
              .toList();

          return {'success': true, 'data': posters};
        } else {
          return {'success': false, 'message': data['message'] ?? '获取海报失败'};
        }
      } else {
        return {'success': false, 'message': '获取海报失败: ${response.statusCode}'};
      }
    } catch (e) {
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  /// 获取分销等级列表
  static Future<Map<String, dynamic>> getDistributorLevels() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.getDistributorLevelsUrl),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200) {
          final List<dynamic> levelsData = data['data'] ?? [];
          final levels = levelsData
              .map((item) => DistributorLevel.fromJson(item))
              .toList();

          return {'success': true, 'data': levels};
        } else {
          return {'success': false, 'message': data['message'] ?? '获取等级列表失败'};
        }
      } else {
        return {
          'success': false,
          'message': '获取等级列表失败: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {'success': false, 'message': '网络错误: $e'};
    }
  }
}
