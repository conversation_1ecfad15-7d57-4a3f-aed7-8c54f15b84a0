import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../config/api/api_config.dart';
import '../../../common/utils/font_util.dart';
import '../models/distributor_models.dart';

/// 下级用户列表组件
class LevelUserList extends StatelessWidget {
  /// 下级用户列表
  final List<LevelUser> users;

  /// 是否显示所有层级
  final bool showAllLevels;

  /// 切换层级显示回调
  final ValueChanged<bool> onToggleLevel;

  /// 修改用户等级回调
  final Function(LevelUser, DistributorLevel)? onUpdateLevel;

  /// 可用的分销等级列表
  final List<DistributorLevel> availableLevels;

  const LevelUserList({
    super.key,
    required this.users,
    required this.showAllLevels,
    required this.onToggleLevel,
    this.onUpdateLevel,
    this.availableLevels = const [],
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 层级切换器
        _buildLevelToggle(context),
        const SizedBox(height: 16),
        // 用户列表
        Expanded(
          child: users.isEmpty
              ? _buildEmptyState(context)
              : _buildUserList(context),
        ),
      ],
    );
  }

  /// 构建层级切换器
  Widget _buildLevelToggle(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppColors.iconBackground(context),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildToggleButton(
              context,
              '直接下级',
              !showAllLevels,
              () => onToggleLevel(false),
            ),
          ),
          Expanded(
            child: _buildToggleButton(
              context,
              '所有层级',
              showAllLevels,
              () => onToggleLevel(true),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建切换按钮
  Widget _buildToggleButton(
    BuildContext context,
    String text,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: isSelected
                  ? Colors.white
                  : AppColors.textSecondary(context),
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建用户列表
  Widget _buildUserList(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: users.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        return _buildUserItem(context, users[index]);
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: AppColors.textHint(context),
          ),
          const SizedBox(height: 16),
          Text(
            '暂无下级用户',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建用户项
  Widget _buildUserItem(BuildContext context, LevelUser user) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border(context), width: 1),
      ),
      child: Row(
        children: [
          // 用户头像
          _buildUserAvatar(user.avatar),
          const SizedBox(width: 12),
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.nickname,
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.textPrimary(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    user.levelName,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 修改等级按钮
          if (availableLevels.isNotEmpty && onUpdateLevel != null)
            GestureDetector(
              onTap: () => _showLevelUpdateDialog(context, user),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppColors.iconBackground(context),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  '修改等级',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary(context),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建用户头像
  Widget _buildUserAvatar(String? avatarPath) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        color: AppColors.primary.withValues(alpha: 0.1),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: avatarPath != null && avatarPath.isNotEmpty
            ? Image.network(
                ApiConfig.buildAvatarUrl(avatarPath),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar();
                },
              )
            : _buildDefaultAvatar(),
      ),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Icon(Icons.person, color: AppColors.primary, size: 24);
  }

  /// 显示等级修改对话框
  void _showLevelUpdateDialog(BuildContext context, LevelUser user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          '修改用户等级',
          style: FontUtil.createHeadingTextStyle(
            text: '修改用户等级',
            color: AppColors.textPrimary(context),
          ),
        ),
        backgroundColor: AppColors.cardBackground(context),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '用户: ${user.nickname}',
              style: FontUtil.createBodyTextStyle(
                text: '用户: ${user.nickname}',
                color: AppColors.textSecondary(context),
              ),
            ),
            Text(
              '当前等级: ${user.levelName}',
              style: FontUtil.createBodyTextStyle(
                text: '当前等级: ${user.levelName}',
                color: AppColors.textSecondary(context),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '选择新等级:',
              style: FontUtil.createSubheadingTextStyle(
                text: '选择新等级:',
                color: AppColors.textPrimary(context),
              ),
            ),
            const SizedBox(height: 8),
            ...availableLevels.map(
              (level) => ListTile(
                contentPadding: EdgeInsets.zero,
                title: Text(
                  level.name,
                  style: FontUtil.createBodyTextStyle(
                    text: level.name,
                    color: AppColors.textPrimary(context),
                  ),
                ),
                leading: Radio<int>(
                  value: level.id,
                  groupValue: -1, // 默认不选中任何项
                  onChanged: (value) {
                    Navigator.of(context).pop();
                    onUpdateLevel?.call(user, level);
                  },
                  activeColor: AppColors.primary,
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  onUpdateLevel?.call(user, level);
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              '取消',
              style: TextStyle(color: AppColors.textSecondary(context)),
            ),
          ),
        ],
      ),
    );
  }
}
