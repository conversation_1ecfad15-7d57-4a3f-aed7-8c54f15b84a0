import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../models/distributor_models.dart';

/// 余额变动记录列表组件
class BalanceRecordList extends StatelessWidget {
  /// 余额记录列表
  final List<BalanceRecord> records;

  /// 是否正在加载
  final bool isLoading;

  /// 是否还有更多数据
  final bool hasMore;

  /// 加载更多回调
  final VoidCallback? onLoadMore;

  const BalanceRecordList({
    super.key,
    required this.records,
    this.isLoading = false,
    this.hasMore = false,
    this.onLoadMore,
  });

  @override
  Widget build(BuildContext context) {
    if (records.isEmpty && !isLoading) {
      return _buildEmptyState(context);
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: records.length + (hasMore ? 1 : 0),
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        if (index < records.length) {
          return _buildRecordItem(context, records[index]);
        } else {
          // 加载更多指示器
          return _buildLoadMoreIndicator(context);
        }
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: AppColors.textHint(context),
          ),
          const SizedBox(height: 16),
          Text(
            '暂无余额变动记录',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建记录项
  Widget _buildRecordItem(BuildContext context, BalanceRecord record) {
    final isPositive = record.money >= 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border(context), width: 1),
      ),
      child: Row(
        children: [
          // 类型图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isPositive
                  ? AppColors.success.withValues(alpha: 0.1)
                  : AppColors.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              isPositive ? Icons.add : Icons.remove,
              color: isPositive ? AppColors.success : AppColors.error,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          // 记录信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      record.sourceName.isNotEmpty
                          ? record.sourceName
                          : _getTypeDisplayName(record.type),
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.textPrimary(context),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${isPositive ? '+' : ''}¥${record.money.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 16,
                        color: isPositive ? AppColors.success : AppColors.error,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                if (record.remark.isNotEmpty) ...[
                  Text(
                    record.remark,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary(context),
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
                Text(
                  _formatDateTime(record.createdAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textHint(context),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载更多指示器
  Widget _buildLoadMoreIndicator(BuildContext context) {
    return GestureDetector(
      onTap: onLoadMore,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isLoading) ...[
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '加载中...',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary(context),
                ),
              ),
            ] else ...[
              Icon(Icons.refresh, size: 16, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                '点击加载更多',
                style: TextStyle(fontSize: 14, color: AppColors.primary),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 获取类型显示名称
  String _getTypeDisplayName(String type) {
    switch (type) {
      case 'recharge':
        return '充值';
      case 'withdraw':
        return '提现';
      case 'commission':
        return '佣金';
      case 'bonus':
        return '奖励';
      case 'refund':
        return '退款';
      default:
        return '其他';
    }
  }

  /// 格式化日期时间
  String _formatDateTime(String dateTimeStr) {
    try {
      final dateTime = DateTime.parse(dateTimeStr);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays == 0) {
        return '今天 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else if (difference.inDays == 1) {
        return '昨天 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}天前';
      } else {
        return '${dateTime.month}/${dateTime.day} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      return dateTimeStr;
    }
  }
}
