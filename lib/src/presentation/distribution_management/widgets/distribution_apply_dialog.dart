import 'package:flutter/material.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../config/themes/app_colors.dart';
import '../../../common/utils/font_util.dart';
import '../services/distribution_service.dart';

/// 分销员申请对话框
class DistributionApplyDialog extends StatefulWidget {
  /// 用户token
  final String token;

  /// 申请成功回调
  final VoidCallback? onApplySuccess;

  const DistributionApplyDialog({
    super.key,
    required this.token,
    this.onApplySuccess,
  });

  @override
  State<DistributionApplyDialog> createState() =>
      _DistributionApplyDialogState();
}

class _DistributionApplyDialogState extends State<DistributionApplyDialog> {
  bool _isApplying = false;

  /// 申请成为分销员
  Future<void> _applyDistributor() async {
    setState(() {
      _isApplying = true;
    });

    try {
      final result = await DistributionService.applyDistributor(widget.token);

      if (mounted) {
        setState(() {
          _isApplying = false;
        });

        if (result['success']) {
          Navigator.of(context).pop();
          widget.onApplySuccess?.call();
          _showSuccessSnackBar(
            AppLocalizations.of(context).applicationSubmitted,
          );
        } else {
          _showErrorSnackBar(
            result['message'] ?? AppLocalizations.of(context).applicationFailed,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isApplying = false;
        });
        _showErrorSnackBar(
          '${AppLocalizations.of(context).applicationFailed}: $e',
        );
      }
    }
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.success),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.error),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppColors.cardBackground(context),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Icon(Icons.info_outline, color: AppColors.primary, size: 24),
          const SizedBox(width: 8),
          Text(
            AppLocalizations.of(context).reminderTitle,
            style: FontUtil.createHeadingTextStyle(
              text: AppLocalizations.of(context).reminderTitle,
              fontSize: 18,
              color: AppColors.textPrimary(context),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).distributionAccessDeniedMessage,
            style: FontUtil.createBodyTextStyle(
              text: AppLocalizations.of(
                context,
              ).distributionAccessDeniedMessage,
              fontSize: 16,
              color: AppColors.textSecondary(context),
              height: 1.5,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.iconBackground(context),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    AppLocalizations.of(context).distributorBenefitDescription,
                    style: FontUtil.createBodyTextStyle(
                      text: AppLocalizations.of(
                        context,
                      ).distributorBenefitDescription,
                      fontSize: 14,
                      color: AppColors.textSecondary(context),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isApplying ? null : () => Navigator.of(context).pop(),
          child: Text(
            AppLocalizations.of(context).cancel,
            style: FontUtil.createButtonTextStyle(
              text: AppLocalizations.of(context).cancel,
              color: AppColors.textSecondary(context),
            ),
          ),
        ),
        ElevatedButton(
          onPressed: _isApplying ? null : _applyDistributor,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: _isApplying
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  AppLocalizations.of(context).applyButton,
                  style: FontUtil.createButtonTextStyle(
                    text: AppLocalizations.of(context).applyButton,
                    color: Colors.white,
                  ),
                ),
        ),
      ],
    );
  }
}
