import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../models/distributor_models.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 收入概览卡片组件
class IncomeOverviewCard extends StatelessWidget {
  /// 分销员收入信息
  final DistributorMoneys moneys;

  const IncomeOverviewCard({super.key, required this.moneys});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.textHint(context).withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // 当前余额
            _buildMainBalance(context),
            const SizedBox(height: 24),
            // 收入信息网格
            _buildIncomeGrid(context),
          ],
        ),
      ),
    );
  }

  /// 构建主要余额显示
  Widget _buildMainBalance(BuildContext context) {
    return Column(
      children: [
        Text(
          AppLocalizations.of(context).currentBalance,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.textSecondary(context),
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '¥${moneys.money.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: 32,
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// 构建收入信息网格
  Widget _buildIncomeGrid(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildIncomeItem(
            context,
            AppLocalizations.of(context).todayIncome,
            '¥${moneys.todayRevenue.toStringAsFixed(2)}',
            AppColors.success,
          ),
        ),
        Container(width: 1, height: 40, color: AppColors.divider(context)),
        Expanded(
          child: _buildIncomeItem(
            context,
            AppLocalizations.of(context).totalIncome,
            '¥${moneys.totalRevenue.toStringAsFixed(2)}',
            AppColors.primary,
          ),
        ),
      ],
    );
  }

  /// 构建单个收入项
  Widget _buildIncomeItem(
    BuildContext context,
    String label,
    String value,
    Color color,
  ) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary(context),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
