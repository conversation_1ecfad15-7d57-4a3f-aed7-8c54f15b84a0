import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../services/dpi_adaptation_service.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// DPI适配设置页面
class DpiAdaptationSettingsPage extends StatefulWidget {
  const DpiAdaptationSettingsPage({super.key});

  @override
  State<DpiAdaptationSettingsPage> createState() =>
      _DpiAdaptationSettingsPageState();
}

class _DpiAdaptationSettingsPageState extends State<DpiAdaptationSettingsPage> {
  final DpiAdaptationService _dpiService = DpiAdaptationService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).dpiAdaptationSettings,
          style: TextStyle(
            color: ThemeHelper.getTextPrimary(context),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ThemeHelper.getTextPrimary(context),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: AnimatedBuilder(
        animation: _dpiService,
        builder: (context, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCurrentInfoCard(),
                const SizedBox(height: 16),
                _buildModeSelectionCard(),
                const SizedBox(height: 16),
                _buildDeviceInfoCard(),
                const SizedBox(height: 16),
                _buildPreviewCard(),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建当前状态信息卡片
  Widget _buildCurrentInfoCard() {
    return Card(
      color: ThemeHelper.getCardBackground(context),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).currentStatus,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              AppLocalizations.of(context).adaptationMode,
              _getCurrentModeDisplayName(),
            ),
            _buildInfoRow(
              AppLocalizations.of(context).scaleFactor,
              '${(_dpiService.getScaleFactor(context) * 100).toInt()}%',
            ),
          ],
        ),
      ),
    );
  }

  /// 构建模式选择卡片
  Widget _buildModeSelectionCard() {
    return Card(
      color: ThemeHelper.getCardBackground(context),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.tune, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).adaptationMode,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...DpiAdaptationMode.values.map((mode) => _buildModeOption(mode)),
          ],
        ),
      ),
    );
  }

  /// 构建模式选项
  Widget _buildModeOption(DpiAdaptationMode mode) {
    final isSelected = _dpiService.currentMode == mode;
    final modeInfo = _getModeInfo(mode);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _dpiService.setDpiAdaptationMode(mode),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.primary.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected
                  ? AppColors.primary
                  : ThemeHelper.getBorder(context),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Radio<DpiAdaptationMode>(
                value: mode,
                groupValue: _dpiService.currentMode,
                onChanged: (value) {
                  if (value != null) {
                    _dpiService.setDpiAdaptationMode(value);
                  }
                },
                activeColor: AppColors.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      modeInfo['title']!,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: ThemeHelper.getTextPrimary(context),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      modeInfo['description']!,
                      style: TextStyle(
                        fontSize: 12,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建设备信息卡片
  Widget _buildDeviceInfoCard() {
    final deviceInfo = _dpiService.getDeviceInfo(context);

    return Card(
      color: ThemeHelper.getCardBackground(context),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.phone_android, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).deviceInfo,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              AppLocalizations.of(context).screenSize,
              '${deviceInfo['screenWidth']?.toInt()} × ${deviceInfo['screenHeight']?.toInt()}',
            ),
            _buildInfoRow(
              AppLocalizations.of(context).devicePixelRatio,
              '${deviceInfo['devicePixelRatio']}',
            ),
            _buildInfoRow(
              AppLocalizations.of(context).screenDiagonal,
              '${deviceInfo['screenDiagonal']}" ${AppLocalizations.of(context).inches}',
            ),
            _buildInfoRow(
              AppLocalizations.of(context).autoScaleFactor,
              '${(double.parse(deviceInfo['autoScaleFactor']) * 100).toInt()}%',
            ),
          ],
        ),
      ),
    );
  }

  /// 构建预览卡片
  Widget _buildPreviewCard() {
    return Card(
      color: ThemeHelper.getCardBackground(context),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.preview, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).effectPreview,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPreviewElements(),
          ],
        ),
      ),
    );
  }

  /// 构建预览元素
  Widget _buildPreviewElements() {
    return Column(
      children: [
        // 按钮预览
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(AppLocalizations.of(context).sampleButton),
              ),
            ),
            const SizedBox(width: 12),
            IconButton(
              onPressed: () {},
              icon: Icon(Icons.favorite, color: AppColors.primary),
              style: IconButton.styleFrom(
                backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // 文本预览
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: ThemeHelper.getBackground(context),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: ThemeHelper.getBorder(context)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.of(context).titleText,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                AppLocalizations.of(context).sampleDescription,
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取当前模式的显示名称
  String _getCurrentModeDisplayName() {
    switch (_dpiService.currentMode) {
      case DpiAdaptationMode.auto:
        return AppLocalizations.of(context).dpiModeAuto;
      case DpiAdaptationMode.small:
        return AppLocalizations.of(context).dpiModeSmall;
      case DpiAdaptationMode.standard:
        return AppLocalizations.of(context).dpiModeStandard;
      case DpiAdaptationMode.large:
        return AppLocalizations.of(context).dpiModeLarge;
    }
  }

  /// 获取模式信息
  Map<String, String> _getModeInfo(DpiAdaptationMode mode) {
    switch (mode) {
      case DpiAdaptationMode.auto:
        return {
          'title': AppLocalizations.of(context).dpiModeAuto,
          'description': AppLocalizations.of(context).dpiModeAutoDesc,
        };
      case DpiAdaptationMode.small:
        return {
          'title': AppLocalizations.of(context).dpiModeSmall,
          'description': AppLocalizations.of(context).dpiModeSmallDesc,
        };
      case DpiAdaptationMode.standard:
        return {
          'title': AppLocalizations.of(context).dpiModeStandard,
          'description': AppLocalizations.of(context).dpiModeStandardDesc,
        };
      case DpiAdaptationMode.large:
        return {
          'title': AppLocalizations.of(context).dpiModeLarge,
          'description': AppLocalizations.of(context).dpiModeLargeDesc,
        };
    }
  }
}
