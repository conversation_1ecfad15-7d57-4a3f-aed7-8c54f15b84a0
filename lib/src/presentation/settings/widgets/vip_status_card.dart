import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../config/routes/app_routes.dart';
import '../../../common/widgets/user_avatar_widget.dart';
import '../../../common/utils/responsive_util.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../services/user_info_manager_service.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// VIP状态卡片 - 显示用户头像、会员状态和到期日期
class VipStatusCard extends StatelessWidget {
  final dynamic user;
  final bool isLoggedIn;

  const VipStatusCard({
    super.key,
    required this.user,
    required this.isLoggedIn,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: UserInfoManagerService(),
      builder: (context, child) {
        // 获取完整的用户信息
        final userInfoManager = UserInfoManagerService();
        final userProfile = userInfoManager.currentUserProfile;

        return _buildVipCard(context, userProfile);
      },
    );
  }

  Widget _buildVipCard(BuildContext context, dynamic userProfile) {
    // 根据主题模式调整渐变色和阴影
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final gradientAlpha = isDark ? 0.25 : 0.15;

    // 检查是否为VIP会员，如果是年度会员使用金色主题
    final isVipUser = userProfile?.vip == true || user?.vip == true;
    final isAnnualVip =
        (userProfile?.vipData?.type == 1) || (user?.vipData?.type == 1);

    // 根据VIP状态选择颜色
    List<Color> gradientColors;
    Color shadowColor;

    if (isVipUser && isAnnualVip) {
      // 年度VIP - 金色主题
      gradientColors = [
        const Color(0xFFFFD700).withValues(alpha: gradientAlpha), // 金色
        const Color(0xFFFFA500).withValues(alpha: gradientAlpha * 0.8), // 橙色
        const Color(0xFFFF8C00).withValues(alpha: gradientAlpha * 0.6), // 深橙色
      ];
      shadowColor = const Color(0xFFFFD700).withValues(alpha: 0.25);
    } else if (isVipUser) {
      // 普通VIP - 紫色主题
      gradientColors = [
        const Color(0xFF6B46C1).withValues(alpha: gradientAlpha),
        const Color(0xFF9333EA).withValues(alpha: gradientAlpha * 0.8),
        const Color(0xFFB45309).withValues(alpha: gradientAlpha * 0.6),
      ];
      shadowColor = const Color(0xFF6B46C1).withValues(alpha: 0.2);
    } else {
      // 普通用户 - 默认蓝色主题
      gradientColors = [
        AppColors.primary.withValues(alpha: gradientAlpha),
        AppColors.secondary.withValues(alpha: gradientAlpha * 0.6),
        AppColors.accent.withValues(alpha: gradientAlpha * 0.3),
      ];
      shadowColor = AppColors.primary.withValues(alpha: 0.15);
    }

    return Container(
      margin: ResponsiveUtil.cardPadding(context),
      padding: ResponsiveUtil.cardPadding(context),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
        ),
        borderRadius: BorderRadius.circular(
          ResponsiveUtil.borderRadius(context, baseRadius: 16),
        ),
        boxShadow: isDark
            ? []
            : [
                // 暗色模式下去掉发光效果
                BoxShadow(
                  color: shadowColor,
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
              ],
      ),
      child: Column(
        children: [
          // 用户头像和基本信息
          Row(
            children: [
              // 头像
              _buildUserAvatar(context),

              SizedBox(width: ResponsiveUtil.largeSpacing(context)),

              // 用户信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 用户名或登录提示
                    GestureDetector(
                      onTap: () {
                        if (!isLoggedIn) {
                          AppRoutes.navigateToLogin(context);
                        }
                      },
                      child: Text(
                        isLoggedIn
                            ? (userProfile?.nickname ??
                                  user?.username ??
                                  AppLocalizations.of(context).user)
                            : AppLocalizations.of(context).clickToLogin,
                        style: TextStyleUtil.getCustomStyle(
                          context: context,
                          baseFontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: isLoggedIn
                              ? AppColors.textPrimary(context)
                              : AppColors.primary,
                        ),
                      ),
                    ),

                    SizedBox(height: ResponsiveUtil.smallSpacing(context) / 2),

                    // 手机号
                    Text(
                      isLoggedIn
                          ? (userProfile?.phone ?? user?.phone ?? '139****8877')
                          : AppLocalizations.of(
                              context,
                            ).loginToEnjoyMoreFeatures,
                      style: TextStyleUtil.getCustomStyle(
                        context: context,
                        baseFontSize: 14,
                        color: AppColors.textSecondary(context),
                      ),
                    ),
                  ],
                ),
              ),

              // 编辑按钮 - 只在用户已登录时显示
              if (isLoggedIn) _buildEditButton(context),
            ],
          ),

          SizedBox(height: ResponsiveUtil.largeSpacing(context)),

          // 会员状态信息
          if (isLoggedIn) _buildVipStatusInfo(context),
        ],
      ),
    );
  }

  /// 构建用户头像
  Widget _buildUserAvatar(BuildContext context) {
    if (!isLoggedIn) {
      // 未登录时显示默认头像
      return UserAvatarWidget(
        size: ResponsiveUtil.fromTextTheme(context, multiplier: 4.0),
        showBorder: true,
        borderWidth: ResponsiveUtil.smallSpacing(context) / 2,
        borderColor: Colors.white,
      );
    }

    // 已登录时使用新的头像组件
    return UserAvatarWidget(
      size: ResponsiveUtil.fromTextTheme(context, multiplier: 4.0),
      showBorder: true,
      borderWidth: ResponsiveUtil.smallSpacing(context) / 2,
      borderColor: Colors.white,
    );
  }

  /// 构建VIP状态信息
  Widget _buildVipStatusInfo(BuildContext context) {
    // 根据主题模式获取背景色
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDark
        ? AppColors.cardBackground(context).withValues(alpha: 0.9)
        : Colors.white.withValues(alpha: 0.8);

    return Container(
      padding: ResponsiveUtil.cardPadding(context),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(
          ResponsiveUtil.borderRadius(context, baseRadius: 12),
        ),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // VIP状态
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).userCardMemberStatusLabel,
                  style: TextStyleUtil.getCustomStyle(
                    context: context,
                    baseFontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textHint(context),
                  ),
                ),
                SizedBox(height: ResponsiveUtil.smallSpacing(context) / 2),
                Row(
                  children: [
                    Container(
                      width: ResponsiveUtil.smallSpacing(context),
                      height: ResponsiveUtil.smallSpacing(context),
                      decoration: const BoxDecoration(
                        color: AppColors.success,
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: ResponsiveUtil.smallSpacing(context) * 1.5),
                    Expanded(
                      child: Text(
                        _getVipStatusText(context),
                        style: TextStyleUtil.getCustomStyle(
                          context: context,
                          baseFontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary(context),
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 分割线
          Container(
            width: 1,
            height: ResponsiveUtil.fromTextTheme(context, multiplier: 2.0),
            color: AppColors.divider(context),
          ),

          // 到期日期
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  AppLocalizations.of(context).userCardExpiryDateLabel,
                  style: TextStyleUtil.getCustomStyle(
                    context: context,
                    baseFontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textHint(context),
                  ),
                ),
                SizedBox(height: ResponsiveUtil.smallSpacing(context) / 2),
                Text(
                  _getVipEndTime(),
                  style: TextStyleUtil.getCustomStyle(
                    context: context,
                    baseFontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary(context),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建编辑按钮
  Widget _buildEditButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(context, AppRoutes.profileEdit);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: ResponsiveUtil.largeSpacing(context),
          vertical: ResponsiveUtil.smallSpacing(context),
        ),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(
            ResponsiveUtil.borderRadius(context, baseRadius: 16),
          ),
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.edit,
              size: ResponsiveUtil.fromTextTheme(context, multiplier: 1.2),
              color: AppColors.primary,
            ),
            SizedBox(width: ResponsiveUtil.smallSpacing(context) * 1.5),
            Text(
              AppLocalizations.of(context).editProfileButton,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取VIP状态文本
  String _getVipStatusText(BuildContext context) {
    if (!isLoggedIn) {
      return AppLocalizations.of(context).pleaseLogin;
    }

    // 优先使用UserInfoManagerService的完整用户信息
    final userInfoManager = UserInfoManagerService();
    final userProfile = userInfoManager.currentUserProfile;

    if (userProfile?.vipData != null) {
      return _localizeVipName(
        context,
        userProfile!.vipData!.vipName,
        userProfile.vipData!.type,
      );
    } else if (userProfile?.vip == true) {
      return AppLocalizations.of(context).vipMember;
    } else if (user?.vipData != null) {
      return _localizeVipName(
        context,
        user!.vipData!.vipName,
        user!.vipData!.type,
      );
    } else if (user?.vip == true) {
      return AppLocalizations.of(context).vipMember;
    } else {
      return AppLocalizations.of(context).normalUser;
    }
  }

  /// 本地化VIP名称
  String _localizeVipName(BuildContext context, String vipName, int? type) {
    // 根据VIP类型返回本地化的名称
    switch (type) {
      case 1: // 年度VIP
        return AppLocalizations.of(context).annualVipMember;
      case 2: // 月度VIP
        return AppLocalizations.of(context).monthlyVipMember;
      case 3: // 终身VIP
        return AppLocalizations.of(context).lifetimeVipMember;
      default:
        // 如果类型未知，尝试根据名称匹配
        if (vipName.contains('年度') || vipName.contains('Annual')) {
          return AppLocalizations.of(context).annualVipMember;
        } else if (vipName.contains('月度') || vipName.contains('Monthly')) {
          return AppLocalizations.of(context).monthlyVipMember;
        } else if (vipName.contains('终身') || vipName.contains('Lifetime')) {
          return AppLocalizations.of(context).lifetimeVipMember;
        } else {
          // 默认返回通用VIP会员
          return AppLocalizations.of(context).vipMember;
        }
    }
  }

  /// 获取VIP到期时间
  String _getVipEndTime() {
    if (!isLoggedIn) {
      return '--';
    }

    // 优先使用UserInfoManagerService的完整用户信息
    final userInfoManager = UserInfoManagerService();
    final userProfile = userInfoManager.currentUserProfile;

    String? endTimeString;
    if (userProfile?.vipData?.endTime != null) {
      endTimeString = userProfile!.vipData!.endTime;
    } else if (user?.vipData?.endTime != null) {
      endTimeString = user!.vipData!.endTime;
    }

    if (endTimeString != null && endTimeString.isNotEmpty) {
      try {
        final endTime = DateTime.parse(endTimeString);
        return '${endTime.year}.${endTime.month.toString().padLeft(2, '0')}.${endTime.day.toString().padLeft(2, '0')}';
      } catch (e) {
        return endTimeString;
      }
    } else {
      return '--';
    }
  }
}
