import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../services/font_size_service.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../common/utils/app_responsive_sizes.dart';
import '../../../common/utils/responsive_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 字体大小设置项组件 - 用于调整字体大小
class FontSizeSettingItem extends StatefulWidget {
  final IconData icon;
  final Color iconColor;
  final String title;
  final String subtitle;

  const FontSizeSettingItem({
    super.key,
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.subtitle,
  });

  @override
  State<FontSizeSettingItem> createState() => _FontSizeSettingItemState();
}

class _FontSizeSettingItemState extends State<FontSizeSettingItem> {
  final FontSizeService _fontSizeService = FontSizeService();

  @override
  void initState() {
    super.initState();
    // 监听字体大小变化
    _fontSizeService.addListener(_onFontSizeChanged);
  }

  @override
  void dispose() {
    _fontSizeService.removeListener(_onFontSizeChanged);
    super.dispose();
  }

  void _onFontSizeChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  /// 获取当前档位的显示文本
  String _getCurrentLevelDisplayText(BuildContext context) {
    switch (_fontSizeService.currentLevel) {
      case FontSizeLevel.small:
        return AppLocalizations.of(context).fontSizeSmall;
      case FontSizeLevel.medium:
        return AppLocalizations.of(context).fontSizeMedium;
      case FontSizeLevel.large:
        return AppLocalizations.of(context).fontSizeLarge;
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsiveSizes = AppResponsiveSizes(context);

    return InkWell(
      onTap: () => _showFontSizeDialog(context),
      borderRadius: BorderRadius.circular(responsiveSizes.cardBorderRadius),
      child: Container(
        padding: ResponsiveUtil.listItemPadding(context),
        child: Row(
          children: [
            // 图标背景
            Container(
              width: ResponsiveUtil.fromTextTheme(context, multiplier: 2.5),
              height: ResponsiveUtil.fromTextTheme(context, multiplier: 2.5),
              decoration: BoxDecoration(
                color: AppColors.withAlpha(widget.iconColor, 26),
                borderRadius: BorderRadius.circular(
                  ResponsiveUtil.borderRadius(context, baseRadius: 10),
                ),
              ),
              child: Icon(
                widget.icon,
                color: widget.iconColor,
                size: ResponsiveUtil.fromTextTheme(context, multiplier: 1.4),
              ),
            ),

            SizedBox(width: ResponsiveUtil.largeSpacing(context)),

            // 标题和副标题
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title,
                    style: TextStyleUtil.getSettingTitleStyle(context),
                  ),
                  SizedBox(height: ResponsiveUtil.smallSpacing(context) / 2),
                  Text(
                    widget.subtitle,
                    style: TextStyleUtil.getSettingSubtitleStyle(context),
                  ),
                ],
              ),
            ),

            // 当前字体档位显示
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: responsiveSizes.spacingS,
                vertical: responsiveSizes.spacingXS / 2,
              ),
              decoration: BoxDecoration(
                color: AppColors.withAlpha(AppColors.info, 26),
                borderRadius: BorderRadius.circular(responsiveSizes.spacingXS),
              ),
              child: Text(
                _getCurrentLevelDisplayText(context),
                style: TextStyle(
                  fontSize: responsiveSizes.getRelativeSize(0.75),
                  fontWeight: FontWeight.w500,
                  color: AppColors.info,
                ),
              ),
            ),

            SizedBox(width: responsiveSizes.spacingS),

            // 箭头
            Icon(
              Icons.arrow_forward_ios,
              color: ThemeHelper.getTextHint(context),
              size: ResponsiveUtil.fromTextTheme(context, multiplier: 1.0),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示字体大小调整对话框
  void _showFontSizeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _FontSizeSelectionDialog(),
    );
  }
}

/// 字体大小选择对话框
class _FontSizeSelectionDialog extends StatefulWidget {
  @override
  _FontSizeSelectionDialogState createState() =>
      _FontSizeSelectionDialogState();
}

class _FontSizeSelectionDialogState extends State<_FontSizeSelectionDialog> {
  final FontSizeService _fontSizeService = FontSizeService();
  late FontSizeLevel _selectedLevel;

  @override
  void initState() {
    super.initState();
    _selectedLevel = _fontSizeService.currentLevel;
  }

  /// 获取当前档位的显示文本
  String _getCurrentLevelDisplayText(BuildContext context) {
    switch (_selectedLevel) {
      case FontSizeLevel.small:
        return AppLocalizations.of(context).fontSizeSmall;
      case FontSizeLevel.medium:
        return AppLocalizations.of(context).fontSizeMedium;
      case FontSizeLevel.large:
        return AppLocalizations.of(context).fontSizeLarge;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        AppLocalizations.of(context).adjustFontSize,
        style: TextStyleUtil.getFixedSizeStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ThemeHelper.getTextPrimary(context),
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 字体大小预览
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(_fontSizeService.getRelativeSpacing(16)),
            decoration: BoxDecoration(
              color: ThemeHelper.getBackground(context),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              AppLocalizations.of(context).fontPreviewText,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: _getPreviewFontSize(_selectedLevel),
                color: ThemeHelper.getTextPrimary(context),
                fontFamily: 'UKIJTor',
              ),
            ),
          ),

          SizedBox(height: _fontSizeService.getRelativeSpacing(24)),

          // 三个字体大小选择按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildFontSizeButton(
                context,
                FontSizeLevel.small,
                AppLocalizations.of(context).fontSizeSmall,
              ),
              _buildFontSizeButton(
                context,
                FontSizeLevel.medium,
                AppLocalizations.of(context).fontSizeMedium,
              ),
              _buildFontSizeButton(
                context,
                FontSizeLevel.large,
                AppLocalizations.of(context).fontSizeLarge,
              ),
            ],
          ),

          SizedBox(height: _fontSizeService.getRelativeSpacing(16)),

          // 当前档位说明
          Text(
            AppLocalizations.of(
              context,
            ).currentSizeLabel(_getCurrentLevelDisplayText(context)),
            style: TextStyleUtil.getFixedSizeStyle(
              fontSize: 14,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            AppLocalizations.of(context).cancel,
            style: TextStyleUtil.getFixedSizeStyle(
              fontSize: 14,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            await _fontSizeService.setFontSizeLevel(_selectedLevel);
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.info,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            AppLocalizations.of(context).confirm,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
        ),
      ],
    );
  }

  /// 构建字体大小选择按钮
  Widget _buildFontSizeButton(
    BuildContext context,
    FontSizeLevel level,
    String label,
  ) {
    final isSelected = _selectedLevel == level;

    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: _fontSizeService.getRelativeSpacing(4),
        ),
        child: InkWell(
          onTap: () {
            setState(() {
              _selectedLevel = level;
            });
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            height: _fontSizeService.getRelativeSpacing(60),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.info.withValues(alpha: 0.1)
                  : ThemeHelper.getBackground(context),
              border: Border.all(
                color: isSelected
                    ? AppColors.info
                    : ThemeHelper.getDivider(context),
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 示例文字
                Text(
                  'Aa',
                  style: TextStyle(
                    fontSize: _getButtonDemoFontSize(level),
                    fontWeight: FontWeight.w600,
                    color: isSelected
                        ? AppColors.info
                        : ThemeHelper.getTextPrimary(context),
                  ),
                ),
                SizedBox(height: _fontSizeService.getRelativeSpacing(4)),
                // 档位标签
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected
                        ? AppColors.info
                        : ThemeHelper.getTextSecondary(context),
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 获取预览文字的字体大小
  double _getPreviewFontSize(FontSizeLevel level) {
    switch (level) {
      case FontSizeLevel.small:
        return 16.0 * 0.9;
      case FontSizeLevel.medium:
        return 16.0 * 1.1;
      case FontSizeLevel.large:
        return 16.0 * 1.3;
    }
  }

  /// 获取按钮演示文字的字体大小
  double _getButtonDemoFontSize(FontSizeLevel level) {
    switch (level) {
      case FontSizeLevel.small:
        return 18.0;
      case FontSizeLevel.medium:
        return 22.0;
      case FontSizeLevel.large:
        return 26.0;
    }
  }
}
