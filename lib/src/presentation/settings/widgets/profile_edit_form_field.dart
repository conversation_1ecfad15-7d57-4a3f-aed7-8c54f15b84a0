import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 个人资料编辑表单字段组件
class ProfileEditFormField extends StatefulWidget {
  final String label;
  final TextEditingController controller;
  final bool isPassword;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final int? maxLines;
  final String? hintText;

  const ProfileEditFormField({
    super.key,
    required this.label,
    required this.controller,
    this.isPassword = false,
    this.validator,
    this.keyboardType,
    this.maxLines = 1,
    this.hintText,
  });

  @override
  State<ProfileEditFormField> createState() => _ProfileEditFormFieldState();
}

class _ProfileEditFormFieldState extends State<ProfileEditFormField> {
  bool _obscureText = true;
  String _currentText = '';

  @override
  void initState() {
    super.initState();
    _obscureText = widget.isPassword;
    _currentText = widget.controller.text;

    // 监听文本变化以更新样式
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    if (_currentText != widget.controller.text) {
      setState(() {
        _currentText = widget.controller.text;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 字段标签
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 8),
            child: Text(
              widget.label,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary(context),
              ),
            ),
          ),

          // 输入框
          Focus(
            onFocusChange: (hasFocus) {
              setState(() {
                // 可以在这里处理焦点变化的视觉效果
              });
            },
            child: TextFormField(
              controller: widget.controller,
              obscureText: widget.isPassword ? _obscureText : false,
              validator: widget.validator,
              keyboardType: widget.keyboardType,
              maxLines: widget.maxLines,
              minLines: widget.maxLines == 1 ? 1 : null,
              expands: false,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 16,
                color: AppColors.textPrimary(context),
                text: _currentText,
              ),
              textAlignVertical: widget.maxLines != null && widget.maxLines! > 1
                  ? TextAlignVertical.top
                  : TextAlignVertical.center,
              textInputAction: widget.maxLines == 1
                  ? TextInputAction.next
                  : TextInputAction.newline,
              scrollPadding: const EdgeInsets.all(20.0),
              enableSuggestions: true,
              autocorrect: false,
              decoration: InputDecoration(
                hintText: widget.hintText ?? _getDefaultHintText(context),
                hintStyle: TextStyleUtil.getCustomStyle(
                  context: context,
                  baseFontSize: 16,
                  color: AppColors.textHint(context),
                ),
                filled: true,
                fillColor: AppColors.background(context),
                border: _buildEnhancedBorder(AppColors.border(context)),
                enabledBorder: _buildEnhancedBorder(AppColors.border(context)),
                focusedBorder: _buildEnhancedBorder(AppColors.primary),
                errorBorder: _buildEnhancedBorder(AppColors.error),
                focusedErrorBorder: _buildEnhancedBorder(AppColors.error),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: widget.maxLines != null && widget.maxLines! > 1
                      ? 16
                      : 18,
                ),
                alignLabelWithHint:
                    widget.maxLines != null && widget.maxLines! > 1,
                isDense: false,
                suffixIcon: widget.isPassword
                    ? IconButton(
                        icon: Icon(
                          _obscureText
                              ? Icons.visibility_off_outlined
                              : Icons.visibility_outlined,
                          color: AppColors.textHint(context),
                          size: 22,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscureText = !_obscureText;
                          });
                        },
                      )
                    : null,
                errorStyle: TextStyle(
                  color: AppColors.error,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取默认的提示文本
  String _getDefaultHintText(BuildContext context) {
    // 根据标签内容返回合适的国际化提示文本
    if (widget.label.contains('密码') ||
        widget.label.toLowerCase().contains('password')) {
      return AppLocalizations.of(context).passwordHint;
    } else if (widget.label.contains('昵称') ||
        widget.label.toLowerCase().contains('nickname')) {
      // 为昵称提供特定的提示文本，或使用通用格式
      return AppLocalizations.of(context).enterUsername; // 复用现有的"请输入用户名"
    }

    // 对于其他字段，使用通用格式（这里暂时保留原逻辑，但实际使用时应该明确传递hintText）
    return '${AppLocalizations.of(context).pleaseEnterPhoneNumber.split(' ')[0]} ${widget.label}'; // 取"请输入"部分
  }

  /// 构建增强的边框样式
  OutlineInputBorder _buildEnhancedBorder(Color color) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(14),
      borderSide: BorderSide(color: color.withValues(alpha: 0.6), width: 1.5),
    );
  }
}
