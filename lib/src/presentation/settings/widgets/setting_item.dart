import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../common/utils/responsive_util.dart';
import '../../../common/utils/text_style_util.dart';

/// 基础设置项组件 - 用于普通的点击设置项
/// 已优化：使用响应式布局，减少硬编码像素值
class SettingItem extends StatelessWidget {
  final IconData icon;
  final Color iconColor;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const SettingItem({
    super.key,
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(ResponsiveUtil.borderRadius(context)),
      child: Container(
        padding: ResponsiveUtil.listItemPadding(context),
        constraints: BoxConstraints(
          minHeight: ResponsiveUtil.minTouchTarget(context),
        ),
        child: Row(
          children: [
            // 图标背景
            Container(
              width: ResponsiveUtil.fromTextTheme(context, multiplier: 2.5),
              height: ResponsiveUtil.fromTextTheme(context, multiplier: 2.5),
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(
                  ResponsiveUtil.borderRadius(context, baseRadius: 10),
                ),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: ResponsiveUtil.fromTextTheme(context, multiplier: 1.4),
              ),
            ),

            SizedBox(width: ResponsiveUtil.largeSpacing(context)),

            // 标题和副标题
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyleUtil.getSettingTitleStyle(context),
                  ),
                  SizedBox(height: ResponsiveUtil.smallSpacing(context) / 2),
                  Text(
                    subtitle,
                    style: TextStyleUtil.getSettingSubtitleStyle(context),
                  ),
                ],
              ),
            ),

            // 箭头
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.textHint(context),
              size: ResponsiveUtil.fromTextTheme(context, multiplier: 1.0),
            ),
          ],
        ),
      ),
    );
  }
}
