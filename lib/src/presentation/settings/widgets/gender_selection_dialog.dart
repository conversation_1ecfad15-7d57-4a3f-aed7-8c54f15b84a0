import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 性别选择对话框
class GenderSelectionDialog extends StatelessWidget {
  final String currentGender;
  final Function(String) onGenderSelected;

  const GenderSelectionDialog({
    super.key,
    required this.currentGender,
    required this.onGenderSelected,
  });

  @override
  Widget build(BuildContext context) {
    final genderOptions = [
      {'key': 'male', 'label': AppLocalizations.of(context).male},
      {'key': 'female', 'label': AppLocalizations.of(context).female},
      {'key': 'notSet', 'label': AppLocalizations.of(context).notSet},
    ];

    return AlertDialog(
      backgroundColor: AppColors.cardBackground(context),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Text(
        AppLocalizations.of(context).gender,
        style: TextStyleUtil.getCustomStyle(
          context: context,
          baseFontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary(context),
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: genderOptions.map((genderOption) {
          final genderKey = genderOption['key']!;
          final genderLabel = genderOption['label']!;
          final isSelected = genderKey == currentGender;
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () {
                  onGenderSelected(genderKey);
                  Navigator.pop(context);
                },
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppColors.primary.withValues(alpha: 0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? AppColors.primary
                          : AppColors.border(context),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      // 性别图标
                      Icon(
                        genderKey == 'male'
                            ? Icons.male
                            : genderKey == 'female'
                            ? Icons.female
                            : Icons.privacy_tip,
                        size: 20,
                        color: isSelected
                            ? AppColors.primary
                            : AppColors.textSecondary(context),
                      ),

                      const SizedBox(width: 12),

                      // 性别文字
                      Expanded(
                        child: Text(
                          genderLabel,
                          style: TextStyleUtil.getCustomStyle(
                            context: context,
                            baseFontSize: 16,
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.normal,
                            color: isSelected
                                ? AppColors.primary
                                : AppColors.textPrimary(context),
                          ),
                        ),
                      ),

                      // 选中指示器
                      if (isSelected)
                        Icon(
                          Icons.check_circle,
                          size: 20,
                          color: AppColors.primary,
                        ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            AppLocalizations.of(context).cancel,
            style: TextStyle(
              color: AppColors.textSecondary(context),
              fontSize: 16,
            ),
          ),
        ),
      ],
    );
  }
}
