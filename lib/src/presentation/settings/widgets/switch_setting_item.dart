import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../common/utils/responsive_util.dart';
import '../../../common/utils/text_style_util.dart';

/// 开关设置项组件 - 用于带开关的设置项
/// 已优化：使用响应式布局，减少硬编码像素值
class SwitchSettingItem extends StatelessWidget {
  final IconData icon;
  final Color iconColor;
  final String title;
  final String subtitle;
  final bool value;
  final ValueChanged<bool> onChanged;

  const SwitchSettingItem({
    super.key,
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.subtitle,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: ResponsiveUtil.listItemPadding(context),
      constraints: BoxConstraints(
        minHeight: ResponsiveUtil.minTouchTarget(context),
      ),
      child: Row(
        children: [
          // 图标背景
          Container(
            width: ResponsiveUtil.fromTextTheme(context, multiplier: 2.5),
            height: ResponsiveUtil.fromTextTheme(context, multiplier: 2.5),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(
                ResponsiveUtil.borderRadius(context, baseRadius: 10),
              ),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: ResponsiveUtil.fromTextTheme(context, multiplier: 1.4),
            ),
          ),

          SizedBox(width: ResponsiveUtil.largeSpacing(context)),

          // 标题和副标题
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: TextStyleUtil.getSettingTitleStyle(context)),
                SizedBox(height: ResponsiveUtil.smallSpacing(context) / 2),
                Text(
                  subtitle,
                  style: TextStyleUtil.getSettingSubtitleStyle(context),
                ),
              ],
            ),
          ),

          // 开关
          Transform.scale(
            scale: ResponsiveUtil.fromTextTheme(
              context,
              multiplier: 0.06,
              fallbackBaseFontSize: 16.0,
            ),
            child: Switch(
              value: value,
              onChanged: onChanged,
              activeColor: AppColors.primary,
              activeTrackColor: AppColors.primary.withValues(alpha: 0.3),
              inactiveThumbColor: AppColors.textHint(context),
              inactiveTrackColor: AppColors.divider(context),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }
}
