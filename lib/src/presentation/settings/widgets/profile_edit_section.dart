import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../common/utils/responsive_util.dart';

/// 个人资料编辑分组容器组件
/// 已优化：使用响应式布局，减少硬编码像素值
class ProfileEditSection extends StatelessWidget {
  final String title;
  final List<Widget> children;

  const ProfileEditSection({
    super.key,
    required this.title,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: ResponsiveUtil.cardPadding(context),
      decoration: BoxDecoration(
        color: AppColors.cardBackground(context),
        borderRadius: BorderRadius.circular(
          ResponsiveUtil.borderRadius(context, baseRadius: 16),
        ),
        border: Border.all(color: AppColors.border(context), width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.05),
            blurRadius: ResponsiveUtil.mediumSpacing(context),
            offset: Offset(0, ResponsiveUtil.smallSpacing(context) / 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分组标题
          Text(title, style: TextStyleUtil.getSubtitleStyle(context)),

          SizedBox(height: ResponsiveUtil.xLargeSpacing(context)),

          // 分组内容
          ...children,
        ],
      ),
    );
  }
}
