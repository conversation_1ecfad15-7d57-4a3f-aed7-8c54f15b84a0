import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../services/language_service.dart';
import '../../../common/utils/app_responsive_sizes.dart';
import '../../../common/utils/responsive_util.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 语言设置项组件 - 用于语言切换
class LanguageSettingItem extends StatelessWidget {
  final IconData icon;
  final Color iconColor;
  final String title;
  final String subtitle;
  final String currentLanguage;
  final ValueChanged<String> onChanged;

  const LanguageSettingItem({
    super.key,
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.subtitle,
    required this.currentLanguage,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveSizes = AppResponsiveSizes(context);

    return InkWell(
      onTap: () => _showLanguageDialog(context),
      borderRadius: BorderRadius.circular(responsiveSizes.cardBorderRadius),
      child: Container(
        padding: ResponsiveUtil.listItemPadding(context),
        child: Row(
          children: [
            // 图标背景
            Container(
              width: ResponsiveUtil.fromTextTheme(context, multiplier: 2.5),
              height: ResponsiveUtil.fromTextTheme(context, multiplier: 2.5),
              decoration: BoxDecoration(
                color: AppColors.withAlpha(iconColor, 26),
                borderRadius: BorderRadius.circular(
                  ResponsiveUtil.borderRadius(context, baseRadius: 10),
                ),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: ResponsiveUtil.fromTextTheme(context, multiplier: 1.4),
              ),
            ),

            SizedBox(width: ResponsiveUtil.largeSpacing(context)),

            // 标题和副标题
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyleUtil.getSettingTitleStyle(context),
                  ),
                  SizedBox(height: ResponsiveUtil.smallSpacing(context) / 2),
                  Text(
                    subtitle,
                    style: TextStyleUtil.getSettingSubtitleStyle(context),
                  ),
                ],
              ),
            ),

            // 当前语言显示
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: responsiveSizes.spacingS,
                vertical: responsiveSizes.spacingXS / 2,
              ),
              decoration: BoxDecoration(
                color: AppColors.withAlpha(AppColors.primary, 26),
                borderRadius: BorderRadius.circular(responsiveSizes.spacingXS),
              ),
              child: Text(
                currentLanguage,
                style: TextStyle(
                  fontSize: responsiveSizes.getRelativeSize(0.75),
                  fontWeight: FontWeight.w500,
                  color: AppColors.primary,
                ),
              ),
            ),

            SizedBox(width: responsiveSizes.spacingS),

            // 箭头
            Icon(
              Icons.arrow_forward_ios,
              color: ThemeHelper.getTextHint(context),
              size: ResponsiveUtil.fromTextTheme(context, multiplier: 1.0),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示语言选择对话框
  void _showLanguageDialog(BuildContext context) {
    final languageOptions = LanguageService().getLanguageOptions(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(context).selectLanguage,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 18,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: languageOptions.map((option) {
            final isSelected = option.name == currentLanguage;
            final isEnabled = option.isEnabled;

            return InkWell(
              onTap: isEnabled
                  ? () {
                      Navigator.of(context).pop();
                      onChanged(option.code);
                    }
                  : null,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 16,
                ),
                margin: const EdgeInsets.symmetric(vertical: 2),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.withAlpha(AppColors.primary, 26)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Text(
                      option.name,
                      style: TextStyleUtil.getCustomStyle(
                        context: context,
                        baseFontSize: 16,
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.normal,
                        color: isEnabled
                            ? (isSelected
                                  ? AppColors.primary
                                  : ThemeHelper.getTextPrimary(context))
                            : ThemeHelper.getTextHint(context),
                      ),
                    ),
                    if (!isEnabled) ...[
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context).comingSoon,
                        style: TextStyleUtil.getCustomStyle(
                          context: context,
                          baseFontSize: 12,
                          color: ThemeHelper.getTextHint(context),
                        ),
                      ),
                    ],
                    const Spacer(),
                    if (isSelected)
                      Icon(
                        Icons.check,
                        color: AppColors.primary,
                        size: ResponsiveUtil.fromTextTheme(
                          context,
                          multiplier: 1.2,
                        ),
                      ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
