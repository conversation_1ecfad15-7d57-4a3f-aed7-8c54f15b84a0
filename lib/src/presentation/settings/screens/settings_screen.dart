import 'package:flutter/material.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import '../../../config/themes/app_colors.dart';
import '../../../config/routes/app_routes.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_info_manager_service.dart';
import '../../../services/theme_service.dart';
import '../../../services/language_service.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../common/utils/font_util.dart';
import '../../../utils/toast_util.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../widgets/vip_status_card.dart';
import '../widgets/setting_item.dart';
import '../widgets/switch_setting_item.dart';
import '../widgets/language_setting_item.dart';
import '../widgets/font_size_setting_item.dart';
import '../pages/dpi_adaptation_settings_page.dart';
import 'help_feedback_screen.dart';
import 'change_password_screen.dart';

/// 设置页面 - 展示用户信息和各种设置选项
class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: UserInfoManagerService(),
      builder: (context, child) {
        final user = AuthService().currentUser;
        final userProfile = UserInfoManagerService().currentUserProfile;
        final isLoggedIn = user != null;

        return _buildSettingsScreen(context, user, userProfile, isLoggedIn);
      },
    );
  }

  Widget _buildSettingsScreen(
    BuildContext context,
    dynamic user,
    dynamic userProfile,
    bool isLoggedIn,
  ) {
    return Scaffold(
      backgroundColor: AppColors.background(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).settingsTitle,
          style: TextStyleUtil.getAppBarTitleStyle(context),
        ),
        backgroundColor: AppColors.cardBackground(context),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back_ios,
            color: AppColors.textPrimary(context),
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // VIP状态卡片
            VipStatusCard(user: user, isLoggedIn: isLoggedIn),

            const SizedBox(height: 12),

            // 显示设置分组
            _buildSettingsGroup(
              context: context,
              title: AppLocalizations.of(context).displaySettings,
              children: [
                SwitchSettingItem(
                  icon: Icons.dark_mode,
                  iconColor: AppColors.warning,
                  title: AppLocalizations.of(context).darkMode,
                  subtitle: AppLocalizations.of(context).darkModeDescription,
                  value: ThemeService().isDarkMode,
                  onChanged: (value) {
                    _showThemeChangeDialog(context, () {
                      ThemeService().toggleDarkMode(value);
                    });
                  },
                ),
                SwitchSettingItem(
                  icon: Icons.phone_android,
                  iconColor: AppColors.info,
                  title: AppLocalizations.of(context).followSystemTheme,
                  subtitle: AppLocalizations.of(
                    context,
                  ).followSystemThemeDescription,
                  value: ThemeService().isSystemMode,
                  onChanged: (value) {
                    _showThemeChangeDialog(context, () {
                      if (value) {
                        ThemeService().setSystemMode();
                      } else {
                        // 如果当前系统是暗色模式，切换到暗色，否则切换到浅色
                        final isDark =
                            MediaQuery.of(context).platformBrightness ==
                            Brightness.dark;
                        if (isDark) {
                          ThemeService().setDarkMode();
                        } else {
                          ThemeService().setLightMode();
                        }
                      }
                    });
                  },
                ),

                FontSizeSettingItem(
                  icon: Icons.format_size,
                  iconColor: AppColors.info,
                  title: AppLocalizations.of(context).fontSize,
                  subtitle: AppLocalizations.of(context).fontSizeDescription,
                ),
                SettingItem(
                  icon: Icons.phone_android,
                  iconColor: AppColors.primary,
                  title: AppLocalizations.of(context).dpiAdaptation,
                  subtitle: AppLocalizations.of(context).dpiAdaptationSubtitle,
                  onTap: () => _handleDpiAdaptation(context),
                ),
                LanguageSettingItem(
                  icon: Icons.language,
                  iconColor: AppColors.success,
                  title: AppLocalizations.of(context).languageSettings,
                  subtitle: AppLocalizations.of(
                    context,
                  ).languageSettingsDescription,
                  currentLanguage: LanguageService().getCurrentLanguageName(),
                  onChanged: (languageCode) async {
                    // 显示语言切换确认对话框
                    _showLanguageChangeDialog(context, () async {
                      await LanguageService().setLanguage(languageCode);
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 账户设置分组（仅在用户已登录时显示）
            if (isLoggedIn) ...[
              _buildSettingsGroup(
                context: context,
                title: AppLocalizations.of(context).accountSettings,
                children: [
                  SettingItem(
                    icon: Icons.lock_outline,
                    iconColor: AppColors.warning,
                    title: AppLocalizations.of(context).changePassword,
                    subtitle: AppLocalizations.of(
                      context,
                    ).changePasswordSubtitle,
                    onTap: () => _handleChangePassword(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // 其他设置分组
            _buildSettingsGroup(
              context: context,
              title: AppLocalizations.of(context).other,
              children: [
                SettingItem(
                  icon: Icons.help_outline,
                  iconColor: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.darkTextSecondary
                      : AppColors.lightTextSecondary,
                  title: AppLocalizations.of(context).helpAndFeedback,
                  subtitle: AppLocalizations.of(
                    context,
                  ).helpAndFeedbackDescription,
                  onTap: () => _handleHelp(context),
                ),
                SettingItem(
                  icon: Icons.info_outline,
                  iconColor: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.darkTextSecondary
                      : AppColors.lightTextSecondary,
                  title: AppLocalizations.of(context).aboutUs,
                  subtitle: AppLocalizations.of(context).aboutUsDescription,
                  onTap: () => _handleAbout(context),
                ),
                // 只有在用户已登录时才显示退出登录选项
                if (isLoggedIn)
                  SettingItem(
                    icon: Icons.logout,
                    iconColor: AppColors.error,
                    title: AppLocalizations.of(context).logout,
                    subtitle: AppLocalizations.of(context).logoutDescription,
                    onTap: () => _handleLogout(context),
                  ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// 构建设置分组
  Widget _buildSettingsGroup({
    required BuildContext context,
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分组标题
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 8),
            child: Text(
              title,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary(context),
              ),
            ),
          ),

          // 设置项容器
          Container(
            decoration: BoxDecoration(
              color: AppColors.cardBackground(context),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: children.asMap().entries.map((entry) {
                final index = entry.key;
                final child = entry.value;
                final isLast = index == children.length - 1;

                return Container(
                  decoration: BoxDecoration(
                    border: isLast
                        ? null
                        : Border(
                            bottom: BorderSide(
                              color: AppColors.divider(context),
                              width: 0.5,
                            ),
                          ),
                  ),
                  child: child,
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 处理修改密码
  void _handleChangePassword(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ChangePasswordScreen()),
    );
  }

  /// 处理帮助
  void _handleHelp(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const HelpFeedbackScreen()));
  }

  /// 处理关于
  void _handleAbout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(context).aboutDialogTitle,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 18,
            fontWeight: FontWeight.w600,
            text: AppLocalizations.of(context).aboutDialogTitle,
          ),
        ),
        content: Text(
          AppLocalizations.of(context).aboutDialogContent,
          style: FontUtil.createBodyTextStyle(
            text: AppLocalizations.of(context).aboutDialogContent,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppLocalizations.of(context).confirm,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.primary,
                text: AppLocalizations.of(context).confirm,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 处理退出登录
  void _handleLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(context).logout,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          AppLocalizations.of(context).logoutConfirmation,
          style: TextStyleUtil.getBodyStyle(context),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                color: AppColors.textSecondary(context),
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // 关闭对话框
              _performLogout(); // 执行退出登录操作
            },
            child: Text(
              AppLocalizations.of(context).confirm,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 执行退出登录操作
  Future<void> _performLogout() async {
    try {
      // 先清除用户信息和头像
      await UserInfoManagerService().handleUserLogout();

      // 执行退出登录
      await AuthService().logout();

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 显示退出成功提示
      ToastUtil.show(context, AppLocalizations.of(context).logoutSuccess);

      // 导航到登录页面
      AppRoutes.navigateToLogin(context);
    } catch (e) {
      // 退出登录失败
      if (!mounted) return;

      ToastUtil.show(
        context,
        AppLocalizations.of(context).logoutFailedError(e.toString()),
      );
    }
  }

  /// 显示主题切换对话框
  void _showThemeChangeDialog(BuildContext context, VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBackground(context),
        title: Text(
          AppLocalizations.of(context).themeSwitch,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 18,
            fontWeight: FontWeight.w600,
            text: AppLocalizations.of(context).themeSwitch,
          ),
        ),
        content: Text(
          AppLocalizations.of(context).themeSwitchMessage,
          style: FontUtil.createBodyTextStyle(
            text: AppLocalizations.of(context).themeSwitchMessage,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 16,
                color: AppColors.textSecondary(context),
                text: AppLocalizations.of(context).cancel,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm(); // 先执行主题切换
              _restartApp(); // 然后重启应用
            },
            child: Text(
              AppLocalizations.of(context).restart,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 16,
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
                text: AppLocalizations.of(context).restart,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示语言切换对话框
  void _showLanguageChangeDialog(BuildContext context, VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBackground(context),
        title: Text(
          AppLocalizations.of(context).languageSettings,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 18,
            fontWeight: FontWeight.w600,
            text: AppLocalizations.of(context).languageSettings,
          ),
        ),
        content: Text(
          AppLocalizations.of(context).languageSwitchMessage,
          style: FontUtil.createBodyTextStyle(
            text: AppLocalizations.of(context).languageSwitchMessage,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 16,
                color: AppColors.textSecondary(context),
                text: AppLocalizations.of(context).cancel,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm(); // 先执行语言切换
              _restartApp(); // 然后重启应用
            },
            child: Text(
              AppLocalizations.of(context).restart,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 16,
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
                text: AppLocalizations.of(context).restart,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 重启应用
  void _restartApp() {
    Phoenix.rebirth(context); // 重启应用
  }

  /// 处理DPI适配设置
  void _handleDpiAdaptation(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DpiAdaptationSettingsPage(),
      ),
    );
  }
}
