import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:city_pickers/city_pickers.dart';
import '../../../config/themes/app_colors.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../utils/toast_util.dart';
import '../../../models/user_profile_model.dart';
import '../../../models/health_profile_model.dart';
import '../../../services/user_profile_cache_service.dart';
import '../../../services/health_profile_cache_service.dart';
import '../../../services/user_profile_service.dart';
import '../../../services/auth_service.dart';
import '../../../services/avatar_manager_service.dart';
import '../../../services/user_info_manager_service.dart';
import '../../../services/location_service.dart';
import '../../../common/widgets/user_avatar_widget.dart';

import '../widgets/profile_edit_form_field.dart';
import '../widgets/profile_edit_section.dart';
import '../widgets/gender_selection_dialog.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 用户个人资料编辑页面
class ProfileEditScreen extends StatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  State<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends State<ProfileEditScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final ImagePicker _imagePicker = ImagePicker();
  final UserProfileCacheService _profileCacheService =
      UserProfileCacheService();
  final HealthProfileCacheService _healthCacheService =
      HealthProfileCacheService();
  final UserProfileService _profileService =
      UserProfileService(); // 用于头像上传等直接API操作
  final AuthService _authService = AuthService();
  final AvatarManagerService _avatarManagerService = AvatarManagerService();
  final UserInfoManagerService _userInfoManager = UserInfoManagerService();
  final LocationService _locationService = LocationService();

  // 表单控制器
  late TextEditingController _usernameController;
  late TextEditingController _heightController;
  late TextEditingController _weightController;
  late TextEditingController _otherAllergensController;
  late TextEditingController _medicationListController;
  late TextEditingController _bloodPressureController;
  late TextEditingController _bloodSugarController;
  late TextEditingController _otherChronicDiseaseController;
  late TextEditingController _surgeryHistoryController;
  late TextEditingController _otherFamilyHistoryController;
  late TextEditingController _addressDetailController;

  // 用户信息状态
  File? _avatarFile;
  String _selectedGender = 'notSet';
  DateTime? _selectedBirthday;
  bool _isProcessing = false;
  bool _isLoading = true;
  String? _uploadedAvatarFileName;
  bool _isUploadingAvatar = false;

  // 地址选择相关
  String _selectedProvince = '';
  String _selectedCity = '';
  String _selectedArea = '';
  bool _isGettingLocation = false;

  String get _selectedRegion {
    if (_selectedProvince.isEmpty) return '';
    String region = _selectedProvince;
    if (_selectedCity.isNotEmpty) region += ' $_selectedCity';
    if (_selectedArea.isNotEmpty) region += ' $_selectedArea';
    return region;
  }

  // 健康信息状态
  String _bloodType = 'unknown';
  bool _hasAllergies = false;
  Set<String> _selectedAllergens = {};
  bool _takingMedication = false;
  bool _hasChronicDisease = false;
  Set<String> _selectedChronicDiseases = {};
  bool _hasHypertension = false;
  bool _hasDiabetes = false;
  bool _hasSurgeryHistory = false;
  Set<String> _selectedFamilyHistory = {};
  String _exerciseFrequency = 'sedentary';
  Set<String> _dietaryPreferences = {};
  String _smokingStatus = 'never';
  String _drinkingStatus = 'never';
  String _sleepDuration = '7_8';
  String _sleepQuality = 'good';
  String _stressLevel = 'very_low';

  // 女性健康信息
  bool _isMenopause = false;
  String _menstrualCycle = 'regular';
  bool _hasPregnancy = false;
  int _birthCount = 0;

  // 原始数据，用于比较是否有改动
  UserProfileModel? _originalProfile;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    // 异步加载用户资料
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUserProfile();
    });
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    _otherAllergensController.dispose();
    _medicationListController.dispose();
    _bloodPressureController.dispose();
    _bloodSugarController.dispose();
    _otherChronicDiseaseController.dispose();
    _surgeryHistoryController.dispose();
    _otherFamilyHistoryController.dispose();
    _addressDetailController.dispose();
    super.dispose();
  }

  /// 初始化表单控制器
  void _initializeControllers() {
    _usernameController = TextEditingController();
    _heightController = TextEditingController();
    _weightController = TextEditingController();
    _otherAllergensController = TextEditingController();
    _medicationListController = TextEditingController();
    _bloodPressureController = TextEditingController();
    _bloodSugarController = TextEditingController();
    _otherChronicDiseaseController = TextEditingController();
    _surgeryHistoryController = TextEditingController();
    _otherFamilyHistoryController = TextEditingController();
    _addressDetailController = TextEditingController();
  }

  /// 加载用户资料和健康档案
  Future<void> _loadUserProfile() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // 从缓存服务获取用户资料和健康档案
      final editData = await _profileCacheService.getEditData();
      final profile = editData['userProfile'] as UserProfileModel?;
      final healthProfile = editData['healthProfile'] as HealthProfileModel?;

      if (mounted && profile != null) {
        setState(() {
          _originalProfile = profile;
          _usernameController.text = profile.nickname;
          _selectedGender = _getGenderKey(profile.sex); // 修复：使用数字转换为键值
          _selectedBirthday = profile.birthdayDate;

          // 加载健康档案数据
          if (healthProfile != null) {
            _loadHealthProfileData(healthProfile);
          }

          _isLoading = false;
        });

        // 如果地址为空，自动获取当前位置
        if (_selectedRegion.isEmpty) {
          _getCurrentLocationDirect();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // 检查是否是认证相关错误
        final errorMessage = e.toString();
        if (errorMessage.contains('登录已过期') ||
            errorMessage.contains('未登录') ||
            errorMessage.contains('用户未登录')) {
          // 只有认证错误才调用 _handleApiError
          await _handleApiError(
            e,
            AppLocalizations.of(context).loadUserProfileFailed,
          );
        } else {
          // 其他错误直接显示，不触发登出
          ToastUtil.show(
            context,
            '${AppLocalizations.of(context).loadUserProfileFailed}: $errorMessage',
          );
        }
      }
    }
  }

  /// 加载健康档案数据到表单状态
  void _loadHealthProfileData(HealthProfileModel healthProfile) {
    // 基础信息
    if (healthProfile.height != null) {
      _heightController.text = healthProfile.height!.toString();
    }
    if (healthProfile.weight != null) {
      _weightController.text = healthProfile.weight!.toString();
    }
    _bloodType = healthProfile.bloodType ?? 'unknown';

    // 地址信息
    _selectedProvince = healthProfile.addressProvince ?? '';
    _selectedCity = healthProfile.addressCity ?? '';
    _selectedArea = healthProfile.addressDistrict ?? '';
    if (healthProfile.addressDetail != null) {
      _addressDetailController.text = healthProfile.addressDetail!;
    }

    // 过敏史
    _hasAllergies = healthProfile.hasAllergies;
    _selectedAllergens = healthProfile.allergyDrugs.toSet()
      ..addAll(healthProfile.allergyFoods);
    if (healthProfile.allergyOthers != null) {
      _otherAllergensController.text = healthProfile.allergyOthers!;
    }

    // 用药情况
    _takingMedication = healthProfile.hasCurrentMedication;
    if (healthProfile.currentMedications != null) {
      _medicationListController.text = healthProfile.currentMedications!;
    }

    // 慢性病史
    _hasChronicDisease = healthProfile.hasChronicDiseases;
    _selectedChronicDiseases = healthProfile.chronicDiseases.toSet();
    _hasHypertension = healthProfile.chronicDiseases.contains('高血压');
    _hasDiabetes = healthProfile.chronicDiseases.contains('糖尿病');

    if (healthProfile.bloodPressureRange != null) {
      _bloodPressureController.text = healthProfile.bloodPressureRange!;
    }
    if (healthProfile.bloodSugarRange != null) {
      _bloodSugarController.text = healthProfile.bloodSugarRange!;
    }
    if (healthProfile.otherChronicDiseases != null) {
      _otherChronicDiseaseController.text = healthProfile.otherChronicDiseases!;
    }

    // 手术史
    _hasSurgeryHistory = healthProfile.hasSurgeryHistory;
    if (healthProfile.surgeryHistory != null) {
      _surgeryHistoryController.text = healthProfile.surgeryHistory!;
    }

    // 家族病史
    _selectedFamilyHistory = healthProfile.familyHistory.toSet();
    if (healthProfile.otherFamilyHistory != null) {
      _otherFamilyHistoryController.text = healthProfile.otherFamilyHistory!;
    }

    // 生活方式
    _exerciseFrequency = healthProfile.exerciseFrequency ?? 'sedentary';
    _dietaryPreferences = healthProfile.dietaryPreferences.toSet();
    _smokingStatus = healthProfile.smokingStatus ?? 'never';
    _drinkingStatus = healthProfile.drinkingStatus ?? 'never';
    _sleepDuration = healthProfile.sleepDuration ?? '7_8';
    _sleepQuality = healthProfile.sleepQuality ?? 'good';
    _stressLevel = healthProfile.stressLevel ?? 'very_low';

    // 女性健康
    if (healthProfile.isMenopause != null) {
      _isMenopause = healthProfile.isMenopause!;
    }
    _menstrualCycle = healthProfile.menstrualCycle ?? 'regular';
    if (healthProfile.hasPregnancy != null) {
      _hasPregnancy = healthProfile.hasPregnancy!;
    }
    _birthCount = healthProfile.birthCount ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background(context),
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingBody() : _buildBody(),
    );
  }

  /// 构建加载状态的主体内容
  Widget _buildLoadingBody() {
    return const Center(child: CircularProgressIndicator());
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        AppLocalizations.of(context).editProfileTitle,
        style: TextStyleUtil.getCustomStyle(
          context: context,
          baseFontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary(context),
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      leading: Container(
        margin: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
        child: Material(
          color: AppColors.cardBackground(context),
          borderRadius: BorderRadius.circular(12),
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.border(context).withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Center(
                child: Icon(
                  Icons.arrow_back_ios_new,
                  color: AppColors.textPrimary(context),
                  size: 18,
                ),
              ),
            ),
          ),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
          child: _isProcessing
              ? Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppColors.primary,
                    ),
                  ),
                )
              : TextButton(
                  onPressed: _saveProfile,
                  style: TextButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    AppLocalizations.of(context).save,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.cardBackground(context),
            AppColors.background(context),
          ],
        ),
      ),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: [
            // 头像编辑区域 - 使用独立的背景
            _buildEnhancedAvatarSection(),

            // 表单内容区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    const SizedBox(height: 8),

                    // 基本信息编辑
                    _buildEnhancedSection(
                      title: AppLocalizations.of(context).basicInfo,
                      icon: Icons.person_outline,
                      children: [
                        ProfileEditFormField(
                          label: AppLocalizations.of(context).nickname,
                          controller: _usernameController,
                          hintText: AppLocalizations.of(context).enterUsername,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return AppLocalizations.of(
                                context,
                              ).nicknameRequired;
                            }
                            if (value.trim().length < 2) {
                              return AppLocalizations.of(
                                context,
                              ).nicknameMinLength;
                            }
                            return null;
                          },
                        ),
                        _buildGenderField(),
                        _buildBirthdayField(),
                        _buildHeightWeightRow(),
                        _buildBloodTypeField(),
                        _buildAddressField(),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // 健康信息分组
                    _buildEnhancedSection(
                      title: AppLocalizations.of(context).healthInfo,
                      icon: Icons.health_and_safety_outlined,
                      children: [
                        _buildAllergySection(),
                        const SizedBox(height: 16),
                        _buildMedicationSection(),
                        const SizedBox(height: 16),
                        _buildChronicDiseaseSection(),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // 医疗历史分组
                    _buildEnhancedSection(
                      title: '医疗历史', // 使用硬编码文本，因为国际化文件中没有这个
                      icon: Icons.medical_information_outlined,
                      children: [
                        _buildSurgeryHistorySection(),
                        const SizedBox(height: 16),
                        _buildFamilyHistorySection(),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // 生活方式分组
                    _buildEnhancedSection(
                      title: AppLocalizations.of(context).lifestyle,
                      icon: Icons.fitness_center_outlined,
                      children: [_buildLifestyleSection()],
                    ),

                    // 女性健康（仅当性别为女时显示）
                    if (_selectedGender == 'female') ...[
                      const SizedBox(height: 20),
                      _buildEnhancedSection(
                        title: AppLocalizations.of(context).womenHealth,
                        icon: Icons.pregnant_woman_outlined,
                        children: [_buildWomenHealthSection()],
                      ),
                    ],

                    const SizedBox(height: 100), // 底部安全区域
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建增强的头像编辑区域
  Widget _buildEnhancedAvatarSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.05),
            AppColors.primary.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // 头像显示区域
          GestureDetector(
            onTap: _showAvatarSelectionDialog,
            child: Stack(
              children: [
                // 头像背景装饰
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.primary.withValues(alpha: 0.1),
                        AppColors.primary.withValues(alpha: 0.05),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withValues(alpha: 0.15),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                ),

                // 头像内容
                Container(
                  width: 120,
                  height: 120,
                  padding: const EdgeInsets.all(4),
                  child: Stack(
                    children: [
                      // 如果有本地选择的图片，优先显示
                      if (_avatarFile != null)
                        Container(
                          width: 112,
                          height: 112,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 3),
                          ),
                          child: ClipOval(
                            child: Image.file(_avatarFile!, fit: BoxFit.cover),
                          ),
                        )
                      else
                        // 否则使用通用头像组件
                        Container(
                          width: 112,
                          height: 112,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 3),
                          ),
                          child: ClipOval(
                            child: UserAvatarWidget(
                              size: 112,
                              showBorder: false,
                            ),
                          ),
                        ),

                      // 编辑指示器
                      if (!_isUploadingAvatar)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            width: 36,
                            height: 36,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primary.withValues(
                                    alpha: 0.3,
                                  ),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.camera_alt,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ),

                      // 上传中的遮罩
                      if (_isUploadingAvatar)
                        Container(
                          width: 112,
                          height: 112,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.black.withValues(alpha: 0.6),
                          ),
                          child: const Center(
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 3,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 更换头像提示文字
          Text(
            _isUploadingAvatar
                ? AppLocalizations.of(context).uploading
                : AppLocalizations.of(context).changeAvatar,
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 16,
              fontWeight: FontWeight.w500,
              color: _isUploadingAvatar
                  ? AppColors.textHint(context)
                  : AppColors.primary,
            ),
          ),

          const SizedBox(height: 4),

          Text(
            '点击头像更换照片',
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 12,
              color: AppColors.textSecondary(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建增强的分组组件
  Widget _buildEnhancedSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 4),
      decoration: BoxDecoration(
        color: AppColors.cardBackground(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.border(context).withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分组标题
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  AppColors.primary.withValues(alpha: 0.05),
                  AppColors.primary.withValues(alpha: 0.02),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, size: 20, color: AppColors.primary),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyleUtil.getCustomStyle(
                      context: context,
                      baseFontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary(context),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 分组内容
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 8, 20, 20),
            child: Column(children: children),
          ),
        ],
      ),
    );
  }

  /// 构建身高体重行组件
  Widget _buildHeightWeightRow() {
    return Row(
      children: [
        // 身高字段
        Expanded(
          child: ProfileEditFormField(
            label: AppLocalizations.of(context).height,
            controller: _heightController,
            hintText: AppLocalizations.of(context).heightHint,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final height = double.tryParse(value);
                if (height == null || height < 50 || height > 250) {
                  return AppLocalizations.of(context).heightValidation;
                }
              }
              return null;
            },
          ),
        ),

        const SizedBox(width: 16),

        // 体重字段
        Expanded(
          child: ProfileEditFormField(
            label: AppLocalizations.of(context).weight,
            controller: _weightController,
            hintText: AppLocalizations.of(context).weightHint,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final weight = double.tryParse(value);
                if (weight == null || weight < 20 || weight > 300) {
                  return AppLocalizations.of(context).weightValidation;
                }
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  /// 显示头像选择对话框
  void _showAvatarSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).selectAvatar),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_camera),
              title: Text(AppLocalizations.of(context).takePhoto),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: Text(AppLocalizations.of(context).selectFromGallery),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppLocalizations.of(context).cancel),
          ),
        ],
      ),
    );
  }

  /// 从相机拍照选择头像
  Future<void> _pickImageFromCamera() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        // 在调试模式下打印选择的文件信息
        assert(() {
          // ignore: avoid_print
          print('从相机选择的文件路径: ${pickedFile.path}');
          // ignore: avoid_print
          print('从相机选择的文件名: ${pickedFile.name}');
          return true;
        }());

        await _navigateToImageCrop(File(pickedFile.path));
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).takePhotoFailed(e.toString()),
        );
      }
    }
  }

  /// 从相册选择头像
  Future<void> _pickImageFromGallery() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        // 在调试模式下打印选择的文件信息
        assert(() {
          // ignore: avoid_print
          print('从相册选择的文件路径: ${pickedFile.path}');
          // ignore: avoid_print
          print('从相册选择的文件名: ${pickedFile.name}');
          return true;
        }());

        await _navigateToImageCrop(File(pickedFile.path));
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).selectImageFailed(e.toString()),
        );
      }
    }
  }

  /// 导航到图片裁剪页面（复用现有的裁剪逻辑）
  Future<void> _navigateToImageCrop(File imageFile) async {
    // 创建一个简化的头像裁剪页面
    final result = await Navigator.push<File>(
      context,
      MaterialPageRoute(
        builder: (context) => AvatarCropScreen(imageFile: imageFile),
      ),
    );

    if (result != null) {
      // 显示本地裁剪的图片，但不立即上传
      setState(() {
        _avatarFile = result;
        // 清除之前上传的头像文件名，因为用户选择了新图片
        _uploadedAvatarFileName = null;
      });
    }
  }

  /// 上传头像到服务器
  Future<bool> _uploadAvatar(File imageFile) async {
    try {
      setState(() {
        _isUploadingAvatar = true;
      });

      final avatarFileName = await _profileService.uploadAvatar(imageFile);

      if (mounted) {
        // 上传成功后，直接保存本地裁剪的文件，而不是重新下载
        await _avatarManagerService.saveAvatarFromFile(imageFile);

        // 使用用户信息管理器处理头像上传成功
        try {
          await _userInfoManager.handleAvatarUploadSuccess(avatarFileName);
        } catch (e) {
          assert(() {
            // ignore: avoid_print
            print('⚠️ 用户信息管理器处理头像上传失败: $e');
            return true;
          }());
        }

        // 强制刷新头像状态，确保所有界面立即更新
        await _avatarManagerService.updateAvatarWithCacheBusting();

        setState(() {
          _uploadedAvatarFileName = avatarFileName;
          _isUploadingAvatar = false;
          // 清除本地选择的文件，现在使用本地持久化的头像
          _avatarFile = null;
        });

        // 在调试模式下打印上传完成的状态
        assert(() {
          // ignore: avoid_print
          print('✅ 头像上传完成，当前状态:');
          // ignore: avoid_print
          print('- 上传的文件名: $avatarFileName');
          // ignore: avoid_print
          print(
            '- 当前AvatarManager路径: ${_avatarManagerService.currentAvatarPath}',
          );
          // ignore: avoid_print
          print('- 本地临时文件已清除: ${_avatarFile == null}');
          return true;
        }());

        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).avatarUploadSuccess,
          );
        }
      }
      return true; // 上传成功
    } catch (e) {
      if (mounted) {
        setState(() {
          _isUploadingAvatar = false;
        });

        // 检查是否是认证相关错误
        final errorMessage = e.toString();
        if (errorMessage.contains('登录已过期') ||
            errorMessage.contains('未登录') ||
            errorMessage.contains('用户未登录')) {
          // 只有认证错误才调用 _handleApiError，这可能会触发登出
          await _handleApiError(
            e,
            AppLocalizations.of(context).avatarUploadFailed,
          );
          return false; // 上传失败，且可能已触发登出
        } else {
          // 其他错误（如网络错误、文件错误等）直接显示，不触发登出
          ToastUtil.show(
            context,
            '${AppLocalizations.of(context).avatarUploadFailed}: $errorMessage',
          );
          return false; // 上传失败，但不触发登出
        }
      }
      return false;
    }
  }

  /// 构建性别选择字段
  Widget _buildGenderField() {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 8),
            child: Text(
              AppLocalizations.of(context).gender,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary(context),
              ),
            ),
          ),
          GestureDetector(
            onTap: _showGenderSelection,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
              decoration: BoxDecoration(
                color: AppColors.background(context),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                  color: AppColors.border(context).withValues(alpha: 0.6),
                  width: 1.5,
                ),
              ),
              child: Row(
                children: [
                  // 性别图标
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      _selectedGender == 'male'
                          ? Icons.male
                          : _selectedGender == 'female'
                          ? Icons.female
                          : Icons.person_outline,
                      size: 18,
                      color: _selectedGender == 'notSet'
                          ? AppColors.textHint(context)
                          : AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _getGenderDisplayText(),
                      style: TextStyleUtil.getCustomStyle(
                        context: context,
                        baseFontSize: 16,
                        color: _selectedGender == 'notSet'
                            ? AppColors.textHint(context)
                            : AppColors.textPrimary(context),
                        text: _getGenderDisplayText(),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                    color: AppColors.textHint(context),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建生日选择字段
  Widget _buildBirthdayField() {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 8),
            child: Text(
              AppLocalizations.of(context).birthday,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary(context),
              ),
            ),
          ),
          GestureDetector(
            onTap: _showBirthdayPicker,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
              decoration: BoxDecoration(
                color: AppColors.background(context),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                  color: AppColors.border(context).withValues(alpha: 0.6),
                  width: 1.5,
                ),
              ),
              child: Row(
                children: [
                  // 生日图标
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.cake_outlined,
                      size: 18,
                      color: _selectedBirthday != null
                          ? AppColors.primary
                          : AppColors.textHint(context),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _selectedBirthday != null
                          ? '${_selectedBirthday!.year}年${_selectedBirthday!.month}月${_selectedBirthday!.day}日'
                          : AppLocalizations.of(context).notSet,
                      style: TextStyleUtil.getCustomStyle(
                        context: context,
                        baseFontSize: 16,
                        color: _selectedBirthday != null
                            ? AppColors.textPrimary(context)
                            : AppColors.textHint(context),
                        text: _selectedBirthday != null
                            ? '${_selectedBirthday!.year}年${_selectedBirthday!.month}月${_selectedBirthday!.day}日'
                            : AppLocalizations.of(context).notSet,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                    color: AppColors.textHint(context),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示性别选择对话框
  void _showGenderSelection() {
    showDialog(
      context: context,
      builder: (context) => GenderSelectionDialog(
        currentGender: _selectedGender,
        onGenderSelected: (gender) {
          setState(() {
            _selectedGender = gender;
          });
        },
      ),
    );
  }

  /// 显示生日选择器
  Future<void> _showBirthdayPicker() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedBirthday ?? DateTime(1995, 1, 1),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      locale: const Locale('zh', 'CN'),
    );

    if (pickedDate != null) {
      setState(() {
        _selectedBirthday = pickedDate;
      });
    }
  }

  /// 构建血型字段
  Widget _buildBloodTypeField() {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 8),
            child: Text(
              AppLocalizations.of(context).bloodType,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary(context),
              ),
            ),
          ),
          DropdownButtonFormField<String>(
            value: _bloodType,
            isExpanded: true,
            decoration: InputDecoration(
              hintText: AppLocalizations.of(context).selectBloodType,
              filled: true,
              fillColor: AppColors.background(context),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(14),
                borderSide: BorderSide(
                  color: AppColors.border(context).withValues(alpha: 0.6),
                  width: 1.5,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(14),
                borderSide: BorderSide(
                  color: AppColors.border(context).withValues(alpha: 0.6),
                  width: 1.5,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(14),
                borderSide: BorderSide(
                  color: AppColors.primary.withValues(alpha: 0.6),
                  width: 1.5,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 18,
              ),
              isDense: false,
              prefixIcon: Container(
                margin: const EdgeInsets.only(left: 12, right: 8),
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  Icons.bloodtype_outlined,
                  size: 18,
                  color: _bloodType != 'unknown'
                      ? AppColors.primary
                      : AppColors.textHint(context),
                ),
              ),
            ),
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 16,
              color: AppColors.textPrimary(context),
              text: _bloodType,
            ),
            items: [
              _buildDropdownMenuItem(
                'A',
                AppLocalizations.of(context).bloodTypeA,
              ),
              _buildDropdownMenuItem(
                'B',
                AppLocalizations.of(context).bloodTypeB,
              ),
              _buildDropdownMenuItem(
                'AB',
                AppLocalizations.of(context).bloodTypeAB,
              ),
              _buildDropdownMenuItem(
                'O',
                AppLocalizations.of(context).bloodTypeO,
              ),
              _buildDropdownMenuItem(
                'unknown',
                AppLocalizations.of(context).bloodTypeUnknown,
              ),
            ],
            onChanged: (value) {
              setState(() {
                _bloodType = value ?? 'unknown';
              });
            },
          ),
        ],
      ),
    );
  }

  /// 构建地址字段
  Widget _buildAddressField() {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 8),
            child: Row(
              children: [
                Text(
                  AppLocalizations.of(context).residentialAddress,
                  style: TextStyleUtil.getCustomStyle(
                    context: context,
                    baseFontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary(context),
                  ),
                ),
                const Spacer(),
                if (_isGettingLocation)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          '定位中...',
                          style: TextStyle(
                            fontSize: 10,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  )
                else
                  TextButton.icon(
                    onPressed: _getCurrentLocationDirect,
                    icon: Icon(
                      Icons.my_location,
                      size: 14,
                      color: AppColors.primary,
                    ),
                    label: Text(
                      AppLocalizations.of(context).locate,
                      style: TextStyleUtil.getCustomStyle(
                        context: context,
                        baseFontSize: 12,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          GestureDetector(
            onTap: _showSystemCityPicker,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
              decoration: BoxDecoration(
                color: AppColors.background(context),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                  color: AppColors.border(context).withValues(alpha: 0.6),
                  width: 1.5,
                ),
              ),
              child: Row(
                children: [
                  // 地址图标
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.location_on_outlined,
                      size: 18,
                      color: _selectedRegion.isEmpty
                          ? AppColors.textHint(context)
                          : AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _selectedRegion.isEmpty
                          ? AppLocalizations.of(
                              context,
                            ).selectResidentialAddress
                          : _selectedRegion,
                      style: TextStyleUtil.getCustomStyle(
                        context: context,
                        baseFontSize: 16,
                        color: _selectedRegion.isEmpty
                            ? AppColors.textHint(context)
                            : AppColors.textPrimary(context),
                        text: _selectedRegion.isEmpty
                            ? AppLocalizations.of(
                                context,
                              ).selectResidentialAddress
                            : _selectedRegion,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColors.textHint(context),
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 保存用户资料
  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // 如果有新选择的头像，先上传
      if (_avatarFile != null && _uploadedAvatarFileName == null) {
        final uploadSuccess = await _uploadAvatar(_avatarFile!);
        if (!uploadSuccess) {
          // 头像上传失败，但不要阻止其他资料的保存
          // 错误信息已在_uploadAvatar中显示
          if (mounted) {
            ToastUtil.show(
              context,
              AppLocalizations.of(context).avatarUploadFailedButProfileWillSave,
            );
          }
        }
      }

      // 更新基本资料
      await _updateBasicProfile();

      // 保存健康档案
      await _saveHealthProfile();

      // 刷新用户信息缓存，确保其他界面能看到更新
      try {
        await UserInfoManagerService().refreshUserInfo();
        debugPrint('✅ 用户信息缓存已刷新');
      } catch (e) {
        debugPrint('⚠️ 刷新用户信息缓存失败: $e');
        // 缓存刷新失败不影响保存成功的提示
      }

      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).profileSaveSuccess,
        );
        // 使用更安全的Navigator操作
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (mounted) {
        // 只在特定的错误情况下调用 _handleApiError
        final errorMessage = e.toString();
        if (errorMessage.contains('登录已过期') || errorMessage.contains('未登录')) {
          await _handleApiError(e, AppLocalizations.of(context).saveFailed);
        } else {
          // 对于其他错误，直接显示错误信息，不触发登出
          ToastUtil.show(
            context,
            '${AppLocalizations.of(context).saveFailed}: $errorMessage',
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// 更新基本资料
  Future<void> _updateBasicProfile() async {
    if (_originalProfile == null) return;

    final nickname = _usernameController.text;
    final sex = _getSexValue(_selectedGender);
    final birthday = _selectedBirthday != null
        ? '${_selectedBirthday!.year}-${_selectedBirthday!.month.toString().padLeft(2, '0')}-${_selectedBirthday!.day.toString().padLeft(2, '0')}'
        : null;

    // 检查是否有改动
    bool hasChanges = false;
    final Map<String, dynamic> updates = {};

    if (nickname != _originalProfile!.nickname) {
      updates['nickname'] = nickname;
      hasChanges = true;
    }

    if (sex != _originalProfile!.sex) {
      updates['sex'] = sex;
      hasChanges = true;
    }

    if (birthday != _originalProfile!.birthday) {
      updates['birthday'] = birthday;
      hasChanges = true;
    }

    // 如果有上传新头像且还未关联，则添加头像信息
    if (_uploadedAvatarFileName != null) {
      updates['avatar'] = _uploadedAvatarFileName;
      hasChanges = true;
    }

    // 只有当有改动时才调用API
    if (hasChanges) {
      await _profileCacheService.updateUserProfile(
        nickname: updates['nickname'],
        sex: updates['sex'],
        birthday: updates['birthday'],
        avatar: updates['avatar'],
      );
    }
  }

  /// 保存健康档案
  Future<void> _saveHealthProfile() async {
    // 构建健康档案数据
    final healthProfile = HealthProfileModel(
      // 基础信息
      height: double.tryParse(_heightController.text),
      weight: double.tryParse(_weightController.text),
      bloodType: _bloodType == 'unknown' ? null : _bloodType,

      // 地址信息
      addressProvince: _selectedProvince.isEmpty ? null : _selectedProvince,
      addressCity: _selectedCity.isEmpty ? null : _selectedCity,
      addressDistrict: _selectedArea.isEmpty ? null : _selectedArea,
      addressDetail: _addressDetailController.text.isEmpty
          ? null
          : _addressDetailController.text,

      // 过敏史
      hasAllergies: _hasAllergies,
      allergyDrugs: _selectedAllergens
          .where(
            (item) =>
                ['青霉素类药物', '头孢类药物', '阿司匹林', '磺胺类药物', '碘造影剂'].contains(item),
          )
          .toList(),
      allergyFoods: _selectedAllergens
          .where(
            (item) => ['花生', '海鲜', '牛奶', '鸡蛋', '坚果', '大豆', '小麦'].contains(item),
          )
          .toList(),
      allergyOthers: _otherAllergensController.text.isEmpty
          ? null
          : _otherAllergensController.text,

      // 用药情况
      hasCurrentMedication: _takingMedication,
      currentMedications: _medicationListController.text.isEmpty
          ? null
          : _medicationListController.text,

      // 慢性病史
      hasChronicDiseases: _hasChronicDisease,
      chronicDiseases: _selectedChronicDiseases.toList(),
      bloodPressureRange: _bloodPressureController.text.isEmpty
          ? null
          : _bloodPressureController.text,
      bloodSugarRange: _bloodSugarController.text.isEmpty
          ? null
          : _bloodSugarController.text,
      otherChronicDiseases: _otherChronicDiseaseController.text.isEmpty
          ? null
          : _otherChronicDiseaseController.text,

      // 手术史
      hasSurgeryHistory: _hasSurgeryHistory,
      surgeryHistory: _surgeryHistoryController.text.isEmpty
          ? null
          : _surgeryHistoryController.text,

      // 家族病史
      familyHistory: _selectedFamilyHistory.toList(),
      otherFamilyHistory: _otherFamilyHistoryController.text.isEmpty
          ? null
          : _otherFamilyHistoryController.text,

      // 生活方式
      exerciseFrequency: _exerciseFrequency,
      dietaryPreferences: _dietaryPreferences.toList(),
      smokingStatus: _smokingStatus,
      drinkingStatus: _drinkingStatus,
      sleepDuration: _sleepDuration,
      sleepQuality: _sleepQuality,
      stressLevel: _stressLevel,

      // 女性健康
      isMenopause: _selectedGender == 'female' ? _isMenopause : null,
      menstrualCycle: _selectedGender == 'female' && !_isMenopause
          ? _menstrualCycle
          : null,
      hasPregnancy: _selectedGender == 'female' ? _hasPregnancy : null,
      birthCount: _selectedGender == 'female' && _hasPregnancy
          ? _birthCount
          : null,
    );

    // 保存健康档案
    await _healthCacheService.updateHealthProfile(healthProfile);
  }

  /// 根据性别文本获取对应的数字值
  int _getSexValue(String genderText) {
    switch (genderText) {
      case 'male':
        return 1;
      case 'female':
        return 2;
      default:
        return 0;
    }
  }

  /// 根据性别数字值获取对应的键值
  String _getGenderKey(int sexValue) {
    switch (sexValue) {
      case 1:
        return 'male';
      case 2:
        return 'female';
      default:
        return 'notSet';
    }
  }

  /// 获取性别显示文本
  String _getGenderDisplayText() {
    switch (_selectedGender) {
      case 'male':
        return AppLocalizations.of(context).male;
      case 'female':
        return AppLocalizations.of(context).female;
      default:
        return AppLocalizations.of(context).notSet;
    }
  }

  /// 处理API错误
  Future<void> _handleApiError(dynamic error, String defaultMessage) async {
    if (!mounted) return;

    final errorMessage = error.toString();

    // 在调试模式下打印错误信息
    assert(() {
      // ignore: avoid_print
      print('API错误: $errorMessage');
      // ignore: avoid_print
      print('默认消息: $defaultMessage');
      return true;
    }());

    // 只有明确的认证错误才触发登出
    if (errorMessage.contains('登录已过期') ||
        errorMessage.contains('未登录') ||
        errorMessage.contains('用户未登录') ||
        errorMessage.contains('Authentication failed') ||
        errorMessage.contains('token expired') ||
        errorMessage.contains('unauthorized')) {
      // 认证失败，导航到登录页面
      ToastUtil.show(
        context,
        AppLocalizations.of(context).loginExpiredPleaseRelogin,
      );

      assert(() {
        // ignore: avoid_print
        print('检测到认证错误，准备执行登出操作');
        return true;
      }());

      try {
        await _authService.logout();
      } catch (logoutError) {
        // 忽略登出错误
        assert(() {
          // ignore: avoid_print
          print('登出时出错: $logoutError');
          return true;
        }());
      }

      // 安全的导航操作
      if (mounted) {
        try {
          Navigator.pushNamedAndRemoveUntil(
            context,
            '/login',
            (route) => false,
          );
        } catch (navError) {
          // 如果Navigator有问题，尝试直接pop到根页面
          assert(() {
            // ignore: avoid_print
            print('导航错误: $navError');
            return true;
          }());
        }
      }
    } else {
      // 非认证错误，只显示错误信息
      ToastUtil.show(context, '$defaultMessage: $error');

      assert(() {
        // ignore: avoid_print
        print('非认证错误，只显示提示信息，不执行登出');
        return true;
      }());
    }
  }

  /// 直接获取当前位置
  Future<void> _getCurrentLocationDirect() async {
    if (_isGettingLocation) return;

    setState(() {
      _isGettingLocation = true;
    });

    try {
      bool success = await _locationService.getCurrentLocation();

      if (success && _locationService.hasCurrentLocation()) {
        setState(() {
          _selectedProvince = _locationService.currentProvince ?? '';
          _selectedCity = _locationService.currentCity ?? '';
          _selectedArea = _locationService.currentArea ?? '';
        });
      }
    } catch (e) {
      // 静默失败
    } finally {
      if (mounted) {
        setState(() {
          _isGettingLocation = false;
        });
      }
    }
  }

  /// 显示系统城市选择器
  Future<void> _showSystemCityPicker() async {
    try {
      final result = await CityPickers.showCityPicker(context: context);

      if (result != null) {
        setState(() {
          _selectedProvince = result.provinceName ?? '';
          _selectedCity = result.cityName ?? '';
          _selectedArea = result.areaName ?? '';
        });
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).regionSelectionFailed,
        );
      }
    }
  }

  /// 构建单选字段
  Widget _buildRadioField({
    required String label,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary(context),
            text: label,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 2,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<bool>(
                title: Text(
                  AppLocalizations.of(context).yes,
                  style: TextStyleUtil.getCustomStyle(
                    context: context,
                    baseFontSize: 14,
                    color: AppColors.textPrimary(context),
                    text: AppLocalizations.of(context).yes,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                value: true,
                groupValue: value,
                onChanged: (val) => onChanged(val ?? false),
                contentPadding: EdgeInsets.zero,
                dense: true,
              ),
            ),
            Expanded(
              child: RadioListTile<bool>(
                title: Text(
                  AppLocalizations.of(context).no,
                  style: TextStyleUtil.getCustomStyle(
                    context: context,
                    baseFontSize: 14,
                    color: AppColors.textPrimary(context),
                    text: AppLocalizations.of(context).no,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                value: false,
                groupValue: value,
                onChanged: (val) => onChanged(val ?? false),
                contentPadding: EdgeInsets.zero,
                dense: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建复选框组
  Widget _buildCheckboxGroup({
    required String label,
    required List<String> options,
    required Set<String> selectedValues,
    required Function(String, bool) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary(context),
            text: label,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 3,
        ),
        const SizedBox(height: 8),
        ...options.map(
          (option) => CheckboxListTile(
            title: Text(
              option,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                color: AppColors.textPrimary(context),
                text: option,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
            value: selectedValues.contains(option),
            onChanged: (isSelected) => onChanged(option, isSelected ?? false),
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
            dense: true,
          ),
        ),
      ],
    );
  }

  /// 构建下拉选择字段
  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?) onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary(context),
              text: label,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: value,
            isExpanded: true, // 防止文字溢出
            decoration: InputDecoration(
              filled: true,
              fillColor: AppColors.cardBackground(context),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.border(context)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.border(context)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.primary, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              isDense: false,
            ),
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 16,
              color: AppColors.textPrimary(context),
              text: value,
            ),
            items: items,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  /// 构建过敏史部分
  Widget _buildAllergySection() {
    return ProfileEditSection(
      title: AppLocalizations.of(context).allergyHistory,
      children: [
        _buildRadioField(
          label: AppLocalizations.of(context).hasAllergies,
          value: _hasAllergies,
          onChanged: (value) {
            setState(() {
              _hasAllergies = value;
              if (!value) {
                _selectedAllergens.clear();
                _otherAllergensController.clear();
              }
            });
          },
        ),
        if (_hasAllergies) ...[
          const SizedBox(height: 16),
          _buildCheckboxGroup(
            label: AppLocalizations.of(context).commonAllergens,
            options: [
              AppLocalizations.of(context).penicillinAllergy,
              AppLocalizations.of(context).cephalosporinAllergy,
              AppLocalizations.of(context).aspirinAllergy,
              AppLocalizations.of(context).peanutAllergy,
              AppLocalizations.of(context).seafoodAllergy,
              AppLocalizations.of(context).milkAllergy,
              AppLocalizations.of(context).eggAllergy,
              AppLocalizations.of(context).pollenDustMiteAllergy,
            ],
            selectedValues: _selectedAllergens,
            onChanged: (value, isSelected) {
              setState(() {
                if (isSelected) {
                  _selectedAllergens.add(value);
                } else {
                  _selectedAllergens.remove(value);
                }
              });
            },
          ),
          const SizedBox(height: 16),
          ProfileEditFormField(
            label: AppLocalizations.of(context).otherAllergens,
            controller: _otherAllergensController,
            hintText: AppLocalizations.of(context).otherAllergensHint,
            maxLines: 2,
          ),
        ],
      ],
    );
  }

  /// 构建当前用药部分
  Widget _buildMedicationSection() {
    return ProfileEditSection(
      title: AppLocalizations.of(context).currentMedication,
      children: [
        _buildRadioField(
          label: AppLocalizations.of(context).takingMedication,
          value: _takingMedication,
          onChanged: (value) {
            setState(() {
              _takingMedication = value;
              if (!value) {
                _medicationListController.clear();
              }
            });
          },
        ),
        if (_takingMedication) ...[
          const SizedBox(height: 16),
          ProfileEditFormField(
            label: AppLocalizations.of(context).medicationList,
            controller: _medicationListController,
            hintText: AppLocalizations.of(context).medicationListHint,
            maxLines: 4,
          ),
        ],
      ],
    );
  }

  /// 构建慢性病史部分
  Widget _buildChronicDiseaseSection() {
    return ProfileEditSection(
      title: AppLocalizations.of(context).chronicDiseaseHistory,
      children: [
        _buildRadioField(
          label: AppLocalizations.of(context).hasChronicDisease,
          value: _hasChronicDisease,
          onChanged: (value) {
            setState(() {
              _hasChronicDisease = value;
              if (!value) {
                _selectedChronicDiseases.clear();
                _hasHypertension = false;
                _hasDiabetes = false;
                _bloodPressureController.clear();
                _bloodSugarController.clear();
                _otherChronicDiseaseController.clear();
              }
            });
          },
        ),
        if (_hasChronicDisease) ...[
          const SizedBox(height: 16),
          _buildChronicDiseaseCheckboxes(),
          const SizedBox(height: 16),
          ProfileEditFormField(
            label: AppLocalizations.of(context).otherChronicDiseases,
            controller: _otherChronicDiseaseController,
            hintText: AppLocalizations.of(context).otherChronicDiseasesHint,
            maxLines: 2,
          ),
        ],
      ],
    );
  }

  /// 构建慢性病复选框组
  Widget _buildChronicDiseaseCheckboxes() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).specificSymptoms,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary(context),
          ),
        ),
        const SizedBox(height: 8),

        // 高血压
        CheckboxListTile(
          title: Text(
            AppLocalizations.of(context).hypertension,
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 14,
              color: AppColors.textPrimary(context),
              text: AppLocalizations.of(context).hypertension,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
          value: _hasHypertension,
          onChanged: (value) {
            setState(() {
              _hasHypertension = value ?? false;
              if (!_hasHypertension) {
                _bloodPressureController.clear();
              }
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
          dense: true,
        ),
        if (_hasHypertension) ...[
          Padding(
            padding: const EdgeInsets.only(left: 32, bottom: 8),
            child: ProfileEditFormField(
              label: AppLocalizations.of(context).bloodPressureRange,
              controller: _bloodPressureController,
              hintText: AppLocalizations.of(context).bloodPressureHint,
            ),
          ),
        ],

        // 糖尿病
        CheckboxListTile(
          title: Text(
            AppLocalizations.of(context).diabetes,
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 14,
              color: AppColors.textPrimary(context),
              text: AppLocalizations.of(context).diabetes,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
          value: _hasDiabetes,
          onChanged: (value) {
            setState(() {
              _hasDiabetes = value ?? false;
              if (!_hasDiabetes) {
                _bloodSugarController.clear();
              }
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
          dense: true,
        ),
        if (_hasDiabetes) ...[
          Padding(
            padding: const EdgeInsets.only(left: 32, bottom: 8),
            child: ProfileEditFormField(
              label: AppLocalizations.of(context).bloodSugarRange,
              controller: _bloodSugarController,
              hintText: AppLocalizations.of(context).bloodSugarHint,
            ),
          ),
        ],
      ],
    );
  }

  /// 构建手术与住院史部分
  Widget _buildSurgeryHistorySection() {
    return ProfileEditSection(
      title: AppLocalizations.of(context).surgeryHistory,
      children: [
        _buildRadioField(
          label: AppLocalizations.of(context).hasSurgeryHistory,
          value: _hasSurgeryHistory,
          onChanged: (value) {
            setState(() {
              _hasSurgeryHistory = value;
              if (!value) {
                _surgeryHistoryController.clear();
              }
            });
          },
        ),
        if (_hasSurgeryHistory) ...[
          const SizedBox(height: 16),
          ProfileEditFormField(
            label: AppLocalizations.of(context).surgeryDetails,
            controller: _surgeryHistoryController,
            hintText: AppLocalizations.of(context).surgeryDetailsHint,
            maxLines: 4,
          ),
        ],
      ],
    );
  }

  /// 构建家族病史部分
  Widget _buildFamilyHistorySection() {
    return ProfileEditSection(
      title: AppLocalizations.of(context).familyHistory,
      children: [
        _buildCheckboxGroup(
          label: AppLocalizations.of(context).familyDiseaseHistory,
          options: [
            AppLocalizations.of(context).familyHypertension,
            AppLocalizations.of(context).familyDiabetes,
            AppLocalizations.of(context).familyHeartDisease,
            AppLocalizations.of(context).familyStroke,
            AppLocalizations.of(context).familyCancer,
            AppLocalizations.of(context).familyMentalHealth,
          ],
          selectedValues: _selectedFamilyHistory,
          onChanged: (value, isSelected) {
            setState(() {
              if (isSelected) {
                _selectedFamilyHistory.add(value);
              } else {
                _selectedFamilyHistory.remove(value);
              }
            });
          },
        ),
        const SizedBox(height: 16),
        ProfileEditFormField(
          label: AppLocalizations.of(context).otherFamilyHistory,
          controller: _otherFamilyHistoryController,
          hintText: AppLocalizations.of(context).otherFamilyHistoryHint,
          maxLines: 2,
        ),
      ],
    );
  }

  /// 构建生活方式部分
  Widget _buildLifestyleSection() {
    return ProfileEditSection(
      title: AppLocalizations.of(context).lifestyle,
      children: [
        _buildDropdownField(
          label: AppLocalizations.of(context).exerciseFrequency,
          value: _exerciseFrequency,
          items: [
            _buildDropdownMenuItem(
              'sedentary',
              AppLocalizations.of(context).exerciseSedentary,
            ),
            _buildDropdownMenuItem(
              'light',
              AppLocalizations.of(context).exerciseLight,
            ),
            _buildDropdownMenuItem(
              'moderate',
              AppLocalizations.of(context).exerciseModerate,
            ),
            _buildDropdownMenuItem(
              'very_active',
              AppLocalizations.of(context).exerciseActive,
            ),
          ],
          onChanged: (value) {
            setState(() {
              _exerciseFrequency = value ?? 'sedentary';
            });
          },
        ),
        const SizedBox(height: 16),
        _buildCheckboxGroup(
          label: AppLocalizations.of(context).dietaryPreferences,
          options: [
            AppLocalizations.of(context).balancedDiet,
            AppLocalizations.of(context).vegetarianDiet,
            AppLocalizations.of(context).meatDiet,
            AppLocalizations.of(context).oilyFood,
            AppLocalizations.of(context).saltyFood,
            AppLocalizations.of(context).sweetFood,
          ],
          selectedValues: _dietaryPreferences,
          onChanged: (value, isSelected) {
            setState(() {
              if (isSelected) {
                _dietaryPreferences.add(value);
              } else {
                _dietaryPreferences.remove(value);
              }
            });
          },
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          label: AppLocalizations.of(context).smokingStatus,
          value: _smokingStatus,
          items: [
            _buildDropdownMenuItem(
              'never',
              AppLocalizations.of(context).neverSmoke,
            ),
            _buildDropdownMenuItem(
              'quit',
              AppLocalizations.of(context).quitSmoking,
            ),
            _buildDropdownMenuItem(
              'occasional',
              AppLocalizations.of(context).occasionalSmoking,
            ),
            _buildDropdownMenuItem(
              'daily',
              AppLocalizations.of(context).dailySmoking,
            ),
          ],
          onChanged: (value) {
            setState(() {
              _smokingStatus = value ?? 'never';
            });
          },
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          label: AppLocalizations.of(context).drinkingStatus,
          value: _drinkingStatus,
          items: [
            _buildDropdownMenuItem(
              'never',
              AppLocalizations.of(context).neverDrink,
            ),
            _buildDropdownMenuItem(
              'quit',
              AppLocalizations.of(context).quitDrinking,
            ),
            _buildDropdownMenuItem(
              'social',
              AppLocalizations.of(context).socialDrinking,
            ),
            _buildDropdownMenuItem(
              'weekly',
              AppLocalizations.of(context).weeklyDrinking,
            ),
            _buildDropdownMenuItem(
              'daily',
              AppLocalizations.of(context).dailyDrinking,
            ),
          ],
          onChanged: (value) {
            setState(() {
              _drinkingStatus = value ?? 'never';
            });
          },
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          label: AppLocalizations.of(context).sleepDuration,
          value: _sleepDuration,
          items: [
            _buildDropdownMenuItem(
              'less_6',
              AppLocalizations.of(context).sleepLessThan6,
            ),
            _buildDropdownMenuItem(
              '6_7',
              AppLocalizations.of(context).sleep6To7,
            ),
            _buildDropdownMenuItem(
              '7_8',
              AppLocalizations.of(context).sleep7To8,
            ),
            _buildDropdownMenuItem(
              'more_8',
              AppLocalizations.of(context).sleepMoreThan8,
            ),
          ],
          onChanged: (value) {
            setState(() {
              _sleepDuration = value ?? '7_8';
            });
          },
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          label: AppLocalizations.of(context).sleepQuality,
          value: _sleepQuality,
          items: [
            _buildDropdownMenuItem(
              'good',
              AppLocalizations.of(context).sleepGood,
            ),
            _buildDropdownMenuItem(
              'fair',
              AppLocalizations.of(context).sleepFair,
            ),
            _buildDropdownMenuItem(
              'poor',
              AppLocalizations.of(context).sleepPoor,
            ),
          ],
          onChanged: (value) {
            setState(() {
              _sleepQuality = value ?? 'good';
            });
          },
        ),
        const SizedBox(height: 16),
        _buildDropdownField(
          label: AppLocalizations.of(context).stressLevel,
          value: _stressLevel,
          items: [
            _buildDropdownMenuItem(
              'very_low',
              AppLocalizations.of(context).stressLow,
            ),
            _buildDropdownMenuItem(
              'low',
              AppLocalizations.of(context).stressMild,
            ),
            _buildDropdownMenuItem(
              'moderate',
              AppLocalizations.of(context).stressModerate,
            ),
            _buildDropdownMenuItem(
              'high',
              AppLocalizations.of(context).stressHigh,
            ),
            _buildDropdownMenuItem(
              'very_high',
              AppLocalizations.of(context).stressExtreme,
            ),
          ],
          onChanged: (value) {
            setState(() {
              _stressLevel = value ?? 'very_low';
            });
          },
        ),
      ],
    );
  }

  /// 构建女性健康部分
  Widget _buildWomenHealthSection() {
    return ProfileEditSection(
      title: AppLocalizations.of(context).womenHealth,
      children: [
        _buildRadioField(
          label: AppLocalizations.of(context).isMenopause,
          value: _isMenopause,
          onChanged: (value) {
            setState(() {
              _isMenopause = value;
            });
          },
        ),
        if (!_isMenopause) ...[
          const SizedBox(height: 16),
          _buildDropdownField(
            label: AppLocalizations.of(context).menstrualCycleRegular,
            value: _menstrualCycle,
            items: [
              _buildDropdownMenuItem(
                'regular',
                AppLocalizations.of(context).menstrualRegular,
              ),
              _buildDropdownMenuItem(
                'irregular',
                AppLocalizations.of(context).menstrualIrregular,
              ),
              _buildDropdownMenuItem(
                'uncertain',
                AppLocalizations.of(context).menstrualUncertain,
              ),
            ],
            onChanged: (value) {
              setState(() {
                _menstrualCycle = value ?? 'regular';
              });
            },
          ),
        ],
        const SizedBox(height: 16),
        _buildRadioField(
          label: AppLocalizations.of(context).hasPregnancy,
          value: _hasPregnancy,
          onChanged: (value) {
            setState(() {
              _hasPregnancy = value;
              if (!value) {
                _birthCount = 0;
              }
            });
          },
        ),
        if (_hasPregnancy) ...[
          const SizedBox(height: 16),
          _buildDropdownField(
            label: AppLocalizations.of(context).birthCount,
            value: _birthCount.toString(),
            items: [
              _buildDropdownMenuItem(
                '0',
                AppLocalizations.of(context).birthCount0,
              ),
              _buildDropdownMenuItem(
                '1',
                AppLocalizations.of(context).birthCount1,
              ),
              _buildDropdownMenuItem(
                '2',
                AppLocalizations.of(context).birthCount2,
              ),
              _buildDropdownMenuItem(
                '3',
                AppLocalizations.of(context).birthCount3,
              ),
              _buildDropdownMenuItem(
                '4',
                AppLocalizations.of(context).birthCount4,
              ),
              _buildDropdownMenuItem(
                '5',
                AppLocalizations.of(context).birthCount5Plus,
              ),
            ],
            onChanged: (value) {
              setState(() {
                _birthCount = int.tryParse(value ?? '0') ?? 0;
              });
            },
          ),
        ],
      ],
    );
  }

  /// 创建带溢出保护的下拉框选项
  DropdownMenuItem<String> _buildDropdownMenuItem(String value, String text) {
    return DropdownMenuItem(
      value: value,
      child: Text(
        text,
        style: TextStyleUtil.getCustomStyle(
          context: context,
          baseFontSize: 14,
          color: AppColors.textPrimary(context),
          text: text,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 2,
      ),
    );
  }
}

/// 头像裁剪页面（简化版）
class AvatarCropScreen extends StatefulWidget {
  final File imageFile;

  const AvatarCropScreen({super.key, required this.imageFile});

  @override
  State<AvatarCropScreen> createState() => _AvatarCropScreenState();
}

class _AvatarCropScreenState extends State<AvatarCropScreen> {
  bool _isProcessing = false;

  // 裁剪框的位置和大小（方形头像）
  double _cropLeft = 50.0;
  double _cropTop = 100.0;
  double _cropSize = 200.0; // 方形裁剪框

  // 图片显示区域的大小
  double _imageDisplayWidth = 0;
  double _imageDisplayHeight = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background(context),
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).cropAvatar),
        backgroundColor: AppColors.cardBackground(context),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // 图片裁剪区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildImageWithCropOverlay(),
              ),
            ),
          ),

          // 底部操作区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // 重新选择按钮
                ElevatedButton.icon(
                  onPressed: _isProcessing
                      ? null
                      : () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.refresh, size: 20),
                  label: Text(AppLocalizations.of(context).reselectImage),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.cardBackground(context),
                    foregroundColor: AppColors.textPrimary(context),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                ),

                // 确认按钮
                ElevatedButton.icon(
                  onPressed: _isProcessing ? null : _confirmCrop,
                  icon: _isProcessing
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(Icons.check, size: 20),
                  label: Text(
                    _isProcessing
                        ? AppLocalizations.of(context).processing
                        : AppLocalizations.of(context).confirmCrop,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageWithCropOverlay() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 更新图片显示区域的大小
        _imageDisplayWidth = constraints.maxWidth;
        _imageDisplayHeight = constraints.maxHeight;

        // 确保裁剪框在有效范围内
        _validateCropBounds();

        return Stack(
          children: [
            // 背景图片
            SizedBox(
              width: constraints.maxWidth,
              height: constraints.maxHeight,
              child: Image.file(
                widget.imageFile,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[300],
                    child: const Center(child: Icon(Icons.error, size: 64)),
                  );
                },
              ),
            ),

            // 暗色遮罩层，中间透明显示原图
            CustomPaint(
              size: Size(constraints.maxWidth, constraints.maxHeight),
              painter: _CropOverlayPainter(
                cropRect: Rect.fromLTWH(
                  _cropLeft,
                  _cropTop,
                  _cropSize,
                  _cropSize,
                ),
                overlayColor: Colors.black.withValues(alpha: 0.5),
              ),
            ),

            // 可拖拽的裁剪框
            Positioned(left: _cropLeft, top: _cropTop, child: _buildCropBox()),
          ],
        );
      },
    );
  }

  Widget _buildCropBox() {
    return SizedBox(
      width: _cropSize,
      height: _cropSize,
      child: Stack(
        children: [
          // 裁剪框边框
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle, // 圆形头像框
                border: Border.all(color: Colors.white, width: 2.0),
              ),
            ),
          ),

          // 中心拖动区域
          Positioned(
            left: 25,
            top: 25,
            right: 25,
            bottom: 25,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onPanUpdate: (details) {
                setState(() {
                  // 移动裁剪框，但保持在边界内
                  _cropLeft = (_cropLeft + details.delta.dx).clamp(
                    0.0,
                    _imageDisplayWidth - _cropSize,
                  );
                  _cropTop = (_cropTop + details.delta.dy).clamp(
                    0.0,
                    _imageDisplayHeight - _cropSize,
                  );
                });
              },
              child: Container(color: Colors.transparent),
            ),
          ),

          // 四个角的控制点（用于调整裁剪框大小）
          _buildCornerHandle(Alignment.topLeft),
          _buildCornerHandle(Alignment.topRight),
          _buildCornerHandle(Alignment.bottomLeft),
          _buildCornerHandle(Alignment.bottomRight),
        ],
      ),
    );
  }

  Widget _buildCornerHandle(Alignment alignment) {
    return Align(
      alignment: alignment,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            final dx = details.delta.dx;
            final dy = details.delta.dy;

            // 计算新的尺寸（保持正方形）
            double delta = 0;

            if (alignment == Alignment.topLeft) {
              delta = -((dx + dy) / 2);
            } else if (alignment == Alignment.topRight) {
              delta = ((dx - dy) / 2);
            } else if (alignment == Alignment.bottomLeft) {
              delta = ((-dx + dy) / 2);
            } else if (alignment == Alignment.bottomRight) {
              delta = ((dx + dy) / 2);
            }

            _resizeCropBox(delta);
          });
        },
        child: Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(Icons.drag_indicator, size: 16, color: AppColors.primary),
        ),
      ),
    );
  }

  void _resizeCropBox(double delta) {
    // 最小和最大尺寸限制
    const double minSize = 50.0;
    final double maxSize =
        (_imageDisplayWidth < _imageDisplayHeight
            ? _imageDisplayWidth
            : _imageDisplayHeight) -
        20;

    // 计算新的尺寸
    double newSize = _cropSize + delta;
    newSize = newSize.clamp(minSize, maxSize);

    // 调整位置以保持裁剪框在图片内
    double newLeft = _cropLeft;
    double newTop = _cropTop;

    if (newLeft + newSize > _imageDisplayWidth) {
      newLeft = _imageDisplayWidth - newSize;
    }
    if (newTop + newSize > _imageDisplayHeight) {
      newTop = _imageDisplayHeight - newSize;
    }

    _cropLeft = newLeft.clamp(0, _imageDisplayWidth - newSize);
    _cropTop = newTop.clamp(0, _imageDisplayHeight - newSize);
    _cropSize = newSize;
  }

  void _validateCropBounds() {
    if (_imageDisplayWidth > 0 && _imageDisplayHeight > 0) {
      final maxSize =
          (_imageDisplayWidth < _imageDisplayHeight
              ? _imageDisplayWidth
              : _imageDisplayHeight) -
          20;

      _cropSize = _cropSize.clamp(50.0, maxSize);
      _cropLeft = _cropLeft.clamp(0.0, _imageDisplayWidth - _cropSize);
      _cropTop = _cropTop.clamp(0.0, _imageDisplayHeight - _cropSize);
    }
  }

  Future<void> _confirmCrop() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // 在调试模式下打印原始文件信息
      assert(() {
        // ignore: avoid_print
        print('原始文件路径: ${widget.imageFile.path}');
        final fileName = widget.imageFile.path.split('/').last;
        // ignore: avoid_print
        print('原始文件名: $fileName');
        final extension = fileName.contains('.')
            ? fileName.substring(fileName.lastIndexOf('.'))
            : '';
        // ignore: avoid_print
        print('原始文件扩展名: $extension');
        return true;
      }());

      // 执行实际的图片裁剪
      final croppedFile = await _cropImage();

      if (!mounted) return;
      Navigator.of(context).pop(croppedFile);
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).processImageFailed(e.toString()),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// 执行图片裁剪
  Future<File> _cropImage() async {
    // 读取原始图片
    final originalBytes = await widget.imageFile.readAsBytes();
    final originalImage = img.decodeImage(originalBytes);

    if (originalImage == null) {
      throw Exception('Cannot parse image file');
    }

    // 计算实际的裁剪区域（相对于原始图片）
    final scaleX = originalImage.width / _imageDisplayWidth;
    final scaleY = originalImage.height / _imageDisplayHeight;

    final cropX = (_cropLeft * scaleX).round();
    final cropY = (_cropTop * scaleY).round();
    final cropWidth = (_cropSize * scaleX).round();
    final cropHeight = (_cropSize * scaleY).round();

    // 在调试模式下打印裁剪信息
    assert(() {
      // ignore: avoid_print
      print('原始图片尺寸: ${originalImage.width} x ${originalImage.height}');
      // ignore: avoid_print
      print('显示区域尺寸: $_imageDisplayWidth x $_imageDisplayHeight');
      // ignore: avoid_print
      print('缩放比例: scaleX=$scaleX, scaleY=$scaleY');
      // ignore: avoid_print
      print('裁剪区域(UI): left=$_cropLeft, top=$_cropTop, size=$_cropSize');
      // ignore: avoid_print
      print(
        '裁剪区域(实际): x=$cropX, y=$cropY, width=$cropWidth, height=$cropHeight',
      );
      return true;
    }());

    // 确保裁剪区域在图片边界内
    final actualCropX = cropX.clamp(0, originalImage.width);
    final actualCropY = cropY.clamp(0, originalImage.height);
    final actualCropWidth = (cropWidth).clamp(
      1,
      originalImage.width - actualCropX,
    );
    final actualCropHeight = (cropHeight).clamp(
      1,
      originalImage.height - actualCropY,
    );

    // 裁剪图片
    final croppedImage = img.copyCrop(
      originalImage,
      x: actualCropX,
      y: actualCropY,
      width: actualCropWidth,
      height: actualCropHeight,
    );

    // 如果需要，调整为正方形（头像通常是正方形的）
    final minSize = croppedImage.width < croppedImage.height
        ? croppedImage.width
        : croppedImage.height;

    final squareImage = img.copyCrop(
      croppedImage,
      x: (croppedImage.width - minSize) ~/ 2,
      y: (croppedImage.height - minSize) ~/ 2,
      width: minSize,
      height: minSize,
    );

    // 调整大小到合适的头像尺寸（例如512x512）
    const targetSize = 512;
    final resizedImage = img.copyResize(
      squareImage,
      width: targetSize,
      height: targetSize,
      interpolation: img.Interpolation.cubic,
    );

    // 编码为JPEG格式
    final croppedBytes = img.encodeJpg(resizedImage, quality: 85);

    // 创建临时文件保存裁剪后的图片
    final tempDir = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final croppedFilePath = '${tempDir.path}/cropped_avatar_$timestamp.jpg';
    final croppedFile = File(croppedFilePath);

    await croppedFile.writeAsBytes(croppedBytes);

    assert(() {
      // ignore: avoid_print
      print('裁剪完成，保存到: $croppedFilePath');
      // ignore: avoid_print
      print('裁剪后图片尺寸: ${resizedImage.width} x ${resizedImage.height}');
      // ignore: avoid_print
      print('裁剪后文件大小: ${croppedBytes.length} bytes');
      return true;
    }());

    return croppedFile;
  }
}

/// 自定义绘制器，绘制圆形裁剪区域的遮罩
class _CropOverlayPainter extends CustomPainter {
  final Rect cropRect;
  final Color overlayColor;

  _CropOverlayPainter({required this.cropRect, required this.overlayColor});

  @override
  void paint(Canvas canvas, Size size) {
    final overlayPaint = Paint()..color = overlayColor;
    final clearPaint = Paint()..blendMode = BlendMode.clear;

    // 保存画布状态
    canvas.saveLayer(Offset.zero & size, Paint());

    // 首先绘制完整的遮罩
    canvas.drawRect(Offset.zero & size, overlayPaint);

    // 然后清除圆形剪裁区域，让底层图片显示出来
    canvas.drawOval(cropRect, clearPaint);

    // 恢复画布状态
    canvas.restore();
  }

  @override
  bool shouldRepaint(_CropOverlayPainter oldDelegate) {
    return cropRect != oldDelegate.cropRect ||
        overlayColor != oldDelegate.overlayColor;
  }
}
