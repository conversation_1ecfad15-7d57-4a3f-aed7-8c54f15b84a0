import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../services/user_profile_service.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 修改密码页面 - 支持国际化和响应式布局
class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _profileService = UserProfileService();

  bool _isLoading = false;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final maxWidth = isTablet ? 600.0 : double.infinity;

    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).changePassword,
          style: FontUtil.createAppBarTitleStyle(context: context),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back_ios,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
      ),
      body: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: maxWidth),
          child: SingleChildScrollView(
            padding: EdgeInsets.all(isTablet ? 32 : 16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // 页面说明
                  _buildDescription(context),
                  const SizedBox(height: 32),

                  // 新密码输入框
                  _buildPasswordField(
                    controller: _newPasswordController,
                    label: AppLocalizations.of(context).newPassword,
                    hintText: AppLocalizations.of(context).newPasswordHint,
                    obscureText: _obscureNewPassword,
                    onToggleVisibility: () {
                      setState(() {
                        _obscureNewPassword = !_obscureNewPassword;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppLocalizations.of(context).newPasswordRequired;
                      }
                      if (value.length < 6) {
                        return AppLocalizations.of(context).passwordMinLength;
                      }
                      if (value.length > 20) {
                        return AppLocalizations.of(
                          context,
                        ).newPasswordMaxLength;
                      }

                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  // 确认新密码输入框
                  _buildPasswordField(
                    controller: _confirmPasswordController,
                    label: AppLocalizations.of(context).confirmNewPassword,
                    hintText: AppLocalizations.of(context).confirmPasswordHint,
                    obscureText: _obscureConfirmPassword,
                    onToggleVisibility: () {
                      setState(() {
                        _obscureConfirmPassword = !_obscureConfirmPassword;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return AppLocalizations.of(
                          context,
                        ).confirmNewPasswordRequired;
                      }
                      if (value != _newPasswordController.text) {
                        return AppLocalizations.of(context).passwordsDoNotMatch;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),

                  // 提交按钮
                  _buildSubmitButton(context),
                  const SizedBox(height: 16),

                  // 密码要求说明
                  _buildPasswordRequirements(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建页面说明
  Widget _buildDescription(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: AppColors.primary, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              AppLocalizations.of(context).changePasswordDescription,
              style: FontUtil.createBodyTextStyle(
                text: AppLocalizations.of(context).changePasswordDescription,
                fontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建密码输入框
  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required String hintText,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    required String? Function(String?) validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontUtil.createBodyTextStyle(
            text: label,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          validator: validator,
          style: FontUtil.createBodyTextStyle(
            text: '',
            fontSize: 16,
            color: ThemeHelper.getTextPrimary(context),
          ),
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: FontUtil.createBodyTextStyle(
              text: hintText,
              fontSize: 16,
              color: ThemeHelper.getTextHint(context),
            ),
            filled: true,
            fillColor: ThemeHelper.getCardBackground(context),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: ThemeHelper.getBorder(context),
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: ThemeHelper.getBorder(context),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 1),
            ),
            suffixIcon: IconButton(
              onPressed: onToggleVisibility,
              icon: Icon(
                obscureText ? Icons.visibility_off : Icons.visibility,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建提交按钮
  Widget _buildSubmitButton(BuildContext context) {
    return SizedBox(
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleSubmit,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          disabledBackgroundColor: AppColors.primary.withValues(alpha: 0.5),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                AppLocalizations.of(context).confirm,
                style: FontUtil.createButtonTextStyle(
                  text: AppLocalizations.of(context).confirm,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  /// 构建密码要求说明
  Widget _buildPasswordRequirements(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ThemeHelper.getBorder(context), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).passwordRequirements,
            style: FontUtil.createBodyTextStyle(
              text: AppLocalizations.of(context).passwordRequirements,
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 8),
          _buildRequirementItem(
            AppLocalizations.of(context).passwordLengthRequirement,
          ),
        ],
      ),
    );
  }

  /// 构建要求项
  Widget _buildRequirementItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: ThemeHelper.getTextSecondary(context),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: FontUtil.createBodyTextStyle(
                text: text,
                fontSize: 12,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 处理提交
  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _profileService.changePassword(
        newPassword: _newPasswordController.text,
        confirmPassword: _confirmPasswordController.text,
      );

      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).passwordChangeSuccess,
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).operationFailed}: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
