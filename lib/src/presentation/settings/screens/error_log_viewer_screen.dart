import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../services/error_logger_service.dart';

/// 错误日志查看器页面（开发调试用）
class ErrorLogViewerScreen extends StatefulWidget {
  const ErrorLogViewerScreen({super.key});

  @override
  State<ErrorLogViewerScreen> createState() => _ErrorLogViewerScreenState();
}

class _ErrorLogViewerScreenState extends State<ErrorLogViewerScreen> {
  final ErrorLoggerService _errorLogger = ErrorLoggerService();
  String _selectedSeverity = 'all';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          '错误日志查看器',
          style: TextStyleUtil.getAppBarTitleStyle(context),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back_ios,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showClearDialog,
            icon: Icon(Icons.delete_outline, color: AppColors.error),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildHeader(),
          _buildFilterRow(),
          Expanded(child: _buildLogList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final stats = _errorLogger.getLogStatistics();

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '日志统计',
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('总计', stats['total']!, AppColors.primary),
              _buildStatItem('关键', stats['critical']!, AppColors.error),
              _buildStatItem('错误', stats['error']!, AppColors.warning),
              _buildStatItem('警告', stats['warning']!, AppColors.info),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count, Color color) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 12,
            color: ThemeHelper.getTextSecondary(context),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterRow() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Text(
            '过滤：',
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('all', '全部'),
                  _buildFilterChip('critical', '关键'),
                  _buildFilterChip('error', '错误'),
                  _buildFilterChip('warning', '警告'),
                  _buildFilterChip('info', '信息'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedSeverity == value;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedSeverity = value;
          });
        },
        backgroundColor: ThemeHelper.getCardBackground(context),
        selectedColor: AppColors.primary.withValues(alpha: 0.2),
        checkmarkColor: AppColors.primary,
        labelStyle: TextStyleUtil.getCustomStyle(
          context: context,
          baseFontSize: 12,
          color: isSelected
              ? AppColors.primary
              : ThemeHelper.getTextSecondary(context),
        ),
      ),
    );
  }

  Widget _buildLogList() {
    final logs = _selectedSeverity == 'all'
        ? _errorLogger.getAllLogs()
        : _errorLogger.getLogsBySeverity(_selectedSeverity);

    if (logs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              size: 64,
              color: ThemeHelper.getTextSecondary(context),
            ),
            const SizedBox(height: 16),
            Text(
              '暂无日志记录',
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 16,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: logs.length,
      itemBuilder: (context, index) {
        final log = logs.reversed.toList()[index];
        return _buildLogItem(log);
      },
    );
  }

  Widget _buildLogItem(ErrorLogEntry log) {
    Color severityColor;
    IconData severityIcon;

    switch (log.severity) {
      case 'critical':
        severityColor = AppColors.error;
        severityIcon = Icons.error;
        break;
      case 'error':
        severityColor = AppColors.warning;
        severityIcon = Icons.warning;
        break;
      case 'warning':
        severityColor = AppColors.info;
        severityIcon = Icons.info;
        break;
      default:
        severityColor = AppColors.success;
        severityIcon = Icons.check_circle;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: severityColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: ExpansionTile(
        leading: Icon(severityIcon, color: severityColor, size: 20),
        title: Text(
          log.errorType,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              log.message,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 12,
                color: ThemeHelper.getTextSecondary(context),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(log.timestamp),
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 10,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('时间', log.timestamp.toIso8601String()),
                _buildDetailRow('类型', log.errorType),
                _buildDetailRow('严重程度', log.severity),
                _buildDetailRow('消息', log.message),

                if (log.context != null && log.context!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    '上下文信息:',
                    style: TextStyleUtil.getCustomStyle(
                      context: context,
                      baseFontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: ThemeHelper.getBackground(context),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      log.context.toString(),
                      style: TextStyle(
                        fontSize: 11,
                        fontFamily: 'monospace',
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                    ),
                  ),
                ],

                if (log.stackTrace != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    '堆栈追踪:',
                    style: TextStyleUtil.getCustomStyle(
                      context: context,
                      baseFontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: ThemeHelper.getBackground(context),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      log.stackTrace!,
                      style: TextStyle(
                        fontSize: 10,
                        fontFamily: 'monospace',
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 12,
                fontWeight: FontWeight.w500,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 12,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inMinutes < 1) {
      return '刚才';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}分钟前';
    } else if (diff.inDays < 1) {
      return '${diff.inHours}小时前';
    } else {
      return '${diff.inDays}天前';
    }
  }

  void _showClearDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          '清除日志',
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          '确定要清除所有错误日志吗？此操作不可撤销。',
          style: TextStyleUtil.getBodyStyle(context),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              '取消',
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop(); // 先关闭对话框
              await _errorLogger.clearAllLogs();
              if (mounted) {
                setState(() {});
              }
            },
            child: Text(
              '确定',
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
