import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:io';
import 'dart:convert';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../config/api/api_config.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_info_manager_service.dart';
import '../../../services/secure_storage_service.dart';
import '../../../services/error_logger_service.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 帮助与反馈页面
class HelpFeedbackScreen extends StatefulWidget {
  const HelpFeedbackScreen({super.key});

  @override
  State<HelpFeedbackScreen> createState() => _HelpFeedbackScreenState();
}

class _HelpFeedbackScreenState extends State<HelpFeedbackScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _descriptionController = TextEditingController();

  bool _isSubmitting = false;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  /// 验证手机号格式
  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.of(context).pleaseEnterPhoneNumber;
    }

    // 中国大陆手机号正则表达式
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    if (!phoneRegex.hasMatch(value)) {
      return AppLocalizations.of(context).pleaseEnterCorrectPhoneFormat;
    }

    return null;
  }

  /// 验证问题描述
  String? _validateDescription(String? value) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.of(context).pleaseDescribeProblem;
    }

    if (value.length < 10) {
      return AppLocalizations.of(context).descriptionMinLength;
    }

    if (value.length > 1000) {
      return AppLocalizations.of(context).descriptionMaxLength;
    }

    return null;
  }

  /// 收集应用信息
  Future<Map<String, dynamic>> _collectAppInfo() async {
    return {
      'appVersion': '1.0.0+1', // 可以从pubspec.yaml读取或硬编码
      'appName': '民汉翻译',
      'flutterVersion': Platform.version,
    };
  }

  /// 收集用户数据
  Future<Map<String, dynamic>> _collectUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final authService = AuthService();
    final userInfoManager = UserInfoManagerService();
    final secureStorage = SecureStorageService();

    // 获取当前用户信息
    final currentUser = authService.currentUser;
    final userProfile = userInfoManager.currentUserProfile;

    // 尝试从安全存储获取更多信息
    DateTime? loginTime;
    DateTime? tokenExpiry;
    try {
      loginTime = await secureStorage.getLoginTime();
      tokenExpiry = await secureStorage.getTokenExpiryTime();
    } catch (e) {
      debugPrint('获取登录时间信息失败: $e');
    }

    return {
      // 用户基本信息
      'isLoggedIn': authService.isLoggedIn,
      'userId': currentUser?.id.toString() ?? '未登录',
      'username': currentUser?.username ?? '无',
      'phone': currentUser?.phone ?? '无',
      'hasToken': (currentUser?.token ?? '').isNotEmpty,
      'tokenPrefix': _getTokenPrefix(currentUser?.token),

      // 用户详细资料信息
      'nickname': userProfile?.nickname ?? '无',
      'profileId': userProfile?.id.toString() ?? '无',
      'isVip': userProfile?.vip ?? false,
      'vipExpire': userProfile?.vipData?.endTime ?? '无',
      'money': userProfile?.money.toString() ?? '0',
      'integral': userProfile?.integral.toString() ?? '0',
      'isReferrer': userProfile?.isReferrer ?? false,
      'referrerLevel': userProfile?.referrerLevel.toString() ?? '0',
      'freeTCount': userProfile?.freeTCount.toString() ?? '0',
      'auth': userProfile?.auth ?? false,

      // 登录时间信息
      'loginTime': loginTime?.toIso8601String() ?? '无',
      'tokenExpiry': tokenExpiry?.toIso8601String() ?? '无',

      // 应用设置
      'themeMode': prefs.getString('theme_mode') ?? 'system',
      'fontSize': prefs.getDouble('font_size_scale') ?? 1.0,
      'language': prefs.getString('app_language') ?? 'zh',
    };
  }

  /// 收集设备信息
  Map<String, dynamic> _collectDeviceInfo() {
    return {
      'platform': Platform.operatingSystem,
      'platformVersion': Platform.operatingSystemVersion,
      'locale': Platform.localeName,
      'numberOfProcessors': Platform.numberOfProcessors,
    };
  }

  /// 收集运行时信息
  Future<Map<String, dynamic>> _collectRuntimeInfo() async {
    final authService = AuthService();
    final userInfoManager = UserInfoManagerService();
    final errorLogger = ErrorLoggerService();

    // 获取错误日志统计信息
    final errorStats = errorLogger.getLogStatistics();
    final recentErrors = errorLogger.getRecentErrors(limit: 5);

    return {
      'appStartTime': '应用启动时间信息需要在应用启动时记录',
      'memoryUsage': '内存使用情况需要平台插件支持',
      'userServiceStatus': userInfoManager.hasLocalUserData ? '有本地数据' : '无本地数据',
      'authServiceStatus': authService.isLoggedIn ? '已登录' : '未登录',
      'themeServiceStatus': '主题服务正常',
      'errorLoggerStatus': errorLogger.isInitialized ? '已初始化' : '未初始化',
      'totalErrors': errorStats['total'] ?? 0,
      'criticalErrors': errorStats['critical'] ?? 0,
      'errors': errorStats['error'] ?? 0,
      'warnings': errorStats['warning'] ?? 0,
      'recentErrors': recentErrors.isEmpty
          ? '暂无错误记录'
          : recentErrors.map((e) => e.toString()).join('\n'),
    };
  }

  /// 获取token前缀（安全处理）
  String _getTokenPrefix(String? token) {
    if (token == null || token.isEmpty) {
      return 'null';
    }
    final length = token.length > 20 ? 20 : token.length;
    return token.substring(0, length);
  }

  /// 生成日志文件
  Future<File> _generateLogFile() async {
    final timestamp = DateTime.now().toIso8601String();
    final appInfo = await _collectAppInfo();
    final userData = await _collectUserData();
    final deviceInfo = _collectDeviceInfo();

    final logContent = StringBuffer();

    // 用户提交信息
    logContent.writeln('======== 用户提交信息 ========');
    logContent.writeln('提交时间: $timestamp');
    logContent.writeln(
      '用户称呼: ${_nameController.text.isEmpty ? "未填写" : _nameController.text}',
    );
    logContent.writeln('联系手机: ${_phoneController.text}');
    logContent.writeln('问题描述:');
    logContent.writeln(_descriptionController.text);
    logContent.writeln();

    // 应用基本信息
    logContent.writeln('======== 应用基本信息 ========');
    logContent.writeln('应用名称: ${appInfo['appName']}');
    logContent.writeln('应用版本: ${appInfo['appVersion']}');
    logContent.writeln('Flutter版本: ${appInfo['flutterVersion']}');
    logContent.writeln();

    // 设备信息
    logContent.writeln('======== 设备信息 ========');
    logContent.writeln('操作系统: ${deviceInfo['platform']}');
    logContent.writeln('系统版本: ${deviceInfo['platformVersion']}');
    logContent.writeln('本地化设置: ${deviceInfo['locale']}');
    logContent.writeln('处理器数量: ${deviceInfo['numberOfProcessors']}');
    logContent.writeln();

    // 用户持久化数据
    logContent.writeln('======== 用户持久化数据 ========');
    logContent.writeln('登录状态: ${userData['isLoggedIn']}');
    logContent.writeln('用户ID: ${userData['userId']}');
    logContent.writeln('用户名: ${userData['username']}');
    logContent.writeln('昵称: ${userData['nickname']}');
    logContent.writeln('手机号: ${userData['phone']}');
    logContent.writeln('是否有Token: ${userData['hasToken']}');
    logContent.writeln('Token前缀: ${userData['tokenPrefix']}...');
    logContent.writeln('登录时间: ${userData['loginTime']}');
    logContent.writeln('Token过期时间: ${userData['tokenExpiry']}');
    logContent.writeln();

    logContent.writeln('======== VIP和分销信息 ========');
    logContent.writeln('VIP状态: ${userData['isVip']}');
    logContent.writeln('VIP到期时间: ${userData['vipExpire']}');
    logContent.writeln('账户余额: ${userData['money']}');
    logContent.writeln('积分: ${userData['integral']}');
    logContent.writeln('是否分销员: ${userData['isReferrer']}');
    logContent.writeln('分销等级: ${userData['referrerLevel']}');
    logContent.writeln('免费翻译次数: ${userData['freeTCount']}');
    logContent.writeln('资料完善状态: ${userData['auth']}');
    logContent.writeln();

    logContent.writeln('======== 应用设置 ========');
    logContent.writeln('主题模式: ${userData['themeMode']}');
    logContent.writeln('字体缩放: ${userData['fontSize']}');
    logContent.writeln('应用语言: ${userData['language']}');
    logContent.writeln();

    // 应用运行时日志
    logContent.writeln('======== 应用运行时日志 ========');
    logContent.writeln('日志生成时间: $timestamp');

    // 获取运行时状态信息
    final runtimeInfo = await _collectRuntimeInfo();
    logContent.writeln('应用启动时间: ${runtimeInfo['appStartTime']}');
    logContent.writeln('内存使用情况: ${runtimeInfo['memoryUsage']}');
    logContent.writeln('用户信息服务状态: ${runtimeInfo['userServiceStatus']}');
    logContent.writeln('认证服务状态: ${runtimeInfo['authServiceStatus']}');
    logContent.writeln('主题服务状态: ${runtimeInfo['themeServiceStatus']}');
    logContent.writeln('错误日志服务状态: ${runtimeInfo['errorLoggerStatus']}');
    logContent.writeln();

    logContent.writeln('======== 错误统计信息 ========');
    logContent.writeln('总错误数: ${runtimeInfo['totalErrors']}');
    logContent.writeln('关键错误: ${runtimeInfo['criticalErrors']}');
    logContent.writeln('一般错误: ${runtimeInfo['errors']}');
    logContent.writeln('警告: ${runtimeInfo['warnings']}');
    logContent.writeln();

    logContent.writeln('======== 最近错误记录 ========');
    logContent.writeln(runtimeInfo['recentErrors']);
    logContent.writeln();

    // 应用功能使用状态
    logContent.writeln('======== 应用功能状态 ========');
    logContent.writeln('翻译功能: 可用');
    logContent.writeln('语音功能: 可用');
    logContent.writeln('相机功能: 可用');
    logContent.writeln('历史记录: 可用');
    logContent.writeln('设置页面: 可用');
    logContent.writeln('AI导游: 可用');
    logContent.writeln();

    // 详细错误日志
    logContent.writeln('======== 详细错误日志 ========');
    final errorLogger = ErrorLoggerService();
    final detailedErrors = errorLogger.exportLogsAsString(limit: 20);
    logContent.writeln(detailedErrors);

    logContent.writeln('======== 结束 ========');

    // 写入文件
    final tempDir = await getTemporaryDirectory();
    final logFile = File(
      '${tempDir.path}/app_feedback_${DateTime.now().millisecondsSinceEpoch}.log',
    );
    await logFile.writeAsString(logContent.toString());

    return logFile;
  }

  /// 提交反馈
  Future<void> _submitFeedback() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // 生成日志文件
      final logFile = await _generateLogFile();

      // 检查文件大小 (10MB限制)
      final fileSize = await logFile.length();
      if (fileSize > 10 * 1024 * 1024) {
        throw Exception('日志文件过大，请尝试精简问题描述或联系客服');
      }

      // 构造multipart请求
      final uri = Uri.parse('${ApiConfig.baseUrl}/applet/v1/app/feedback');
      final request = http.MultipartRequest('POST', uri);

      // 添加手机号字段
      request.fields['phone'] = _phoneController.text.trim();

      // 添加日志文件
      final multipartFile = await http.MultipartFile.fromPath(
        'log_file',
        logFile.path,
        filename: 'app_feedback.log',
      );
      request.files.add(multipartFile);

      // 发送请求
      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      // 清理临时文件
      if (await logFile.exists()) {
        await logFile.delete();
      }

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(responseBody);
        if (jsonResponse['code'] == 200) {
          // 成功
          await ErrorLoggerService().logInfo(
            'FeedbackSubmit',
            '用户反馈提交成功',
            context: {
              'phone': _phoneController.text,
              'description_length': _descriptionController.text.length,
            },
          );

          if (mounted) {
            ToastUtil.show(
              context,
              AppLocalizations.of(context).feedbackSubmittedSuccessfully,
            );
            // 清空表单
            _nameController.clear();
            _phoneController.clear();
            _descriptionController.clear();
            Navigator.of(context).pop();
          }
        } else {
          // 业务错误
          await ErrorLoggerService().logWarning(
            'FeedbackSubmitFailed',
            '反馈提交业务错误: ${jsonResponse['message']}',
          );

          if (mounted) {
            ToastUtil.show(
              context,
              jsonResponse['message'] ??
                  AppLocalizations.of(context).submissionFailed,
            );
          }
        }
      } else {
        // HTTP错误
        final jsonResponse = json.decode(responseBody);
        await ErrorLoggerService().logError(
          'FeedbackSubmitHttpError',
          'HTTP错误: ${response.statusCode}',
          context: {
            'statusCode': response.statusCode,
            'responseBody': responseBody,
          },
        );

        if (mounted) {
          ToastUtil.show(
            context,
            jsonResponse['message'] ??
                AppLocalizations.of(context).networkRequestFailed,
          );
        }
      }
    } catch (e) {
      await ErrorLoggerService().logError(
        'FeedbackSubmitException',
        '反馈提交异常',
        stackTrace: e.toString(),
      );

      debugPrint('提交反馈失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          e.toString().contains('日志文件过大')
              ? e.toString()
              : AppLocalizations.of(context).submissionFailedCheckNetwork,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).helpFeedbackTitle,
          style: TextStyleUtil.getAppBarTitleStyle(context),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back_ios,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 说明文本
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: AppColors.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          AppLocalizations.of(context).feedbackInstructions,
                          style: TextStyleUtil.getCustomStyle(
                            context: context,
                            baseFontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      AppLocalizations.of(context).feedbackInstructionsText,
                      style: TextStyleUtil.getCustomStyle(
                        context: context,
                        baseFontSize: 14,
                        color: ThemeHelper.getTextSecondary(context),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // 用户称呼输入框
              _buildInputField(
                controller: _nameController,
                label: AppLocalizations.of(context).yourNameOptional,
                hint: AppLocalizations.of(context).enterYourName,
                icon: Icons.person_outline,
                validator: null, // 可选字段，不验证
              ),

              const SizedBox(height: 16),

              // 手机号输入框
              _buildInputField(
                controller: _phoneController,
                label:
                    '${AppLocalizations.of(context).yourPhoneNumber} ${AppLocalizations.of(context).phoneNumberForContact}',
                hint: AppLocalizations.of(context).enterPhoneNumber,
                icon: Icons.phone_outlined,
                keyboardType: TextInputType.phone,
                validator: _validatePhone,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(11),
                ],
              ),

              const SizedBox(height: 16),

              // 问题描述输入框
              _buildMultilineInputField(
                controller: _descriptionController,
                label: AppLocalizations.of(context).describeProblemDetail,
                hint: AppLocalizations.of(context).problemDescriptionHint,
                icon: Icons.description_outlined,
                validator: _validateDescription,
              ),

              const SizedBox(height: 32),

              // 提交按钮
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitFeedback,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    disabledBackgroundColor: AppColors.primary.withValues(
                      alpha: 0.3,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: _isSubmitting
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white.withValues(alpha: 0.8),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              AppLocalizations.of(context).submitting,
                              style: TextStyleUtil.getCustomStyle(
                                context: context,
                                baseFontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white.withValues(alpha: 0.8),
                              ),
                            ),
                          ],
                        )
                      : Text(
                          AppLocalizations.of(context).submitFeedback,
                          style: TextStyleUtil.getCustomStyle(
                            context: context,
                            baseFontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                ),
              ),

              const SizedBox(height: 16),

              // 隐私说明
              Text(
                AppLocalizations.of(context).privacyNotice,
                style: TextStyleUtil.getCustomStyle(
                  context: context,
                  baseFontSize: 12,
                  color: ThemeHelper.getTextHint(context),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建单行输入框
  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 14,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          validator: validator,
          inputFormatters: inputFormatters,
          style: TextStyleUtil.getBodyStyle(context),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 14,
              color: ThemeHelper.getTextHint(context),
            ),
            prefixIcon: Icon(
              icon,
              color: ThemeHelper.getTextSecondary(context),
            ),
            filled: true,
            fillColor: ThemeHelper.getCardBackground(context),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: ThemeHelper.getDivider(context),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.error, width: 1),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建多行输入框
  Widget _buildMultilineInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyleUtil.getCustomStyle(
            context: context,
            baseFontSize: 14,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: 6,
          minLines: 4,
          maxLength: 1000,
          style: TextStyleUtil.getBodyStyle(context),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 14,
              color: ThemeHelper.getTextHint(context),
            ),
            prefixIcon: Padding(
              padding: const EdgeInsets.only(bottom: 80),
              child: Icon(icon, color: ThemeHelper.getTextSecondary(context)),
            ),
            filled: true,
            fillColor: ThemeHelper.getCardBackground(context),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: ThemeHelper.getDivider(context),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.error, width: 1),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }
}
