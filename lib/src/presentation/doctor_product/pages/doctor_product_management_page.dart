import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/doctor_product_cache_service.dart';
import '../../../services/doctor_product_service.dart';
import '../../../utils/toast_util.dart';
import '../widgets/product_statistics_card.dart';
import '../widgets/product_list_item.dart';
import 'product_edit_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 医生产品管理页面
class DoctorProductManagementPage extends StatefulWidget {
  const DoctorProductManagementPage({super.key});

  @override
  State<DoctorProductManagementPage> createState() =>
      _DoctorProductManagementPageState();
}

/// 产品管理内容组件（可复用）
class DoctorProductManagementContent extends StatefulWidget {
  const DoctorProductManagementContent({super.key});

  @override
  State<DoctorProductManagementContent> createState() =>
      _DoctorProductManagementContentState();
}

class _DoctorProductManagementPageState
    extends State<DoctorProductManagementPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      body: const DoctorProductManagementContent(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        '产品管理',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ThemeHelper.getTextPrimary(context),
        ),
      ),
      backgroundColor: ThemeHelper.getCardBackground(context),
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: ThemeHelper.getTextPrimary(context),
        ),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }
}

class _DoctorProductManagementContentState
    extends State<DoctorProductManagementContent>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final DoctorProductCacheService _productCacheService =
      DoctorProductCacheService();

  List<DoctorProductModel> _allProducts = [];
  List<DoctorProductModel> _filteredProducts = [];
  ProductStatisticsModel? _statistics;
  bool _isLoading = true;
  int _currentStatusFilter = -1; // -1表示全部

  // 移除硬编码的状态标签列表，改为在build方法中动态获取

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this); // 固定为5个状态标签
    _tabController.addListener(_onTabChanged);
    _loadDataInstantly();
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) return;

    setState(() {
      _currentStatusFilter = _tabController.index == 0
          ? -1
          : _tabController.index - 1;
      _filterProducts();
    });
  }

  /// 立即加载产品数据（优先使用缓存，避免刷新）
  void _loadDataInstantly() {
    // 先尝试同步获取缓存数据
    final cachedProducts = _productCacheService.getCachedProductsSync();
    final cachedStatistics = _productCacheService.getCachedStatisticsSync();

    if (cachedProducts.isNotEmpty) {
      // 如果有缓存数据，立即显示
      setState(() {
        _allProducts = cachedProducts;
        _statistics = cachedStatistics;
        _filterProducts();
        _isLoading = false;
      });

      // 后台异步检查更新（不阻塞UI）
      _backgroundRefreshData();
    } else {
      // 如果没有缓存数据，显示加载状态并异步加载
      setState(() {
        _isLoading = true;
      });
      _loadDataAsync();
    }
  }

  /// 后台刷新产品数据（不阻塞UI）
  void _backgroundRefreshData() {
    // 异步后台刷新，不影响当前显示
    Future.microtask(() async {
      try {
        final products = await _productCacheService.getProducts();
        final statistics = await _productCacheService.getStatistics();

        if (mounted) {
          // 只有数据真正变化时才更新UI
          if (_hasProductDataChanged(products) ||
              _hasStatisticsChanged(statistics)) {
            setState(() {
              _allProducts = products;
              _statistics = statistics;
              _filterProducts();
            });
          }
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
      }
    });
  }

  /// 检查产品数据是否发生变化
  bool _hasProductDataChanged(List<DoctorProductModel> newProducts) {
    if (_allProducts.length != newProducts.length) return true;

    for (int i = 0; i < _allProducts.length; i++) {
      if (_allProducts[i].id != newProducts[i].id ||
          _allProducts[i].updatedAt != newProducts[i].updatedAt) {
        return true;
      }
    }

    return false;
  }

  /// 检查统计数据是否发生变化
  bool _hasStatisticsChanged(ProductStatisticsModel newStatistics) {
    if (_statistics == null) return true;

    return _statistics!.totalProducts != newStatistics.totalProducts ||
        _statistics!.pendingReview != newStatistics.pendingReview ||
        _statistics!.approvedProducts != newStatistics.approvedProducts ||
        _statistics!.rejectedProducts != newStatistics.rejectedProducts;
  }

  /// 异步加载产品数据（用于首次加载）
  Future<void> _loadDataAsync() async {
    try {
      final products = await _productCacheService.getProducts();
      final statistics = await _productCacheService.getStatistics();

      if (mounted) {
        setState(() {
          _allProducts = products;
          _statistics = statistics;
          _filterProducts();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(context, '加载数据失败: $e');
      }
    }
  }

  /// 加载数据
  Future<void> _loadData() async {
    try {
      print('DoctorProductManagementPage: 开始加载数据');
      setState(() {
        _isLoading = true;
      });

      print('DoctorProductManagementPage: 开始加载产品列表');
      // 分别加载产品列表和统计信息
      final products = await _productCacheService.getProducts();
      print('DoctorProductManagementPage: 产品列表加载完成，数量: ${products.length}');

      print('DoctorProductManagementPage: 开始加载统计信息');
      final statistics = await _productCacheService.getStatistics();
      print('DoctorProductManagementPage: 统计信息加载完成');

      if (mounted) {
        setState(() {
          _allProducts = products;
          _statistics = statistics;
          _filterProducts();
          _isLoading = false;
        });
        print('DoctorProductManagementPage: 数据加载完成并更新UI');
      }
    } catch (e, stackTrace) {
      print('DoctorProductManagementPage: 加载数据失败: $e');
      print('DoctorProductManagementPage: 堆栈跟踪: $stackTrace');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(context, '加载数据失败: $e');
      }
    }
  }

  /// 根据状态筛选产品
  void _filterProducts() {
    if (_currentStatusFilter == -1) {
      _filteredProducts = List.from(_allProducts);
    } else {
      _filteredProducts = _allProducts
          .where((product) => product.status == _currentStatusFilter)
          .toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      body: _isLoading ? _buildLoadingView() : _buildContent(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// 构建加载视图
  Widget _buildLoadingView() {
    return const Center(child: CircularProgressIndicator());
  }

  /// 构建主要内容
  Widget _buildContent() {
    return Column(
      children: [
        // 统计信息卡片
        if (_statistics != null)
          ProductStatisticsCard(statistics: _statistics!),

        // 状态筛选标签
        _buildStatusTabs(),

        // 产品列表
        Expanded(child: _buildProductList()),
      ],
    );
  }

  /// 构建状态筛选标签
  Widget _buildStatusTabs() {
    return Container(
      color: ThemeHelper.getCardBackground(context),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start, // 让标签从左边开始
        labelColor: AppColors.primary,
        unselectedLabelColor: ThemeHelper.getTextSecondary(context),
        indicatorColor: AppColors.primary,
        indicatorWeight: 2,
        indicatorSize: TabBarIndicatorSize.label, // 指示器大小跟随标签
        labelStyle: FontUtil.createTabLabelStyle(fontSize: 14),
        unselectedLabelStyle: FontUtil.createTabUnselectedLabelStyle(
          fontSize: 14,
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 16), // 调整标签内边距
        padding: const EdgeInsets.symmetric(horizontal: 16), // 整体内边距
        dividerColor: Colors.transparent, // 移除下方的白色分割线
        tabs: [
          Tab(text: AppLocalizations.of(context).allStatus),
          Tab(text: AppLocalizations.of(context).pendingReview),
          Tab(text: AppLocalizations.of(context).reviewApproved),
          Tab(text: AppLocalizations.of(context).reviewRejected),
          Tab(text: AppLocalizations.of(context).offShelf),
        ],
      ),
    );
  }

  /// 构建产品列表
  Widget _buildProductList() {
    if (_filteredProducts.isEmpty) {
      return _buildEmptyView();
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredProducts.length,
        itemBuilder: (context, index) {
          final product = _filteredProducts[index];
          return ProductListItem(
            product: product,
            onTap: () => _editProduct(product),
            onDelete: () => _deleteProduct(product),
          );
        },
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: ThemeHelper.getTextHint(context),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).noProductsMessage,
            style: TextStyle(
              fontSize: 16,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).addFirstProductHint,
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextHint(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _addProduct,
      backgroundColor: AppColors.primary,
      child: const Icon(Icons.add, color: Colors.white),
    );
  }

  /// 添加产品
  void _addProduct() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProductEditPage(
          onSaved: (product) {
            _loadData(); // 重新加载数据
          },
        ),
      ),
    );
  }

  /// 编辑产品
  void _editProduct(DoctorProductModel product) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProductEditPage(
          product: product,
          onSaved: (updatedProduct) {
            _loadData(); // 重新加载数据
          },
        ),
      ),
    );
  }

  /// 删除产品
  Future<void> _deleteProduct(DoctorProductModel product) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除产品"${product.name}"吗？\n\n注意：有未完成订单的产品无法删除。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await DoctorProductService().deleteProduct(product.id);
        if (mounted) {
          ToastUtil.show(context, '删除成功');
          _loadData(); // 重新加载数据
        }
      } catch (e) {
        if (mounted) {
          ToastUtil.show(context, '删除失败: $e');
        }
      }
    }
  }
}
