import 'dart:io';
import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/doctor_product_service.dart';
import '../../../services/product_image_service.dart';
import '../../../utils/toast_util.dart';
import '../widgets/product_image_picker.dart';
import '../widgets/product_multi_image_picker.dart';
import '../../admin/widgets/multilingual_text_field.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 产品编辑页面
class ProductEditPage extends StatefulWidget {
  final DoctorProductModel? product; // null表示新建产品
  final Function(DoctorProductModel) onSaved;

  const ProductEditPage({super.key, this.product, required this.onSaved});

  @override
  State<ProductEditPage> createState() => _ProductEditPageState();
}

class _ProductEditPageState extends State<ProductEditPage> {
  final _formKey = GlobalKey<FormState>();

  // 多语言数据
  Map<String, String> _nameData = {'zh': '', 'en': '', 'ug': ''};
  Map<String, String> _descriptionData = {'zh': '', 'en': '', 'ug': ''};
  Map<String, String> _detailedDescriptionData = {'zh': '', 'en': '', 'ug': ''};
  Map<String, String> _manufacturerData = {'zh': '', 'en': '', 'ug': ''};
  Map<String, String> _categoryData = {'zh': '', 'en': '', 'ug': ''};

  // 非多语言字段的控制器
  late TextEditingController _priceController;
  late TextEditingController _originalPriceController;
  late TextEditingController _inventoryCountController;

  // 图片相关
  File? _mainImageFile;
  String? _originalMainImageUrl;
  List<File> _detailImageFiles = [];
  List<String>? _originalDetailImageUrls;

  bool _isLoading = false;
  bool _hasChanges = false;

  // 判断是否为新建产品
  bool get _isNewProduct => widget.product == null;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _addListeners();
  }

  void _initializeControllers() {
    final product = widget.product;

    // 先初始化多语言数据为默认值，避免UI渲染时出现null错误
    if (!_isNewProduct && product != null) {
      // 编辑现有产品，先使用现有的单语言数据作为中文内容
      _nameData = _parseMultilingualField(product.name);
      _descriptionData = _parseMultilingualField(product.description);
      _detailedDescriptionData = _parseMultilingualField(
        product.detailedDescription,
      );
      _manufacturerData = _parseMultilingualField(product.manufacturer);
      _categoryData = _parseMultilingualField(product.category);

      // 然后异步加载完整的多语言数据
      _loadProductMultiLangData();
    } else {
      // 新建产品，初始化空的多语言数据
      _nameData = {'zh': '', 'en': '', 'ug': ''};
      _descriptionData = {'zh': '', 'en': '', 'ug': ''};
      _detailedDescriptionData = {'zh': '', 'en': '', 'ug': ''};
      _manufacturerData = {'zh': '', 'en': '', 'ug': ''};
      _categoryData = {'zh': '', 'en': '', 'ug': ''};
    }

    // 初始化非多语言字段控制器
    _priceController = TextEditingController(
      text: product?.price.toString() ?? '',
    );
    _originalPriceController = TextEditingController(
      text: product?.originalPrice?.toString() ?? '',
    );
    _inventoryCountController = TextEditingController(
      text: product?.inventoryCount.toString() ?? '0',
    );

    // 初始化图片URL
    _originalMainImageUrl = product?.mainImageUrl;
    _originalDetailImageUrls = product?.imageUrls;
  }

  /// 解析多语言字段，支持字符串和多语言JSON对象
  Map<String, String> _parseMultilingualField(dynamic value) {
    if (value == null) {
      return {'zh': '', 'en': '', 'ug': ''};
    }

    // 如果是字符串，将其设置为中文，其他语言为空
    if (value is String) {
      if (value.isEmpty) {
        return {'zh': '', 'en': '', 'ug': ''};
      }
      return {'zh': value, 'en': '', 'ug': ''};
    }

    // 如果是多语言对象，提取所有语言
    if (value is Map<String, dynamic>) {
      return {
        'zh': value['zh']?.toString() ?? '',
        'en': value['en']?.toString() ?? '',
        'ug': value['ug']?.toString() ?? '',
      };
    }

    return {'zh': '', 'en': '', 'ug': ''};
  }

  /// 加载产品的完整多语言数据
  Future<void> _loadProductMultiLangData() async {
    try {
      // 重新获取产品详细信息，这次会包含完整的多语言数据
      final productDetail = await DoctorProductService().getProductRawDataById(
        widget.product!.id,
      );

      // 使用解析方法处理多语言数据
      _nameData = _parseMultilingualField(productDetail['name']);
      _descriptionData = _parseMultilingualField(productDetail['description']);
      _detailedDescriptionData = _parseMultilingualField(
        productDetail['detailed_description'],
      );
      _manufacturerData = _parseMultilingualField(
        productDetail['manufacturer'],
      );
      _categoryData = _parseMultilingualField(productDetail['category']);

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      print('加载产品多语言数据失败: $e');
      // 如果加载失败，使用现有的单语言数据作为中文内容
      _nameData = _parseMultilingualField(widget.product?.name);
      _descriptionData = _parseMultilingualField(widget.product?.description);
      _detailedDescriptionData = _parseMultilingualField(
        widget.product?.detailedDescription,
      );
      _manufacturerData = _parseMultilingualField(widget.product?.manufacturer);
      _categoryData = _parseMultilingualField(widget.product?.category);

      if (mounted) {
        setState(() {});
      }
    }
  }

  void _addListeners() {
    // 只为非多语言字段添加监听器
    _priceController.addListener(_onTextChanged);
    _originalPriceController.addListener(_onTextChanged);
    _inventoryCountController.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      print('产品编辑页面: 检测到更改，设置 _hasChanges = true');
      setState(() {
        _hasChanges = true;
      });
    }
  }

  @override
  void dispose() {
    // 只释放非多语言字段的控制器
    _priceController.dispose();
    _originalPriceController.dispose();
    _inventoryCountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        _isNewProduct
            ? AppLocalizations.of(context).addProduct
            : AppLocalizations.of(context).editProduct,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ThemeHelper.getTextPrimary(context),
        ),
      ),
      backgroundColor: ThemeHelper.getCardBackground(context),
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: ThemeHelper.getTextPrimary(context),
        ),
        onPressed: _onBackPressed,
      ),
      actions: [
        if (_hasChanges)
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: Text(
              AppLocalizations.of(context).save,
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 基本信息卡片
            _buildBasicInfoCard(),
            const SizedBox(height: 16),

            // 价格信息卡片
            _buildPriceInfoCard(),
            const SizedBox(height: 16),

            // 详细信息卡片
            _buildDetailInfoCard(),
            const SizedBox(height: 16),

            // 库存信息卡片
            _buildInventoryCard(),

            // 底部间距
            const SizedBox(height: 100),
          ],
        ),
      ),
    );
  }

  /// 构建基本信息卡片
  Widget _buildBasicInfoCard() {
    return _buildCard(
      title: AppLocalizations.of(context).basicInfo,
      icon: Icons.info_outline,
      children: [
        MultilingualTextField(
          label: AppLocalizations.of(context).productName,
          hint: AppLocalizations.of(context).enterProductName,
          initialValues: _nameData,
          onChanged: (values) {
            _nameData = values;
            _onTextChanged();
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return AppLocalizations.of(context).enterProductName;
            }
            return null;
          },
          required: true,
        ),
        const SizedBox(height: 16),

        MultilingualTextField(
          label: AppLocalizations.of(context).productDescription,
          hint: AppLocalizations.of(context).enterProductDescription,
          initialValues: _descriptionData,
          onChanged: (values) {
            _descriptionData = values;
            _onTextChanged();
          },
          maxLines: 3,
        ),
        const SizedBox(height: 16),

        MultilingualTextField(
          label: AppLocalizations.of(context).productCategory,
          hint: AppLocalizations.of(context).enterProductCategory,
          initialValues: _categoryData,
          onChanged: (values) {
            _categoryData = values;
            _onTextChanged();
          },
        ),
        const SizedBox(height: 16),

        MultilingualTextField(
          label: AppLocalizations.of(context).manufacturer,
          hint: AppLocalizations.of(context).enterManufacturer,
          initialValues: _manufacturerData,
          onChanged: (values) {
            _manufacturerData = values;
            _onTextChanged();
          },
        ),
        const SizedBox(height: 16),

        // 产品图片选择器
        ProductImagePicker(
          title: AppLocalizations.of(context).productMainImage,
          imageUrl: widget.product?.fullMainImageUrl,
          imageFile: _mainImageFile,
          onImageSelected: (file) {
            setState(() {
              _mainImageFile = file;
              _hasChanges = true;
            });
          },
          isRequired: true,
          height: 200,
        ),
        const SizedBox(height: 16),

        // 产品详情图片选择器
        ProductMultiImagePicker(
          title: AppLocalizations.of(context).productDetailImages,
          imageUrls: widget.product?.fullImageUrls,
          imageFiles: _detailImageFiles,
          onImagesSelected: (files) {
            setState(() {
              _detailImageFiles = files;
              _hasChanges = true;
            });
          },
          maxImages: 6,
          itemHeight: 100,
        ),
      ],
    );
  }

  /// 构建价格信息卡片
  Widget _buildPriceInfoCard() {
    return _buildCard(
      title: AppLocalizations.of(context).priceInfo,
      icon: Icons.monetization_on_outlined,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                controller: _priceController,
                label: AppLocalizations.of(context).currentPrice,
                hint: AppLocalizations.of(context).enterCurrentPrice,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return AppLocalizations.of(context).enterCurrentPrice;
                  }
                  final price = double.tryParse(value);
                  if (price == null || price <= 0) {
                    return AppLocalizations.of(context).enterCurrentPrice;
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTextField(
                controller: _originalPriceController,
                label: AppLocalizations.of(context).originalPrice,
                hint: AppLocalizations.of(context).enterOriginalPrice,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    final originalPrice = double.tryParse(value);
                    if (originalPrice == null || originalPrice <= 0) {
                      return AppLocalizations.of(context).enterOriginalPrice;
                    }
                    final price = double.tryParse(_priceController.text);
                    if (price != null && originalPrice <= price) {
                      return AppLocalizations.of(context).enterOriginalPrice;
                    }
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建详细信息卡片
  Widget _buildDetailInfoCard() {
    return _buildCard(
      title: AppLocalizations.of(context).detailedInfo,
      icon: Icons.description_outlined,
      children: [
        MultilingualTextField(
          label: AppLocalizations.of(context).detailedDescription,
          hint: AppLocalizations.of(context).enterDetailedDescription,
          initialValues: _detailedDescriptionData,
          onChanged: (values) {
            _detailedDescriptionData = values;
            _onTextChanged();
          },
          maxLines: 6,
        ),
      ],
    );
  }

  /// 构建库存信息卡片
  Widget _buildInventoryCard() {
    return _buildCard(
      title: AppLocalizations.of(context).inventoryInfo,
      icon: Icons.inventory_outlined,
      children: [
        _buildTextField(
          controller: _inventoryCountController,
          label: AppLocalizations.of(context).inventoryCount,
          hint: AppLocalizations.of(context).enterInventoryCount,
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return AppLocalizations.of(context).enterInventoryCount;
            }
            final count = int.tryParse(value);
            if (count == null || count < 0) {
              return AppLocalizations.of(context).enterInventoryCount;
            }
            return null;
          },
        ),
      ],
    );
  }

  /// 构建卡片容器
  Widget _buildCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片标题
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                ),
              ],
            ),
          ),
          // 卡片内容
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建文本输入框
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          keyboardType: keyboardType,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: ThemeHelper.getTextHint(context)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        border: Border(
          top: BorderSide(color: ThemeHelper.getBorder(context), width: 1),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _onBackPressed,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: ThemeHelper.getBorder(context)),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  AppLocalizations.of(context).cancel,
                  style: TextStyle(
                    color: ThemeHelper.getTextSecondary(context),
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: (_hasChanges && !_isLoading) ? _saveProduct : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : Text(
                        _isNewProduct
                            ? AppLocalizations.of(context).addProduct
                            : AppLocalizations.of(context).saveChanges,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 处理返回按钮
  void _onBackPressed() {
    if (_hasChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(AppLocalizations.of(context).confirmExit),
          content: Text(AppLocalizations.of(context).unsavedChangesMessage),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(AppLocalizations.of(context).cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); // 关闭对话框
                Navigator.pop(context); // 返回上一页
              },
              child: Text(AppLocalizations.of(context).confirmAction),
            ),
          ],
        ),
      );
    } else {
      Navigator.pop(context);
    }
  }

  /// 保存产品
  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 验证图片（新建产品时必须有图片）
    print(
      'ProductEditPage: 图片验证 - _isNewProduct: $_isNewProduct, _mainImageFile: ${_mainImageFile?.path}, _originalMainImageUrl: $_originalMainImageUrl',
    );
    if (_isNewProduct &&
        _mainImageFile == null &&
        (_originalMainImageUrl?.isEmpty ?? true)) {
      print('ProductEditPage: 图片验证失败 - 新建产品需要上传主图');
      ToastUtil.show(context, '请上传产品主图');
      return;
    }
    print('ProductEditPage: 图片验证通过');

    setState(() {
      _isLoading = true;
    });

    try {
      // 构建产品数据
      final priceText = _priceController.text.trim();
      final originalPriceText = _originalPriceController.text.trim();
      final inventoryText = _inventoryCountController.text.trim();

      // 安全解析数字
      final price = double.tryParse(priceText);
      if (price == null) {
        throw Exception('价格格式不正确');
      }

      final originalPrice = originalPriceText.isEmpty
          ? null
          : double.tryParse(originalPriceText);
      if (originalPriceText.isNotEmpty && originalPrice == null) {
        throw Exception('原价格式不正确');
      }

      final inventoryCount = int.tryParse(inventoryText);
      if (inventoryCount == null) {
        throw Exception('库存数量格式不正确');
      }

      // 上传图片（如果有新图片）
      String? mainImageUrl = _originalMainImageUrl;
      if (_mainImageFile != null) {
        print('ProductEditPage: 开始上传主图');
        mainImageUrl = await ProductImageService().uploadImage(_mainImageFile!);
        print('ProductEditPage: 主图上传成功: $mainImageUrl');
      }

      // 上传详情图片（如果有新图片）
      List<String> detailImageUrls = List.from(_originalDetailImageUrls ?? []);
      if (_detailImageFiles.isNotEmpty) {
        print('ProductEditPage: 开始上传详情图片，数量: ${_detailImageFiles.length}');
        final uploadedUrls = await ProductImageService().uploadImages(
          _detailImageFiles,
        );
        detailImageUrls.addAll(uploadedUrls);
        print('ProductEditPage: 详情图片上传成功: $uploadedUrls');
      }

      final productData = {
        'name': _nameData,
        'description': _descriptionData,
        'detailed_description': _detailedDescriptionData,
        'price': price,
        'original_price': originalPrice,
        'manufacturer': _manufacturerData,
        'main_image_url': mainImageUrl,
        'image_urls': detailImageUrls.isNotEmpty ? detailImageUrls : null,
        'category': _categoryData,
        'inventory_count': inventoryCount,
      };

      print('ProductEditPage: 产品数据构建完成: $productData');

      DoctorProductModel result;
      if (_isNewProduct) {
        print('ProductEditPage: 开始创建新产品');
        // 创建新产品
        result = await DoctorProductService().createProductWithMultiLangData(
          nameData: _nameData,
          descriptionData: _descriptionData,
          detailedDescriptionData: _detailedDescriptionData,
          manufacturerData: _manufacturerData,
          categoryData: _categoryData,
          price: price,
          originalPrice: originalPrice,
          inventoryCount: inventoryCount,
          mainImageUrl: mainImageUrl,
          imageUrls: detailImageUrls.isNotEmpty ? detailImageUrls : null,
        );
        print('ProductEditPage: 产品创建成功');
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).productCreatedSuccess,
          );
        }
      } else {
        print('ProductEditPage: 开始更新产品，ID: ${widget.product!.id}');
        // 更新现有产品
        result = await DoctorProductService().updateProductWithMultiLangData(
          productId: widget.product!.id,
          nameData: _nameData,
          descriptionData: _descriptionData,
          detailedDescriptionData: _detailedDescriptionData,
          manufacturerData: _manufacturerData,
          categoryData: _categoryData,
          price: price,
          originalPrice: originalPrice,
          inventoryCount: inventoryCount,
          mainImageUrl: mainImageUrl,
          imageUrls: detailImageUrls.isNotEmpty ? detailImageUrls : null,
        );
        print('ProductEditPage: 产品更新成功');
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).productUpdatedSuccess,
          );
        }
      }

      print('ProductEditPage: 调用回调函数');
      // 调用回调函数
      widget.onSaved(result);

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e, stackTrace) {
      print('ProductEditPage: 保存失败: $e');
      print('ProductEditPage: 堆栈跟踪: $stackTrace');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(context, '保存失败: $e');
      }
    }
  }
}
