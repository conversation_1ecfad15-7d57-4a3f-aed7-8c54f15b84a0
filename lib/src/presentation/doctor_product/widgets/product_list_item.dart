import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/language_service.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../common/widgets/cached_image_widget.dart';

/// 产品列表项组件
class ProductListItem extends StatelessWidget {
  final DoctorProductModel product;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const ProductListItem({
    super.key,
    required this.product,
    this.onTap,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 产品信息行
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 产品图片
                  _buildProductImage(),
                  const SizedBox(width: 12),

                  // 产品信息
                  Expanded(child: _buildProductInfo(context)),

                  // 操作按钮
                  _buildActionButtons(context),
                ],
              ),

              const SizedBox(height: 12),

              // 底部信息
              _buildBottomInfo(context),

              // 审核拒绝原因（仅在状态为拒绝时显示）
              if (product.status == 2 &&
                  product.adminReviewNote?.isNotEmpty == true)
                _buildRejectReason(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建产品图片
  Widget _buildProductImage() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: ProductImageWidget(
          imageUrl: product.fullMainImageUrl,
          width: 60,
          height: 60,
        ),
      ),
    );
  }

  /// 构建产品信息
  Widget _buildProductInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 产品名称
        Text(
          product.getLocalizedName(LanguageService().getCurrentLanguageCode()),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),

        // 产品描述
        Builder(
          builder: (context) {
            final localizedDescription = product.getLocalizedDescription(
              LanguageService().getCurrentLanguageCode(),
            );
            if (localizedDescription.isNotEmpty) {
              return Text(
                localizedDescription,
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              );
            }
            return const SizedBox.shrink();
          },
        ),
        const SizedBox(height: 8),

        // 价格信息
        _buildPriceInfo(context),
      ],
    );
  }

  /// 构建价格信息
  Widget _buildPriceInfo(BuildContext context) {
    return Row(
      children: [
        // 当前价格
        Text(
          '¥${product.price.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: AppColors.primary,
          ),
        ),

        // 原价（如果有折扣）
        if (product.hasDiscount) ...[
          const SizedBox(width: 8),
          Text(
            '¥${product.originalPrice!.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 12,
              color: ThemeHelper.getTextHint(context),
              decoration: TextDecoration.lineThrough,
            ),
          ),
        ],
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.more_vert, color: ThemeHelper.getTextHint(context)),
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onTap?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              const Icon(Icons.edit_outlined, size: 18),
              const SizedBox(width: 8),
              Text(AppLocalizations.of(context).edit),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              const Icon(Icons.delete_outline, size: 18, color: Colors.red),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).delete,
                style: const TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建底部信息
  Widget _buildBottomInfo(BuildContext context) {
    return Row(
      children: [
        // 状态标签
        _buildStatusChip(context),
        const SizedBox(width: 12),

        // 库存信息
        Icon(
          Icons.inventory_outlined,
          size: 14,
          color: ThemeHelper.getTextHint(context),
        ),
        const SizedBox(width: 4),
        Text(
          '${AppLocalizations.of(context).inventory}: ${product.inventoryCount}',
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextHint(context),
          ),
        ),
        const SizedBox(width: 12),

        // 销量信息
        Icon(
          Icons.shopping_cart_outlined,
          size: 14,
          color: ThemeHelper.getTextHint(context),
        ),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            '${AppLocalizations.of(context).salesVolume}: ${product.salesCount}',
            style: TextStyle(
              fontSize: 12,
              color: ThemeHelper.getTextHint(context),
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),

        const Spacer(),

        // 创建时间
        Text(
          _formatDate(product.createdAt),
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextHint(context),
          ),
        ),
      ],
    );
  }

  /// 构建状态标签
  Widget _buildStatusChip(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Color(product.statusColor).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Color(product.statusColor).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        _getLocalizedStatusText(context),
        style: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          color: Color(product.statusColor),
        ),
      ),
    );
  }

  /// 获取本地化的状态文本
  String _getLocalizedStatusText(BuildContext context) {
    switch (product.status) {
      case 0:
        return AppLocalizations.of(context).pendingReview;
      case 1:
        return AppLocalizations.of(context).approved;
      case 2:
        return AppLocalizations.of(context).rejected;
      case 3:
        return AppLocalizations.of(context).offline;
      default:
        return AppLocalizations.of(context).orderStatusUnknown;
    }
  }

  /// 构建审核拒绝原因
  Widget _buildRejectReason(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFE74C3C).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFE74C3C).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.info_outline, size: 16, color: const Color(0xFFE74C3C)),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${AppLocalizations.of(context).reviewStatus}：',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFFE74C3C),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  product.adminReviewNote!,
                  style: TextStyle(
                    fontSize: 12,
                    color: ThemeHelper.getTextSecondary(context),
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}';
  }
}
