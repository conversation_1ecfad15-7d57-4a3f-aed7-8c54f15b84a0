import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../health_assistant/screens/health_assistant_image_crop_screen.dart';

/// 产品多图片选择器组件
class ProductMultiImagePicker extends StatefulWidget {
  final String title;
  final List<String>? imageUrls;
  final List<File>? imageFiles;
  final Function(List<File>) onImagesSelected;
  final int maxImages;
  final double? itemHeight;

  const ProductMultiImagePicker({
    super.key,
    required this.title,
    this.imageUrls,
    this.imageFiles,
    required this.onImagesSelected,
    this.maxImages = 6,
    this.itemHeight = 120,
  });

  @override
  State<ProductMultiImagePicker> createState() =>
      _ProductMultiImagePickerState();
}

class _ProductMultiImagePickerState extends State<ProductMultiImagePicker> {
  final ImagePicker _imagePicker = ImagePicker();
  late List<File> _selectedImages;

  @override
  void initState() {
    super.initState();
    _selectedImages = List.from(widget.imageFiles ?? []);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Row(
          children: [
            Text(
              widget.title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '(${AppLocalizations.of(context).maxSixImages})',
              style: TextStyle(
                fontSize: 12,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // 图片网格
        _buildImageGrid(),
      ],
    );
  }

  /// 构建图片网格
  Widget _buildImageGrid() {
    final totalImages =
        _selectedImages.length + (widget.imageUrls?.length ?? 0);
    final canAddMore = totalImages < widget.maxImages;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeHelper.getTextHint(context).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          // 显示现有的网络图片
          if (widget.imageUrls != null)
            ...widget.imageUrls!.map((url) => _buildNetworkImageItem(url)),

          // 显示选中的本地图片
          ..._selectedImages.asMap().entries.map(
            (entry) => _buildLocalImageItem(entry.value, entry.key),
          ),

          // 添加图片按钮
          if (canAddMore) _buildAddImageButton(),
        ],
      ),
    );
  }

  /// 构建网络图片项
  Widget _buildNetworkImageItem(String imageUrl) {
    return Container(
      width: widget.itemHeight,
      height: widget.itemHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ThemeHelper.getTextHint(context).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // 图片显示
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  color: ThemeHelper.getTextHint(
                    context,
                  ).withValues(alpha: 0.1),
                  child: Icon(
                    Icons.broken_image,
                    color: ThemeHelper.getTextHint(context),
                  ),
                ),
              ),
            ),
          ),

          // 网络图片标识
          Positioned(
            top: 4,
            left: 4,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                AppLocalizations.of(context).networkImage,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建本地图片项
  Widget _buildLocalImageItem(File imageFile, int index) {
    return Container(
      width: widget.itemHeight,
      height: widget.itemHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ThemeHelper.getTextHint(context).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // 图片显示
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(imageFile, fit: BoxFit.cover),
            ),
          ),

          // 删除按钮
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () => _removeImage(index),
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.8),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建添加图片按钮
  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _showImageSourceDialog,
      child: Container(
        width: widget.itemHeight,
        height: widget.itemHeight,
        decoration: BoxDecoration(
          color: ThemeHelper.getTextHint(context).withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: ThemeHelper.getTextHint(context).withValues(alpha: 0.3),
            width: 1,
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 32,
              color: ThemeHelper.getTextHint(context),
            ),
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context).addImage,
              style: TextStyle(
                fontSize: 12,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 移除图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
    widget.onImagesSelected(_selectedImages);
  }

  /// 显示图片来源选择对话框
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: ThemeHelper.getCardBackground(context),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Text(
                '选择图片来源',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
              const SizedBox(height: 20),

              // 拍照选项
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.camera_alt, color: AppColors.primary),
                ),
                title: const Text('拍照'),
                subtitle: const Text('使用相机拍摄照片'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromCamera();
                },
              ),

              // 相册选项
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.photo_library, color: AppColors.primary),
                ),
                title: const Text('从相册选择'),
                subtitle: const Text('从手机相册中选择图片'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 从相机拍照
  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        await _processSelectedImage(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '拍照失败: $e');
      }
    }
  }

  /// 从相册选择图片
  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        await _processSelectedImage(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '选择图片失败: $e');
      }
    }
  }

  /// 处理选中的图片（进行裁剪）
  Future<void> _processSelectedImage(File imageFile) async {
    try {
      // 导航到图片裁剪页面
      final result = await Navigator.push<Map<String, dynamic>>(
        context,
        MaterialPageRoute(
          builder: (context) =>
              HealthAssistantImageCropScreen(imageFile: imageFile),
        ),
      );

      if (result != null && result['croppedImagePath'] != null) {
        final croppedImagePath = result['croppedImagePath'] as String;
        final croppedFile = File(croppedImagePath);

        // 添加到选中的图片列表
        setState(() {
          _selectedImages.add(croppedFile);
        });

        // 回调更新的图片列表
        widget.onImagesSelected(_selectedImages);
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '处理图片失败: $e');
      }
    }
  }
}
