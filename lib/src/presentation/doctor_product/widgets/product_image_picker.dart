import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../health_assistant/screens/health_assistant_image_crop_screen.dart';

/// 产品图片选择器组件
class ProductImagePicker extends StatefulWidget {
  final String title;
  final String? imageUrl;
  final File? imageFile;
  final Function(File?) onImageSelected;
  final bool isRequired;
  final double? width;
  final double? height;

  const ProductImagePicker({
    super.key,
    required this.title,
    this.imageUrl,
    this.imageFile,
    required this.onImageSelected,
    this.isRequired = false,
    this.width,
    this.height,
  });

  @override
  State<ProductImagePicker> createState() => _ProductImagePickerState();
}

class _ProductImagePickerState extends State<ProductImagePicker> {
  final ImagePicker _imagePicker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Row(
          children: [
            Text(
              widget.title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
            if (widget.isRequired) ...[
              const SizedBox(width: 4),
              Text('*', style: TextStyle(fontSize: 16, color: Colors.red)),
            ],
          ],
        ),
        const SizedBox(height: 12),

        // 图片选择区域
        _buildImagePickerArea(),
      ],
    );
  }

  /// 构建图片选择区域
  Widget _buildImagePickerArea() {
    final hasImage =
        widget.imageFile != null || (widget.imageUrl?.isNotEmpty == true);

    return Container(
      width: widget.width ?? double.infinity,
      height: widget.height ?? 200,
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeHelper.getTextHint(context).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: hasImage ? _buildImagePreview() : _buildImagePlaceholder(),
    );
  }

  /// 构建图片预览
  Widget _buildImagePreview() {
    return Stack(
      children: [
        // 图片显示
        Positioned.fill(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: widget.imageFile != null
                ? Image.file(widget.imageFile!, fit: BoxFit.cover)
                : Image.network(
                    widget.imageUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildImagePlaceholder();
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Center(
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                    loadingProgress.expectedTotalBytes!
                              : null,
                          strokeWidth: 2,
                          color: AppColors.primary,
                        ),
                      );
                    },
                  ),
          ),
        ),

        // 右上角操作按钮
        Positioned(
          top: 8,
          right: 8,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 重新选择按钮
              _buildCircularActionButton(
                icon: Icons.edit,
                tooltip: '重新选择',
                onPressed: _showImageSourceDialog,
                backgroundColor: Colors.blue,
              ),
              const SizedBox(width: 8),
              // 删除按钮
              _buildCircularActionButton(
                icon: Icons.delete,
                tooltip: '删除图片',
                onPressed: () => widget.onImageSelected(null),
                backgroundColor: Colors.red,
              ),
            ],
          ),
        ),

        // 底部渐变遮罩（可选，用于显示图片信息）
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 40,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.6),
                ],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                children: [
                  Icon(Icons.image, size: 16, color: Colors.white),
                  const SizedBox(width: 6),
                  Text(
                    widget.imageFile != null
                        ? AppLocalizations.of(context).imageSelected
                        : AppLocalizations.of(context).networkImage,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建图片占位符
  Widget _buildImagePlaceholder() {
    return InkWell(
      onTap: _showImageSourceDialog,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: ThemeHelper.getTextHint(context).withValues(alpha: 0.05),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 48,
              color: ThemeHelper.getTextHint(context),
            ),
            const SizedBox(height: 8),
            Text(
              '点击选择图片',
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '支持拍照或从相册选择',
              style: TextStyle(
                fontSize: 12,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建圆形操作按钮
  Widget _buildCircularActionButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    required Color backgroundColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor.withValues(alpha: 0.9),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            width: 36,
            height: 36,
            alignment: Alignment.center,
            child: Icon(icon, size: 18, color: Colors.white),
          ),
        ),
      ),
    );
  }

  /// 显示图片来源选择对话框
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: ThemeHelper.getCardBackground(context),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Text(
                '选择图片来源',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
              const SizedBox(height: 20),

              // 拍照选项
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.camera_alt, color: AppColors.primary),
                ),
                title: const Text('拍照'),
                subtitle: const Text('使用相机拍摄照片'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromCamera();
                },
              ),

              // 相册选项
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.photo_library, color: AppColors.primary),
                ),
                title: const Text('从相册选择'),
                subtitle: const Text('从手机相册中选择图片'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 从相机拍照
  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        await _processSelectedImage(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '拍照失败: $e');
      }
    }
  }

  /// 从相册选择图片
  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        await _processSelectedImage(File(image.path));
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '选择图片失败: $e');
      }
    }
  }

  /// 处理选中的图片（进行裁剪）
  Future<void> _processSelectedImage(File imageFile) async {
    try {
      // 导航到图片裁剪页面
      final result = await Navigator.push<Map<String, dynamic>>(
        context,
        MaterialPageRoute(
          builder: (context) =>
              HealthAssistantImageCropScreen(imageFile: imageFile),
        ),
      );

      if (result != null && result['croppedImagePath'] != null) {
        final croppedImagePath = result['croppedImagePath'] as String;
        final croppedFile = File(croppedImagePath);

        // 回调选中的图片
        widget.onImageSelected(croppedFile);
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '处理图片失败: $e');
      }
    }
  }
}
