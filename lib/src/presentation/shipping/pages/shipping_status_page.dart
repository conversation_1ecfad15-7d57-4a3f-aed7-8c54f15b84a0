import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/shipping_model.dart';
import '../../../services/shipping_service.dart';
import '../../../utils/toast_util.dart';
import '../../../services/language_service.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 物流状态页面
class ShippingStatusPage extends StatefulWidget {
  final dynamic order; // 支持两种不同的ProductOrderModel
  final bool isDoctorView; // 是否为医生端查看

  const ShippingStatusPage({
    super.key,
    required this.order,
    this.isDoctorView = false,
  });

  @override
  State<ShippingStatusPage> createState() => _ShippingStatusPageState();
}

class _ShippingStatusPageState extends State<ShippingStatusPage> {
  ShippingStatusResponse? _shippingStatus;
  bool _isLoading = true;
  String? _errorMessage;

  // 辅助方法来获取订单属性
  int get _orderId => widget.order.id;
  String get _orderSn => widget.order.orderSn;
  String get _productName {
    final languageCode = LanguageService().getCurrentLanguageCode();
    return widget.order.getProductName(languageCode);
  }

  int get _quantity => widget.order.quantity;
  String get _formattedTotalAmount => widget.order.formattedTotalAmount;
  String? get _shippingAddress => widget.order.shippingAddress;

  @override
  void initState() {
    super.initState();
    _loadShippingStatus();
  }

  /// 加载物流状态
  Future<void> _loadShippingStatus() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final shippingService = ShippingService();
      final status = widget.isDoctorView
          ? await shippingService.getDoctorOrderShippingStatus(_orderId)
          : await shippingService.getUserOrderShippingStatus(_orderId);

      if (mounted) {
        setState(() {
          _shippingStatus = status;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        AppLocalizations.of(context).shippingStatus,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ThemeHelper.getTextPrimary(context),
        ),
      ),
      backgroundColor: ThemeHelper.getCardBackground(context),
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: ThemeHelper.getTextPrimary(context),
        ),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.refresh, color: ThemeHelper.getTextPrimary(context)),
          onPressed: _loadShippingStatus,
        ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return _buildErrorView();
    }

    if (_shippingStatus == null) {
      return _buildEmptyView();
    }

    return RefreshIndicator(
      onRefresh: _loadShippingStatus,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 订单基本信息
            _buildOrderInfoCard(),
            const SizedBox(height: 16),

            // 物流状态信息
            _buildShippingStatusCard(),

            // 如果有快递单号，显示快递信息
            if (_shippingStatus!.trackingNumber?.isNotEmpty == true) ...[
              const SizedBox(height: 16),
              _buildTrackingInfoCard(),
            ],

            const SizedBox(height: 16),
            // 时间轴
            _buildTimelineCard(),
          ],
        ),
      ),
    );
  }

  /// 构建错误视图
  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: ThemeHelper.getTextSecondary(context),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).loadFailed,
            style: TextStyle(
              fontSize: 16,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? AppLocalizations.of(context).error,
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextHint(context),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadShippingStatus,
            child: Text(AppLocalizations.of(context).retry),
          ),
        ],
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_shipping_outlined,
            size: 64,
            color: ThemeHelper.getTextSecondary(context),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).noShippingInfo,
            style: TextStyle(
              fontSize: 16,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建订单信息卡片
  Widget _buildOrderInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).orderInfoTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 12),
          _buildInfoRow(AppLocalizations.of(context).orderNumber, _orderSn),
          _buildInfoRow(AppLocalizations.of(context).productName, _productName),
          _buildInfoRow(AppLocalizations.of(context).quantity, '$_quantity'),
          _buildInfoRow(
            AppLocalizations.of(context).totalAmount,
            _formattedTotalAmount,
          ),
          if (_shippingAddress?.isNotEmpty == true)
            _buildInfoRow(
              AppLocalizations.of(context).shippingAddress,
              _shippingAddress!,
            ),
        ],
      ),
    );
  }

  /// 构建物流状态卡片
  Widget _buildShippingStatusCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.local_shipping, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).shippingStatus,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _getLocalizedShippingStatusText(),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建快递信息卡片
  Widget _buildTrackingInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).expressInfo,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 12),
          _buildTrackingRow(
            AppLocalizations.of(context).expressNumber,
            _shippingStatus!.trackingNumber!,
            canCopy: true,
          ),
          if (_shippingStatus!.shippingCompany?.isNotEmpty == true)
            _buildInfoRow(
              AppLocalizations.of(context).expressCompany,
              _shippingStatus!.shippingCompany ?? '',
            ),
          if (_shippingStatus!.shippingNote?.isNotEmpty == true)
            _buildInfoRow(
              AppLocalizations.of(context).shippingNote,
              _shippingStatus!.shippingNote!,
            ),
        ],
      ),
    );
  }

  /// 构建时间轴卡片
  Widget _buildTimelineCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).orderTimeline,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 16),
          // 这里可以添加时间轴组件
          _buildTimelineItem(
            AppLocalizations.of(context).orderCreated,
            _formatDateTime(_shippingStatus!.createdAt),
            true,
          ),
          if (_shippingStatus!.payTime != null)
            _buildTimelineItem(
              AppLocalizations.of(context).paymentCompleted,
              _formatDateTime(_shippingStatus!.payTime!),
              true,
            ),
          if (_shippingStatus!.shipTime != null)
            _buildTimelineItem(
              AppLocalizations.of(context).goodsShipped,
              _formatDateTime(_shippingStatus!.shipTime!),
              true,
            ),
          if (_shippingStatus!.completeTime != null)
            _buildTimelineItem(
              AppLocalizations.of(context).orderCompleted,
              _formatDateTime(_shippingStatus!.completeTime!),
              true,
            ),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建可复制的快递单号行
  Widget _buildTrackingRow(String label, String value, {bool canCopy = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
          ),
          if (canCopy)
            IconButton(
              onPressed: () => _copyToClipboard(value),
              icon: Icon(Icons.copy, size: 16, color: AppColors.primary),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
        ],
      ),
    );
  }

  /// 构建时间轴项目
  Widget _buildTimelineItem(String title, String time, bool isCompleted) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: isCompleted ? AppColors.primary : Colors.grey,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                ),
                Text(
                  time,
                  style: TextStyle(
                    fontSize: 12,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 复制到剪贴板
  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ToastUtil.show(
      context,
      AppLocalizations.of(context).copiedToClipboardMessage,
    );
  }

  /// 获取本地化的物流状态文本
  String _getLocalizedShippingStatusText() {
    // 首先尝试使用后端返回的状态文本（如果后端支持国际化）
    if (_shippingStatus!.shippingStatusText.isNotEmpty) {
      // 检查是否为中文文本，如果是则进行本地化处理
      if (_isChineseText(_shippingStatus!.shippingStatusText)) {
        return _getStatusTextByOrderStatus(_shippingStatus!.orderStatus);
      }
      return _shippingStatus!.shippingStatusText;
    }

    // 如果后端没有返回状态文本，则根据订单状态生成
    return _getStatusTextByOrderStatus(_shippingStatus!.orderStatus);
  }

  /// 检查是否为中文文本
  bool _isChineseText(String text) {
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  /// 根据订单状态获取本地化状态文本
  String _getStatusTextByOrderStatus(int orderStatus) {
    switch (orderStatus) {
      case 0:
        return AppLocalizations.of(context).orderStatusPending;
      case 1:
        return AppLocalizations.of(context).orderStatusPendingShipment;
      case 2:
        return AppLocalizations.of(context).orderStatusShipped;
      case 3:
        return AppLocalizations.of(context).orderStatusCompleted;
      case 4:
        return AppLocalizations.of(context).orderStatusCancelled;
      default:
        return AppLocalizations.of(context).orderStatusUnknown;
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
