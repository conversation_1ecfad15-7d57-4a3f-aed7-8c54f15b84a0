import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/language_service.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../config/api/api_config.dart';

/// 管理员订单列表项
class AdminOrderListItem extends StatelessWidget {
  final ProductOrderModel order;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onSelectionChanged;
  final Function(int status, String? note)? onStatusUpdate;
  final ValueChanged<int>? onPayStatusUpdate;
  final VoidCallback? onAdminShip;
  final VoidCallback? onDelete;

  const AdminOrderListItem({
    super.key,
    required this.order,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onSelectionChanged,
    this.onStatusUpdate,
    this.onPayStatusUpdate,
    this.onAdminShip,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: isSelected
            ? Border.all(color: AppColors.primary, width: 2)
            : null,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 订单头部信息
              Row(
                children: [
                  // 选择框（选择模式下显示）
                  if (isSelectionMode) ...[
                    Checkbox(
                      value: isSelected,
                      onChanged: (value) =>
                          onSelectionChanged?.call(value ?? false),
                      activeColor: AppColors.primary,
                    ),
                    const SizedBox(width: 8),
                  ],

                  // 订单号和状态
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(
                            context,
                          ).orderNumberLabel(order.orderSn),
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: ThemeHelper.getTextPrimary(context),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            _buildStatusChip(context),
                            const SizedBox(width: 8),
                            Text(
                              _formatDateTime(order.createdAt),
                              style: TextStyle(
                                fontSize: 12,
                                color: ThemeHelper.getTextSecondary(context),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // 操作按钮
                  if (!isSelectionMode) _buildActionButtons(context),
                ],
              ),

              const SizedBox(height: 12),

              // 订单详情
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 产品图片
                  _buildProductImage(),
                  const SizedBox(width: 12),

                  // 产品信息
                  Expanded(child: _buildProductInfo(context)),
                ],
              ),

              const SizedBox(height: 12),

              // 客户和医生信息
              _buildUserDoctorInfo(context),

              const SizedBox(height: 12),

              // 价格信息
              _buildPriceInfo(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建产品图片
  Widget _buildProductImage() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: order.productImage?.isNotEmpty == true
            ? Image.network(
                _getFullImageUrl(order.productImage!),
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultImage();
                },
              )
            : _buildDefaultImage(),
      ),
    );
  }

  /// 构建默认图片
  Widget _buildDefaultImage() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: AppColors.primary.withValues(alpha: 0.1),
      ),
      child: Icon(
        Icons.inventory_2_outlined,
        color: AppColors.primary,
        size: 20,
      ),
    );
  }

  /// 构建产品信息
  Widget _buildProductInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          order.getProductName(LanguageService().getCurrentLanguageCode()),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          AppLocalizations.of(context).quantityLabel(order.quantity),
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextSecondary(context),
          ),
        ),
      ],
    );
  }

  /// 构建用户和医生信息
  Widget _buildUserDoctorInfo(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.of(context).customerLabel(order.userNickname),
                style: TextStyle(
                  fontSize: 12,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
              if (order.shippingAddress?.isNotEmpty == true)
                Text(
                  AppLocalizations.of(
                    context,
                  ).addressLabel(order.shippingAddress!),
                  style: TextStyle(
                    fontSize: 12,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        Text(
          AppLocalizations.of(context).doctorLabel(
            order.getDoctorName(LanguageService().getCurrentLanguageCode()),
          ),
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextSecondary(context),
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
      ],
    );
  }

  /// 构建价格信息
  Widget _buildPriceInfo(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          AppLocalizations.of(
            context,
          ).unitPriceLabel(order.unitPrice.toStringAsFixed(2)),
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextSecondary(context),
          ),
        ),
        Text(
          AppLocalizations.of(
            context,
          ).totalAmountLabel(order.totalAmount.toStringAsFixed(2)),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF27AE60),
          ),
        ),
      ],
    );
  }

  /// 构建状态标签
  Widget _buildStatusChip(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    String statusText;

    switch (order.orderStatus) {
      case 0:
        backgroundColor = const Color(0xFFF39C12).withValues(alpha: 0.1);
        textColor = const Color(0xFFF39C12);
        statusText = AppLocalizations.of(context).orderStatusPendingPayment;
        break;
      case 1:
        backgroundColor = const Color(0xFF3498DB).withValues(alpha: 0.1);
        textColor = const Color(0xFF3498DB);
        statusText = AppLocalizations.of(context).pendingShipment;
        break;
      case 2:
        backgroundColor = AppColors.primary.withValues(alpha: 0.1);
        textColor = AppColors.primary;
        statusText = AppLocalizations.of(context).shipped;
        break;
      case 3:
        backgroundColor = const Color(0xFF27AE60).withValues(alpha: 0.1);
        textColor = const Color(0xFF27AE60);
        statusText = AppLocalizations.of(context).completed;
        break;
      case 4:
        backgroundColor = const Color(0xFFE74C3C).withValues(alpha: 0.1);
        textColor = const Color(0xFFE74C3C);
        statusText = AppLocalizations.of(context).cancelled;
        break;
      default:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
        statusText = '未知';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.more_vert, color: ThemeHelper.getTextSecondary(context)),
      onSelected: (value) => _handleAction(context, value),
      itemBuilder: (context) => [
        // 支付状态操作
        if (order.payStatus == 0) ...[
          PopupMenuItem(
            value: 'mark_paid',
            child: Row(
              children: [
                Icon(Icons.payment, color: Colors.green, size: 16),
                SizedBox(width: 8),
                Text(AppLocalizations.of(context).markAsPaidAction),
              ],
            ),
          ),
        ],
        if (order.payStatus == 1) ...[
          PopupMenuItem(
            value: 'refund',
            child: Row(
              children: [
                Icon(Icons.money_off, color: Colors.orange, size: 16),
                SizedBox(width: 8),
                Text(AppLocalizations.of(context).payStatusRefunded),
              ],
            ),
          ),
        ],

        // 订单状态操作
        if (order.orderStatus == 1 && order.payStatus == 1) ...[
          PopupMenuItem(
            value: 'admin_ship',
            child: Row(
              children: [
                const Icon(Icons.local_shipping, color: Colors.blue, size: 16),
                const SizedBox(width: 8),
                Text(AppLocalizations.of(context).adminShipAction),
              ],
            ),
          ),
        ],
        PopupMenuItem(
          value: 'complete',
          child: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 16),
              const SizedBox(width: 8),
              Text(AppLocalizations.of(context).markCompleteAction),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'cancel',
          child: Row(
            children: [
              const Icon(Icons.cancel, color: Colors.orange, size: 16),
              const SizedBox(width: 8),
              Text(AppLocalizations.of(context).markCancelAction),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              const Icon(Icons.delete, color: Colors.red, size: 16),
              const SizedBox(width: 8),
              Text(AppLocalizations.of(context).deleteOrderAction),
            ],
          ),
        ),
      ],
    );
  }

  /// 处理操作
  void _handleAction(BuildContext context, String action) {
    switch (action) {
      case 'mark_paid':
        onPayStatusUpdate?.call(1); // 已支付
        break;
      case 'refund':
        onPayStatusUpdate?.call(2); // 已退款
        break;
      case 'admin_ship':
        onAdminShip?.call(); // 管理员发货
        break;
      case 'complete':
        onStatusUpdate?.call(3, null); // 已完成
        break;
      case 'cancel':
        onStatusUpdate?.call(4, null); // 已取消
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 获取完整的图片URL
  String _getFullImageUrl(String imageUrl) {
    // 使用ApiConfig中的buildImageUrl方法来处理URL
    return ApiConfig.buildImageUrl(imageUrl);
  }
}
