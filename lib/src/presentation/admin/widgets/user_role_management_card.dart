import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/admin_user_model.dart';
import '../../../services/admin/user_admin_service.dart';
import '../../../utils/toast_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 用户角色管理卡片
class UserRoleManagementCard extends StatefulWidget {
  final AdminUserModel user;
  final VoidCallback? onRoleUpdated;

  const UserRoleManagementCard({
    super.key,
    required this.user,
    this.onRoleUpdated,
  });

  @override
  State<UserRoleManagementCard> createState() => _UserRoleManagementCardState();
}

class _UserRoleManagementCardState extends State<UserRoleManagementCard> {
  final UserAdminService _userService = UserAdminService();
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              Icon(
                Icons.admin_panel_settings,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).roleManagement,
                style: FontUtil.createHeadingTextStyle(
                  text: AppLocalizations.of(context).roleManagement,
                ).copyWith(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 当前角色显示
          _buildCurrentRoles(),

          const SizedBox(height: 16),

          // 角色管理选项
          _buildRoleOptions(),

          const SizedBox(height: 16),

          // 操作按钮
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isUpdating ? null : _showRoleEditDialog,
                  icon: Icon(Icons.edit, size: 16),
                  label: Text(AppLocalizations.of(context).editRole),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isUpdating ? null : _showResetPasswordDialog,
                  icon: Icon(Icons.lock_reset, size: 16),
                  label: Text(AppLocalizations.of(context).resetPassword),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.warning,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建当前角色显示
  Widget _buildCurrentRoles() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).currentRoles,
          style:
              FontUtil.createBodyTextStyle(
                text: AppLocalizations.of(context).currentRoles,
              ).copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: ThemeHelper.getTextSecondary(context),
              ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _getLocalizedRoles(context).map((roleInfo) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: roleInfo.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: roleInfo.color.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Text(
                roleInfo.name,
                style: FontUtil.createBodyTextStyle(text: roleInfo.name)
                    .copyWith(
                      fontSize: 12,
                      color: roleInfo.color,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建角色选项
  Widget _buildRoleOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).roleDetails,
          style:
              FontUtil.createBodyTextStyle(
                text: AppLocalizations.of(context).roleDetails,
              ).copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: ThemeHelper.getTextSecondary(context),
              ),
        ),
        const SizedBox(height: 8),

        // 管理员角色
        _buildRoleItem(
          icon: Icons.admin_panel_settings,
          title: AppLocalizations.of(context).adminUser,
          isActive: widget.user.isAdmin,
          color: AppColors.error,
        ),

        // 医生角色
        _buildRoleItem(
          icon: Icons.medical_services,
          title: AppLocalizations.of(context).doctorUser,
          isActive: widget.user.isDoctor,
          color: AppColors.info,
          subtitle: widget.user.isDoctor && widget.user.doctorInfo != null
              ? '${AppLocalizations.of(context).associatedDoctor}: ${_getDoctorName(context)}'
              : null,
        ),

        // 分销员角色
        _buildRoleItem(
          icon: Icons.people,
          title: AppLocalizations.of(context).referrerText,
          isActive: widget.user.isReferrer,
          color: AppColors.warning,
          subtitle: widget.user.isReferrer
              ? '${AppLocalizations.of(context).level}: ${widget.user.referrerLevel}'
              : null,
        ),
      ],
    );
  }

  /// 构建角色项
  Widget _buildRoleItem({
    required IconData icon,
    required String title,
    required bool isActive,
    required Color color,
    String? subtitle,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isActive
            ? color.withValues(alpha: 0.1)
            : ThemeHelper.getTextSecondary(context).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isActive
              ? color.withValues(alpha: 0.3)
              : ThemeHelper.getTextSecondary(context).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: isActive ? color : ThemeHelper.getTextSecondary(context),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: FontUtil.createBodyTextStyle(text: title).copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isActive
                        ? color
                        : ThemeHelper.getTextPrimary(context),
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: FontUtil.createCaptionTextStyle(text: subtitle)
                        .copyWith(
                          fontSize: 12,
                          color: ThemeHelper.getTextSecondary(context),
                        ),
                  ),
                ],
              ],
            ),
          ),
          if (isActive) Icon(Icons.check_circle, color: color, size: 20),
        ],
      ),
    );
  }

  /// 显示角色编辑对话框
  void _showRoleEditDialog() {
    bool isAdmin = widget.user.isAdmin;
    bool isDoctor = widget.user.isDoctor;
    bool isReferrer = widget.user.isReferrer;
    int referrerLevel = widget.user.referrerLevel;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(AppLocalizations.of(context).editUserRole),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 管理员角色
                CheckboxListTile(
                  title: Text(AppLocalizations.of(context).roleAdmin),
                  subtitle: Text(
                    AppLocalizations.of(context).adminRoleDescription,
                  ),
                  value: isAdmin,
                  onChanged: (value) {
                    setState(() {
                      isAdmin = value ?? false;
                    });
                  },
                  activeColor: AppColors.error,
                ),

                // 医生角色
                CheckboxListTile(
                  title: Text(AppLocalizations.of(context).roleDoctor),
                  subtitle: Text(
                    AppLocalizations.of(context).doctorRoleDescription,
                  ),
                  value: isDoctor,
                  onChanged: (value) {
                    setState(() {
                      isDoctor = value ?? false;
                    });
                  },
                  activeColor: AppColors.info,
                ),

                // 分销员角色
                CheckboxListTile(
                  title: Text(AppLocalizations.of(context).roleReferrer),
                  subtitle: Text(
                    AppLocalizations.of(context).referrerRoleDescription,
                  ),
                  value: isReferrer,
                  onChanged: (value) {
                    setState(() {
                      isReferrer = value ?? false;
                    });
                  },
                  activeColor: AppColors.warning,
                ),

                // 分销员等级（仅在选择分销员时显示）
                if (isReferrer) ...[
                  const SizedBox(height: 16),
                  TextField(
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).referrerLevel,
                      hintText: AppLocalizations.of(
                        context,
                      ).pleaseEnterReferrerLevel,
                      border: const OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    controller: TextEditingController(
                      text: referrerLevel.toString(),
                    ),
                    onChanged: (value) {
                      referrerLevel = int.tryParse(value) ?? 0;
                    },
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context).cancel),
            ),
            ElevatedButton(
              onPressed: () => _updateUserRole(
                context,
                isAdmin,
                isDoctor,
                isReferrer,
                referrerLevel,
              ),
              child: Text(AppLocalizations.of(context).confirm),
            ),
          ],
        ),
      ),
    );
  }

  /// 更新用户角色
  Future<void> _updateUserRole(
    BuildContext dialogContext,
    bool isAdmin,
    bool isDoctor,
    bool isReferrer,
    int referrerLevel,
  ) async {
    Navigator.of(dialogContext).pop();

    setState(() {
      _isUpdating = true;
    });

    try {
      await _userService.updateUserRole(
        widget.user.id,
        isAdmin: isAdmin,
        isDoctor: isDoctor,
        isReferrer: isReferrer,
        referrerLevel: referrerLevel,
      );

      if (mounted) {
        widget.onRoleUpdated?.call();
        ToastUtil.show(context, AppLocalizations.of(context).updateRoleSuccess);
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).updateRoleFailed}: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  /// 获取医生名字（根据当前语言）
  String _getDoctorName(BuildContext context) {
    if (widget.user.doctorInfo?.name == null) {
      return AppLocalizations.of(context).unknown;
    }

    // 获取当前语言代码
    final locale = Localizations.localeOf(context);
    final languageCode = locale.languageCode;

    // 根据当前语言获取医生名字
    final doctorName =
        widget.user.doctorInfo!.name[languageCode] ??
        widget.user.doctorInfo!.name['zh'] ??
        widget.user.doctorInfo!.name['en'] ??
        AppLocalizations.of(context).unknown;

    return doctorName;
  }

  /// 获取本地化的角色信息
  List<_RoleInfo> _getLocalizedRoles(BuildContext context) {
    final roles = <_RoleInfo>[];

    if (widget.user.isAdmin) {
      roles.add(
        _RoleInfo(
          name: AppLocalizations.of(context).roleAdmin,
          color: AppColors.error,
        ),
      );
    }

    if (widget.user.isDoctor) {
      roles.add(
        _RoleInfo(
          name: AppLocalizations.of(context).roleDoctor,
          color: AppColors.info,
        ),
      );
    }

    if (widget.user.isReferrer) {
      roles.add(
        _RoleInfo(
          name: AppLocalizations.of(context).roleReferrer,
          color: AppColors.warning,
        ),
      );
    }

    if (roles.isEmpty) {
      roles.add(
        _RoleInfo(
          name: AppLocalizations.of(context).roleNormalUser,
          color: AppColors.textSecondaryStatic,
        ),
      );
    }

    return roles;
  }

  /// 显示重置密码对话框
  void _showResetPasswordDialog() {
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).resetPassword),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).newPassword,
                hintText: AppLocalizations.of(context).pleaseEnterNewPassword,
                helperText: AppLocalizations.of(context).passwordLength,
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () => _resetPassword(context, passwordController.text),
            child: Text(AppLocalizations.of(context).confirm),
          ),
        ],
      ),
    );
  }

  /// 重置密码
  Future<void> _resetPassword(
    BuildContext dialogContext,
    String newPassword,
  ) async {
    if (newPassword.isEmpty) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseEnterNewPassword,
      );
      return;
    }

    if (newPassword.length < 6 || newPassword.length > 20) {
      ToastUtil.show(context, AppLocalizations.of(context).passwordLength);
      return;
    }

    Navigator.of(dialogContext).pop();

    setState(() {
      _isUpdating = true;
    });

    try {
      await _userService.resetUserPassword(widget.user.id, newPassword);

      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).resetPasswordSuccess,
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).resetPasswordFailed}: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }
}

/// 角色信息数据类
class _RoleInfo {
  final String name;
  final Color color;

  const _RoleInfo({required this.name, required this.color});
}
