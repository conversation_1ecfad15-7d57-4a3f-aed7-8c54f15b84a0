import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/admin_user_model.dart';
import '../../../services/admin/user_admin_service.dart';
import '../../../utils/toast_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 用户余额积分管理卡片
class UserBalanceIntegralCard extends StatefulWidget {
  final AdminUserModel user;
  final VoidCallback? onBalanceUpdated;
  final VoidCallback? onIntegralUpdated;

  const UserBalanceIntegralCard({
    super.key,
    required this.user,
    this.onBalanceUpdated,
    this.onIntegralUpdated,
  });

  @override
  State<UserBalanceIntegralCard> createState() =>
      _UserBalanceIntegralCardState();
}

class _UserBalanceIntegralCardState extends State<UserBalanceIntegralCard> {
  final UserAdminService _userService = UserAdminService();
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).balanceIntegralManagement,
                style: FontUtil.createHeadingTextStyle(
                  text: AppLocalizations.of(context).balanceIntegralManagement,
                ).copyWith(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 余额和积分显示
          Row(
            children: [
              Expanded(child: _buildBalanceCard()),
              const SizedBox(width: 12),
              Expanded(child: _buildIntegralCard()),
            ],
          ),

          const SizedBox(height: 16),

          // 操作按钮
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isUpdating ? null : _showAdjustBalanceDialog,
                  icon: Icon(Icons.add_circle_outline, size: 16),
                  label: Text(AppLocalizations.of(context).adjustBalance),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.success,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isUpdating ? null : _showAdjustIntegralDialog,
                  icon: Icon(Icons.stars, size: 16),
                  label: Text(AppLocalizations.of(context).adjustIntegral),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.warning,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建余额卡片
  Widget _buildBalanceCard() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.account_balance_wallet,
            color: AppColors.success,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            '¥${widget.user.money}',
            style:
                FontUtil.createHeadingTextStyle(
                  text: '¥${widget.user.money}',
                ).copyWith(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
          ),
          Text(
            AppLocalizations.of(context).userBalance,
            style:
                FontUtil.createCaptionTextStyle(
                  text: AppLocalizations.of(context).userBalance,
                ).copyWith(
                  fontSize: 12,
                  color: ThemeHelper.getTextSecondary(context),
                ),
          ),
        ],
      ),
    );
  }

  /// 构建积分卡片
  Widget _buildIntegralCard() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(Icons.stars, color: AppColors.warning, size: 24),
          const SizedBox(height: 8),
          Text(
            widget.user.integral.toString(),
            style:
                FontUtil.createHeadingTextStyle(
                  text: widget.user.integral.toString(),
                ).copyWith(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.warning,
                ),
          ),
          Text(
            AppLocalizations.of(context).userIntegral,
            style:
                FontUtil.createCaptionTextStyle(
                  text: AppLocalizations.of(context).userIntegral,
                ).copyWith(
                  fontSize: 12,
                  color: ThemeHelper.getTextSecondary(context),
                ),
          ),
        ],
      ),
    );
  }

  /// 显示调整余额对话框
  void _showAdjustBalanceDialog() {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).adjustBalance),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
                signed: true,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
              ],
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).adjustAmount,
                hintText: AppLocalizations.of(context).pleaseEnterAmount,
                helperText: AppLocalizations.of(context).positiveForIncrease,
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).adjustReason,
                hintText: AppLocalizations.of(context).pleaseEnterReason,
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () => _adjustBalance(
              context,
              amountController.text,
              reasonController.text,
            ),
            child: Text(AppLocalizations.of(context).confirm),
          ),
        ],
      ),
    );
  }

  /// 显示调整积分对话框
  void _showAdjustIntegralDialog() {
    final amountController = TextEditingController();
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).adjustIntegral),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              keyboardType: const TextInputType.numberWithOptions(signed: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^-?\d*')),
              ],
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).adjustAmount,
                hintText: AppLocalizations.of(context).pleaseEnterAmount,
                helperText: AppLocalizations.of(context).positiveForIncrease,
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).adjustReason,
                hintText: AppLocalizations.of(context).pleaseEnterReason,
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () => _adjustIntegral(
              context,
              amountController.text,
              reasonController.text,
            ),
            child: Text(AppLocalizations.of(context).confirm),
          ),
        ],
      ),
    );
  }

  /// 调整余额
  Future<void> _adjustBalance(
    BuildContext dialogContext,
    String amountStr,
    String reason,
  ) async {
    if (amountStr.isEmpty || reason.isEmpty) {
      ToastUtil.show(context, AppLocalizations.of(context).pleaseCompleteInfo);
      return;
    }

    final amount = double.tryParse(amountStr);
    if (amount == null) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseEnterValidAmount,
      );
      return;
    }

    Navigator.of(dialogContext).pop();

    setState(() {
      _isUpdating = true;
    });

    try {
      await _userService.adjustUserBalance(widget.user.id, amount, reason);

      if (mounted) {
        widget.onBalanceUpdated?.call();
        ToastUtil.show(
          context,
          AppLocalizations.of(context).adjustBalanceSuccess,
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).adjustBalanceFailed}: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  /// 调整积分
  Future<void> _adjustIntegral(
    BuildContext dialogContext,
    String amountStr,
    String reason,
  ) async {
    if (amountStr.isEmpty || reason.isEmpty) {
      ToastUtil.show(context, AppLocalizations.of(context).pleaseCompleteInfo);
      return;
    }

    final amount = int.tryParse(amountStr);
    if (amount == null) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseEnterValidPoints,
      );
      return;
    }

    Navigator.of(dialogContext).pop();

    setState(() {
      _isUpdating = true;
    });

    try {
      await _userService.adjustUserIntegral(widget.user.id, amount, reason);

      if (mounted) {
        widget.onIntegralUpdated?.call();
        ToastUtil.show(
          context,
          AppLocalizations.of(context).adjustIntegralSuccess,
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).adjustIntegralFailed}: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }
}
