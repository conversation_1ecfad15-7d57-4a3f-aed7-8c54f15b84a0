import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_product_model.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 管理员产品统计信息卡片
class AdminProductStatisticsCard extends StatefulWidget {
  final ProductStatisticsModel statistics;

  const AdminProductStatisticsCard({super.key, required this.statistics});

  @override
  State<AdminProductStatisticsCard> createState() =>
      _AdminProductStatisticsCardState();
}

class _AdminProductStatisticsCardState extends State<AdminProductStatisticsCard>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 可点击的标题栏
          InkWell(
            onTap: _toggleExpanded,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    color: AppColors.primary,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context).reviewStatistics,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: ThemeHelper.getTextPrimary(context),
                    ),
                  ),
                  const Spacer(),
                  // 核心数据预览（折叠时显示）
                  if (!_isExpanded) ...[
                    _buildCompactStats(context),
                    const SizedBox(width: 8),
                  ],
                  AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: ThemeHelper.getTextSecondary(context),
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 可展开的详细统计
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: _buildDetailedStatistics(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建紧凑的统计预览（折叠时显示）
  Widget _buildCompactStats(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildMiniStat(
          '${widget.statistics.totalProducts}',
          AppLocalizations.of(context).total,
          AppColors.primary,
        ),
        const SizedBox(width: 16),
        _buildMiniStat(
          '${widget.statistics.pendingReview}',
          AppLocalizations.of(context).pending,
          const Color(0xFFF39C12),
        ),
      ],
    );
  }

  /// 构建迷你统计项
  Widget _buildMiniStat(String value, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 0.5),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              color: ThemeHelper.getTextHint(context),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建详细统计信息（展开时显示）
  Widget _buildDetailedStatistics(BuildContext context) {
    return Column(
      children: [
        // 使用2x3网格布局，更紧凑
        Row(
          children: [
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).totalProducts,
                value: widget.statistics.totalProducts.toString(),
                icon: Icons.inventory_2_outlined,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).totalSales,
                value: '¥${widget.statistics.totalSales.toStringAsFixed(0)}',
                icon: Icons.monetization_on_outlined,
                color: const Color(0xFF27AE60),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).totalOrders,
                value: widget.statistics.totalOrders.toString(),
                icon: Icons.shopping_cart_outlined,
                color: const Color(0xFF3498DB),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).pending,
                value: widget.statistics.pendingReview.toString(),
                icon: Icons.pending_outlined,
                color: const Color(0xFFF39C12),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).approvedProducts,
                value: widget.statistics.approvedProducts.toString(),
                icon: Icons.check_circle_outline,
                color: const Color(0xFF27AE60),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).rejectedProducts,
                value: widget.statistics.rejectedProducts.toString(),
                icon: Icons.cancel_outlined,
                color: const Color(0xFFE74C3C),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建紧凑的统计项（用于详细视图）
  Widget _buildCompactStatItem(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.15), width: 0.5),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: ThemeHelper.getTextSecondary(context),
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
