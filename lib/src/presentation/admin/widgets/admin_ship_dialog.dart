import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/admin/admin_order_service.dart';
import '../../../services/language_service.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 管理员发货对话框
class AdminShipDialog extends StatefulWidget {
  final ProductOrderModel order;
  final VoidCallback? onShipped;

  const AdminShipDialog({super.key, required this.order, this.onShipped});

  @override
  State<AdminShipDialog> createState() => _AdminShipDialogState();
}

class _AdminShipDialogState extends State<AdminShipDialog> {
  final AdminOrderService _adminOrderService = AdminOrderService();
  final _formKey = GlobalKey<FormState>();
  final _trackingNumberController = TextEditingController();
  final _shippingNoteController = TextEditingController();

  String? _selectedCompany;
  bool _isLoading = false;

  // 快递公司选项
  final List<Map<String, String>> _shippingCompanies = [
    {'value': '顺丰速运', 'label': '顺丰速运'},
    {'value': '中通快递', 'label': '中通快递'},
    {'value': '圆通速递', 'label': '圆通速递'},
    {'value': '申通快递', 'label': '申通快递'},
    {'value': '韵达速递', 'label': '韵达速递'},
    {'value': '百世快递', 'label': '百世快递'},
    {'value': '德邦快递', 'label': '德邦快递'},
    {'value': 'EMS', 'label': 'EMS'},
    {'value': '京东物流', 'label': '京东物流'},
    {'value': '其他', 'label': '其他'},
  ];

  @override
  void dispose() {
    _trackingNumberController.dispose();
    _shippingNoteController.dispose();
    super.dispose();
  }

  /// 获取本地化的产品名称
  String _getLocalizedProductName() {
    final languageCode = LanguageService().getCurrentLanguageCode();
    return widget.order.getProductName(languageCode);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 400,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        decoration: BoxDecoration(
          color: ThemeHelper.getCardBackground(context),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            _buildHeader(),

            // 内容区域
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 订单信息卡片
                      _buildOrderInfoCard(),
                      const SizedBox(height: 24),

                      // 发货信息标题
                      _buildSectionTitle(
                        AppLocalizations.of(context).shippingInfo,
                        Icons.local_shipping_outlined,
                      ),
                      const SizedBox(height: 16),

                      // 快递单号输入
                      _buildTrackingNumberField(),
                      const SizedBox(height: 20),

                      // 快递公司选择
                      _buildShippingCompanyField(),
                      const SizedBox(height: 20),

                      // 发货备注
                      _buildShippingNoteField(),
                      const SizedBox(height: 32),

                      // 操作按钮
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建标题栏
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.03),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.local_shipping,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              AppLocalizations.of(context).adminShipment,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
          ),
          IconButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            icon: Icon(
              Icons.close,
              color: ThemeHelper.getTextSecondary(context),
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.transparent,
              padding: EdgeInsets.zero,
              minimumSize: const Size(32, 32),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建区域标题
  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppColors.primary, size: 18),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
      ],
    );
  }

  /// 构建订单信息卡片
  Widget _buildOrderInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.receipt_long, color: AppColors.primary, size: 16),
              const SizedBox(width: 6),
              Text(
                AppLocalizations.of(context).orderInfoTitle,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildOrderInfoRow(
            AppLocalizations.of(context).orderNumber,
            widget.order.orderSn,
          ),
          _buildOrderInfoRow(
            AppLocalizations.of(context).productLabel,
            _getLocalizedProductName(),
          ),
          _buildOrderInfoRow(
            AppLocalizations.of(context).customer,
            widget.order.userNickname,
          ),
          _buildOrderInfoRow(
            AppLocalizations.of(context).shippingAddress,
            widget.order.shippingAddress ?? '',
            isAddress: true,
          ),
        ],
      ),
    );
  }

  /// 构建订单信息行
  Widget _buildOrderInfoRow(
    String label,
    String value, {
    bool isAddress = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 70,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: ThemeHelper.getTextSecondary(context),
                fontWeight: FontWeight.w500,
              ),
              maxLines: isAddress ? 2 : 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建快递单号输入框
  Widget _buildTrackingNumberField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _trackingNumberController,
        style: TextStyle(
          fontSize: 14,
          color: ThemeHelper.getTextPrimary(context),
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: '${AppLocalizations.of(context).trackingNumber} *',
          labelStyle: TextStyle(
            fontSize: 14,
            color: ThemeHelper.getTextHint(context),
            fontWeight: FontWeight.w500,
          ),
          hintText: AppLocalizations.of(context).enterTrackingNumber,
          hintStyle: TextStyle(
            fontSize: 14,
            color: ThemeHelper.getTextHint(context),
          ),
          filled: true,
          fillColor: ThemeHelper.getCardBackground(context),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getBorder(context),
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getBorder(context),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.primary, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red, width: 1),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red, width: 2),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          prefixIcon: Icon(
            Icons.confirmation_number_outlined,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return AppLocalizations.of(context).trackingNumberRequired;
          }
          return null;
        },
      ),
    );
  }

  /// 构建快递公司选择框
  Widget _buildShippingCompanyField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedCompany,
        style: TextStyle(
          fontSize: 14,
          color: ThemeHelper.getTextPrimary(context),
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).shippingCompanyLabel,
          labelStyle: TextStyle(
            fontSize: 14,
            color: ThemeHelper.getTextHint(context),
            fontWeight: FontWeight.w500,
          ),
          hintText: AppLocalizations.of(context).shippingCompanyHint,
          hintStyle: TextStyle(
            fontSize: 14,
            color: ThemeHelper.getTextHint(context),
          ),
          filled: true,
          fillColor: ThemeHelper.getCardBackground(context),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getBorder(context),
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getBorder(context),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.primary, width: 2),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          prefixIcon: Icon(
            Icons.business_outlined,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        items: _shippingCompanies.map((company) {
          return DropdownMenuItem<String>(
            value: company['value'],
            child: Text(
              company['label']!,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextPrimary(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedCompany = value;
          });
        },
      ),
    );
  }

  /// 构建发货备注输入框
  Widget _buildShippingNoteField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _shippingNoteController,
        style: TextStyle(
          fontSize: 14,
          color: ThemeHelper.getTextPrimary(context),
          fontWeight: FontWeight.w500,
        ),
        maxLines: 3,
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).shippingNoteLabel,
          labelStyle: TextStyle(
            fontSize: 14,
            color: ThemeHelper.getTextHint(context),
            fontWeight: FontWeight.w500,
          ),
          hintText: AppLocalizations.of(context).shippingNoteHint,
          hintStyle: TextStyle(
            fontSize: 14,
            color: ThemeHelper.getTextHint(context),
          ),
          filled: true,
          fillColor: ThemeHelper.getCardBackground(context),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getBorder(context),
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getBorder(context),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.primary, width: 2),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          prefixIcon: Icon(
            Icons.note_outlined,
            color: AppColors.primary,
            size: 20,
          ),
          alignLabelWithHint: true,
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        // 取消按钮
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: ThemeHelper.getBorder(context)),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        // 确认发货按钮
        Expanded(
          flex: 2,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary,
                  AppColors.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _handleShip,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      AppLocalizations.of(context).confirmShip,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ),
      ],
    );
  }

  /// 处理发货
  Future<void> _handleShip() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _adminOrderService.adminShipOrder(
        orderId: widget.order.id,
        trackingNumber: _trackingNumberController.text.trim(),
        shippingCompany: _selectedCompany,
        shippingNote: _shippingNoteController.text.trim().isNotEmpty
            ? _shippingNoteController.text.trim()
            : null,
      );

      if (mounted) {
        Navigator.pop(context);
        ToastUtil.show(context, AppLocalizations.of(context).shipmentSuccess);
        if (widget.onShipped != null) {
          widget.onShipped!();
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).shipmentFailedWithError(e.toString()),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
