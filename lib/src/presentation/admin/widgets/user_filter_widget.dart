import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 用户筛选组件
class UserFilterWidget extends StatefulWidget {
  final Function({
    String? keyword,
    int? status,
    int? role,
    int? gender,
    int? source,
  })
  onFiltersChanged;
  final Function(String) onSearch;

  // 添加初始筛选状态参数
  final String? initialKeyword;
  final int? initialStatus;
  final int? initialRole;
  final int? initialGender;
  final int? initialSource;

  const UserFilterWidget({
    super.key,
    required this.onFiltersChanged,
    required this.onSearch,
    this.initialKeyword,
    this.initialStatus,
    this.initialRole,
    this.initialGender,
    this.initialSource,
  });

  @override
  State<UserFilterWidget> createState() => _UserFilterWidgetState();
}

class _UserFilterWidgetState extends State<UserFilterWidget> {
  final TextEditingController _searchController = TextEditingController();

  int? _selectedStatus;
  int? _selectedRole;
  int? _selectedGender;
  int? _selectedSource;

  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    // 使用初始值设置筛选状态
    _updateFilterStates();
  }

  @override
  void didUpdateWidget(UserFilterWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当父组件传递的筛选状态改变时，更新内部状态
    if (oldWidget.initialKeyword != widget.initialKeyword ||
        oldWidget.initialStatus != widget.initialStatus ||
        oldWidget.initialRole != widget.initialRole ||
        oldWidget.initialGender != widget.initialGender ||
        oldWidget.initialSource != widget.initialSource) {
      _updateFilterStates();
    }
  }

  /// 更新筛选状态
  void _updateFilterStates() {
    _searchController.text = widget.initialKeyword ?? '';
    _selectedStatus = widget.initialStatus;
    _selectedRole = widget.initialRole;
    _selectedGender = widget.initialGender;
    _selectedSource = widget.initialSource;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 搜索框
          _buildSearchField(),

          // 展开/收起按钮
          const SizedBox(height: 12),
          _buildExpandButton(),

          // 筛选选项
          if (_isExpanded) ...[
            const SizedBox(height: 16),
            _buildFilterOptions(),
          ],
        ],
      ),
    );
  }

  /// 构建搜索框
  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      style: FontUtil.createBodyTextStyle(text: _searchController.text),
      decoration: InputDecoration(
        hintText: AppLocalizations.of(context).searchByNicknamePhoneId,
        hintStyle: FontUtil.createBodyTextStyle(
          text: AppLocalizations.of(context).searchByNicknamePhoneId,
        ).copyWith(color: ThemeHelper.getTextSecondary(context)),
        prefixIcon: Icon(
          Icons.search,
          color: ThemeHelper.getTextSecondary(context),
        ),
        suffixIcon: _searchController.text.isNotEmpty
            ? IconButton(
                icon: Icon(
                  Icons.clear,
                  color: ThemeHelper.getTextSecondary(context),
                ),
                onPressed: () {
                  _searchController.clear();
                  widget.onSearch('');
                },
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: ThemeHelper.getTextSecondary(context).withValues(alpha: 0.3),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: ThemeHelper.getTextSecondary(context).withValues(alpha: 0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      onSubmitted: widget.onSearch,
      onChanged: (value) {
        setState(() {}); // 更新清除按钮显示状态
      },
    );
  }

  /// 构建展开按钮
  Widget _buildExpandButton() {
    return InkWell(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _isExpanded
                  ? AppLocalizations.of(context).collapseFilters
                  : AppLocalizations.of(context).expandFilters,
              style:
                  FontUtil.createBodyTextStyle(
                    text: _isExpanded
                        ? AppLocalizations.of(context).collapseFilters
                        : AppLocalizations.of(context).expandFilters,
                  ).copyWith(
                    fontSize: 14,
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
            ),
            const SizedBox(width: 4),
            // 显示当前筛选状态
            if (!_isExpanded && _hasActiveFilters()) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  _getActiveFiltersCount().toString(),
                  style:
                      FontUtil.createCaptionTextStyle(
                        text: _getActiveFiltersCount().toString(),
                      ).copyWith(
                        fontSize: 10,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
              const SizedBox(width: 4),
            ],
            Icon(
              _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
              color: AppColors.primary,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  /// 检查是否有活跃的筛选条件
  bool _hasActiveFilters() {
    return _selectedStatus != null ||
        _selectedRole != null ||
        _selectedGender != null ||
        _selectedSource != null ||
        _searchController.text.trim().isNotEmpty;
  }

  /// 获取活跃筛选条件的数量
  int _getActiveFiltersCount() {
    int count = 0;
    if (_selectedStatus != null) count++;
    if (_selectedRole != null) count++;
    if (_selectedGender != null) count++;
    if (_selectedSource != null) count++;
    if (_searchController.text.trim().isNotEmpty) count++;
    return count;
  }

  /// 构建筛选选项
  Widget _buildFilterOptions() {
    return Column(
      children: [
        // 第一行：状态和角色
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(child: _buildStatusFilter()),
              const SizedBox(width: 12),
              Expanded(child: _buildRoleFilter()),
            ],
          ),
        ),

        const SizedBox(height: 12),

        // 第二行：性别和注册来源
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(child: _buildGenderFilter()),
              const SizedBox(width: 12),
              Expanded(child: _buildSourceFilter()),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // 操作按钮
        _buildActionButtons(),
      ],
    );
  }

  /// 构建状态筛选
  Widget _buildStatusFilter() {
    return _buildDropdown<int?>(
      label: AppLocalizations.of(context).userStatus,
      value: _selectedStatus,
      items: [
        DropdownMenuItem(
          value: null,
          child: Text(AppLocalizations.of(context).allStatus),
        ),
        DropdownMenuItem(
          value: 1,
          child: Text(AppLocalizations.of(context).enabledStatus),
        ),
        DropdownMenuItem(
          value: 0,
          child: Text(AppLocalizations.of(context).disabledStatus),
        ),
      ],
      onChanged: (value) {
        setState(() {
          _selectedStatus = value;
        });
      },
    );
  }

  /// 构建角色筛选
  Widget _buildRoleFilter() {
    return _buildDropdown<int?>(
      label: AppLocalizations.of(context).userRole,
      value: _selectedRole,
      items: [
        DropdownMenuItem(
          value: null,
          child: Text(AppLocalizations.of(context).allRoles),
        ),
        DropdownMenuItem(
          value: 0,
          child: Text(AppLocalizations.of(context).normalUser),
        ),
        DropdownMenuItem(
          value: 1,
          child: Text(AppLocalizations.of(context).adminUser),
        ),
        DropdownMenuItem(
          value: 2,
          child: Text(AppLocalizations.of(context).doctorUser),
        ),
      ],
      onChanged: (value) {
        setState(() {
          _selectedRole = value;
        });
      },
    );
  }

  /// 构建性别筛选
  Widget _buildGenderFilter() {
    return _buildDropdown<int?>(
      label: AppLocalizations.of(context).userGender,
      value: _selectedGender,
      items: [
        DropdownMenuItem(
          value: null,
          child: Text(AppLocalizations.of(context).allStatus),
        ),
        DropdownMenuItem(
          value: 1,
          child: Text(AppLocalizations.of(context).male),
        ),
        DropdownMenuItem(
          value: 2,
          child: Text(AppLocalizations.of(context).female),
        ),
        DropdownMenuItem(
          value: 0,
          child: Text(AppLocalizations.of(context).unknown),
        ),
      ],
      onChanged: (value) {
        setState(() {
          _selectedGender = value;
        });
      },
    );
  }

  /// 构建注册来源筛选
  Widget _buildSourceFilter() {
    return _buildDropdown<int?>(
      label: AppLocalizations.of(context).registerSource,
      value: _selectedSource,
      items: [
        DropdownMenuItem(
          value: null,
          child: Text(AppLocalizations.of(context).allStatus),
        ),
        DropdownMenuItem(
          value: 1,
          child: Text(AppLocalizations.of(context).appSource),
        ),
        DropdownMenuItem(
          value: 2,
          child: Text(AppLocalizations.of(context).miniProgramSource),
        ),
      ],
      onChanged: (value) {
        setState(() {
          _selectedSource = value;
        });
      },
    );
  }

  /// 构建下拉框
  Widget _buildDropdown<T>({
    required String label,
    required T value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontUtil.createCaptionTextStyle(text: label).copyWith(
            fontSize: 12,
            color: ThemeHelper.getTextSecondary(context),
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          onChanged: onChanged,
          style: FontUtil.createBodyTextStyle(text: ''),
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: ThemeHelper.getTextSecondary(
                  context,
                ).withValues(alpha: 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: ThemeHelper.getTextSecondary(
                  context,
                ).withValues(alpha: 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
            isDense: true,
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        // 重置按钮
        Expanded(
          child: OutlinedButton(
            onPressed: _resetFilters,
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              AppLocalizations.of(context).reset,
              style: FontUtil.createBodyTextStyle(
                text: AppLocalizations.of(context).reset,
              ).copyWith(color: AppColors.primary, fontWeight: FontWeight.w500),
            ),
          ),
        ),

        const SizedBox(width: 12),

        // 应用筛选按钮
        Expanded(
          child: ElevatedButton(
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              AppLocalizations.of(context).apply,
              style: FontUtil.createBodyTextStyle(
                text: AppLocalizations.of(context).apply,
              ).copyWith(color: Colors.white, fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ],
    );
  }

  /// 重置筛选
  void _resetFilters() {
    setState(() {
      _selectedStatus = null;
      _selectedRole = null;
      _selectedGender = null;
      _selectedSource = null;
      _searchController.clear();
    });

    widget.onFiltersChanged(
      keyword: null,
      status: null,
      role: null,
      gender: null,
      source: null,
    );

    // 重置后收起筛选面板
    setState(() {
      _isExpanded = false;
    });
  }

  /// 应用筛选
  void _applyFilters() {
    widget.onFiltersChanged(
      keyword: _searchController.text.trim().isEmpty
          ? null
          : _searchController.text.trim(),
      status: _selectedStatus,
      role: _selectedRole,
      gender: _selectedGender,
      source: _selectedSource,
    );

    // 应用筛选后收起筛选面板
    setState(() {
      _isExpanded = false;
    });
  }
}
