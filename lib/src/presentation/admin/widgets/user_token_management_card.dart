import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/admin_user_model.dart';
import '../../../services/admin/user_admin_service.dart';
import '../../../utils/toast_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 用户令牌管理卡片
class UserTokenManagementCard extends StatefulWidget {
  final AdminUserModel user;
  final VoidCallback? onTokensUpdated;

  const UserTokenManagementCard({
    super.key,
    required this.user,
    this.onTokensUpdated,
  });

  @override
  State<UserTokenManagementCard> createState() =>
      _UserTokenManagementCardState();
}

class _UserTokenManagementCardState extends State<UserTokenManagementCard> {
  final UserAdminService _userService = UserAdminService();
  bool _isLoading = false;
  bool _isUpdating = false;
  List<UserTokenModel> _tokens = [];

  @override
  void initState() {
    super.initState();
    _loadUserTokens();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              Icon(Icons.security, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).userTokens,
                style: FontUtil.createHeadingTextStyle(
                  text: AppLocalizations.of(context).userTokens,
                ).copyWith(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const Spacer(),
              // 刷新按钮
              IconButton(
                onPressed: _isLoading ? null : _loadUserTokens,
                icon: Icon(Icons.refresh, size: 18, color: AppColors.primary),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 令牌统计
          _buildTokenStats(),

          const SizedBox(height: 16),

          // 令牌列表
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildTokenList(),

          const SizedBox(height: 16),

          // 操作按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: (_isUpdating || _tokens.isEmpty)
                  ? null
                  : _showClearAllTokensDialog,
              icon: Icon(Icons.clear_all, size: 16),
              label: Text(AppLocalizations.of(context).clearAllTokens),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建令牌统计
  Widget _buildTokenStats() {
    final validTokens = _tokens.where((token) => !token.isExpired).length;
    final expiredTokens = _tokens.where((token) => token.isExpired).length;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                Text(
                  _tokens.length.toString(),
                  style:
                      FontUtil.createHeadingTextStyle(
                        text: _tokens.length.toString(),
                      ).copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.info,
                      ),
                ),
                Text(
                  AppLocalizations.of(context).totalTokens,
                  style:
                      FontUtil.createCaptionTextStyle(
                        text: AppLocalizations.of(context).totalTokens,
                      ).copyWith(
                        fontSize: 12,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                ),
              ],
            ),
          ),
          Container(
            width: 1,
            height: 30,
            color: ThemeHelper.getTextSecondary(context).withValues(alpha: 0.3),
          ),
          Expanded(
            child: Column(
              children: [
                Text(
                  validTokens.toString(),
                  style:
                      FontUtil.createHeadingTextStyle(
                        text: validTokens.toString(),
                      ).copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.success,
                      ),
                ),
                Text(
                  AppLocalizations.of(context).valid,
                  style:
                      FontUtil.createCaptionTextStyle(
                        text: AppLocalizations.of(context).valid,
                      ).copyWith(
                        fontSize: 12,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                ),
              ],
            ),
          ),
          Container(
            width: 1,
            height: 30,
            color: ThemeHelper.getTextSecondary(context).withValues(alpha: 0.3),
          ),
          Expanded(
            child: Column(
              children: [
                Text(
                  expiredTokens.toString(),
                  style:
                      FontUtil.createHeadingTextStyle(
                        text: expiredTokens.toString(),
                      ).copyWith(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                ),
                Text(
                  AppLocalizations.of(context).expired,
                  style:
                      FontUtil.createCaptionTextStyle(
                        text: AppLocalizations.of(context).expired,
                      ).copyWith(
                        fontSize: 12,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建令牌列表
  Widget _buildTokenList() {
    if (_tokens.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Center(
          child: Text(
            AppLocalizations.of(context).noTokensFound,
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 14,
            ),
          ),
        ),
      );
    }

    return Column(
      children: _tokens.map((token) => _buildTokenItem(token)).toList(),
    );
  }

  /// 构建令牌项
  Widget _buildTokenItem(UserTokenModel token) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: token.isExpired
            ? AppColors.error.withValues(alpha: 0.05)
            : AppColors.success.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: token.isExpired
              ? AppColors.error.withValues(alpha: 0.2)
              : AppColors.success.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 设备类型图标
          Icon(
            _getDeviceIcon(token.deviceType),
            color: token.isExpired ? AppColors.error : AppColors.success,
            size: 20,
          ),
          const SizedBox(width: 12),

          // 令牌信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  token.deviceType,
                  style: FontUtil.createBodyTextStyle(
                    text: token.deviceType,
                  ).copyWith(fontSize: 14, fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 2),
                Text(
                  '${AppLocalizations.of(context).createTime}: ${token.createdAt.split(' ')[0]}',
                  style:
                      FontUtil.createCaptionTextStyle(
                        text:
                            '${AppLocalizations.of(context).createTime}: ${token.createdAt.split(' ')[0]}',
                      ).copyWith(
                        fontSize: 12,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                ),
                Text(
                  '${AppLocalizations.of(context).expiryTime}: ${token.expiresAt.split(' ')[0]}',
                  style:
                      FontUtil.createCaptionTextStyle(
                        text:
                            '${AppLocalizations.of(context).expiryTime}: ${token.expiresAt.split(' ')[0]}',
                      ).copyWith(
                        fontSize: 12,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                ),
              ],
            ),
          ),

          // 状态和操作
          Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: token.isExpired
                      ? AppColors.error.withValues(alpha: 0.1)
                      : AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  token.isExpired
                      ? AppLocalizations.of(context).expired
                      : AppLocalizations.of(context).valid,
                  style:
                      FontUtil.createCaptionTextStyle(
                        text: token.isExpired
                            ? AppLocalizations.of(context).expired
                            : AppLocalizations.of(context).valid,
                      ).copyWith(
                        fontSize: 10,
                        color: token.isExpired
                            ? AppColors.error
                            : AppColors.success,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ),
              const SizedBox(height: 4),
              if (!token.isExpired)
                IconButton(
                  onPressed: _isUpdating
                      ? null
                      : () => _clearSingleToken(token),
                  icon: Icon(
                    Icons.delete_outline,
                    size: 16,
                    color: AppColors.error,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                  padding: EdgeInsets.zero,
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// 获取设备类型图标
  IconData _getDeviceIcon(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'android_app':
      case 'android':
        return Icons.android;
      case 'ios_app':
      case 'ios':
        return Icons.phone_iphone;
      case 'web':
        return Icons.web;
      default:
        return Icons.devices;
    }
  }

  /// 加载用户令牌
  Future<void> _loadUserTokens() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final tokens = await _userService.getUserTokens(widget.user.id);

      if (mounted) {
        setState(() {
          _tokens = tokens;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).loadTokensFailed}: $e',
        );
      }
    }
  }

  /// 显示清除所有令牌对话框
  void _showClearAllTokensDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).confirmClearTokens),
        content: Text(AppLocalizations.of(context).clearTokensWarning),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _clearAllTokens();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: Text(
              AppLocalizations.of(context).confirm,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// 清除所有令牌
  Future<void> _clearAllTokens() async {
    setState(() {
      _isUpdating = true;
    });

    try {
      await _userService.clearUserAllTokens(widget.user.id);

      if (mounted) {
        widget.onTokensUpdated?.call();
        await _loadUserTokens();
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).tokenClearSuccess,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).clearTokensFailed}: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  /// 清除单个令牌
  Future<void> _clearSingleToken(UserTokenModel token) async {
    setState(() {
      _isUpdating = true;
    });

    try {
      await _userService.clearUserToken(widget.user.id, token.id);

      if (mounted) {
        widget.onTokensUpdated?.call();
        await _loadUserTokens();
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).tokenClearSuccess,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).tokenClearFailed}: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }
}
