import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../config/api/api_config.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/admin_user_model.dart';
import '../../../services/admin/user_admin_service.dart';
import '../../../utils/toast_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 用户基本信息卡片
class UserDetailInfoCard extends StatefulWidget {
  final AdminUserModel user;
  final Function(bool)? onStatusChanged;
  final VoidCallback? onUserUpdated;

  const UserDetailInfoCard({
    super.key,
    required this.user,
    this.onStatusChanged,
    this.onUserUpdated,
  });

  @override
  State<UserDetailInfoCard> createState() => _UserDetailInfoCardState();
}

class _UserDetailInfoCardState extends State<UserDetailInfoCard> {
  final UserAdminService _userService = UserAdminService();
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              Icon(Icons.person, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                AppLocalizations.of(context).userDetail,
                style: FontUtil.createHeadingTextStyle(
                  text: AppLocalizations.of(context).userDetail,
                ).copyWith(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const Spacer(),
              // 编辑按钮
              TextButton.icon(
                onPressed: _isUpdating ? null : _showEditDialog,
                icon: Icon(Icons.edit, size: 16, color: AppColors.primary),
                label: Text(
                  AppLocalizations.of(context).editUser,
                  style: FontUtil.createBodyTextStyle(
                    text: AppLocalizations.of(context).editUser,
                  ).copyWith(fontSize: 12, color: AppColors.primary),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 用户头像和基本信息
          Row(
            children: [
              // 头像
              _buildUserAvatar(),
              const SizedBox(width: 16),

              // 基本信息
              Expanded(child: _buildBasicInfo()),

              // 状态开关
              _buildStatusSwitch(),
            ],
          ),

          const SizedBox(height: 16),

          // 详细信息
          _buildDetailedInfo(),
        ],
      ),
    );
  }

  /// 构建用户头像
  Widget _buildUserAvatar() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        color: AppColors.primary.withValues(alpha: 0.1),
      ),
      child: widget.user.avatar?.isNotEmpty == true
          ? ClipRRect(
              borderRadius: BorderRadius.circular(30),
              child: _buildNetworkImage(widget.user.avatar!),
            )
          : _buildDefaultAvatar(),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Icon(Icons.person, color: AppColors.primary, size: 32);
  }

  /// 构建网络图片
  Widget _buildNetworkImage(String avatarUrl) {
    // 检查URL是否有效
    if (avatarUrl.trim().isEmpty) {
      print('UserDetailInfoCard: 头像URL为空');
      return _buildDefaultAvatar();
    }

    final fullUrl = ApiConfig.buildUserAvatarUrl(avatarUrl);
    print('UserDetailInfoCard: 加载头像URL: $fullUrl (原始: $avatarUrl)');

    return Image.network(
      fullUrl,
      width: 60,
      height: 60,
      fit: BoxFit.cover,
      headers: const {
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      errorBuilder: (context, error, stackTrace) {
        print('UserDetailInfoCard: 头像加载失败: $error');
        print('UserDetailInfoCard: 错误堆栈: $stackTrace');
        return _buildDefaultAvatar();
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          print('UserDetailInfoCard: 头像加载成功');
          return child;
        }
        return Center(
          child: CircularProgressIndicator(
            value: loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                : null,
            strokeWidth: 2,
          ),
        );
      },
    );
  }

  /// 构建基本信息
  Widget _buildBasicInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 昵称
        Text(
          widget.user.nickname,
          style: FontUtil.createHeadingTextStyle(
            text: widget.user.nickname,
          ).copyWith(fontSize: 18, fontWeight: FontWeight.w600),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),

        const SizedBox(height: 4),

        // 电话
        Text(
          widget.user.phone,
          style: FontUtil.createBodyTextStyle(text: widget.user.phone).copyWith(
            fontSize: 14,
            color: ThemeHelper.getTextSecondary(context),
          ),
        ),

        const SizedBox(height: 4),

        // ID
        Text(
          'ID: ${widget.user.id}',
          style: FontUtil.createCaptionTextStyle(text: 'ID: ${widget.user.id}')
              .copyWith(
                fontSize: 12,
                color: ThemeHelper.getTextSecondary(context),
              ),
        ),
      ],
    );
  }

  /// 构建状态开关
  Widget _buildStatusSwitch() {
    return Column(
      children: [
        Switch(
          value: widget.user.status,
          onChanged: _isUpdating ? null : _handleStatusChange,
          activeColor: AppColors.success,
          inactiveThumbColor: AppColors.error,
        ),
        Text(
          widget.user.status
              ? AppLocalizations.of(context).enabledStatus
              : AppLocalizations.of(context).disabledStatus,
          style:
              FontUtil.createCaptionTextStyle(
                text: widget.user.status
                    ? AppLocalizations.of(context).enabledStatus
                    : AppLocalizations.of(context).disabledStatus,
              ).copyWith(
                fontSize: 10,
                color: widget.user.status ? AppColors.success : AppColors.error,
              ),
        ),
      ],
    );
  }

  /// 构建详细信息
  Widget _buildDetailedInfo() {
    return Column(
      children: [
        // 分割线
        Container(
          height: 1,
          margin: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: ThemeHelper.getTextSecondary(
                  context,
                ).withValues(alpha: 0.2),
                width: 1,
                style: BorderStyle.solid,
              ),
            ),
          ),
        ),

        // 详细信息网格
        _buildInfoGrid(),
      ],
    );
  }

  /// 构建信息网格
  Widget _buildInfoGrid() {
    final infoItems = [
      _InfoItem(
        label: AppLocalizations.of(context).userGender,
        value: widget.user.getSexText(context),
        icon: widget.user.sex == 1
            ? Icons.male
            : widget.user.sex == 2
            ? Icons.female
            : Icons.help_outline,
      ),
      _InfoItem(
        label: AppLocalizations.of(context).userBirthday,
        value: widget.user.birthday ?? AppLocalizations.of(context).unknown,
        icon: Icons.cake,
      ),
      _InfoItem(
        label: AppLocalizations.of(context).registerSource,
        value: widget.user.getRegisterSourceText(context),
        icon: widget.user.registerSource == 1 ? Icons.phone_android : Icons.web,
      ),
      _InfoItem(
        label: AppLocalizations.of(context).registrationTime,
        value: widget.user.createdAt.split(' ')[0],
        icon: Icons.access_time,
      ),
      _InfoItem(
        label: AppLocalizations.of(context).lastLoginTime,
        value:
            widget.user.loginAt?.split(' ')[0] ??
            AppLocalizations.of(context).unknown,
        icon: Icons.login,
      ),
      _InfoItem(
        label: AppLocalizations.of(context).ipAddress,
        value: widget.user.ip ?? AppLocalizations.of(context).unknown,
        icon: Icons.location_on,
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 8,
      ),
      itemCount: infoItems.length,
      itemBuilder: (context, index) {
        final item = infoItems[index];
        return _buildInfoItem(item);
      },
    );
  }

  /// 构建单个信息项
  Widget _buildInfoItem(_InfoItem item) {
    return Row(
      children: [
        Icon(item.icon, size: 16, color: ThemeHelper.getTextSecondary(context)),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                item.label,
                style: FontUtil.createCaptionTextStyle(text: item.label)
                    .copyWith(
                      fontSize: 10,
                      color: ThemeHelper.getTextSecondary(context),
                    ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                item.value,
                style: FontUtil.createBodyTextStyle(
                  text: item.value,
                ).copyWith(fontSize: 12, fontWeight: FontWeight.w500),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 处理状态变更
  Future<void> _handleStatusChange(bool newStatus) async {
    setState(() {
      _isUpdating = true;
    });

    try {
      await _userService.updateUserStatus(widget.user.id, newStatus);

      if (mounted) {
        widget.onStatusChanged?.call(newStatus);
        ToastUtil.show(
          context,
          newStatus
              ? AppLocalizations.of(context).enableUserSuccess
              : AppLocalizations.of(context).disableUserSuccess,
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).updateUserFailed}: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  /// 显示编辑对话框
  void _showEditDialog() {
    final nicknameController = TextEditingController(
      text: widget.user.nickname,
    );
    final phoneController = TextEditingController(text: widget.user.phone);
    String? selectedBirthday = widget.user.birthday;
    int selectedGender = widget.user.sex;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(AppLocalizations.of(context).editUserInfo),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 昵称
                TextField(
                  controller: nicknameController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).userNicknameLabel,
                    hintText: AppLocalizations.of(context).pleaseEnterNickname,
                    border: const OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // 电话
                TextField(
                  controller: phoneController,
                  keyboardType: TextInputType.phone,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).userPhoneLabel,
                    hintText: AppLocalizations.of(context).pleaseEnterPhone,
                    border: const OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // 生日
                InkWell(
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: selectedBirthday != null
                          ? DateTime.tryParse(selectedBirthday!) ??
                                DateTime.now()
                          : DateTime.now(),
                      firstDate: DateTime(1900),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() {
                        selectedBirthday = date.toString().split(' ')[0];
                      });
                    }
                  },
                  child: InputDecorator(
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).userBirthdayLabel,
                      border: const OutlineInputBorder(),
                    ),
                    child: Text(
                      selectedBirthday ??
                          AppLocalizations.of(context).selectBirthday,
                      style: TextStyle(
                        color: selectedBirthday != null
                            ? ThemeHelper.getTextPrimary(context)
                            : ThemeHelper.getTextSecondary(context),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // 性别
                DropdownButtonFormField<int>(
                  value: selectedGender,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).userGenderLabel,
                    border: const OutlineInputBorder(),
                  ),
                  items: [
                    DropdownMenuItem(
                      value: 0,
                      child: Text(AppLocalizations.of(context).genderUnknown),
                    ),
                    DropdownMenuItem(
                      value: 1,
                      child: Text(AppLocalizations.of(context).genderMale),
                    ),
                    DropdownMenuItem(
                      value: 2,
                      child: Text(AppLocalizations.of(context).genderFemale),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedGender = value;
                      });
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context).cancel),
            ),
            ElevatedButton(
              onPressed: () => _updateUserInfo(
                context,
                nicknameController.text,
                phoneController.text,
                selectedBirthday,
                selectedGender,
              ),
              child: Text(AppLocalizations.of(context).confirm),
            ),
          ],
        ),
      ),
    );
  }

  /// 更新用户信息
  Future<void> _updateUserInfo(
    BuildContext dialogContext,
    String nickname,
    String phone,
    String? birthday,
    int gender,
  ) async {
    if (nickname.isEmpty || phone.isEmpty) {
      ToastUtil.show(context, AppLocalizations.of(context).pleaseCompleteInfo);
      return;
    }

    Navigator.of(dialogContext).pop();

    setState(() {
      _isUpdating = true;
    });

    try {
      await _userService.updateUserInfo(
        widget.user.id,
        nickname: nickname,
        phone: phone,
        birthday: birthday,
        gender: gender,
      );

      if (mounted) {
        widget.onUserUpdated?.call();
        ToastUtil.show(context, AppLocalizations.of(context).updateUserSuccess);
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).updateUserFailed}: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }
}

/// 信息项数据类
class _InfoItem {
  final String label;
  final String value;
  final IconData icon;

  const _InfoItem({
    required this.label,
    required this.value,
    required this.icon,
  });
}
