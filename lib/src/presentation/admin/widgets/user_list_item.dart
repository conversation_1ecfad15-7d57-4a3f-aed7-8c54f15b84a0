import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../config/api/api_config.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/admin_user_model.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 用户列表项组件
class UserListItem extends StatelessWidget {
  final AdminUserModel user;
  final VoidCallback? onTap;
  final Function(bool)? onStatusChanged;

  const UserListItem({
    super.key,
    required this.user,
    this.onTap,
    this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户基本信息行
                Row(
                  children: [
                    // 用户头像
                    _buildUserAvatar(),
                    const SizedBox(width: 12),

                    // 用户信息
                    Expanded(child: _buildUserInfo(context)),

                    // 状态开关
                    _buildStatusSwitch(context),
                  ],
                ),

                const SizedBox(height: 12),

                // 用户详细信息
                _buildUserDetails(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建用户头像
  Widget _buildUserAvatar() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        color: AppColors.primary.withValues(alpha: 0.1),
      ),
      child: user.avatar?.isNotEmpty == true
          ? ClipRRect(
              borderRadius: BorderRadius.circular(25),
              child: _buildNetworkImage(user.avatar!),
            )
          : _buildDefaultAvatar(),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Icon(Icons.person, color: AppColors.primary, size: 28);
  }

  /// 构建网络图片
  Widget _buildNetworkImage(String avatarUrl) {
    // 检查URL是否有效
    if (avatarUrl.trim().isEmpty) {
      print('UserListItem: 头像URL为空');
      return _buildDefaultAvatar();
    }

    final fullUrl = ApiConfig.buildUserAvatarUrl(avatarUrl);
    print('UserListItem: 加载头像URL: $fullUrl (原始: $avatarUrl)');

    return Image.network(
      fullUrl,
      width: 50,
      height: 50,
      fit: BoxFit.cover,
      headers: const {
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      errorBuilder: (context, error, stackTrace) {
        print('UserListItem: 头像加载失败: $error');
        print('UserListItem: 错误堆栈: $stackTrace');
        return _buildDefaultAvatar();
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          print('UserListItem: 头像加载成功');
          return child;
        }
        return Center(
          child: CircularProgressIndicator(
            value: loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                : null,
            strokeWidth: 2,
          ),
        );
      },
    );
  }

  /// 构建用户信息
  Widget _buildUserInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 用户昵称和ID
        Row(
          children: [
            Expanded(
              flex: 3,
              child: Text(
                user.nickname,
                style: FontUtil.createBodyTextStyle(
                  text: user.nickname,
                ).copyWith(fontSize: 16, fontWeight: FontWeight.w600),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            Flexible(
              flex: 1,
              child: Text(
                'ID: ${user.id}',
                style: FontUtil.createCaptionTextStyle(text: 'ID: ${user.id}')
                    .copyWith(
                      fontSize: 12,
                      color: ThemeHelper.getTextSecondary(context),
                    ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),

        const SizedBox(height: 4),

        // 用户电话
        Text(
          user.phone,
          style: FontUtil.createCaptionTextStyle(text: user.phone).copyWith(
            fontSize: 14,
            color: ThemeHelper.getTextSecondary(context),
          ),
        ),
      ],
    );
  }

  /// 构建状态开关
  Widget _buildStatusSwitch(BuildContext context) {
    return Column(
      children: [
        Switch(
          value: user.status,
          onChanged: onStatusChanged,
          activeColor: AppColors.success,
          inactiveThumbColor: AppColors.error,
        ),
        Text(
          user.status
              ? AppLocalizations.of(context).enabledStatus
              : AppLocalizations.of(context).disabledStatus,
          style:
              FontUtil.createCaptionTextStyle(
                text: user.status
                    ? AppLocalizations.of(context).enabledStatus
                    : AppLocalizations.of(context).disabledStatus,
              ).copyWith(
                fontSize: 10,
                color: user.status ? AppColors.success : AppColors.error,
              ),
        ),
      ],
    );
  }

  /// 构建用户详细信息
  Widget _buildUserDetails(BuildContext context) {
    return Column(
      children: [
        // 分割线
        Container(
          height: 1,
          margin: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: ThemeHelper.getTextSecondary(
                  context,
                ).withValues(alpha: 0.2),
                width: 1,
                style: BorderStyle.solid,
              ),
            ),
          ),
        ),

        // 详细信息行
        Row(
          children: [
            // 角色标签
            Expanded(flex: 2, child: _buildRoleTags(context)),

            // 余额和积分
            Expanded(flex: 3, child: _buildBalanceInfo(context)),
          ],
        ),

        const SizedBox(height: 8),

        // 注册信息
        _buildRegistrationInfo(context),
      ],
    );
  }

  /// 构建角色标签
  Widget _buildRoleTags(BuildContext context) {
    final roles = _getLocalizedRoles(context);

    return SizedBox(
      width: double.infinity,
      child: Wrap(
        spacing: 4,
        runSpacing: 4,
        children: roles.take(2).map((roleInfo) {
          // 最多显示2个角色标签
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: roleInfo.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: roleInfo.color.withValues(alpha: 0.3),
                width: 0.5,
              ),
            ),
            child: Text(
              roleInfo.name,
              style: FontUtil.createCaptionTextStyle(text: roleInfo.name)
                  .copyWith(
                    fontSize: 10,
                    color: roleInfo.color,
                    fontWeight: FontWeight.w500,
                  ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 获取本地化的角色信息
  List<_RoleInfo> _getLocalizedRoles(BuildContext context) {
    final roles = <_RoleInfo>[];

    if (user.isAdmin) {
      roles.add(
        _RoleInfo(
          name: AppLocalizations.of(context).roleAdmin,
          color: AppColors.error,
        ),
      );
    }

    if (user.isDoctor) {
      roles.add(
        _RoleInfo(
          name: AppLocalizations.of(context).roleDoctor,
          color: AppColors.info,
        ),
      );
    }

    if (user.isReferrer) {
      roles.add(
        _RoleInfo(
          name: AppLocalizations.of(context).roleReferrer,
          color: AppColors.warning,
        ),
      );
    }

    if (roles.isEmpty) {
      roles.add(
        _RoleInfo(
          name: AppLocalizations.of(context).roleNormalUser,
          color: AppColors.textSecondaryStatic,
        ),
      );
    }

    return roles;
  }

  /// 构建余额和积分信息
  Widget _buildBalanceInfo(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // 余额
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '¥${user.money}',
              style: FontUtil.createBodyTextStyle(text: '¥${user.money}')
                  .copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.success,
                  ),
            ),
            Text(
              AppLocalizations.of(context).userBalance,
              style:
                  FontUtil.createCaptionTextStyle(
                    text: AppLocalizations.of(context).userBalance,
                  ).copyWith(
                    fontSize: 10,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
            ),
          ],
        ),

        const SizedBox(width: 16),

        // 积分
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              user.integral.toString(),
              style:
                  FontUtil.createBodyTextStyle(
                    text: user.integral.toString(),
                  ).copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.warning,
                  ),
            ),
            Text(
              AppLocalizations.of(context).userIntegral,
              style:
                  FontUtil.createCaptionTextStyle(
                    text: AppLocalizations.of(context).userIntegral,
                  ).copyWith(
                    fontSize: 10,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建注册信息
  Widget _buildRegistrationInfo(BuildContext context) {
    return Row(
      children: [
        // 性别
        Flexible(
          flex: 1,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                user.sex == 1
                    ? Icons.male
                    : user.sex == 2
                    ? Icons.female
                    : Icons.help_outline,
                size: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  user.getSexText(context),
                  style:
                      FontUtil.createCaptionTextStyle(
                        text: user.getSexText(context),
                      ).copyWith(
                        fontSize: 12,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(width: 8),

        // 注册来源
        Flexible(
          flex: 1,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                user.registerSource == 1 ? Icons.phone_android : Icons.web,
                size: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  user.getRegisterSourceText(),
                  style:
                      FontUtil.createCaptionTextStyle(
                        text: user.getRegisterSourceText(),
                      ).copyWith(
                        fontSize: 12,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),

        const Spacer(),

        // 注册时间
        Flexible(
          flex: 1,
          child: Text(
            user.createdAt.split(' ')[0], // 只显示日期部分
            style:
                FontUtil.createCaptionTextStyle(
                  text: user.createdAt.split(' ')[0],
                ).copyWith(
                  fontSize: 12,
                  color: ThemeHelper.getTextSecondary(context),
                ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }
}

/// 角色信息数据类
class _RoleInfo {
  final String name;
  final Color color;

  const _RoleInfo({required this.name, required this.color});
}
