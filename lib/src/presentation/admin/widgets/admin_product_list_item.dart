import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/language_service.dart';
import '../pages/admin_product_detail_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 管理员产品列表项组件
class AdminProductListItem extends StatelessWidget {
  final DoctorProductModel product;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final Function(int status, String? note)? onReview;

  const AdminProductListItem({
    super.key,
    required this.product,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onReview,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: isSelected
            ? Border.all(color: AppColors.primary, width: 2)
            : Border.all(color: Colors.transparent, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: InkWell(
        onTap: isSelectionMode ? onTap : () => _navigateToDetail(context),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部信息
              _buildHeader(context),
              const SizedBox(height: 12),

              // 产品信息
              _buildProductInfo(context),
              const SizedBox(height: 12),

              // 状态和操作
              _buildStatusAndActions(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部信息
  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        // 选择框
        if (isSelectionMode)
          Container(
            margin: const EdgeInsets.only(right: 12),
            child: Icon(
              isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
              color: isSelected
                  ? AppColors.primary
                  : ThemeHelper.getTextHint(context),
              size: 24,
            ),
          ),

        // 产品图片
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: ThemeHelper.getTextHint(context).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: product.fullMainImageUrl.isNotEmpty
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    product.fullMainImageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Icon(
                      Icons.image_not_supported,
                      color: ThemeHelper.getTextHint(context),
                    ),
                  ),
                )
              : Icon(
                  Icons.inventory_2_outlined,
                  color: ThemeHelper.getTextHint(context),
                ),
        ),
        const SizedBox(width: 12),

        // 产品基本信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                product.getLocalizedName(
                  LanguageService().getCurrentLanguageCode(),
                ),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                '${AppLocalizations.of(context).doctor}: ${product.getLocalizedDoctorName(LanguageService().getCurrentLanguageCode()).isEmpty ? AppLocalizations.of(context).unknown : product.getLocalizedDoctorName(LanguageService().getCurrentLanguageCode())}',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
            ],
          ),
        ),

        // 产品ID和日期
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: ThemeHelper.getTextHint(context).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'ID: ${product.id}',
                style: TextStyle(
                  fontSize: 12,
                  color: ThemeHelper.getTextSecondary(context),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _formatDate(product.createdAt),
              style: TextStyle(
                fontSize: 11,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建产品信息
  Widget _buildProductInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Builder(
          builder: (context) {
            final localizedDescription = product.getLocalizedDescription(
              LanguageService().getCurrentLanguageCode(),
            );
            if (localizedDescription.isNotEmpty) {
              return Text(
                localizedDescription,
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              );
            }
            return const SizedBox.shrink();
          },
        ),
        const SizedBox(height: 8),

        // 价格和库存信息
        Row(
          children: [
            _buildInfoChip(
              context,
              icon: Icons.monetization_on_outlined,
              label: '¥${product.price.toStringAsFixed(2)}',
              color: const Color(0xFF27AE60),
            ),
            const SizedBox(width: 8),
            _buildInfoChip(
              context,
              icon: Icons.inventory_outlined,
              label:
                  '${AppLocalizations.of(context).inventory}: ${product.inventoryCount}',
              color: const Color(0xFF3498DB),
            ),
            const SizedBox(width: 8),
            _buildInfoChip(
              context,
              icon: Icons.shopping_cart_outlined,
              label:
                  '${AppLocalizations.of(context).sales}: ${product.salesCount}',
              color: const Color(0xFF9B59B6),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建信息标签
  Widget _buildInfoChip(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态和操作
  Widget _buildStatusAndActions(BuildContext context) {
    return Row(
      children: [
        // 状态标签
        _buildStatusChip(context),
        const Spacer(),

        // 操作按钮（仅在非选择模式下显示）
        if (!isSelectionMode && product.status == 0) ...[
          _buildActionButton(
            context,
            label: AppLocalizations.of(context).approve,
            color: const Color(0xFF27AE60),
            onPressed: () =>
                onReview?.call(1, AppLocalizations.of(context).reviewApproved),
          ),
          const SizedBox(width: 8),
          _buildActionButton(
            context,
            label: AppLocalizations.of(context).reject,
            color: const Color(0xFFE74C3C),
            onPressed: () => _showRejectDialog(context),
          ),
        ],
      ],
    );
  }

  /// 构建状态标签
  Widget _buildStatusChip(BuildContext context) {
    final statusConfig = _getStatusConfig(product.status, context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusConfig['color'].withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: statusConfig['color'].withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusConfig['icon'], size: 14, color: statusConfig['color']),
          const SizedBox(width: 4),
          Text(
            statusConfig['text'],
            style: TextStyle(
              fontSize: 12,
              color: statusConfig['color'],
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(
    BuildContext context, {
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      height: 32,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
          elevation: 0,
        ),
        child: Text(
          label,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  /// 显示拒绝对话框
  void _showRejectDialog(BuildContext context) {
    final TextEditingController noteController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).rejectReview),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppLocalizations.of(context).confirmRejectProduct(
                product.getLocalizedName(
                  LanguageService().getCurrentLanguageCode(),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: noteController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).rejectReason,
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onReview?.call(
                2,
                noteController.text.trim().isEmpty
                    ? null
                    : noteController.text.trim(),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE74C3C),
              foregroundColor: Colors.white,
            ),
            child: Text(AppLocalizations.of(context).confirmReject),
          ),
        ],
      ),
    );
  }

  /// 获取状态配置
  Map<String, dynamic> _getStatusConfig(int status, BuildContext context) {
    switch (status) {
      case 0:
        return {
          'text': AppLocalizations.of(context).pending,
          'color': const Color(0xFFF39C12),
          'icon': Icons.pending_outlined,
        };
      case 1:
        return {
          'text': AppLocalizations.of(context).approved,
          'color': const Color(0xFF27AE60),
          'icon': Icons.check_circle_outline,
        };
      case 2:
        return {
          'text': AppLocalizations.of(context).rejected,
          'color': const Color(0xFFE74C3C),
          'icon': Icons.cancel_outlined,
        };
      case 3:
        return {
          'text': AppLocalizations.of(context).offline,
          'color': const Color(0xFF95A5A6),
          'icon': Icons.remove_circle_outline,
        };
      default:
        return {
          'text': AppLocalizations.of(context).unknown,
          'color': const Color(0xFFBDC3C7),
          'icon': Icons.help_outline,
        };
    }
  }

  /// 导航到产品详情页面
  void _navigateToDetail(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AdminProductDetailPage(
          productId: product.id,
          initialProduct: product,
        ),
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime dateTime) {
    return '${dateTime.month}/${dateTime.day} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
