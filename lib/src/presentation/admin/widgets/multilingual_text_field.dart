import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/widgets/dynamic_direction_text_field.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 多语言文本输入组件
/// 支持中文、英文、维吾尔语三种语言的输入
class MultilingualTextField extends StatefulWidget {
  final String label;
  final String hint;
  final Map<String, String> initialValues;
  final ValueChanged<Map<String, String>> onChanged;
  final String? Function(String?)? validator;
  final int maxLines;
  final bool required;

  const MultilingualTextField({
    super.key,
    required this.label,
    required this.hint,
    required this.initialValues,
    required this.onChanged,
    this.validator,
    this.maxLines = 1,
    this.required = false,
  });

  @override
  State<MultilingualTextField> createState() => _MultilingualTextFieldState();
}

class _MultilingualTextFieldState extends State<MultilingualTextField> {
  bool _isExpanded = false;
  late Map<String, TextEditingController> _controllers;
  late Map<String, String> _values;
  late Map<String, VoidCallback> _listeners;

  // 支持的语言
  static const List<Map<String, String>> _languages = [
    {'code': 'zh', 'name': '中文'},
    {'code': 'en', 'name': 'English'},
    {'code': 'ug', 'name': 'ئۇيغۇرچە'},
  ];

  @override
  void initState() {
    super.initState();
    _values = Map<String, String>.from(widget.initialValues);
    _controllers = {};
    _listeners = {};

    // 初始化控制器
    for (final lang in _languages) {
      final code = lang['code']!;
      _controllers[code] = TextEditingController(text: _values[code] ?? '');
      _listeners[code] = () => _onTextChanged(code);
      _controllers[code]!.addListener(_listeners[code]!);
    }
  }

  @override
  void didUpdateWidget(MultilingualTextField oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果initialValues发生变化，更新控制器的文本
    if (widget.initialValues != oldWidget.initialValues) {
      _values = Map<String, String>.from(widget.initialValues);

      for (final lang in _languages) {
        final code = lang['code']!;
        final newText = _values[code] ?? '';
        if (_controllers[code]!.text != newText) {
          // 暂时移除监听器，避免触发onChanged回调
          _controllers[code]!.removeListener(_listeners[code]!);
          _controllers[code]!.text = newText;
          // 重新添加监听器
          _controllers[code]!.addListener(_listeners[code]!);
        }
      }
    }
  }

  @override
  void dispose() {
    for (final lang in _languages) {
      final code = lang['code']!;
      _controllers[code]!.removeListener(_listeners[code]!);
      _controllers[code]!.dispose();
    }
    super.dispose();
  }

  void _onTextChanged(String languageCode) {
    _values[languageCode] = _controllers[languageCode]!.text;
    print(
      'MultilingualTextField: 文本更改 - $languageCode: ${_controllers[languageCode]!.text}',
    );
    widget.onChanged(_values);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标签和展开按钮
        Row(
          children: [
            Text(
              widget.label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
            if (widget.required)
              Text(' *', style: TextStyle(fontSize: 16, color: Colors.red)),
            const Spacer(),
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              icon: Icon(
                _isExpanded ? Icons.expand_less : Icons.expand_more,
                size: 20,
                color: AppColors.primary,
              ),
              label: Text(
                _isExpanded
                    ? AppLocalizations.of(context).collapse
                    : AppLocalizations.of(context).multilingual,
                style: TextStyle(fontSize: 14, color: AppColors.primary),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // 输入框区域
        if (!_isExpanded) ...[
          // 折叠状态：只显示中文输入框
          _buildLanguageTextField('zh'),
        ] else ...[
          // 展开状态：显示所有语言输入框
          ..._languages.map(
            (lang) => Column(
              children: [
                _buildLanguageTextField(lang['code']!),
                if (lang != _languages.last) const SizedBox(height: 12),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildLanguageTextField(String languageCode) {
    final language = _languages.firstWhere(
      (lang) => lang['code'] == languageCode,
    );
    final controller = _controllers[languageCode]!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_isExpanded) ...[
          // 语言标签（仅在展开状态显示）
          Text(
            language['name']!,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
          const SizedBox(height: 6),
        ],

        // 输入框 - 使用动态文本方向
        DynamicDirectionTextField(
          controller: controller,
          maxLines: widget.maxLines,
          validator: widget.required && languageCode == 'zh'
              ? widget.validator
              : null, // 只对中文进行必填验证
          decoration: InputDecoration(
            hintText: _isExpanded
                ? '${widget.hint}（${language['name']}）'
                : widget.hint,
            hintStyle: TextStyle(color: ThemeHelper.getTextHint(context)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12,
              vertical: widget.maxLines > 1 ? 12 : 16,
            ),
          ),
        ),
      ],
    );
  }
}
