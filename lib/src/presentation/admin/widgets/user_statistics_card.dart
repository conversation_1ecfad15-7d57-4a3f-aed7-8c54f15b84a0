import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/admin_user_model.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 用户统计信息卡片
class UserStatisticsCard extends StatefulWidget {
  final UserStatisticsModel statistics;

  const UserStatisticsCard({super.key, required this.statistics});

  @override
  State<UserStatisticsCard> createState() => _UserStatisticsCardState();
}

class _UserStatisticsCardState extends State<UserStatisticsCard> {
  bool _isExpanded = false;

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 可点击的标题栏
          InkWell(
            onTap: _toggleExpanded,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    color: AppColors.primary,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context).userStatistics,
                    style: FontUtil.createHeadingTextStyle(
                      text: AppLocalizations.of(context).userStatistics,
                    ).copyWith(fontSize: 14, fontWeight: FontWeight.w600),
                  ),
                  const Spacer(),
                  // 核心数据预览（折叠时显示）
                  if (!_isExpanded) ...[
                    _buildCompactStats(context),
                    const SizedBox(width: 8),
                  ],
                  // 展开/折叠图标
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: ThemeHelper.getTextSecondary(context),
                    size: 20,
                  ),
                ],
              ),
            ),
          ),

          // 详细统计信息（展开时显示）
          if (_isExpanded) ...[
            Container(
              width: double.infinity,
              height: 1,
              color: ThemeHelper.getDivider(context),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: _buildDetailedStatistics(context),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建紧凑统计信息（折叠时显示）
  Widget _buildCompactStats(BuildContext context) {
    return Flexible(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildCompactStatItem(
            context,
            widget.statistics.totalUsers.toString(),
            AppLocalizations.of(context).totalUsers,
            AppColors.primary,
          ),
          const SizedBox(width: 8),
          _buildCompactStatItem(
            context,
            widget.statistics.activeUsers.toString(),
            AppLocalizations.of(context).activeUsers,
            AppColors.success,
          ),
        ],
      ),
    );
  }

  /// 构建紧凑统计项
  Widget _buildCompactStatItem(
    BuildContext context,
    String value,
    String label,
    Color color,
  ) {
    return Flexible(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: FontUtil.createBodyTextStyle(
              text: value,
            ).copyWith(fontSize: 14, fontWeight: FontWeight.bold, color: color),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            label,
            style: FontUtil.createCaptionTextStyle(text: label).copyWith(
              fontSize: 9,
              color: ThemeHelper.getTextSecondary(context),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建详细统计信息（展开时显示）
  Widget _buildDetailedStatistics(BuildContext context) {
    final statisticsItems = [
      _StatisticsItem(
        title: AppLocalizations.of(context).totalUsers,
        value: widget.statistics.totalUsers.toString(),
        icon: Icons.people,
        color: AppColors.primary,
      ),
      _StatisticsItem(
        title: AppLocalizations.of(context).activeUsers,
        value: widget.statistics.activeUsers.toString(),
        icon: Icons.person_outline,
        color: AppColors.success,
      ),
      _StatisticsItem(
        title: AppLocalizations.of(context).adminUsers,
        value: widget.statistics.adminUsers.toString(),
        icon: Icons.admin_panel_settings,
        color: AppColors.warning,
      ),
      _StatisticsItem(
        title: AppLocalizations.of(context).doctorUsers,
        value: widget.statistics.doctorUsers.toString(),
        icon: Icons.medical_services,
        color: AppColors.info,
      ),
      _StatisticsItem(
        title: AppLocalizations.of(context).todayNewUsers,
        value: widget.statistics.todayNewUsers.toString(),
        icon: Icons.today,
        color: AppColors.primary,
      ),
      _StatisticsItem(
        title: AppLocalizations.of(context).thisWeekNewUsers,
        value: widget.statistics.thisWeekNewUsers.toString(),
        icon: Icons.date_range,
        color: AppColors.success,
      ),
      _StatisticsItem(
        title: AppLocalizations.of(context).totalBalance,
        value: '¥${widget.statistics.totalBalance}',
        icon: Icons.account_balance_wallet,
        color: AppColors.warning,
      ),
      _StatisticsItem(
        title: AppLocalizations.of(context).totalIntegral,
        value: widget.statistics.totalIntegral.toString(),
        icon: Icons.stars,
        color: AppColors.info,
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2.2, // 调整比例避免溢出
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: statisticsItems.length,
      itemBuilder: (context, index) {
        final item = statisticsItems[index];
        return _buildStatisticsItem(context, item);
      },
    );
  }

  /// 构建单个统计项
  Widget _buildStatisticsItem(BuildContext context, _StatisticsItem item) {
    return Container(
      padding: const EdgeInsets.all(12), // 减少内边距
      decoration: BoxDecoration(
        color: item.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: item.color.withValues(alpha: 0.2), width: 1),
      ),
      child: Row(
        children: [
          // 图标
          Container(
            padding: const EdgeInsets.all(6), // 减少图标容器内边距
            decoration: BoxDecoration(
              color: item.color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(item.icon, color: item.color, size: 16), // 减小图标尺寸
          ),
          const SizedBox(width: 8), // 减少间距
          // 文本信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min, // 添加这个属性
              children: [
                Flexible(
                  child: Text(
                    item.value,
                    style: FontUtil.createHeadingTextStyle(text: item.value)
                        .copyWith(
                          fontSize: 14, // 减小字体大小
                          fontWeight: FontWeight.bold,
                          color: item.color,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 2),
                Flexible(
                  child: Text(
                    item.title,
                    style: FontUtil.createCaptionTextStyle(text: item.title)
                        .copyWith(
                          fontSize: 10, // 减小字体大小
                          color: ThemeHelper.getTextSecondary(context),
                        ),
                    maxLines: 2, // 允许标题换行
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 统计项数据类
class _StatisticsItem {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatisticsItem({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });
}
