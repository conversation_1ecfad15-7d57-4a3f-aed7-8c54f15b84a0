import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as image_lib;
import 'package:path_provider/path_provider.dart';
import '../../../config/themes/app_colors.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 自定义绘制器，绘制剪裁区域的遮罩
class DoctorAvatarCropOverlayPainter extends CustomPainter {
  final Rect cropRect;
  final Color overlayColor;

  DoctorAvatarCropOverlayPainter({
    required this.cropRect,
    required this.overlayColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final overlayPaint = Paint()..color = overlayColor;
    final clearPaint = Paint()..blendMode = BlendMode.clear;

    // 保存画布状态
    canvas.saveLayer(Offset.zero & size, Paint());

    // 首先绘制完整的遮罩
    canvas.drawRect(Offset.zero & size, overlayPaint);

    // 然后清除剪裁区域，让底层图片显示出来
    canvas.drawRect(cropRect, clearPaint);

    // 恢复画布状态
    canvas.restore();
  }

  @override
  bool shouldRepaint(DoctorAvatarCropOverlayPainter oldDelegate) {
    return cropRect != oldDelegate.cropRect ||
        overlayColor != oldDelegate.overlayColor;
  }
}

/// 医生头像裁剪页面 - 方形裁剪
class DoctorAvatarCropScreen extends StatefulWidget {
  final File imageFile;

  const DoctorAvatarCropScreen({super.key, required this.imageFile});

  @override
  State<DoctorAvatarCropScreen> createState() => _DoctorAvatarCropScreenState();
}

class _DoctorAvatarCropScreenState extends State<DoctorAvatarCropScreen> {
  // 裁剪框的位置和大小
  double _cropLeft = 50;
  double _cropTop = 50;
  double _cropWidth = 200;
  double _cropHeight = 200;

  // 图片显示的实际尺寸
  double _imageDisplayWidth = 0;
  double _imageDisplayHeight = 0;

  // 处理状态
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeCropArea();
    });
  }

  void _initializeCropArea() {
    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;
    final availableHeight =
        screenSize.height -
        MediaQuery.of(context).padding.top -
        kToolbarHeight -
        100; // 减去底部按钮区域

    // 设置初始裁剪区域为屏幕中央的正方形
    final initialSize = (screenSize.width * 0.6).clamp(150.0, 300.0);

    setState(() {
      _imageDisplayWidth = screenSize.width - 32; // 减去margin
      _imageDisplayHeight = availableHeight - 32;

      _cropWidth = initialSize;
      _cropHeight = initialSize;
      _cropLeft = (_imageDisplayWidth - _cropWidth) / 2;
      _cropTop = (_imageDisplayHeight - _cropHeight) / 2;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background(context),
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).cropAvatar),
        backgroundColor: AppColors.cardBackground(context),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // 图片裁剪区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildCropArea(),
              ),
            ),
          ),

          // 底部操作按钮
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.cardBackground(context),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withValues(alpha: 0.2)
                      : Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                // 取消按钮
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isProcessing
                        ? null
                        : () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(color: AppColors.border(context)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context).cancel,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textSecondary(context),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // 确认按钮
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isProcessing ? null : _processCroppedImage,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: _isProcessing
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : Text(
                            AppLocalizations.of(context).confirm,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCropArea() {
    if (_imageDisplayWidth == 0 || _imageDisplayHeight == 0) {
      return const Center(child: CircularProgressIndicator());
    }

    return Stack(
      children: [
        // 背景图片
        Positioned.fill(child: Image.file(widget.imageFile, fit: BoxFit.cover)),

        // 裁剪遮罩和裁剪框
        Positioned.fill(
          child: CustomPaint(
            painter: DoctorAvatarCropOverlayPainter(
              cropRect: Rect.fromLTWH(
                _cropLeft,
                _cropTop,
                _cropWidth,
                _cropHeight,
              ),
              overlayColor: Theme.of(context).brightness == Brightness.dark
                  ? Colors.black.withValues(alpha: 0.7)
                  : Colors.black.withValues(alpha: 0.5),
            ),
          ),
        ),

        // 裁剪框
        Positioned(left: _cropLeft, top: _cropTop, child: _buildCropBox()),
      ],
    );
  }

  Widget _buildCropBox() {
    return SizedBox(
      width: _cropWidth,
      height: _cropHeight,
      child: Stack(
        children: [
          // 裁剪框边框
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white.withValues(alpha: 0.9)
                      : Colors.white,
                  width: 2.0,
                ),
              ),
            ),
          ),

          // 中心拖动区域（移动整个裁剪框）
          Positioned(
            left: 25,
            top: 25,
            right: 25,
            bottom: 25,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onPanUpdate: (details) {
                setState(() {
                  // 移动裁剪框，但保持在边界内
                  _cropLeft = (_cropLeft + details.delta.dx).clamp(
                    0.0,
                    _imageDisplayWidth - _cropWidth,
                  );
                  _cropTop = (_cropTop + details.delta.dy).clamp(
                    0.0,
                    _imageDisplayHeight - _cropHeight,
                  );
                });
              },
              child: Container(color: Colors.transparent),
            ),
          ),

          // 四个角的拖拽手柄
          _buildCornerHandle(Alignment.topLeft),
          _buildCornerHandle(Alignment.topRight),
          _buildCornerHandle(Alignment.bottomLeft),
          _buildCornerHandle(Alignment.bottomRight),
        ],
      ),
    );
  }

  Widget _buildCornerHandle(Alignment alignment) {
    return Align(
      alignment: alignment,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            final deltaX = details.delta.dx;
            final deltaY = details.delta.dy;

            // 计算新的尺寸，保持正方形
            double newSize = _cropWidth;

            if (alignment == Alignment.topLeft) {
              // 左上角：使用较小的变化量来保持正方形
              final delta = (deltaX + deltaY) / 2;
              newSize = _cropWidth - delta;
              final newLeft = _cropLeft + delta;
              final newTop = _cropTop + delta;

              if (newLeft >= 0 && newTop >= 0 && newSize >= 50) {
                _cropLeft = newLeft;
                _cropTop = newTop;
                _cropWidth = newSize;
                _cropHeight = newSize;
              }
            } else if (alignment == Alignment.topRight) {
              // 右上角：使用平均变化量
              final delta = (deltaX - deltaY) / 2;
              newSize = _cropWidth + delta;
              final newTop = _cropTop - delta;

              if (_cropLeft + newSize <= _imageDisplayWidth &&
                  newTop >= 0 &&
                  newSize >= 50) {
                _cropTop = newTop;
                _cropWidth = newSize;
                _cropHeight = newSize;
              }
            } else if (alignment == Alignment.bottomLeft) {
              // 左下角：使用平均变化量
              final delta = (-deltaX + deltaY) / 2;
              newSize = _cropWidth + delta;
              final newLeft = _cropLeft - delta;

              if (newLeft >= 0 &&
                  _cropTop + newSize <= _imageDisplayHeight &&
                  newSize >= 50) {
                _cropLeft = newLeft;
                _cropWidth = newSize;
                _cropHeight = newSize;
              }
            } else if (alignment == Alignment.bottomRight) {
              // 右下角：使用平均变化量
              final delta = (deltaX + deltaY) / 2;
              newSize = _cropWidth + delta;

              if (_cropLeft + newSize <= _imageDisplayWidth &&
                  _cropTop + newSize <= _imageDisplayHeight &&
                  newSize >= 50) {
                _cropWidth = newSize;
                _cropHeight = newSize;
              }
            }
          });
        },
        child: SizedBox(
          width: 30, // 增大触摸区域
          height: 30,
          child: ColoredBox(
            color: Colors.transparent, // 透明背景增大触摸区域
            child: Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: Stack(
                  children: [
                    // 角部加粗线条 - L形状，增强视觉反馈
                    if (alignment == Alignment.topLeft) ...[
                      Positioned(
                        top: 0,
                        left: 0,
                        child: Container(
                          width: 4,
                          height: 14,
                          decoration: _createLShapeDecoration(),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        left: 0,
                        child: Container(
                          width: 14,
                          height: 4,
                          decoration: _createLShapeDecoration(),
                        ),
                      ),
                    ],
                    if (alignment == Alignment.topRight) ...[
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          width: 4,
                          height: 14,
                          decoration: _createLShapeDecoration(),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          width: 14,
                          height: 4,
                          decoration: _createLShapeDecoration(),
                        ),
                      ),
                    ],
                    if (alignment == Alignment.bottomLeft) ...[
                      Positioned(
                        bottom: 0,
                        left: 0,
                        child: Container(
                          width: 4,
                          height: 14,
                          decoration: _createLShapeDecoration(),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        child: Container(
                          width: 14,
                          height: 4,
                          decoration: _createLShapeDecoration(),
                        ),
                      ),
                    ],
                    if (alignment == Alignment.bottomRight) ...[
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 4,
                          height: 14,
                          decoration: _createLShapeDecoration(),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 14,
                          height: 4,
                          decoration: _createLShapeDecoration(),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 创建L形状装饰，适配暗色模式
  BoxDecoration _createLShapeDecoration() {
    return BoxDecoration(
      color: Theme.of(context).brightness == Brightness.dark
          ? Colors.white.withValues(alpha: 0.9)
          : Colors.white,
      borderRadius: BorderRadius.circular(2),
      boxShadow: [
        BoxShadow(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.black.withValues(alpha: 0.6)
              : Colors.black.withValues(alpha: 0.3),
          blurRadius: 2,
          offset: const Offset(0, 1),
        ),
      ],
    );
  }

  Future<void> _processCroppedImage() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // 执行图片裁剪
      final croppedFile = await _cropImageFile();

      if (!mounted) return;

      // 返回裁剪后的图片路径
      Navigator.of(context).pop({'croppedImagePath': croppedFile.path});
    } catch (e) {
      if (mounted) {
        _showErrorDialog('${AppLocalizations.of(context).processing}: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// 执行图片裁剪，返回裁剪后的图片文件
  Future<File> _cropImageFile() async {
    // 获取本地化字符串，避免异步上下文问题
    final cannotParseImageError = AppLocalizations.of(
      context,
    ).cannotParseImageFile;

    try {
      // 读取原始图片
      final originalBytes = await widget.imageFile.readAsBytes();
      final originalImage = image_lib.decodeImage(originalBytes);

      if (originalImage == null) {
        throw Exception(cannotParseImageError);
      }

      // 计算裁剪区域在原始图片中的实际位置
      final scaleX = originalImage.width / _imageDisplayWidth;
      final scaleY = originalImage.height / _imageDisplayHeight;

      final cropX = (_cropLeft * scaleX).round();
      final cropY = (_cropTop * scaleY).round();
      final cropW = (_cropWidth * scaleX).round();
      final cropH = (_cropHeight * scaleY).round();

      // 执行裁剪
      final croppedImage = image_lib.copyCrop(
        originalImage,
        x: cropX,
        y: cropY,
        width: cropW,
        height: cropH,
      );

      // 保存裁剪后的图片
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final croppedFile = File(
        '${tempDir.path}/doctor_avatar_cropped_$timestamp.jpg',
      );

      final croppedBytes = image_lib.encodeJpg(croppedImage, quality: 85);
      await croppedFile.writeAsBytes(croppedBytes);

      return croppedFile;
    } catch (e) {
      throw Exception('Image crop failed: $e');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).error),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).confirm),
          ),
        ],
      ),
    );
  }
}
