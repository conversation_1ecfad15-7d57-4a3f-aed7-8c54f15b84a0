import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/doctor_model.dart';
import '../../../utils/toast_util.dart';
import '../../../services/admin/doctor_admin_service.dart';
import '../widgets/multilingual_text_field.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../config/api/api_config.dart';
import '../screens/doctor_avatar_crop_screen.dart';

/// 医生编辑页面 - 全屏编辑
class DoctorEditPage extends StatefulWidget {
  final DoctorModel doctor;
  final Function(DoctorModel) onSaved;

  const DoctorEditPage({
    super.key,
    required this.doctor,
    required this.onSaved,
  });

  @override
  State<DoctorEditPage> createState() => _DoctorEditPageState();
}

class _DoctorEditPageState extends State<DoctorEditPage> {
  final _formKey = GlobalKey<FormState>();

  // 多语言数据存储
  late Map<String, String> _nameData;
  late Map<String, String> _specialtyData;
  late Map<String, String> _descriptionData;
  late Map<String, String> _detailedInfoData;

  // 单语言字段控制器
  late TextEditingController _avatarUrlController;
  late TextEditingController _llmModelNameController;
  late TextEditingController _yearsOfExperienceController;
  late TextEditingController _systemPromptController;
  late TextEditingController _ratingController;
  late TextEditingController _digitalHumanUrlController;
  late TextEditingController _phoneController;
  late TextEditingController _addressController;
  late bool _isActive;
  bool _hasChanges = false;
  bool _isUploading = false;
  File? _selectedImage;
  final ImagePicker _imagePicker = ImagePicker();

  // 擅长领域管理（多语言）
  final List<Map<String, String>> _specialtiesData = [];

  // 判断是否为新建医生
  bool get _isNewDoctor => widget.doctor.id == 0;

  /// 解析多语言字段，支持字符串和多语言JSON对象
  Map<String, String> _parseMultilingualField(dynamic value) {
    if (value == null) {
      return {'zh': '', 'en': '', 'ug': ''};
    }

    // 如果是字符串，将其设置为中文，其他语言为空
    if (value is String) {
      if (value.isEmpty) {
        return {'zh': '', 'en': '', 'ug': ''};
      }
      return {'zh': value, 'en': '', 'ug': ''};
    }

    // 如果是多语言对象，提取所有语言
    if (value is Map<String, dynamic>) {
      return {
        'zh': value['zh']?.toString() ?? '',
        'en': value['en']?.toString() ?? '',
        'ug': value['ug']?.toString() ?? '',
      };
    }

    return {'zh': '', 'en': '', 'ug': ''};
  }

  /// 加载医生的完整多语言数据
  Future<void> _loadDoctorMultiLangData() async {
    try {
      // 重新获取医生详细信息，这次会包含完整的多语言数据
      final doctorDetail = await DoctorAdminService().getDoctorRawDataById(
        widget.doctor.id,
      );

      // 使用解析方法处理多语言数据
      _nameData = _parseMultilingualField(doctorDetail['name']);
      _specialtyData = _parseMultilingualField(doctorDetail['specialty']);
      _descriptionData = _parseMultilingualField(doctorDetail['description']);
      _detailedInfoData = _parseMultilingualField(
        doctorDetail['detailed_info'],
      );

      // 系统提示词现在是单语言字段
      _systemPromptController.text =
          doctorDetail['system_prompt']?.toString() ?? '';

      // 处理擅长领域
      final specialtiesData = doctorDetail['specialties'];
      if (specialtiesData != null) {
        final specialtiesMultiLang = _parseMultilingualField(specialtiesData);
        _specialtiesData.clear();

        // 解析每种语言的擅长领域
        final zhSpecialties = specialtiesMultiLang['zh']?.split(',') ?? [];
        final enSpecialties = specialtiesMultiLang['en']?.split(',') ?? [];
        final ugSpecialties = specialtiesMultiLang['ug']?.split(',') ?? [];

        final maxLength = [
          zhSpecialties.length,
          enSpecialties.length,
          ugSpecialties.length,
        ].reduce((a, b) => a > b ? a : b);

        for (int i = 0; i < maxLength; i++) {
          _specialtiesData.add({
            'zh': i < zhSpecialties.length ? zhSpecialties[i].trim() : '',
            'en': i < enSpecialties.length ? enSpecialties[i].trim() : '',
            'ug': i < ugSpecialties.length ? ugSpecialties[i].trim() : '',
          });
        }
      }

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      print('加载医生多语言数据失败: $e');
      // 如果加载失败，使用现有的单语言数据作为中文内容
      _nameData = _parseMultilingualField(widget.doctor.name);
      _specialtyData = _parseMultilingualField(widget.doctor.specialty);
      _descriptionData = _parseMultilingualField(widget.doctor.description);
      _detailedInfoData = _parseMultilingualField(widget.doctor.detailedInfo);

      // 系统提示词现在是单语言字段
      _systemPromptController.text = widget.doctor.systemPrompt ?? '';

      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();

    // 先初始化多语言数据为默认值，避免UI渲染时出现null错误
    if (!_isNewDoctor) {
      // 编辑现有医生，先使用现有的单语言数据作为中文内容
      _nameData = _parseMultilingualField(widget.doctor.name);
      _specialtyData = _parseMultilingualField(widget.doctor.specialty);
      _descriptionData = _parseMultilingualField(widget.doctor.description);
      _detailedInfoData = _parseMultilingualField(widget.doctor.detailedInfo);

      // 然后异步加载完整的多语言数据
      _loadDoctorMultiLangData();
    } else {
      // 新建医生，初始化空的多语言数据
      _nameData = {'zh': '', 'en': '', 'ug': ''};
      _specialtyData = {'zh': '', 'en': '', 'ug': ''};
      _descriptionData = {'zh': '', 'en': '', 'ug': ''};
      _detailedInfoData = {'zh': '', 'en': '', 'ug': ''};
    }

    // 初始化单语言字段控制器
    _avatarUrlController = TextEditingController(
      text: widget.doctor.avatarUrl ?? '',
    );
    _llmModelNameController = TextEditingController(
      text: widget.doctor.llmModelName ?? '',
    );
    _yearsOfExperienceController = TextEditingController(
      text: widget.doctor.yearsOfExperience.toString(),
    );
    _ratingController = TextEditingController(
      text: widget.doctor.rating.toString(),
    );
    _digitalHumanUrlController = TextEditingController(
      text: widget.doctor.digitalHumanUrl ?? '',
    );
    _phoneController = TextEditingController(text: widget.doctor.phone ?? '');
    _addressController = TextEditingController(
      text: widget.doctor.address ?? '',
    );
    _systemPromptController = TextEditingController(
      text: widget.doctor.systemPrompt ?? '',
    );
    _isActive = widget.doctor.isActive;

    // 初始化擅长领域
    _initSpecialties();

    // 监听输入变化（仅单语言字段）
    _avatarUrlController.addListener(_onTextChanged);
    _llmModelNameController.addListener(_onTextChanged);
    _yearsOfExperienceController.addListener(_onTextChanged);
    _ratingController.addListener(_onTextChanged);
    _digitalHumanUrlController.addListener(_onTextChanged);
    _phoneController.addListener(_onTextChanged);
    _addressController.addListener(_onTextChanged);
    _systemPromptController.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  /// 初始化擅长领域（多语言）
  void _initSpecialties() {
    // 清空现有数据
    _specialtiesData.clear();

    // 如果有擅长领域数据，解析并添加
    if (widget.doctor.specialties != null &&
        widget.doctor.specialties!.isNotEmpty) {
      final specialtiesList = widget.doctor.specialtiesList;
      for (var specialty in specialtiesList) {
        _addSpecialtyMultiLang(specialty);
      }
    }

    // 如果没有擅长领域，至少添加一个空的输入框
    if (_specialtiesData.isEmpty) {
      _addSpecialtyMultiLang('');
    }
  }

  /// 添加一个擅长领域（多语言）
  void _addSpecialtyMultiLang(String value) {
    _specialtiesData.add({'zh': value, 'en': '', 'ug': ''});
    setState(() {});
  }

  /// 移除一个擅长领域（多语言）
  void _removeSpecialtyMultiLang(int index) {
    if (index >= 0 && index < _specialtiesData.length) {
      _specialtiesData.removeAt(index);
      setState(() {});
    }
  }

  /// 更新擅长领域数据
  void _updateSpecialtyData(int index, Map<String, String> data) {
    if (index >= 0 && index < _specialtiesData.length) {
      _specialtiesData[index] = data;
      _onTextChanged();
    }
  }

  @override
  void dispose() {
    // 释放单语言字段控制器
    _avatarUrlController.dispose();
    _llmModelNameController.dispose();
    _yearsOfExperienceController.dispose();
    _ratingController.dispose();
    _digitalHumanUrlController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _systemPromptController.dispose();

    // 多语言擅长领域不需要释放控制器

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        _isNewDoctor
            ? AppLocalizations.of(context).addDoctor
            : AppLocalizations.of(context).editDoctor,
        style: FontUtil.createAppBarTitleStyle(
          context: context,
          text: _isNewDoctor
              ? AppLocalizations.of(context).addDoctor
              : AppLocalizations.of(context).editDoctor,
          fontSize: 18,
          color: ThemeHelper.getTextPrimary(context),
        ),
      ),
      backgroundColor: ThemeHelper.getCardBackground(context),
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: ThemeHelper.getTextPrimary(context),
        ),
        onPressed: _onBackPressed,
      ),
      actions: [
        if (_hasChanges)
          TextButton(
            onPressed: _saveDoctor,
            child: Text(
              AppLocalizations.of(context).save,
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 基本信息卡片
            _buildBasicInfoCard(),
            const SizedBox(height: 16),

            // 详细信息卡片
            _buildDetailedInfoCard(),
            const SizedBox(height: 16),

            // AI设置卡片
            _buildAISettingsCard(),
            const SizedBox(height: 16),

            // 状态设置卡片
            _buildStatusCard(),

            // 底部间距
            const SizedBox(height: 100),
          ],
        ),
      ),
    );
  }

  /// 构建基本信息卡片
  Widget _buildBasicInfoCard() {
    return _buildCard(
      title: AppLocalizations.of(context).basicInfo,
      icon: Icons.person,
      children: [
        // 头像上传区域
        _buildAvatarUploadSection(),
        const SizedBox(height: 20),

        MultilingualTextField(
          label: AppLocalizations.of(context).doctorName,
          hint: AppLocalizations.of(context).enterDoctorName,
          initialValues: _nameData,
          onChanged: (values) {
            _nameData = values;
            _onTextChanged();
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return AppLocalizations.of(context).enterDoctorName;
            }
            return null;
          },
          required: true,
        ),
        const SizedBox(height: 16),

        MultilingualTextField(
          label: AppLocalizations.of(context).specialty,
          hint: AppLocalizations.of(context).enterSpecialty,
          initialValues: _specialtyData,
          onChanged: (values) {
            _specialtyData = values;
            _onTextChanged();
          },
        ),
        const SizedBox(height: 16),

        // 擅长领域
        _buildSpecialtiesSection(),
        const SizedBox(height: 16),

        _buildTextField(
          controller: _avatarUrlController,
          label: AppLocalizations.of(context).avatarUrl,
          hint: AppLocalizations.of(context).enterAvatarUrl,
        ),
        const SizedBox(height: 16),

        // 工作年限和评分并排
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                controller: _yearsOfExperienceController,
                label: AppLocalizations.of(context).yearsOfExperience,
                hint: AppLocalizations.of(context).enterYearsOfExperience,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizations.of(context).enterYearsOfExperience;
                  }
                  final years = int.tryParse(value);
                  if (years == null || years < 0) {
                    return AppLocalizations.of(context).enterYearsOfExperience;
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTextField(
                controller: _ratingController,
                label: AppLocalizations.of(context).rating,
                hint: AppLocalizations.of(context).enterRating,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizations.of(context).enterRating;
                  }
                  final rating = double.tryParse(value);
                  if (rating == null || rating < 0 || rating > 5) {
                    return AppLocalizations.of(context).enterRating;
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // 数字人URL
        _buildTextField(
          controller: _digitalHumanUrlController,
          label: AppLocalizations.of(context).digitalHumanUrl,
          hint: AppLocalizations.of(context).enterDigitalHumanUrl,
        ),
        const SizedBox(height: 16),

        // 联系电话
        _buildTextField(
          controller: _phoneController,
          label: AppLocalizations.of(context).phone,
          hint: AppLocalizations.of(context).enterPhone,
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              // 简单的手机号验证
              if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                return AppLocalizations.of(context).enterValidPhone;
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // 工作地址
        _buildTextField(
          controller: _addressController,
          label: AppLocalizations.of(context).address,
          hint: AppLocalizations.of(context).enterAddress,
          maxLines: 2,
        ),
      ],
    );
  }

  /// 构建详细信息卡片
  Widget _buildDetailedInfoCard() {
    return _buildCard(
      title: AppLocalizations.of(context).detailedInfo,
      icon: Icons.description,
      children: [
        MultilingualTextField(
          label: AppLocalizations.of(context).description,
          hint: AppLocalizations.of(context).enterDescription,
          initialValues: _descriptionData,
          onChanged: (values) {
            _descriptionData = values;
            _onTextChanged();
          },
          maxLines: 3,
        ),
        const SizedBox(height: 16),

        MultilingualTextField(
          label: AppLocalizations.of(context).detailedDescription,
          hint: AppLocalizations.of(context).enterDetailedDescription,
          initialValues: _detailedInfoData,
          onChanged: (values) {
            _detailedInfoData = values;
            _onTextChanged();
          },
          maxLines: 5,
        ),
      ],
    );
  }

  /// 构建AI设置卡片
  Widget _buildAISettingsCard() {
    return _buildCard(
      title: AppLocalizations.of(context).aiSettings,
      icon: Icons.smart_toy,
      children: [
        _buildTextField(
          controller: _systemPromptController,
          label: AppLocalizations.of(context).systemPrompt,
          hint: AppLocalizations.of(context).enterSystemPrompt,
          maxLines: 6,
          onChanged: (_) => _onTextChanged(),
        ),
        const SizedBox(height: 16),

        _buildTextField(
          controller: _llmModelNameController,
          label: AppLocalizations.of(context).llmModelName,
          hint: AppLocalizations.of(context).enterLlmModelName,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return AppLocalizations.of(context).enterModelName;
            }
            return null;
          },
        ),
      ],
    );
  }

  /// 构建状态设置卡片
  Widget _buildStatusCard() {
    return _buildCard(
      title: AppLocalizations.of(context).statusSettings,
      icon: Icons.settings,
      children: [
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.of(context).enableStatus,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: ThemeHelper.getTextPrimary(context),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _isActive
                        ? AppLocalizations.of(context).doctorEnabledDescription
                        : AppLocalizations.of(
                            context,
                          ).doctorDisabledDescription,
                    style: TextStyle(
                      fontSize: 14,
                      color: ThemeHelper.getTextSecondary(context),
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                  _hasChanges = true;
                });
              },
              activeColor: AppColors.primary,
            ),
          ],
        ),
      ],
    );
  }

  /// 构建卡片容器
  Widget _buildCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片标题
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                ),
              ],
            ),
          ),
          // 卡片内容
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建头像上传区域
  Widget _buildAvatarUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).doctorAvatar,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            // 头像预览
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: ThemeHelper.getBorder(context),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(11),
                child: _buildAvatarPreview(),
              ),
            ),
            const SizedBox(width: 16),
            // 上传按钮
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ElevatedButton.icon(
                    onPressed: _isUploading ? null : _pickAndUploadImage,
                    icon: _isUploading
                        ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : Icon(Icons.upload, size: 18),
                    label: Text(
                      _isUploading
                          ? AppLocalizations.of(context).uploading
                          : AppLocalizations.of(context).uploadAvatar,
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    AppLocalizations.of(context).supportedImageFormats,
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeHelper.getTextHint(context),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建头像预览
  Widget _buildAvatarPreview() {
    // 如果有选中的本地图片，优先显示
    if (_selectedImage != null) {
      return Image.file(
        _selectedImage!,
        width: 80,
        height: 80,
        fit: BoxFit.cover,
      );
    }

    // 如果有网络头像URL，显示网络图片
    final avatarUrl = _avatarUrlController.text.trim();
    if (avatarUrl.isNotEmpty) {
      // 使用ApiConfig中的buildImageUrl方法来处理URL
      String fullUrl = ApiConfig.buildImageUrl(avatarUrl);

      return Image.network(
        fullUrl,
        width: 80,
        height: 80,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                  : null,
              strokeWidth: 2,
            ),
          );
        },
      );
    }

    // 默认头像
    return _buildDefaultAvatar();
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withValues(alpha: 0.8),
            AppColors.primary.withValues(alpha: 0.6),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Icon(Icons.person, color: Colors.white, size: 40),
    );
  }

  /// 选择并上传图片
  Future<void> _pickAndUploadImage() async {
    try {
      // 显示选择图片来源的对话框
      final ImageSource? source = await _showImageSourceDialog();
      if (source == null) return;

      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 90,
      );

      if (image == null) return;

      final file = File(image.path);

      // 检查文件大小（5MB限制）
      final fileSize = await file.length();
      if (fileSize > 5 * 1024 * 1024) {
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).imageSizeExceedsLimit,
          );
        }
        return;
      }

      // 跳转到图片剪裁页面
      if (mounted) {
        final result = await Navigator.push<Map<String, dynamic>>(
          context,
          MaterialPageRoute(
            builder: (context) => DoctorAvatarCropScreen(imageFile: file),
          ),
        );

        if (result != null && result['croppedImagePath'] != null) {
          final croppedImagePath = result['croppedImagePath'] as String;
          final croppedFile = File(croppedImagePath);

          setState(() {
            _selectedImage = croppedFile;
            _isUploading = true;
            _hasChanges = true;
          });

          // 上传裁剪后的图片
          final avatarUrl = await DoctorAdminService().uploadDoctorAvatar(
            croppedFile,
          );

          // 更新头像URL
          _avatarUrlController.text = avatarUrl;

          if (mounted) {
            setState(() {
              _isUploading = false;
            });

            ToastUtil.show(
              context,
              AppLocalizations.of(context).avatarUploadSuccess,
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isUploading = false;
          _selectedImage = null;
        });
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).uploadFailed}: $e',
        );
      }
    }
  }

  /// 显示选择图片来源的对话框
  Future<ImageSource?> _showImageSourceDialog() async {
    return showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).selectAvatar),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: Text(AppLocalizations.of(context).takePhoto),
                onTap: () {
                  Navigator.pop(context, ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: Text(AppLocalizations.of(context).selectFromGallery),
                onTap: () {
                  Navigator.pop(context, ImageSource.gallery);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(AppLocalizations.of(context).cancel),
            ),
          ],
        );
      },
    );
  }

  /// 构建擅长领域部分
  Widget _buildSpecialtiesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppLocalizations.of(context).specialties,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
            // 添加按钮
            IconButton(
              onPressed: () => _addSpecialtyMultiLang(''),
              icon: const Icon(Icons.add_circle, color: AppColors.primary),
              tooltip: AppLocalizations.of(context).addSpecialty,
            ),
          ],
        ),
        const SizedBox(height: 8),

        // 擅长领域列表（多语言）
        ..._specialtiesData.asMap().entries.map((entry) {
          final index = entry.key;
          final specialtyData = entry.value;

          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Row(
              children: [
                // 多语言输入框
                Expanded(
                  child: MultilingualTextField(
                    label:
                        '${AppLocalizations.of(context).specialtyField} ${index + 1}',
                    hint: AppLocalizations.of(context).enterSpecialtyField,
                    initialValues: specialtyData,
                    onChanged: (values) => _updateSpecialtyData(index, values),
                  ),
                ),
                const SizedBox(width: 8),
                // 删除按钮
                if (_specialtiesData.length > 1)
                  IconButton(
                    onPressed: () => _removeSpecialtyMultiLang(index),
                    icon: const Icon(Icons.remove_circle, color: Colors.red),
                    tooltip: AppLocalizations.of(context).deleteSpecialty,
                  ),
              ],
            ),
          );
        }),

        // 提示文本
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            AppLocalizations.of(context).specialtyInputHint,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ),
      ],
    );
  }

  /// 构建文本输入框
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          validator: validator,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: ThemeHelper.getTextHint(context)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        border: Border(
          top: BorderSide(color: ThemeHelper.getBorder(context), width: 1),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _onBackPressed,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: ThemeHelper.getBorder(context)),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  AppLocalizations.of(context).cancel,
                  style: TextStyle(
                    color: ThemeHelper.getTextSecondary(context),
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: _hasChanges ? _saveDoctor : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  AppLocalizations.of(context).save,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 处理返回按钮
  void _onBackPressed() {
    if (_hasChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(AppLocalizations.of(context).confirmExit),
          content: Text(AppLocalizations.of(context).unsavedChangesWarning),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(AppLocalizations.of(context).cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); // 关闭对话框
                Navigator.pop(context); // 返回上一页
              },
              child: Text(AppLocalizations.of(context).exit),
            ),
          ],
        ),
      );
    } else {
      Navigator.pop(context);
    }
  }

  /// 保存医生信息
  Future<void> _saveDoctor() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 创建擅长领域的多语言数据
    final specialtiesMultiLangData = <String, String>{
      'zh': _specialtiesData
          .map((data) => data['zh']?.trim() ?? '')
          .where((text) => text.isNotEmpty)
          .join(','),
      'en': _specialtiesData
          .map((data) => data['en']?.trim() ?? '')
          .where((text) => text.isNotEmpty)
          .join(','),
      'ug': _specialtiesData
          .map((data) => data['ug']?.trim() ?? '')
          .where((text) => text.isNotEmpty)
          .join(','),
    };

    // 注意：现在直接调用多语言API，不再需要创建DoctorModel对象

    // 如果是新建医生，直接调用多语言API
    if (_isNewDoctor) {
      try {
        final result = await DoctorAdminService().createDoctorWithMultiLangData(
          nameData: _nameData,
          specialtyData: _specialtyData,
          specialtiesData: specialtiesMultiLangData,
          descriptionData: _descriptionData,
          detailedInfoData: _detailedInfoData,
          systemPrompt: _systemPromptController.text.trim(),
          avatarUrl: _avatarUrlController.text.trim().isEmpty
              ? null
              : _avatarUrlController.text.trim(),
          llmModelName: _llmModelNameController.text.trim().isEmpty
              ? null
              : _llmModelNameController.text.trim(),
          isActive: _isActive,
          yearsOfExperience:
              int.tryParse(_yearsOfExperienceController.text.trim()) ?? 0,
          rating: double.tryParse(_ratingController.text.trim()) ?? 0.0,
          digitalHumanUrl: _digitalHumanUrlController.text.trim().isEmpty
              ? null
              : _digitalHumanUrlController.text.trim(),
          phone: _phoneController.text.trim().isEmpty
              ? null
              : _phoneController.text.trim(),
          address: _addressController.text.trim().isEmpty
              ? null
              : _addressController.text.trim(),
        );

        // 调用回调函数
        widget.onSaved(result);
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${AppLocalizations.of(context).createDoctorFailed}: $e',
              ),
            ),
          );
        }
        return;
      }
    } else {
      // 编辑现有医生，直接调用多语言API
      try {
        final result = await DoctorAdminService().updateDoctorWithMultiLangData(
          doctorId: widget.doctor.id,
          nameData: _nameData,
          specialtyData: _specialtyData,
          specialtiesData: specialtiesMultiLangData,
          descriptionData: _descriptionData,
          detailedInfoData: _detailedInfoData,
          systemPrompt: _systemPromptController.text.trim(),
          avatarUrl: _avatarUrlController.text.trim().isEmpty
              ? null
              : _avatarUrlController.text.trim(),
          llmModelName: _llmModelNameController.text.trim().isEmpty
              ? null
              : _llmModelNameController.text.trim(),
          isActive: _isActive,
          yearsOfExperience:
              int.tryParse(_yearsOfExperienceController.text.trim()) ?? 0,
          rating: double.tryParse(_ratingController.text.trim()) ?? 0.0,
          digitalHumanUrl: _digitalHumanUrlController.text.trim().isEmpty
              ? null
              : _digitalHumanUrlController.text.trim(),
          phone: _phoneController.text.trim().isEmpty
              ? null
              : _phoneController.text.trim(),
          address: _addressController.text.trim().isEmpty
              ? null
              : _addressController.text.trim(),
        );

        // 调用回调函数
        widget.onSaved(result);
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${AppLocalizations.of(context).updateDoctorFailed}: $e',
              ),
            ),
          );
        }
        return;
      }
    }

    if (mounted) {
      Navigator.pop(context);
    }
  }
}
