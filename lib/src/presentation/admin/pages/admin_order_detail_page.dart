import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/admin/admin_order_service.dart';
import '../../../services/language_service.dart';
import '../widgets/admin_ship_dialog.dart';
import '../../shipping/pages/shipping_status_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 管理员订单详情页面
class AdminOrderDetailPage extends StatefulWidget {
  final ProductOrderModel order;

  const AdminOrderDetailPage({super.key, required this.order});

  @override
  State<AdminOrderDetailPage> createState() => _AdminOrderDetailPageState();
}

class _AdminOrderDetailPageState extends State<AdminOrderDetailPage> {
  final AdminOrderService _adminOrderService = AdminOrderService();

  ProductOrderModel? _currentOrder;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentOrder = widget.order;
    _loadOrderDetail();
  }

  /// 加载订单详情
  Future<void> _loadOrderDetail() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final orderDetail = await _adminOrderService.getOrderDetail(
        _currentOrder!.id,
      );
      setState(() {
        _currentOrder = orderDetail;
      });
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).orderDetailLoadFailedMessage}: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingView() : _buildBody(),
      bottomNavigationBar: _buildBottomActions(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        AppLocalizations.of(context).orderDetailTitle,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ThemeHelper.getTextPrimary(context),
        ),
      ),
      backgroundColor: ThemeHelper.getCardBackground(context),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: ThemeHelper.getTextPrimary(context),
        ),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        // 管理员操作菜单
        PopupMenuButton<String>(
          icon: Icon(
            Icons.more_vert,
            color: ThemeHelper.getTextPrimary(context),
          ),
          onSelected: _handleAdminAction,
          itemBuilder: (context) => [
            if (_currentOrder?.payStatus == 0) ...[
              PopupMenuItem(
                value: 'mark_paid',
                child: Row(
                  children: [
                    Icon(Icons.payment, color: Colors.green, size: 16),
                    SizedBox(width: 8),
                    Text(AppLocalizations.of(context).markAsPaidAction),
                  ],
                ),
              ),
            ],
            if (_currentOrder?.payStatus == 1) ...[
              PopupMenuItem(
                value: 'refund',
                child: Row(
                  children: [
                    Icon(Icons.money_off, color: Colors.orange, size: 16),
                    SizedBox(width: 8),
                    Text(AppLocalizations.of(context).markAsRefundAction),
                  ],
                ),
              ),
            ],
            if (_currentOrder?.orderStatus == 1 &&
                _currentOrder?.payStatus == 1) ...[
              PopupMenuItem(
                value: 'admin_ship',
                child: Row(
                  children: [
                    Icon(Icons.local_shipping, color: Colors.blue, size: 16),
                    SizedBox(width: 8),
                    Text(AppLocalizations.of(context).adminShipAction),
                  ],
                ),
              ),
            ],
            PopupMenuItem(
              value: 'complete',
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 16),
                  SizedBox(width: 8),
                  Text(AppLocalizations.of(context).markAsCompleteAction),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'cancel',
              child: Row(
                children: [
                  Icon(Icons.cancel, color: Colors.orange, size: 16),
                  SizedBox(width: 8),
                  Text(AppLocalizations.of(context).markAsCancelAction),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red, size: 16),
                  SizedBox(width: 8),
                  Text(AppLocalizations.of(context).deleteOrderAction),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建加载视图
  Widget _buildLoadingView() {
    return const Center(child: CircularProgressIndicator());
  }

  /// 构建主体内容
  Widget _buildBody() {
    if (_currentOrder == null) {
      return Center(
        child: Text(AppLocalizations.of(context).orderInfoLoadFailedMessage),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 订单状态卡片
          _buildOrderStatusCard(),

          const SizedBox(height: 16),

          // 商品信息卡片
          _buildProductInfoCard(),

          const SizedBox(height: 16),

          // 订单信息卡片
          _buildOrderInfoCard(),

          const SizedBox(height: 16),

          // 客户信息卡片
          _buildCustomerInfoCard(),

          const SizedBox(height: 16),

          // 收货信息卡片
          _buildShippingInfoCard(),

          // 如果有快递信息，显示物流信息卡片
          if (_currentOrder!.trackingNumber?.isNotEmpty == true) ...[
            const SizedBox(height: 16),
            _buildTrackingInfoCard(),
          ],

          const SizedBox(height: 16),

          // 费用明细卡片
          _buildPriceInfoCard(),

          const SizedBox(height: 100), // 底部留白
        ],
      ),
    );
  }

  /// 构建订单状态卡片
  Widget _buildOrderStatusCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // 状态图标和文字
          _buildStatusIcon(),
          const SizedBox(height: 12),
          Text(
            _getStatusText(),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getStatusDescription(),
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建状态图标
  Widget _buildStatusIcon() {
    IconData iconData;
    Color iconColor;

    switch (_currentOrder!.orderStatus) {
      case 0:
        iconData = Icons.payment_outlined;
        iconColor = const Color(0xFFF39C12);
        break;
      case 1:
        iconData = Icons.inventory_2_outlined;
        iconColor = const Color(0xFF3498DB);
        break;
      case 2:
        iconData = Icons.local_shipping_outlined;
        iconColor = AppColors.primary;
        break;
      case 3:
        iconData = Icons.check_circle_outline;
        iconColor = const Color(0xFF27AE60);
        break;
      case 4:
        iconData = Icons.cancel_outlined;
        iconColor = const Color(0xFFE74C3C);
        break;
      default:
        iconData = Icons.help_outline;
        iconColor = Colors.grey;
    }

    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(iconData, size: 40, color: iconColor),
    );
  }

  /// 获取状态文字
  String _getStatusText() {
    switch (_currentOrder!.orderStatus) {
      case 0:
        return AppLocalizations.of(context).orderStatusPendingPayment;
      case 1:
        return AppLocalizations.of(context).orderStatusPendingShipment;
      case 2:
        return AppLocalizations.of(context).orderStatusShipped;
      case 3:
        return AppLocalizations.of(context).orderStatusCompleted;
      case 4:
        return AppLocalizations.of(context).orderStatusCancelled;
      default:
        return AppLocalizations.of(context).orderStatusUnknown;
    }
  }

  /// 获取状态描述
  String _getStatusDescription() {
    switch (_currentOrder!.orderStatus) {
      case 0:
        return AppLocalizations.of(context).waitingForPaymentDescription;
      case 1:
        return AppLocalizations.of(context).waitingForShipmentDescription;
      case 2:
        return AppLocalizations.of(context).shippedDescription;
      case 3:
        return AppLocalizations.of(context).orderCompletedDescription;
      case 4:
        return AppLocalizations.of(context).orderCancelledDescription;
      default:
        return AppLocalizations.of(context).orderStatusAbnormalDescription;
    }
  }

  /// 构建商品信息卡片
  Widget _buildProductInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).productInfoTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 商品图片
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.grey[200],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: _currentOrder!.fullProductImageUrl.isNotEmpty
                      ? Image.network(
                          _currentOrder!.fullProductImageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultProductImage();
                          },
                        )
                      : _buildDefaultProductImage(),
                ),
              ),
              const SizedBox(width: 16),
              // 商品信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentOrder!.getProductName(
                        LanguageService().getCurrentLanguageCode(),
                      ),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: ThemeHelper.getTextPrimary(context),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      AppLocalizations.of(context).doctorLabel(
                        _currentOrder!.getDoctorName(
                          LanguageService().getCurrentLanguageCode(),
                        ),
                      ),
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          '¥${_currentOrder!.unitPrice.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF27AE60),
                          ),
                        ),
                        const Spacer(),
                        Text(
                          '×${_currentOrder!.quantity}',
                          style: TextStyle(
                            fontSize: 14,
                            color: ThemeHelper.getTextSecondary(context),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建默认商品图片
  Widget _buildDefaultProductImage() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(Icons.medical_services, color: AppColors.primary, size: 32),
    );
  }

  /// 构建订单信息卡片
  Widget _buildOrderInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).orderInfoTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            AppLocalizations.of(context).orderNumberFieldLabel,
            _currentOrder!.orderSn,
            canCopy: true,
          ),
          _buildInfoRow(
            AppLocalizations.of(context).orderTimeLabel,
            _formatDateTime(_currentOrder!.createdAt),
          ),
          if (_currentOrder!.payTime != null)
            _buildInfoRow(
              AppLocalizations.of(context).paymentTimeLabel,
              _formatDateTime(_currentOrder!.payTime!),
            ),
          if (_currentOrder!.shipTime != null)
            _buildInfoRow(
              AppLocalizations.of(context).shipmentTimeLabel,
              _formatDateTime(_currentOrder!.shipTime!),
            ),
          if (_currentOrder!.completeTime != null)
            _buildInfoRow(
              AppLocalizations.of(context).completionTimeLabel,
              _formatDateTime(_currentOrder!.completeTime!),
            ),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value, {bool canCopy = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      color: ThemeHelper.getTextPrimary(context),
                    ),
                  ),
                ),
                if (canCopy)
                  GestureDetector(
                    onTap: () => _copyToClipboard(value),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.copy,
                        size: 16,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 复制到剪贴板
  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ToastUtil.show(
      context,
      AppLocalizations.of(context).copiedToClipboardMessage,
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 构建客户信息卡片
  Widget _buildCustomerInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).customerInfoTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            AppLocalizations.of(context).customerNicknameLabel,
            _currentOrder!.userNickname,
          ),
          _buildInfoRow(
            AppLocalizations.of(context).userIdLabel,
            _currentOrder!.userId.toString(),
          ),
        ],
      ),
    );
  }

  /// 构建收货信息卡片
  Widget _buildShippingInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).shippingInfoTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            AppLocalizations.of(context).recipientLabel,
            _currentOrder!.shippingName ?? '',
          ),
          _buildInfoRow(
            AppLocalizations.of(context).contactPhoneLabel,
            _currentOrder!.shippingPhone ?? '',
          ),
          _buildInfoRow(
            AppLocalizations.of(context).shippingAddressLabel,
            _currentOrder!.shippingAddress ?? '',
          ),
        ],
      ),
    );
  }

  /// 构建物流信息卡片
  Widget _buildTrackingInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                AppLocalizations.of(context).trackingInfoTitle,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => _viewShippingStatus(),
                child: Text(
                  AppLocalizations.of(context).viewDetailsAction,
                  style: TextStyle(fontSize: 14, color: AppColors.primary),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            AppLocalizations.of(context).trackingNumberLabel,
            _currentOrder!.trackingNumber!,
            canCopy: true,
          ),
          if (_currentOrder!.shippingCompany?.isNotEmpty == true)
            _buildInfoRow(
              AppLocalizations.of(context).shippingCompanyLabel,
              _currentOrder!.shippingCompany!,
            ),
          if (_currentOrder!.shippingNote?.isNotEmpty == true)
            _buildInfoRow(
              AppLocalizations.of(context).shippingNoteLabel,
              _currentOrder!.shippingNote!,
            ),
        ],
      ),
    );
  }

  /// 构建费用明细卡片
  Widget _buildPriceInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).priceDetailsTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 16),
          _buildPriceRow(
            AppLocalizations.of(context).productAmountLabel,
            '¥${(_currentOrder!.unitPrice * _currentOrder!.quantity).toStringAsFixed(2)}',
          ),
          _buildPriceRow(
            AppLocalizations.of(context).shippingFeeLabel,
            '¥0.00',
          ),
          const Divider(height: 24),
          _buildPriceRow(
            AppLocalizations.of(context).totalPaidLabel,
            '¥${_currentOrder!.totalAmount.toStringAsFixed(2)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  /// 构建价格行
  Widget _buildPriceRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
              color: isTotal
                  ? ThemeHelper.getTextPrimary(context)
                  : ThemeHelper.getTextSecondary(context),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 18 : 14,
              fontWeight: isTotal ? FontWeight.w700 : FontWeight.normal,
              color: isTotal
                  ? const Color(0xFF27AE60)
                  : ThemeHelper.getTextPrimary(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 查看物流状态
  void _viewShippingStatus() {
    if (_currentOrder?.trackingNumber?.isNotEmpty == true) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              ShippingStatusPage(order: _currentOrder!, isDoctorView: false),
        ),
      );
    }
  }

  /// 构建底部操作按钮
  Widget? _buildBottomActions() {
    if (_currentOrder == null) return null;

    List<Widget> actions = [];

    // 根据订单状态显示不同的操作按钮
    switch (_currentOrder!.orderStatus) {
      case 0: // 待支付
        actions.addAll([
          Expanded(
            child: OutlinedButton(
              onPressed: () => _handleAdminAction('cancel'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: BorderSide(color: Colors.grey[400]!),
              ),
              child: Text(AppLocalizations.of(context).cancelOrderAction),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: () => _handleAdminAction('mark_paid'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(AppLocalizations.of(context).markAsPaidAction),
            ),
          ),
        ]);
        break;
      case 1: // 待发货
        if (_currentOrder!.payStatus == 1) {
          actions.add(
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _handleAdminAction('admin_ship'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(AppLocalizations.of(context).adminShipAction),
              ),
            ),
          );
        }
        break;
      case 2: // 已发货
      case 3: // 已完成
        if (_currentOrder!.trackingNumber?.isNotEmpty == true) {
          actions.add(
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: _viewShippingStatus,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: AppColors.primary),
                ),
                child: Text(
                  AppLocalizations.of(context).viewTrackingAction,
                  style: TextStyle(color: AppColors.primary),
                ),
              ),
            ),
          );
        }
        break;
    }

    if (actions.isEmpty) return null;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(child: Row(children: actions)),
    );
  }

  /// 处理管理员操作
  void _handleAdminAction(String action) async {
    switch (action) {
      case 'mark_paid':
        await _updatePayStatus(1);
        break;
      case 'refund':
        await _updatePayStatus(2);
        break;
      case 'admin_ship':
        _showAdminShipDialog();
        break;
      case 'complete':
        await _updateOrderStatus(3);
        break;
      case 'cancel':
        await _updateOrderStatus(4);
        break;
      case 'delete':
        await _deleteOrder();
        break;
    }
  }

  /// 更新支付状态
  Future<void> _updatePayStatus(int payStatus) async {
    try {
      await _adminOrderService.updatePayStatus(_currentOrder!.id, payStatus);
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).paymentStatusUpdateSuccessMessage,
        );
        _loadOrderDetail(); // 重新加载订单详情
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).updateFailedMessage(e.toString()),
        );
      }
    }
  }

  /// 更新订单状态
  Future<void> _updateOrderStatus(int status) async {
    try {
      await _adminOrderService.updateOrderStatus(_currentOrder!.id, status);
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).orderStatusUpdateSuccessMessage,
        );
        _loadOrderDetail(); // 重新加载订单详情
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).updateFailedMessage(e.toString()),
        );
      }
    }
  }

  /// 显示管理员发货对话框
  void _showAdminShipDialog() {
    showDialog(
      context: context,
      builder: (context) => AdminShipDialog(
        order: _currentOrder!,
        onShipped: () {
          _loadOrderDetail(); // 重新加载订单详情
        },
      ),
    );
  }

  /// 删除订单
  Future<void> _deleteOrder() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).confirmDeleteOrderTitle),
        content: Text(AppLocalizations.of(context).confirmDeleteOrderMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(AppLocalizations.of(context).cancelAction),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(AppLocalizations.of(context).deleteAction),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _adminOrderService.deleteOrder(_currentOrder!.id);
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).orderDeleteSuccessMessage,
          );
          Navigator.pop(context, true); // 返回并刷新列表
        }
      } catch (e) {
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).deleteFailedMessage(e.toString()),
          );
        }
      }
    }
  }
}
