import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/admin/admin_product_service.dart';
import '../../../services/language_service.dart';
import '../widgets/admin_product_statistics_card.dart';
import '../widgets/admin_product_list_item.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 管理员产品审核页面
class AdminProductReviewPage extends StatefulWidget {
  const AdminProductReviewPage({super.key});

  @override
  State<AdminProductReviewPage> createState() => _AdminProductReviewPageState();
}

class _AdminProductReviewPageState extends State<AdminProductReviewPage>
    with SingleTickerProviderStateMixin {
  final AdminProductService _adminProductService = AdminProductService();

  late TabController _tabController;

  // 数据状态
  bool _isLoading = false;
  List<DoctorProductModel> _products = [];
  ProductStatisticsModel? _statistics;
  List<Map<String, dynamic>> _doctors = [];

  // 筛选状态
  int _currentStatusFilter = -1; // -1表示全部
  int? _currentDoctorFilter;

  // 选择状态
  final Set<int> _selectedProductIds = {};
  bool _isSelectionMode = false;

  // 状态标签 - 将在initState中初始化
  List<String> _statusTabs = [];

  @override
  void initState() {
    super.initState();
    // 延迟初始化状态标签，等待context可用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _statusTabs = [
            AppLocalizations.of(context).all,
            AppLocalizations.of(context).pending,
            AppLocalizations.of(context).approved,
            AppLocalizations.of(context).rejected,
            AppLocalizations.of(context).offline,
          ];
        });
        _initializeTabController();
      }
    });
  }

  /// 初始化TabController
  void _initializeTabController() {
    _tabController = TabController(length: _statusTabs.length, vsync: this);
    _tabController.addListener(_onTabChanged);
    _checkPermissionAndLoadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 检查权限并加载数据
  void _checkPermissionAndLoadData() {
    // 直接加载数据，权限检查由父页面（医生管理页面）负责
    _loadData();
  }

  /// 标签切换事件
  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _currentStatusFilter = _tabController.index - 1; // -1表示全部
        _selectedProductIds.clear();
        _isSelectionMode = false;
      });
      _loadProducts();
    }
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Future.wait([_loadProducts(), _loadStatistics(), _loadDoctors()]);
    } catch (e) {
      print('AdminProductReviewPage: 加载数据失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).loadDataFailed}: $e',
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 加载产品列表
  Future<void> _loadProducts() async {
    try {
      final products = await _adminProductService.getAllProducts(
        status: _currentStatusFilter >= 0 ? _currentStatusFilter : null,
        doctorId: _currentDoctorFilter,
      );

      setState(() {
        _products = products;
      });

      print('AdminProductReviewPage: 产品列表加载完成，数量: ${products.length}');
    } catch (e) {
      print('AdminProductReviewPage: 加载产品列表失败: $e');
      rethrow;
    }
  }

  /// 加载统计信息
  Future<void> _loadStatistics() async {
    try {
      final statistics = await _adminProductService.getStatistics();
      setState(() {
        _statistics = statistics;
      });
      print('AdminProductReviewPage: 统计信息加载完成');
    } catch (e) {
      print('AdminProductReviewPage: 加载统计信息失败: $e');
      rethrow;
    }
  }

  /// 加载医生列表
  Future<void> _loadDoctors() async {
    try {
      final doctors = await _adminProductService.getDoctorsList();
      setState(() {
        _doctors = doctors;
      });
      print('AdminProductReviewPage: 医生列表加载完成，数量: ${doctors.length}');
    } catch (e) {
      print('AdminProductReviewPage: 加载医生列表失败: $e');
      rethrow;
    }
  }

  /// 审核产品
  Future<void> _reviewProduct(int productId, int status, {String? note}) async {
    try {
      await _adminProductService.reviewProduct(
        productId,
        status,
        reviewNote: note,
      );

      if (mounted) {
        ToastUtil.show(context, AppLocalizations.of(context).reviewSuccess);
      }
      await _loadData(); // 重新加载数据
    } catch (e) {
      print('AdminProductReviewPage: 审核产品失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).reviewFailed}: $e',
        );
      }
    }
  }

  /// 批量审核产品
  Future<void> _batchReviewProducts(int status, {String? note}) async {
    if (_selectedProductIds.isEmpty) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseSelectProducts,
      );
      return;
    }

    try {
      await _adminProductService.batchReviewProducts(
        _selectedProductIds.toList(),
        status,
        reviewNote: note,
      );

      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).batchReviewSuccess,
        );
        setState(() {
          _selectedProductIds.clear();
          _isSelectionMode = false;
        });
      }
      await _loadData(); // 重新加载数据
    } catch (e) {
      print('AdminProductReviewPage: 批量审核失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).batchReviewFailed}: $e',
        );
      }
    }
  }

  /// 切换选择模式
  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedProductIds.clear();
      }
    });
  }

  /// 切换产品选择状态
  void _toggleProductSelection(int productId) {
    setState(() {
      if (_selectedProductIds.contains(productId)) {
        _selectedProductIds.remove(productId);
      } else {
        _selectedProductIds.add(productId);
      }
    });
  }

  /// 获取医生的本地化名称
  String _getLocalizedDoctorName(Map<String, dynamic> doctor) {
    final languageCode = LanguageService().getCurrentLanguageCode();
    final nameData = doctor['name'];

    if (nameData == null) return '';

    // 如果是字符串，直接返回
    if (nameData is String) {
      return nameData;
    }

    // 如果是多语言对象，根据当前语言提取
    if (nameData is Map<String, dynamic>) {
      // 优先使用当前语言
      final localizedName = nameData[languageCode]?.toString();
      if (localizedName?.isNotEmpty == true) {
        return localizedName!;
      }

      // 回退策略：中文 > 英文 > 维吾尔语
      final zhName = nameData['zh']?.toString();
      if (zhName?.isNotEmpty == true) return zhName!;

      final enName = nameData['en']?.toString();
      if (enName?.isNotEmpty == true) return enName!;

      final ugName = nameData['ug']?.toString();
      if (ugName?.isNotEmpty == true) return ugName!;
    }

    return doctor['name']?.toString() ?? '';
  }

  /// 全选/取消全选
  void _toggleSelectAll() {
    setState(() {
      if (_selectedProductIds.length == _products.length) {
        _selectedProductIds.clear();
      } else {
        _selectedProductIds.clear();
        _selectedProductIds.addAll(_products.map((p) => p.id));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      body: _isLoading ? _buildLoadingView() : _buildContent(),
      floatingActionButton: _products.isNotEmpty
          ? _buildFloatingActionButtons()
          : null,
    );
  }

  /// 构建加载视图
  Widget _buildLoadingView() {
    return const Center(child: CircularProgressIndicator());
  }

  /// 构建主要内容
  Widget _buildContent() {
    return Column(
      children: [
        // 统计信息卡片
        if (_statistics != null)
          AdminProductStatisticsCard(statistics: _statistics!),

        // 医生筛选
        _buildDoctorFilter(),

        // 状态筛选标签
        _buildStatusTabs(),

        // 产品列表
        Expanded(child: _buildProductList()),
      ],
    );
  }

  /// 构建医生筛选
  Widget _buildDoctorFilter() {
    if (_doctors.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: DropdownButtonFormField<int?>(
        value: _currentDoctorFilter,
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).filterDoctors,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
        ),
        items: [
          DropdownMenuItem<int?>(
            value: null,
            child: Text(AppLocalizations.of(context).allDoctors),
          ),
          ..._doctors.map(
            (doctor) => DropdownMenuItem<int?>(
              value: doctor['id'],
              child: Text(
                '${_getLocalizedDoctorName(doctor)} (${AppLocalizations.of(context).productsCount(doctor['total_products'])})',
              ),
            ),
          ),
        ],
        onChanged: (value) {
          setState(() {
            _currentDoctorFilter = value;
            _selectedProductIds.clear();
            _isSelectionMode = false;
          });
          _loadProducts();
        },
      ),
    );
  }

  /// 构建状态筛选标签
  Widget _buildStatusTabs() {
    return Container(
      color: ThemeHelper.getCardBackground(context),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        labelColor: AppColors.primary,
        unselectedLabelColor: ThemeHelper.getTextSecondary(context),
        indicatorColor: AppColors.primary,
        indicatorWeight: 2,
        indicatorSize: TabBarIndicatorSize.label,
        labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        dividerColor: Colors.transparent,
        tabs: _statusTabs.map((status) => Tab(text: status)).toList(),
      ),
    );
  }

  /// 构建产品列表
  Widget _buildProductList() {
    if (_products.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: ThemeHelper.getTextHint(context),
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context).noProducts,
              style: TextStyle(
                fontSize: 16,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _products.length,
        itemBuilder: (context, index) {
          final product = _products[index];
          return AdminProductListItem(
            product: product,
            isSelected: _selectedProductIds.contains(product.id),
            isSelectionMode: _isSelectionMode,
            onTap: () => _toggleProductSelection(product.id),
            onReview: (status, note) =>
                _reviewProduct(product.id, status, note: note),
          );
        },
      ),
    );
  }

  /// 构建悬浮操作按钮
  Widget _buildFloatingActionButtons() {
    if (_isSelectionMode) {
      // 批量选择模式
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 批量操作按钮（仅在有选中项时显示）
          if (_selectedProductIds.isNotEmpty) ...[
            FloatingActionButton.extended(
              heroTag: "batch_approve",
              onPressed: () => _batchReviewProducts(
                1,
                note: AppLocalizations.of(context).batchApproved,
              ),
              backgroundColor: const Color(0xFF27AE60),
              icon: const Icon(Icons.check, color: Colors.white),
              label: Text(
                '${AppLocalizations.of(context).batchApprove} (${_selectedProductIds.length})',
                style: const TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 8),
            FloatingActionButton.extended(
              heroTag: "batch_reject",
              onPressed: () => _batchReviewProducts(
                2,
                note: AppLocalizations.of(context).batchRejected,
              ),
              backgroundColor: const Color(0xFFE74C3C),
              icon: const Icon(Icons.close, color: Colors.white),
              label: Text(
                '${AppLocalizations.of(context).batchReject} (${_selectedProductIds.length})',
                style: const TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(height: 8),
          ],

          // 底部按钮行：全选按钮 + 退出选择按钮
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 全选/取消全选按钮
              FloatingActionButton(
                heroTag: "toggle_select_all",
                onPressed: _toggleSelectAll,
                backgroundColor: const Color(0xFF3498DB),
                tooltip: _selectedProductIds.length == _products.length
                    ? AppLocalizations.of(context).deselectAll
                    : AppLocalizations.of(context).selectAll,
                child: Icon(
                  _selectedProductIds.length == _products.length
                      ? Icons.deselect
                      : Icons.select_all,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              // 退出选择按钮
              FloatingActionButton(
                heroTag: "cancel_selection",
                onPressed: _toggleSelectionMode,
                backgroundColor: ThemeHelper.getTextSecondary(context),
                tooltip: AppLocalizations.of(context).exitSelection,
                child: const Icon(Icons.close, color: Colors.white),
              ),
            ],
          ),
        ],
      );
    } else {
      // 普通模式 - 显示批量选择按钮
      return FloatingActionButton(
        heroTag: "toggle_selection",
        onPressed: _toggleSelectionMode,
        backgroundColor: AppColors.primary,
        tooltip: AppLocalizations.of(context).batchSelection,
        child: const Icon(Icons.checklist, color: Colors.white),
      );
    }
  }
}
