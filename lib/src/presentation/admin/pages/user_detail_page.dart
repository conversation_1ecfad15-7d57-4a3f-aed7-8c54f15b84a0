import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/admin_user_model.dart';
import '../../../services/admin/user_admin_service.dart';
import '../../../utils/toast_util.dart';
import '../widgets/user_detail_info_card.dart';
import '../widgets/user_balance_integral_card.dart';
import '../widgets/user_role_management_card.dart';
import '../widgets/user_token_management_card.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 用户详情页面
class UserDetailPage extends StatefulWidget {
  final int userId;
  final AdminUserModel? initialUser; // 可选的初始用户数据

  const UserDetailPage({super.key, required this.userId, this.initialUser});

  @override
  State<UserDetailPage> createState() => _UserDetailPageState();
}

class _UserDetailPageState extends State<UserDetailPage> {
  final UserAdminService _userService = UserAdminService();

  AdminUserModel? _user;
  bool _isLoading = true;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialUser != null) {
      _user = widget.initialUser;
      _isLoading = false;
      // 仍然在后台加载最新数据
      _loadUserDetail(showLoading: false);
    } else {
      _loadUserDetail();
    }
  }

  /// 加载用户详情
  Future<void> _loadUserDetail({bool showLoading = true}) async {
    if (showLoading) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      final user = await _userService.getUserDetail(widget.userId);

      if (mounted) {
        setState(() {
          _user = user;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).loadUserDetailFailed}: $e',
        );
      }
    }
  }

  /// 刷新用户数据
  Future<void> _refreshUserData() async {
    setState(() {
      _isRefreshing = true;
    });

    try {
      await _loadUserDetail(showLoading: false);
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  /// 更新用户状态
  Future<void> _updateUserStatus(bool newStatus) async {
    try {
      await _userService.updateUserStatus(widget.userId, newStatus);

      // 更新本地状态
      if (mounted && _user != null) {
        setState(() {
          _user = AdminUserModel(
            id: _user!.id,
            uuid: _user!.uuid,
            nickname: _user!.nickname,
            phone: _user!.phone,
            sex: _user!.sex,
            avatar: _user!.avatar,
            birthday: _user!.birthday,
            money: _user!.money,
            integral: _user!.integral,
            disCode: _user!.disCode,
            status: newStatus,
            isAdmin: _user!.isAdmin,
            isDoctor: _user!.isDoctor,
            doctorId: _user!.doctorId,
            isReferrer: _user!.isReferrer,
            referrerLevel: _user!.referrerLevel,
            registerSource: _user!.registerSource,
            ip: _user!.ip,
            createdAt: _user!.createdAt,
            updatedAt: _user!.updatedAt,
            loginAt: _user!.loginAt,
            doctorInfo: _user!.doctorInfo,
            tokenCount: _user!.tokenCount,
            addressCount: _user!.addressCount,
          );
        });

        ToastUtil.show(
          context,
          newStatus
              ? AppLocalizations.of(context).enableUserSuccess
              : AppLocalizations.of(context).disableUserSuccess,
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).updateUserFailed}: $e',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).userDetail,
          style: FontUtil.createHeadingTextStyle(
            text: AppLocalizations.of(context).userDetail,
          ).copyWith(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        actions: [
          if (_user != null)
            IconButton(
              icon: Icon(
                Icons.refresh,
                color: ThemeHelper.getTextPrimary(context),
              ),
              onPressed: _isRefreshing ? null : _refreshUserData,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _user == null
          ? _buildErrorState()
          : _buildContent(),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: ThemeHelper.getTextSecondary(context),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).loadUserDetailFailed,
            style: TextStyle(
              fontSize: 16,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _loadUserDetail(),
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary),
            child: Text(
              AppLocalizations.of(context).retry,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建主要内容
  Widget _buildContent() {
    return RefreshIndicator(
      onRefresh: _refreshUserData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户基本信息卡片
            UserDetailInfoCard(
              user: _user!,
              onStatusChanged: _updateUserStatus,
              onUserUpdated: () => _loadUserDetail(showLoading: false),
            ),

            const SizedBox(height: 16),

            // 余额和积分管理卡片
            UserBalanceIntegralCard(
              user: _user!,
              onBalanceUpdated: () => _loadUserDetail(showLoading: false),
              onIntegralUpdated: () => _loadUserDetail(showLoading: false),
            ),

            const SizedBox(height: 16),

            // 角色权限管理卡片
            UserRoleManagementCard(
              user: _user!,
              onRoleUpdated: () => _loadUserDetail(showLoading: false),
            ),

            const SizedBox(height: 16),

            // 登录令牌管理卡片
            UserTokenManagementCard(
              user: _user!,
              onTokensUpdated: () => _loadUserDetail(showLoading: false),
            ),
            // ),
            //
            // const SizedBox(height: 16),
            //
            // // 角色权限管理卡片
            // UserRoleManagementCard(
            //   user: _user!,
            //   onRoleUpdated: () => _loadUserDetail(showLoading: false),
            // ),
            //
            // const SizedBox(height: 16),
            //
            // // 登录令牌管理卡片
            // UserTokenManagementCard(
            //   user: _user!,
            //   onTokensUpdated: () => _loadUserDetail(showLoading: false),
            // ),
          ],
        ),
      ),
    );
  }
}
