import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/admin/admin_product_service.dart';
import '../../../services/language_service.dart';
import '../../../utils/toast_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 管理员产品详情页面
class AdminProductDetailPage extends StatefulWidget {
  final int productId;
  final DoctorProductModel? initialProduct;

  const AdminProductDetailPage({
    super.key,
    required this.productId,
    this.initialProduct,
  });

  @override
  State<AdminProductDetailPage> createState() => _AdminProductDetailPageState();
}

class _AdminProductDetailPageState extends State<AdminProductDetailPage> {
  final AdminProductService _adminProductService = AdminProductService();

  DoctorProductModel? _product;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _product = widget.initialProduct;
    if (_product == null) {
      _loadProductDetail();
    }
  }

  /// 加载产品详情
  Future<void> _loadProductDetail() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final product = await _adminProductService.getProductById(
        widget.productId,
      );
      setState(() {
        _product = product;
      });
    } catch (e) {
      print('AdminProductDetailPage: 加载产品详情失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).loadProductDetailFailed}: $e',
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 审核产品
  Future<void> _reviewProduct(int status, {String? note}) async {
    if (_product == null) return;

    try {
      await _adminProductService.reviewProduct(
        _product!.id,
        status,
        reviewNote: note,
      );

      if (mounted) {
        ToastUtil.show(context, AppLocalizations.of(context).reviewSuccess);
        // 返回结果给上一页
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      print('AdminProductDetailPage: 审核产品失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).reviewFailed}: $e',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _product == null
          ? _buildErrorView()
          : _buildContent(),
      bottomNavigationBar: _product?.status == 0 ? _buildBottomActions() : null,
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        AppLocalizations.of(context).productDetail,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ThemeHelper.getTextPrimary(context),
        ),
      ),
      backgroundColor: ThemeHelper.getCardBackground(context),
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: ThemeHelper.getTextPrimary(context),
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }

  /// 构建错误视图
  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: ThemeHelper.getTextHint(context),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).loadProductDetailFailed,
            style: TextStyle(
              fontSize: 16,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadProductDetail,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 构建主要内容
  Widget _buildContent() {
    if (_product == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 产品基本信息
          _buildBasicInfo(),
          const SizedBox(height: 16),

          // 产品图片
          if (_product!.mainImageUrl?.isNotEmpty == true) ...[
            _buildProductImages(),
            const SizedBox(height: 16),
          ],

          // 产品描述
          _buildProductDescription(),
          const SizedBox(height: 16),

          // 产品规格
          if (_product!.specifications != null) ...[
            _buildProductSpecifications(),
            const SizedBox(height: 16),
          ],

          // 医生信息
          _buildDoctorInfo(),
          const SizedBox(height: 16),

          // 审核信息
          _buildReviewInfo(),

          // 底部留白，避免被底部按钮遮挡
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  /// 构建基本信息
  Widget _buildBasicInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  _product!.getLocalizedName(
                    LanguageService().getCurrentLanguageCode(),
                  ),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                ),
              ),
              _buildStatusChip(),
            ],
          ),
          const SizedBox(height: 12),

          // 价格信息
          Row(
            children: [
              Text(
                '¥${_product!.price.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFF27AE60),
                ),
              ),
              if (_product!.originalPrice != null &&
                  _product!.originalPrice! > _product!.price) ...[
                const SizedBox(width: 8),
                Text(
                  '¥${_product!.originalPrice!.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeHelper.getTextHint(context),
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 12),

          // 库存和销量
          Row(
            children: [
              _buildInfoChip(
                icon: Icons.inventory_outlined,
                label:
                    '${AppLocalizations.of(context).inventory}: ${_product!.inventoryCount}',
                color: const Color(0xFF3498DB),
              ),
              const SizedBox(width: 12),
              _buildInfoChip(
                icon: Icons.shopping_cart_outlined,
                label:
                    '${AppLocalizations.of(context).sales}: ${_product!.salesCount}',
                color: const Color(0xFF9B59B6),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建产品图片
  Widget _buildProductImages() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).productImages,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 12),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              _product!.fullMainImageUrl,
              width: double.infinity,
              height: 200,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: double.infinity,
                height: 200,
                color: ThemeHelper.getTextHint(context).withValues(alpha: 0.1),
                child: Icon(
                  Icons.image_not_supported,
                  color: ThemeHelper.getTextHint(context),
                  size: 48,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建产品描述
  Widget _buildProductDescription() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).productDescription,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 12),
          Builder(
            builder: (context) {
              final languageCode = LanguageService().getCurrentLanguageCode();
              final localizedDescription = _product!.getLocalizedDescription(
                languageCode,
              );
              final localizedDetailedDescription = _product!
                  .getLocalizedDetailedDescription(languageCode);

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (localizedDescription.isNotEmpty) ...[
                    Text(
                      localizedDescription,
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeHelper.getTextSecondary(context),
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],
                  if (localizedDetailedDescription.isNotEmpty) ...[
                    Text(
                      AppLocalizations.of(context).detailedDescription,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: ThemeHelper.getTextPrimary(context),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      localizedDetailedDescription,
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeHelper.getTextSecondary(context),
                        height: 1.5,
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  /// 构建产品规格
  Widget _buildProductSpecifications() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).productSpecifications,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 12),
          ...(_product!.specifications as Map<String, dynamic>).entries.map(
            (entry) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 80,
                    child: Text(
                      '${entry.key}:',
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      entry.value.toString(),
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeHelper.getTextPrimary(context),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建医生信息
  Widget _buildDoctorInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).doctorInfo,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.person_outline, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                _product!
                        .getLocalizedDoctorName(
                          LanguageService().getCurrentLanguageCode(),
                        )
                        .isEmpty
                    ? AppLocalizations.of(context).unknownDoctor
                    : _product!.getLocalizedDoctorName(
                        LanguageService().getCurrentLanguageCode(),
                      ),
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.badge_outlined, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'ID: ${_product!.doctorId}',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建审核信息
  Widget _buildReviewInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).reviewInfo,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                '${AppLocalizations.of(context).productId}: ',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
              Text(
                '${_product!.id}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                '${AppLocalizations.of(context).createdAt}: ',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
              Text(
                _formatDateTime(_product!.createdAt),
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                '${AppLocalizations.of(context).updatedAt}: ',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
              Text(
                _formatDateTime(_product!.updatedAt),
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建状态标签
  Widget _buildStatusChip() {
    final statusConfig = _getStatusConfig(_product!.status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusConfig['color'].withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: statusConfig['color'].withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusConfig['icon'], size: 14, color: statusConfig['color']),
          const SizedBox(width: 4),
          Text(
            statusConfig['text'],
            style: TextStyle(
              fontSize: 12,
              color: statusConfig['color'],
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建信息标签
  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        border: Border(
          top: BorderSide(
            color: ThemeHelper.getTextHint(context).withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () => _showRejectDialog(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFE74C3C),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: Text(
                AppLocalizations.of(context).reject,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () => _reviewProduct(
                1,
                note: AppLocalizations.of(context).reviewApproved,
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF27AE60),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: Text(
                AppLocalizations.of(context).approve,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示拒绝对话框
  void _showRejectDialog() {
    final TextEditingController noteController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).rejectReview),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppLocalizations.of(context).confirmRejectProduct(
                _product!.getLocalizedName(
                  LanguageService().getCurrentLanguageCode(),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: noteController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).rejectReason,
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _reviewProduct(
                2,
                note: noteController.text.trim().isEmpty
                    ? null
                    : noteController.text.trim(),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE74C3C),
              foregroundColor: Colors.white,
            ),
            child: Text(AppLocalizations.of(context).confirmReject),
          ),
        ],
      ),
    );
  }

  /// 获取状态配置
  Map<String, dynamic> _getStatusConfig(int status) {
    switch (status) {
      case 0:
        return {
          'text': AppLocalizations.of(context).pending,
          'color': const Color(0xFFF39C12),
          'icon': Icons.pending_outlined,
        };
      case 1:
        return {
          'text': AppLocalizations.of(context).approved,
          'color': const Color(0xFF27AE60),
          'icon': Icons.check_circle_outline,
        };
      case 2:
        return {
          'text': AppLocalizations.of(context).rejected,
          'color': const Color(0xFFE74C3C),
          'icon': Icons.cancel_outlined,
        };
      case 3:
        return {
          'text': AppLocalizations.of(context).offline,
          'color': const Color(0xFF95A5A6),
          'icon': Icons.remove_circle_outline,
        };
      default:
        return {
          'text': AppLocalizations.of(context).unknown,
          'color': const Color(0xFFBDC3C7),
          'icon': Icons.help_outline,
        };
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
