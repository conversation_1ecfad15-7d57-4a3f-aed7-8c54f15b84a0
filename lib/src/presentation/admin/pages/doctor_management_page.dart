import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/doctor_model.dart';
import '../../../services/admin/doctor_admin_service.dart';
import '../../../services/language_service.dart';
import '../../../utils/toast_util.dart';
import 'doctor_edit_page.dart';
import 'admin_product_review_page.dart';
import 'admin_order_management_page.dart';
import 'user_management_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 管理员管理页面 - 包含医生管理和产品审核
class DoctorManagementPage extends StatefulWidget {
  const DoctorManagementPage({super.key});

  @override
  State<DoctorManagementPage> createState() => _DoctorManagementPageState();
}

class _DoctorManagementPageState extends State<DoctorManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this); // 增加到4个标签页
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      body: TabBarView(
        controller: _tabController,
        children: [
          const DoctorListTab(),
          const AdminProductReviewPage(),
          const AdminOrderManagementPage(),
          UserManagementTab(), // 新增用户管理标签页
        ],
      ),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        AppLocalizations.of(context).adminManagementTitle,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ThemeHelper.getTextPrimary(context),
        ),
      ),
      backgroundColor: ThemeHelper.getCardBackground(context),
      elevation: 0,
      bottom: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: ThemeHelper.getTextSecondary(context),
        indicatorColor: AppColors.primary,
        indicatorWeight: 2,
        labelStyle: FontUtil.createTabLabelStyle(fontSize: 14),
        unselectedLabelStyle: FontUtil.createTabUnselectedLabelStyle(
          fontSize: 14,
        ),
        dividerColor: Colors.transparent, // 移除下方的白色分割线
        tabs: [
          Tab(text: AppLocalizations.of(context).doctorManagementTab),
          Tab(text: AppLocalizations.of(context).productReviewTab),
          Tab(text: AppLocalizations.of(context).orderManagementTab),
          Tab(text: AppLocalizations.of(context).userManagementTab),
        ],
      ),
    );
  }
}

/// 医生列表标签页
class DoctorListTab extends StatefulWidget {
  const DoctorListTab({super.key});

  @override
  State<DoctorListTab> createState() => _DoctorListTabState();
}

class _DoctorListTabState extends State<DoctorListTab> {
  List<DoctorModel> _doctors = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDoctors();
  }

  /// 加载医生列表
  Future<void> _loadDoctors() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final doctors = await DoctorAdminService().getAllDoctors();
      if (mounted) {
        setState(() {
          _doctors = doctors;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).loadDoctorListFailed}: $e',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildDoctorList(),
      floatingActionButton: FloatingActionButton(
        onPressed: _addDoctor,
        backgroundColor: AppColors.primary,
        tooltip: AppLocalizations.of(context).addDoctor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  /// 构建医生列表
  Widget _buildDoctorList() {
    if (_doctors.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.medical_services_outlined,
              size: 64,
              color: ThemeHelper.getTextHint(context),
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context).noDoctorData,
              style: TextStyle(
                fontSize: 16,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDoctors,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _doctors.length,
        itemBuilder: (context, index) {
          final doctor = _doctors[index];
          return _buildDoctorCard(doctor);
        },
      ),
    );
  }

  /// 构建医生卡片
  Widget _buildDoctorCard(DoctorModel doctor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _editDoctor(doctor),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 医生头像
                _buildDoctorAvatar(doctor),
                const SizedBox(width: 16),
                // 医生信息
                Expanded(child: _buildDoctorInfo(doctor)),
                // 状态指示器
                _buildStatusIndicator(doctor),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建医生头像
  Widget _buildDoctorAvatar(DoctorModel doctor) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: doctor.fullAvatarUrl.isNotEmpty
            ? Image.network(
                doctor.fullAvatarUrl,
                width: 56,
                height: 56,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar();
                },
              )
            : _buildDefaultAvatar(),
      ),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withValues(alpha: 0.8),
            AppColors.primary.withValues(alpha: 0.6),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: const Icon(Icons.person, color: Colors.white, size: 32),
    );
  }

  /// 构建医生信息
  Widget _buildDoctorInfo(DoctorModel doctor) {
    final languageCode = LanguageService().getCurrentLanguageCode();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 医生姓名
        Text(
          doctor.getLocalizedName(languageCode),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        const SizedBox(height: 4),
        // 专科
        Text(
          doctor.getLocalizedSpecialty(languageCode),
          style: TextStyle(
            fontSize: 14,
            color: ThemeHelper.getTextSecondary(context),
          ),
        ),
        const SizedBox(height: 4),
        // 联系方式
        if (doctor.phone != null && doctor.phone!.isNotEmpty)
          Row(
            children: [
              Icon(
                Icons.phone,
                size: 12,
                color: ThemeHelper.getTextHint(context),
              ),
              const SizedBox(width: 4),
              Text(
                doctor.phone!,
                style: TextStyle(
                  fontSize: 12,
                  color: ThemeHelper.getTextHint(context),
                ),
              ),
            ],
          ),
        if (doctor.phone != null && doctor.phone!.isNotEmpty)
          const SizedBox(height: 4),
        // 简短描述
        Text(
          doctor.getLocalizedDescription(languageCode),
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextHint(context),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// 构建状态指示器
  Widget _buildStatusIndicator(DoctorModel doctor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: doctor.isActive
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        doctor.isActive
            ? AppLocalizations.of(context).enabled
            : AppLocalizations.of(context).disabled,
        style: TextStyle(
          fontSize: 12,
          color: doctor.isActive ? AppColors.success : AppColors.error,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// 添加医生
  void _addDoctor() {
    // 创建一个空的医生模型用于添加
    final newDoctor = DoctorModel(
      id: 0, // 新医生ID为0，后端会分配真实ID
      name: '',
      specialty: null,
      avatarUrl: null,
      description: null,
      detailedInfo: null,
      systemPrompt: null,
      llmModelName: null,
      isActive: true,
      yearsOfExperience: 0,
      rating: 0.0,
      digitalHumanUrl: null,
      phone: null,
      address: null,
    );

    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => DoctorEditPage(
              doctor: newDoctor,
              onSaved: (createdDoctor) async {
                // 注意：DoctorEditPage已经处理了创建逻辑，这里的onSaved回调实际上不会被调用
                // 因为DoctorEditPage内部直接处理保存并返回结果
                // 这个回调保留是为了兼容性，但实际逻辑在DoctorEditPage内部
                print(
                  '${AppLocalizations.of(context).doctorCreationCompleteCallback}: ${createdDoctor.name}',
                );
              },
            ),
          ),
        )
        .then((_) {
          // 当从编辑页面返回时，刷新医生列表
          _loadDoctors();
        });
  }

  /// 编辑医生
  void _editDoctor(DoctorModel doctor) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DoctorEditPage(
          doctor: doctor,
          onSaved: (updatedDoctor) async {
            // 更新本地列表 - 医生编辑页面已经直接调用了API
            if (!mounted) return;
            setState(() {
              final index = _doctors.indexWhere((d) => d.id == doctor.id);
              if (index != -1) {
                _doctors[index] = updatedDoctor;
              }
            });
          },
        ),
      ),
    );
  }
}
