import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/admin/admin_order_service.dart';
import '../../../services/language_service.dart';
import '../widgets/admin_order_statistics_card.dart';
import '../widgets/admin_order_list_item.dart';
import '../widgets/admin_ship_dialog.dart';
import 'admin_order_detail_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 管理员订单管理页面
class AdminOrderManagementPage extends StatefulWidget {
  const AdminOrderManagementPage({super.key});

  @override
  State<AdminOrderManagementPage> createState() =>
      _AdminOrderManagementPageState();
}

class _AdminOrderManagementPageState extends State<AdminOrderManagementPage>
    with SingleTickerProviderStateMixin {
  final AdminOrderService _adminOrderService = AdminOrderService();

  late TabController _tabController;

  // 数据状态
  bool _isLoading = false;
  List<ProductOrderModel> _orders = [];
  OrderStatisticsModel? _statistics;
  List<Map<String, dynamic>> _doctors = [];

  // 筛选状态
  int _currentStatusFilter = -1; // -1表示全部
  int? _currentDoctorFilter;

  // 选择状态
  final Set<int> _selectedOrderIds = {};
  bool _isSelectionMode = false;

  // 状态标签 - 将在initState中初始化
  List<String> _statusTabs = [];

  @override
  void initState() {
    super.initState();
    // 延迟初始化，等待context可用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeTabsAndLoadData();
    });
  }

  /// 初始化标签页并加载数据
  void _initializeTabsAndLoadData() {
    if (!mounted) return;

    // 初始化状态标签
    _statusTabs = [
      AppLocalizations.of(context).orderStatusAll,
      AppLocalizations.of(context).orderStatusPending,
      AppLocalizations.of(context).orderStatusPendingShipment,
      AppLocalizations.of(context).orderStatusShipped,
      AppLocalizations.of(context).orderStatusCompleted,
      AppLocalizations.of(context).orderStatusCancelled,
    ];

    // 初始化TabController
    _tabController = TabController(length: _statusTabs.length, vsync: this);
    _tabController.addListener(_onTabChanged);

    // 加载数据
    _checkPermissionAndLoadData();
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  /// 标签页变化监听
  void _onTabChanged() {
    if (_tabController.indexIsChanging) return;

    setState(() {
      _currentStatusFilter = _tabController.index == 0
          ? -1
          : _tabController.index - 1;
      _clearSelection();
    });
  }

  /// 检查权限并加载数据
  Future<void> _checkPermissionAndLoadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Future.wait([_loadOrders(), _loadStatistics(), _loadDoctors()]);
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '加载数据失败: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 加载订单列表
  Future<void> _loadOrders() async {
    try {
      print('AdminOrderManagementPage: 开始加载订单列表');
      final orders = await _adminOrderService.getAllOrders();

      setState(() {
        _orders = orders;
      });

      print('AdminOrderManagementPage: 订单列表加载完成，数量: ${orders.length}');
    } catch (e) {
      print('AdminOrderManagementPage: 加载订单列表失败: $e');
      rethrow;
    }
  }

  /// 加载统计信息
  Future<void> _loadStatistics() async {
    try {
      final statistics = await _adminOrderService.getStatistics();
      setState(() {
        _statistics = statistics;
      });
      print('AdminOrderManagementPage: 统计信息加载完成');
    } catch (e) {
      print('AdminOrderManagementPage: 加载统计信息失败: $e');
      rethrow;
    }
  }

  /// 加载医生列表
  Future<void> _loadDoctors() async {
    try {
      final doctors = await _adminOrderService.getDoctorsList();
      setState(() {
        _doctors = doctors;
      });
      print('AdminOrderManagementPage: 医生列表加载完成，数量: ${doctors.length}');
    } catch (e) {
      print('AdminOrderManagementPage: 加载医生列表失败: $e');
      rethrow;
    }
  }

  /// 更新订单状态
  Future<void> _updateOrderStatus(
    int orderId,
    int status, {
    String? note,
  }) async {
    try {
      await _adminOrderService.updateOrderStatus(orderId, status, note: note);

      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).orderStatusUpdateSuccessMessage,
        );
        _loadOrders(); // 重新加载订单列表
        _loadStatistics(); // 重新加载统计信息
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '更新失败: $e');
      }
    }
  }

  /// 更新支付状态
  Future<void> _updatePayStatus(int orderId, int payStatus) async {
    try {
      await _adminOrderService.updatePayStatus(orderId, payStatus);

      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).paymentStatusUpdateSuccessMessage,
        );
        _loadOrders(); // 重新加载订单列表
        _loadStatistics(); // 重新加载统计信息
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '更新失败: $e');
      }
    }
  }

  /// 显示管理员发货对话框
  void _showAdminShipDialog(ProductOrderModel order) {
    showDialog(
      context: context,
      builder: (context) => AdminShipDialog(
        order: order,
        onShipped: () {
          _loadOrders(); // 重新加载订单列表
          _loadStatistics(); // 重新加载统计信息
        },
      ),
    );
  }

  /// 批量更新订单状态
  Future<void> _batchUpdateOrderStatus(int status, {String? note}) async {
    if (_selectedOrderIds.isEmpty) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseSelectOrdersMessage,
      );
      return;
    }

    try {
      await _adminOrderService.batchUpdateOrderStatus(
        _selectedOrderIds.toList(),
        status,
        note: note,
      );

      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).batchOperationSuccess,
        );
        _clearSelection();
        _loadOrders(); // 重新加载订单列表
        _loadStatistics(); // 重新加载统计信息
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).batchOperationFailed(e.toString()),
        );
      }
    }
  }

  /// 删除订单
  Future<void> _deleteOrder(int orderId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(context).confirmDeleteTitle,
          style: FontUtil.createHeadingTextStyle(
            text: AppLocalizations.of(context).confirmDeleteTitle,
          ),
        ),
        content: Text(
          AppLocalizations.of(context).confirmDeleteMessage,
          style: FontUtil.createBodyTextStyle(
            text: AppLocalizations.of(context).confirmDeleteMessage,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              AppLocalizations.of(context).cancelAction,
              style: FontUtil.createButtonTextStyle(
                text: AppLocalizations.of(context).cancelAction,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(
              AppLocalizations.of(context).deleteAction,
              style: FontUtil.createButtonTextStyle(
                text: AppLocalizations.of(context).deleteAction,
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _adminOrderService.deleteOrder(orderId);
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).orderDeleteSuccessMessage,
          );
          _loadOrders(); // 重新加载订单列表
          _loadStatistics(); // 重新加载统计信息
        }
      } catch (e) {
        if (mounted) {
          ToastUtil.show(context, '删除失败: $e');
        }
      }
    }
  }

  /// 清除选择
  void _clearSelection() {
    setState(() {
      _selectedOrderIds.clear();
      _isSelectionMode = false;
    });
  }

  /// 切换选择模式
  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedOrderIds.clear();
      }
    });
  }

  /// 全选/取消全选
  void _toggleSelectAll() {
    setState(() {
      if (_selectedOrderIds.length == _getFilteredOrders().length) {
        _selectedOrderIds.clear();
      } else {
        _selectedOrderIds.clear();
        _selectedOrderIds.addAll(_getFilteredOrders().map((order) => order.id));
      }
    });
  }

  /// 获取医生显示名称（支持多语言）
  String? _getDoctorDisplayName(Map<String, dynamic> doctor) {
    final name = doctor['name'];
    if (name is Map<String, dynamic>) {
      final languageCode = LanguageService().getCurrentLanguageCode();
      return name[languageCode]?.toString() ??
          name['zh']?.toString() ??
          name.values.first?.toString();
    }
    return name?.toString();
  }

  /// 获取筛选后的订单
  List<ProductOrderModel> _getFilteredOrders() {
    List<ProductOrderModel> filtered = _orders;

    // 按状态筛选
    if (_currentStatusFilter != -1) {
      filtered = filtered
          .where((order) => order.orderStatus == _currentStatusFilter)
          .toList();
    }

    // 按医生筛选
    if (_currentDoctorFilter != null) {
      filtered = filtered
          .where((order) => order.doctorId == _currentDoctorFilter)
          .toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      body: _isLoading ? _buildLoadingView() : _buildContent(),
      floatingActionButton: _orders.isNotEmpty
          ? _buildFloatingActionButtons()
          : null,
    );
  }

  /// 构建加载视图
  Widget _buildLoadingView() {
    return const Center(child: CircularProgressIndicator());
  }

  /// 构建主要内容
  Widget _buildContent() {
    return Column(
      children: [
        // 统计信息卡片
        if (_statistics != null)
          AdminOrderStatisticsCard(statistics: _statistics!),

        // 医生筛选
        _buildDoctorFilter(),

        // 状态筛选标签
        _buildStatusTabs(),

        // 订单列表
        Expanded(child: _buildOrderList()),
      ],
    );
  }

  /// 构建医生筛选
  Widget _buildDoctorFilter() {
    if (_doctors.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: DropdownButtonFormField<int?>(
        value: _currentDoctorFilter,
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).filterDoctorsLabel,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
        ),
        items: [
          DropdownMenuItem<int?>(
            value: null,
            child: Text(AppLocalizations.of(context).allDoctorsOption),
          ),
          ..._doctors.map(
            (doctor) => DropdownMenuItem<int?>(
              value: doctor['id'],
              child: Text(
                _getDoctorDisplayName(doctor) ??
                    AppLocalizations.of(context).unknownDoctorLabel,
              ),
            ),
          ),
        ],
        onChanged: (value) {
          setState(() {
            _currentDoctorFilter = value;
            _clearSelection();
          });
        },
      ),
    );
  }

  /// 构建状态筛选标签
  Widget _buildStatusTabs() {
    return Container(
      color: ThemeHelper.getCardBackground(context),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        labelColor: AppColors.primary,
        unselectedLabelColor: ThemeHelper.getTextSecondary(context),
        indicatorColor: AppColors.primary,
        indicatorWeight: 2,
        indicatorSize: TabBarIndicatorSize.label,
        labelStyle: FontUtil.createTabLabelStyle(fontSize: 14),
        unselectedLabelStyle: FontUtil.createTabUnselectedLabelStyle(
          fontSize: 14,
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        dividerColor: Colors.transparent,
        tabs: _statusTabs.map((status) => Tab(text: status)).toList(),
      ),
    );
  }

  /// 构建订单列表
  Widget _buildOrderList() {
    final filteredOrders = _getFilteredOrders();

    if (filteredOrders.isEmpty) {
      return _buildEmptyView();
    }

    return RefreshIndicator(
      onRefresh: _checkPermissionAndLoadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredOrders.length,
        itemBuilder: (context, index) {
          final order = filteredOrders[index];
          final isSelected = _selectedOrderIds.contains(order.id);

          return AdminOrderListItem(
            order: order,
            isSelected: isSelected,
            isSelectionMode: _isSelectionMode,
            onTap: () => _handleOrderTap(order),
            onSelectionChanged: (selected) =>
                _handleOrderSelection(order.id, selected),
            onStatusUpdate: (status, note) =>
                _updateOrderStatus(order.id, status, note: note),
            onPayStatusUpdate: (payStatus) =>
                _updatePayStatus(order.id, payStatus),
            onAdminShip: () => _showAdminShipDialog(order),
            onDelete: () => _deleteOrder(order.id),
          );
        },
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: ThemeHelper.getTextHint(context),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).noOrdersTitle,
            style: TextStyle(
              fontSize: 16,
              color: ThemeHelper.getTextSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).noOrdersMessage,
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextHint(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 选择模式切换按钮
        FloatingActionButton(
          heroTag: "selection_mode",
          onPressed: _toggleSelectionMode,
          backgroundColor: _isSelectionMode ? AppColors.primary : Colors.grey,
          child: Icon(
            _isSelectionMode ? Icons.close : Icons.checklist,
            color: Colors.white,
          ),
        ),

        // 批量操作按钮（仅在选择模式下显示）
        if (_isSelectionMode) ...[
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: "select_all",
            onPressed: _toggleSelectAll,
            backgroundColor:
                _selectedOrderIds.length == _getFilteredOrders().length
                ? AppColors.primary
                : Colors.grey,
            child: const Icon(Icons.select_all, color: Colors.white),
          ),
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: "batch_actions",
            onPressed: _selectedOrderIds.isNotEmpty
                ? _showBatchActionsDialog
                : null,
            backgroundColor: _selectedOrderIds.isNotEmpty
                ? AppColors.primary
                : Colors.grey,
            child: const Icon(Icons.more_horiz, color: Colors.white),
          ),
        ],
      ],
    );
  }

  /// 处理订单点击
  void _handleOrderTap(ProductOrderModel order) async {
    if (_isSelectionMode) {
      _handleOrderSelection(order.id, !_selectedOrderIds.contains(order.id));
    } else {
      // 跳转到订单详情页面
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AdminOrderDetailPage(order: order),
        ),
      );

      // 如果从详情页面返回并且有变化，刷新订单列表
      if (result == true) {
        _loadOrders();
        _loadStatistics();
      }
    }
  }

  /// 处理订单选择
  void _handleOrderSelection(int orderId, bool selected) {
    setState(() {
      if (selected) {
        _selectedOrderIds.add(orderId);
      } else {
        _selectedOrderIds.remove(orderId);
      }
    });
  }

  /// 显示批量操作对话框
  void _showBatchActionsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(
            context,
          ).batchOperationsTitle(_selectedOrderIds.length),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.check_circle, color: Colors.green),
              title: Text(AppLocalizations.of(context).markAsCompletedAction),
              onTap: () {
                Navigator.pop(context);
                _batchUpdateOrderStatus(3); // 已完成
              },
            ),
            ListTile(
              leading: const Icon(Icons.cancel, color: Colors.red),
              title: Text(AppLocalizations.of(context).markAsCancelledAction),
              onTap: () {
                Navigator.pop(context);
                _batchUpdateOrderStatus(4); // 已取消
              },
            ),
            ListTile(
              leading: const Icon(Icons.local_shipping, color: Colors.blue),
              title: Text(AppLocalizations.of(context).markAsShippedAction),
              onTap: () {
                Navigator.pop(context);
                _batchUpdateOrderStatus(2); // 已发货
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppLocalizations.of(context).cancelAction),
          ),
        ],
      ),
    );
  }
}
