import 'package:flutter/material.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/admin_user_model.dart';
import '../../../services/admin/user_admin_service.dart';
import '../../../utils/toast_util.dart';
import '../widgets/user_statistics_card.dart';
import '../widgets/user_list_item.dart';
import '../widgets/user_filter_widget.dart';
import 'user_detail_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 用户管理标签页
class UserManagementTab extends StatefulWidget {
  const UserManagementTab({super.key});

  @override
  State<UserManagementTab> createState() => _UserManagementTabState();
}

class _UserManagementTabState extends State<UserManagementTab> {
  final UserAdminService _userService = UserAdminService();
  final TextEditingController _searchController = TextEditingController();

  List<AdminUserModel> _users = [];
  UserStatisticsModel? _statistics;
  bool _isLoading = true;
  bool _isLoadingMore = false;

  // 分页参数
  int _currentPage = 1;
  final int _pageSize = 20;
  bool _hasMoreData = true;

  // 筛选参数
  String? _searchKeyword;
  int? _statusFilter;
  int? _roleFilter;
  int? _genderFilter;
  int? _sourceFilter;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 滚动监听
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMoreData) {
        _loadMoreUsers();
      }
    }
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 并行加载统计数据和用户列表
      final results = await Future.wait([
        _userService.getUserStatistics(),
        _userService.getUsers(
          page: 1,
          pageSize: _pageSize,
          keyword: _searchKeyword,
          status: _statusFilter,
          isAdmin: _roleFilter == 1 ? 1 : null,
          isDoctor: _roleFilter == 2 ? 1 : null,
          sex: _genderFilter,
          registerSource: _sourceFilter,
        ),
      ]);

      if (mounted) {
        setState(() {
          _statistics = results[0] as UserStatisticsModel;
          final userResponse = results[1] as UserListResponse;
          _users = userResponse.users;
          _currentPage = 1;
          _hasMoreData = userResponse.users.length >= _pageSize;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).loadUserListFailed}: $e',
        );
      }
    }
  }

  /// 仅加载用户列表（筛选时使用，不重新加载统计数据）
  Future<void> _loadUserListOnly() async {
    try {
      final userResponse = await _userService.getUsers(
        page: 1,
        pageSize: _pageSize,
        keyword: _searchKeyword,
        status: _statusFilter,
        isAdmin: _roleFilter == 1 ? 1 : null,
        isDoctor: _roleFilter == 2 ? 1 : null,
        sex: _genderFilter,
        registerSource: _sourceFilter,
      );

      if (mounted) {
        setState(() {
          _users = userResponse.users;
          _currentPage = 1;
          _hasMoreData = userResponse.users.length >= _pageSize;
        });
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).loadUserListFailed}: $e',
        );
      }
    }
  }

  /// 加载更多用户
  Future<void> _loadMoreUsers() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final userResponse = await _userService.getUsers(
        page: _currentPage + 1,
        pageSize: _pageSize,
        keyword: _searchKeyword,
        status: _statusFilter,
        isAdmin: _roleFilter == 1 ? 1 : null,
        isDoctor: _roleFilter == 2 ? 1 : null,
        sex: _genderFilter,
        registerSource: _sourceFilter,
      );

      if (mounted) {
        setState(() {
          _users.addAll(userResponse.users);
          _currentPage++;
          _hasMoreData = userResponse.users.length >= _pageSize;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).loadUserListFailed}: $e',
        );
      }
    }
  }

  /// 应用筛选
  void _applyFilters({
    String? keyword,
    int? status,
    int? role,
    int? gender,
    int? source,
  }) {
    setState(() {
      _searchKeyword = keyword?.isNotEmpty == true ? keyword : null;
      _statusFilter = status;
      _roleFilter = role;
      _genderFilter = gender;
      _sourceFilter = source;
    });
    // 筛选时只重新加载用户列表，不重新加载统计数据
    _loadUserListOnly();
  }

  /// 搜索用户
  void _searchUsers(String keyword) {
    _applyFilters(
      keyword: keyword,
      status: _statusFilter,
      role: _roleFilter,
      gender: _genderFilter,
      source: _sourceFilter,
    );
  }

  /// 刷新数据
  Future<void> _refreshData() async {
    await _loadInitialData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  /// 构建主要内容
  Widget _buildContent() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: CustomScrollView(
        controller: _scrollController, // 添加滚动控制器
        slivers: [
          // 统计信息卡片
          if (_statistics != null)
            SliverToBoxAdapter(
              child: UserStatisticsCard(statistics: _statistics!),
            ),

          // 筛选器
          SliverToBoxAdapter(
            child: UserFilterWidget(
              onFiltersChanged: _applyFilters,
              onSearch: _searchUsers,
              initialKeyword: _searchKeyword,
              initialStatus: _statusFilter,
              initialRole: _roleFilter,
              initialGender: _genderFilter,
              initialSource: _sourceFilter,
            ),
          ),

          // 用户列表
          _buildUserListSliver(),
        ],
      ),
    );
  }

  /// 构建用户列表 Sliver
  Widget _buildUserListSliver() {
    if (_users.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.people_outline,
                size: 64,
                color: ThemeHelper.getTextSecondary(context),
              ),
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).noUsersFound,
                style: TextStyle(
                  fontSize: 16,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate((context, index) {
          if (index >= _users.length) {
            // 加载更多指示器
            return const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          final user = _users[index];
          return UserListItem(
            user: user,
            onTap: () => _navigateToUserDetail(user),
            onStatusChanged: (newStatus) => _updateUserStatus(user, newStatus),
          );
        }, childCount: _users.length + (_isLoadingMore ? 1 : 0)),
      ),
    );
  }

  /// 导航到用户详情页面
  void _navigateToUserDetail(AdminUserModel user) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            UserDetailPage(userId: user.id, initialUser: user),
      ),
    );
  }

  /// 更新用户状态
  Future<void> _updateUserStatus(AdminUserModel user, bool newStatus) async {
    try {
      await _userService.updateUserStatus(user.id, newStatus);

      // 更新本地状态
      setState(() {
        final index = _users.indexWhere((u) => u.id == user.id);
        if (index != -1) {
          _users[index] = AdminUserModel(
            id: user.id,
            uuid: user.uuid,
            nickname: user.nickname,
            phone: user.phone,
            sex: user.sex,
            avatar: user.avatar,
            birthday: user.birthday,
            money: user.money,
            integral: user.integral,
            disCode: user.disCode,
            status: newStatus,
            isAdmin: user.isAdmin,
            isDoctor: user.isDoctor,
            doctorId: user.doctorId,
            isReferrer: user.isReferrer,
            referrerLevel: user.referrerLevel,
            registerSource: user.registerSource,
            ip: user.ip,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            loginAt: user.loginAt,
            doctorInfo: user.doctorInfo,
            tokenCount: user.tokenCount,
            addressCount: user.addressCount,
          );
        }
      });

      if (mounted) {
        ToastUtil.show(
          context,
          newStatus
              ? AppLocalizations.of(context).enableUserSuccess
              : AppLocalizations.of(context).disableUserSuccess,
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).updateUserFailed}: $e',
        );
      }
    }
  }
}
