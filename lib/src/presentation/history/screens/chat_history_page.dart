import 'package:flutter/material.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../services/chat_repository.dart';
import '../../../services/language_service.dart';
import '../../../models/conversation_model.dart';
import '../../../utils/chat_navigation_helper.dart';

/// 聊天历史页面
class ChatHistoryPage extends StatefulWidget {
  const ChatHistoryPage({super.key});

  @override
  State<ChatHistoryPage> createState() => _ChatHistoryPageState();
}

class _ChatHistoryPageState extends State<ChatHistoryPage> {
  DateTime selectedDate = DateTime.now();
  late DateTime currentMonth;
  late List<DateTime> monthDays;
  late ScrollController _dateScrollController;

  // 聊天数据相关状态
  List<ConversationModel> _conversations = [];
  bool _isLoading = false;
  final ChatRepository _chatRepository = ChatRepository();

  @override
  void initState() {
    super.initState();
    currentMonth = DateTime(selectedDate.year, selectedDate.month);
    _dateScrollController = ScrollController();
    _generateMonthDays();
    // 延迟滚动到当前日期，确保ListView已经构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedDate();
    });

    // 加载聊天历史数据
    _loadChatHistory();
  }

  @override
  void dispose() {
    _dateScrollController.dispose();
    super.dispose();
  }

  /// 加载聊天历史数据
  Future<void> _loadChatHistory() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // 监听对话流并获取第一个结果
      final conversationsStream = _chatRepository.getConversations();
      await for (final conversations in conversationsStream) {
        setState(() {
          _conversations = conversations;
          _isLoading = false;
        });
        break; // 只获取第一次结果
      }
    } catch (e) {
      debugPrint('加载聊天历史失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 根据选中日期过滤聊天记录
  List<ConversationModel> _getFilteredConversations() {
    if (_conversations.isEmpty) return [];

    final selectedDateOnly = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
    );

    return _conversations.where((conversation) {
      final conversationDate = DateTime(
        conversation.updatedAt.year,
        conversation.updatedAt.month,
        conversation.updatedAt.day,
      );
      return conversationDate.isAtSameMomentAs(selectedDateOnly);
    }).toList();
  }

  /// 生成当月的所有日期
  void _generateMonthDays() {
    monthDays = [];
    final lastDay = DateTime(currentMonth.year, currentMonth.month + 1, 0);

    for (int day = 1; day <= lastDay.day; day++) {
      monthDays.add(DateTime(currentMonth.year, currentMonth.month, day));
    }
  }

  /// 切换月份
  void _changeMonth(int monthOffset) {
    setState(() {
      currentMonth = DateTime(
        currentMonth.year,
        currentMonth.month + monthOffset,
      );
      _generateMonthDays();
      // 如果选中的日期不在新月份中，则选择新月份的第一天
      if (selectedDate.month != currentMonth.month ||
          selectedDate.year != currentMonth.year) {
        selectedDate = DateTime(currentMonth.year, currentMonth.month, 1);
      }
    });
    // 切换月份后滚动到选中的日期
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedDate();
    });
  }

  /// 滚动到选中的日期
  void _scrollToSelectedDate() {
    if (!_dateScrollController.hasClients) return;

    final selectedIndex = monthDays.indexWhere(
      (date) =>
          date.day == selectedDate.day &&
          date.month == selectedDate.month &&
          date.year == selectedDate.year,
    );

    if (selectedIndex != -1) {
      // 每个日期项的宽度是58（50 + 8的margin）
      const itemWidth = 58.0;
      final scrollOffset = selectedIndex * itemWidth;

      // 获取ListView的可见宽度
      final viewportWidth = _dateScrollController.position.viewportDimension;

      // 计算滚动位置，让选中的日期居中显示
      final targetOffset = scrollOffset - (viewportWidth / 2) + (itemWidth / 2);

      _dateScrollController.animateTo(
        targetOffset.clamp(0.0, _dateScrollController.position.maxScrollExtent),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ThemeHelper.getTextPrimary(context),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          AppLocalizations.of(context).chatHistoryPageTitle,
          style: TextStyle(
            color: ThemeHelper.getTextPrimary(context),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Column(
        children: [
          // 日期选择区域
          _buildDateSelector(),
          // 聊天记录列表
          Expanded(child: _buildChatList()),
        ],
      ),
    );
  }

  /// 构建日期选择器
  Widget _buildDateSelector() {
    return Container(
      color: ThemeHelper.getCardBackground(context),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 月份年份标题
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: Icon(
                  Icons.chevron_left,
                  color: ThemeHelper.getTextPrimary(context),
                ),
                onPressed: () => _changeMonth(-1),
              ),
              GestureDetector(
                onTap: () => _showYearMonthPicker(context),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _formatYearMonth(currentMonth),
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: AppColors.primary,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.chevron_right,
                  color: ThemeHelper.getTextPrimary(context),
                ),
                onPressed: () => _changeMonth(1),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 日期选择器
          SizedBox(
            height: 60,
            child: ListView.builder(
              controller: _dateScrollController,
              scrollDirection: Axis.horizontal,
              itemCount: monthDays.length,
              itemBuilder: (context, index) {
                final date = monthDays[index];
                final isSelected =
                    date.day == selectedDate.day &&
                    date.month == selectedDate.month &&
                    date.year == selectedDate.year;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedDate = date;
                    });
                    // 选择日期后滚动到该日期
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _scrollToSelectedDate();
                    });
                  },
                  child: Container(
                    width: 50,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.primary
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? AppColors.primary
                            : Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          date.day.toString(),
                          style: TextStyle(
                            color: isSelected
                                ? Colors.white
                                : ThemeHelper.getTextPrimary(context),
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          _getWeekday(date.weekday),
                          style: TextStyle(
                            color: isSelected
                                ? Colors.white.withValues(alpha: 0.8)
                                : ThemeHelper.getTextSecondary(context),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 获取星期几的本地化显示
  String _getWeekday(int weekday) {
    final l10n = AppLocalizations.of(context);
    const weekdays = [
      'weekdayMon',
      'weekdayTue',
      'weekdayWed',
      'weekdayThu',
      'weekdayFri',
      'weekdaySat',
      'weekdaySun',
    ];

    switch (weekdays[weekday - 1]) {
      case 'weekdayMon':
        return l10n.weekdayMon;
      case 'weekdayTue':
        return l10n.weekdayTue;
      case 'weekdayWed':
        return l10n.weekdayWed;
      case 'weekdayThu':
        return l10n.weekdayThu;
      case 'weekdayFri':
        return l10n.weekdayFri;
      case 'weekdaySat':
        return l10n.weekdaySat;
      case 'weekdaySun':
        return l10n.weekdaySun;
      default:
        return '';
    }
  }

  /// 格式化年月显示
  String _formatYearMonth(DateTime date) {
    final l10n = AppLocalizations.of(context);
    final currentLang = LanguageService().currentLocale.languageCode;

    if (currentLang == 'en') {
      // 英文格式：January 2024
      const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];
      return '${months[date.month - 1]} ${date.year}';
    } else {
      // 中文格式：2024年1月
      return '${date.year}${l10n.year}${date.month}${l10n.month}';
    }
  }

  /// 显示年月选择器弹窗
  Future<void> _showYearMonthPicker(BuildContext context) async {
    final result = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return _YearMonthPickerDialog(initialDate: currentMonth);
      },
    );

    if (result != null) {
      setState(() {
        currentMonth = result;
        _generateMonthDays();
        // 选择新月份的第一天
        selectedDate = DateTime(currentMonth.year, currentMonth.month, 1);
      });
      // 选择新年月后滚动到选中的日期
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToSelectedDate();
      });
    }
  }

  /// 构建聊天记录列表
  Widget _buildChatList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // 获取过滤后的聊天记录
    final filteredConversations = _getFilteredConversations();

    if (filteredConversations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context).noChatRecordsForDate,
              style: TextStyle(
                color: ThemeHelper.getTextSecondary(context),
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: filteredConversations.length,
      separatorBuilder: (context, index) =>
          Divider(height: 1, color: Colors.grey.shade200),
      itemBuilder: (context, index) {
        final conversation = filteredConversations[index];
        return _buildChatItem(conversation);
      },
    );
  }

  /// 构建单个聊天记录项
  Widget _buildChatItem(ConversationModel conversation) {
    return InkWell(
      onTap: () {
        // 使用ChatNavigationHelper请求加载对话
        ChatNavigationHelper().requestLoadConversation(conversation.id);

        // 标记需要切换到健康助手页面
        ChatNavigationHelper().markSwitchToAiGuideNeeded();

        // 返回到主页面，主页面会自动切换到健康助手页面并加载对话
        Navigator.of(context).popUntil((route) => route.isFirst);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 4),
        child: Row(
          children: [
            // 医生头像
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: ClipOval(
                child: conversation.doctor?.fullAvatarUrl.isNotEmpty == true
                    ? Image.network(
                        conversation.doctor!.fullAvatarUrl,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildDefaultAvatar();
                        },
                      )
                    : _buildDefaultAvatar(),
              ),
            ),
            const SizedBox(width: 12),
            // 聊天信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 医生姓名和专科
                  Row(
                    children: [
                      Flexible(
                        child: Text(
                          conversation.doctor?.name ?? '未知医生',
                          style: TextStyle(
                            color: ThemeHelper.getTextPrimary(context),
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      if (conversation.doctor?.specialty != null) ...[
                        const SizedBox(width: 8),
                        Flexible(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              conversation.doctor!.specialty!,
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.primary,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  // 聊天标题预览
                  Text(
                    conversation.title.isNotEmpty
                        ? conversation.title
                        : AppLocalizations.of(context).newConversation,
                    style: TextStyle(
                      color: ThemeHelper.getTextSecondary(context),
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            // 编辑按钮、时间和消息数量
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // 编辑按钮
                IconButton(
                  onPressed: () => _showEditTitleDialog(conversation),
                  icon: Icon(
                    Icons.edit_outlined,
                    size: 16,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatTime(conversation.updatedAt),
                  style: TextStyle(
                    color: ThemeHelper.getTextSecondary(context),
                    fontSize: 12,
                  ),
                ),
                if (conversation.messageCount > 0) ...[
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      '${conversation.messageCount}',
                      style: TextStyle(fontSize: 10, color: AppColors.primary),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 格式化时间显示
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (messageDate == today) {
      // 今天显示时间
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      // 其他日期显示月日
      return '${dateTime.month}/${dateTime.day}';
    }
  }

  /// 构建默认医生头像
  Widget _buildDefaultAvatar() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withValues(alpha: 0.8),
            AppColors.primary.withValues(alpha: 0.6),
          ],
        ),
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.local_hospital, color: Colors.white, size: 24),
    );
  }

  /// 显示编辑标题对话框
  void _showEditTitleDialog(ConversationModel conversation) {
    final TextEditingController titleController = TextEditingController(
      text: conversation.title,
    );

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: ThemeHelper.getCardBackground(context),
          title: Text(
            AppLocalizations.of(context).editTitle,
            style: TextStyle(
              color: ThemeHelper.getTextPrimary(context),
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: TextField(
            controller: titleController,
            autofocus: true,
            maxLength: 50,
            decoration: InputDecoration(
              hintText: AppLocalizations.of(context).enterNewTitle,
              hintStyle: TextStyle(
                color: ThemeHelper.getTextSecondary(context),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.primary,
                  width: 2,
                ),
              ),
            ),
            style: TextStyle(color: ThemeHelper.getTextPrimary(context)),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                AppLocalizations.of(context).cancel,
                style: TextStyle(color: ThemeHelper.getTextSecondary(context)),
              ),
            ),
            TextButton(
              onPressed: () async {
                final newTitle = titleController.text.trim();
                if (newTitle.isNotEmpty && newTitle != conversation.title) {
                  Navigator.of(dialogContext).pop();
                  await _updateConversationTitle(conversation.id, newTitle);
                } else {
                  Navigator.of(dialogContext).pop();
                }
              },
              child: Text(
                AppLocalizations.of(context).confirm,
                style: const TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 更新对话标题
  Future<void> _updateConversationTitle(
    String conversationId,
    String newTitle,
  ) async {
    try {
      // 调用ChatRepository更新标题
      await _chatRepository.updateConversationTitle(conversationId, newTitle);

      // 重新加载聊天历史数据
      await _loadChatHistory();

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context).titleUpdateSuccess),
            backgroundColor: AppColors.primary,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppLocalizations.of(context).titleUpdateFailed(e.toString()),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}

/// 年月选择器弹窗
class _YearMonthPickerDialog extends StatefulWidget {
  final DateTime initialDate;

  const _YearMonthPickerDialog({required this.initialDate});

  @override
  State<_YearMonthPickerDialog> createState() => _YearMonthPickerDialogState();
}

class _YearMonthPickerDialogState extends State<_YearMonthPickerDialog> {
  late int selectedYear;
  late int selectedMonth;

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialDate.year;
    selectedMonth = widget.initialDate.month;
  }

  /// 格式化年份显示
  String _formatYear(int year) {
    final l10n = AppLocalizations.of(context);
    return '$year${l10n.year}';
  }

  /// 格式化月份显示
  String _formatMonth(int month) {
    final l10n = AppLocalizations.of(context);
    final currentLang = LanguageService().currentLocale.languageCode;

    if (currentLang == 'en') {
      // 英文格式：January, February, etc.
      const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];
      return months[month - 1];
    } else {
      // 中文格式：1月, 2月, etc.
      return '$month${l10n.month}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Text(
              AppLocalizations.of(context).selectYearMonth,
              style: TextStyle(
                color: ThemeHelper.getTextPrimary(context),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            // 年份选择
            Row(
              children: [
                Text(
                  AppLocalizations.of(context).yearLabel,
                  style: TextStyle(
                    color: ThemeHelper.getTextPrimary(context),
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButton<int>(
                    value: selectedYear,
                    isExpanded: true,
                    items: List.generate(10, (index) {
                      final year = DateTime.now().year - 5 + index;
                      return DropdownMenuItem<int>(
                        value: year,
                        child: Text(_formatYear(year)),
                      );
                    }),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedYear = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 月份选择
            Row(
              children: [
                Text(
                  AppLocalizations.of(context).monthLabel,
                  style: TextStyle(
                    color: ThemeHelper.getTextPrimary(context),
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButton<int>(
                    value: selectedMonth,
                    isExpanded: true,
                    items: List.generate(12, (index) {
                      final month = index + 1;
                      return DropdownMenuItem<int>(
                        value: month,
                        child: Text(_formatMonth(month)),
                      );
                    }),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedMonth = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            // 按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    AppLocalizations.of(context).cancel,
                    style: TextStyle(
                      color: ThemeHelper.getTextSecondary(context),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () {
                    final selectedDate = DateTime(selectedYear, selectedMonth);
                    Navigator.of(context).pop(selectedDate);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(AppLocalizations.of(context).ok),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
