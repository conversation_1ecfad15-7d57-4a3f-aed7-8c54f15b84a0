import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/app_toast.dart';
import '../../../utils/chat_navigation_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../pages/health_assistant_page.dart';
import '../pages/profile_page.dart';
import '../../product_list/pages/product_list_page.dart';

/// 健康助手主页面 - 包含底部导航栏和不同功能页面，支持通过底部导航按钮切换
class HomeScreen extends StatefulWidget {
  final int? selectedDoctorId;
  final bool switchToHealthAssistant;

  const HomeScreen({
    super.key,
    this.selectedDoctorId,
    this.switchToHealthAssistant = false,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // 当前选中的页面索引 - 默认显示AI导游页面
  int _currentIndex = 0;

  // PageView控制器，用于程序化控制页面切换
  late PageController _pageController;

  // 双击退出相关
  DateTime? _lastBackPressedTime;

  // 页面列表 - 健康助手、产品列表和我的页面
  final List<Widget> _pages = [
    const HealthAssistantPage(), // 健康助手页面
    const ProductListPage(), // 产品列表页面
    const ProfilePage(), // 个人页面
  ];

  @override
  void initState() {
    super.initState();

    // 如果通过参数传递了医生选择，默认显示健康助手页面
    if (widget.selectedDoctorId != null && widget.switchToHealthAssistant) {
      _currentIndex = 0;
    }

    _pageController = PageController(initialPage: _currentIndex);

    // 添加应用生命周期监听器
    WidgetsBinding.instance.addObserver(this);

    // 处理医生选择
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.selectedDoctorId != null && widget.switchToHealthAssistant) {
        // 直接设置医生选择并通知健康助手页面
        ChatNavigationHelper().setSelectedDoctorId(widget.selectedDoctorId!);
        _notifyPageVisibility(0);
      } else {
        // 检查是否有其他待处理的导航请求
        _handlePendingNavigation();
      }
    });
  }

  /// 处理待处理的导航请求
  void _handlePendingNavigation() {
    if (ChatNavigationHelper().hasPendingConversation ||
        ChatNavigationHelper().hasPendingDoctorSelection) {
      setState(() {
        _currentIndex = 0;
      });
      _pageController.jumpToPage(0);
      _notifyPageVisibility(0);
    }
  }

  // 通知页面可见性变化
  void _notifyPageVisibility(int currentIndex) {
    // 如果切换到健康助手页面，处理待处理的请求
    if (currentIndex == 0) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ChatNavigationHelper().processPendingConversationIfNeeded();
        ChatNavigationHelper().processPendingDoctorSelectionIfNeeded();
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _pageController.dispose();
    super.dispose();
  }

  // 处理底部导航栏点击 - 直接跳转
  void _onBottomNavTap(int index) {
    if (index != _currentIndex) {
      // 提供轻微的触觉反馈（仅在点击时）
      HapticFeedback.selectionClick();

      setState(() {
        _currentIndex = index;
      });

      // 直接跳转到指定页面，无动画
      _pageController.jumpToPage(index);

      // 通知页面可见性变化
      _notifyPageVisibility(index);
    }
  }

  // 处理PageView页面切换 - 始终更新状态以保持同步
  void _onPageChanged(int index) {
    if (_currentIndex != index) {
      setState(() {
        _currentIndex = index;
      });

      // 当切换到健康助手页面时，播放轻微的触觉反馈
      if (index == 0) {
        HapticFeedback.lightImpact();
      }
    }

    // 通知页面可见性变化
    _notifyPageVisibility(index);
  }

  /// 处理返回键逻辑 - 双击退出
  Future<bool> _handleBackPressed() async {
    final currentTime = DateTime.now();

    // 如果当前有Toast显示，说明用户已经按过一次返回键
    if (AppToast.isVisible) {
      // 检查是否在有效时间窗口内
      if (_lastBackPressedTime != null &&
          currentTime.difference(_lastBackPressedTime!).inSeconds <= 3) {
        // 在有效时间窗口内，退出应用
        SystemNavigator.pop();
        return true;
      }
    }

    // 记录这次按下返回键的时间
    _lastBackPressedTime = currentTime;

    // 显示退出提示
    AppToast.showExitConfirm(
      context,
      AppLocalizations.of(context).exitAppConfirm,
      duration: const Duration(seconds: 3),
    );

    // 不退出应用
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return PopScope(
      canPop: false, // 禁用默认的返回行为
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          await _handleBackPressed();
        }
      },
      child: Scaffold(
        body: Stack(
          children: [
            // 主要内容 - PageView
            PageView(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              // 禁用所有滑动手势
              physics: const NeverScrollableScrollPhysics(),
              children: _pages,
            ),
          ],
        ),
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            color: isDark ? const Color(0xFF1E1E1E) : AppColors.white,
            boxShadow: [
              BoxShadow(
                color: isDark
                    ? Colors.black.withAlpha(77)
                    : AppColors.black.withAlpha(51),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, -1),
              ),
            ],
          ),
          child: Theme(
            // 移除Material水波纹效果
            data: Theme.of(context).copyWith(
              splashColor: Colors.transparent, // 移除水波纹
              highlightColor: Colors.transparent, // 移除高亮效果
              hoverColor: Colors.transparent, // 移除悬停效果
              focusColor: Colors.transparent, // 移除焦点效果
              // 彻底移除所有Material交互效果
              splashFactory: NoSplash.splashFactory, // 移除水波纹工厂
            ),
            child: BottomNavigationBar(
              currentIndex: _currentIndex,
              onTap: _onBottomNavTap,
              backgroundColor: isDark
                  ? const Color(0xFF1E1E1E)
                  : AppColors.white,
              selectedItemColor: AppColors.primary,
              unselectedItemColor: isDark
                  ? Colors.grey.shade400
                  : AppColors.darkGrey,
              selectedLabelStyle: FontUtil.createTabLabelStyle(
                text: AppLocalizations.of(context).bottomNavAiGuide,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: FontUtil.createTabUnselectedLabelStyle(
                text: AppLocalizations.of(context).bottomNavAiGuide,
                fontSize: 12,
                fontWeight: FontWeight.normal,
              ),
              type: BottomNavigationBarType.fixed,
              elevation: 0,
              // 移除默认的Material行为
              enableFeedback: false, // 禁用系统触觉反馈，使用自定义的
              mouseCursor: SystemMouseCursors.click, // 确保鼠标悬停体验
              items: [
                BottomNavigationBarItem(
                  icon: _buildNavIcon(Icons.health_and_safety_outlined, 0),
                  activeIcon: _buildNavIcon(
                    Icons.health_and_safety,
                    0,
                    isActive: true,
                  ),
                  label: AppLocalizations.of(context).bottomNavAiGuide,
                ),
                BottomNavigationBarItem(
                  icon: _buildNavIcon(Icons.list_alt_outlined, 1),
                  activeIcon: _buildNavIcon(Icons.list_alt, 1, isActive: true),
                  label: AppLocalizations.of(context).bottomNavSearch,
                ),
                BottomNavigationBarItem(
                  icon: _buildNavIcon(Icons.person_outline, 2),
                  activeIcon: _buildNavIcon(Icons.person, 2, isActive: true),
                  label: AppLocalizations.of(context).bottomNavProfile,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建导航图标，提供简洁的选中状态指示
  Widget _buildNavIcon(IconData icon, int index, {bool isActive = false}) {
    final isSelected = _currentIndex == index;

    return AnimatedContainer(
      // 使用系统推荐的动画时长，自动适配刷新率
      duration: kThemeAnimationDuration,
      curve: Curves.easeInOutCubic, // 使用更流畅的缓动曲线
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        // 简洁的背景色变化，无其他特效
        color: isSelected && isActive
            ? AppColors.primary.withValues(alpha: 0.1)
            : Colors.transparent,
      ),
      child: AnimatedScale(
        // 图标缩放动画，适配刷新率
        duration: kThemeAnimationDuration,
        curve: Curves.easeInOutCubic,
        scale: isActive ? 1.0 : 0.95, // 轻微的缩放效果
        child: Icon(
          icon,
          size: isActive ? 26 : 24, // 选中时稍微放大
          color: isSelected
              ? AppColors.primary
              : (Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade400
                    : AppColors.darkGrey),
        ),
      ),
    );
  }
}
