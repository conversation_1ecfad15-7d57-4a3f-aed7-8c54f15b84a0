import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../../../config/themes/app_colors.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_info_manager_service.dart';
import '../../../models/user_model.dart';
import '../../../models/user_profile_model.dart';
import '../../../config/routes/app_routes.dart';
import '../../../common/widgets/user_avatar_widget.dart';
import '../../../common/utils/responsive_util.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../services/login_check_service.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../settings/screens/settings_screen.dart';
import '../../distribution_management/distribution_management.dart';
import '../../admin/pages/doctor_management_page.dart';
import '../../doctor_management/pages/doctor_management_page.dart'
    as doctor_mgmt;
import '../../history/screens/chat_history_page.dart';
import '../../orders/pages/my_orders_page.dart';
import '../widgets/health_profile_card.dart';
import '../../cart/pages/cart_page.dart';
import 'favorite_doctors_page.dart';
import 'liked_doctors_page.dart';

/// 个人资料页面 - 现代化设计，显示用户信息和功能菜单
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: UserInfoManagerService(),
      builder: (context, child) {
        // 获取用户信息 - 优先使用完整的用户信息
        final userInfoManager = UserInfoManagerService();
        final user = AuthService().currentUser;
        final userProfile = userInfoManager.currentUserProfile;
        final isLoggedIn = user != null;

        return _buildProfilePage(context, user, userProfile, isLoggedIn);
      },
    );
  }

  Widget _buildProfilePage(
    BuildContext context,
    dynamic user,
    dynamic userProfile,
    bool isLoggedIn,
  ) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).profileTitle,
          style: TextStyleUtil.getAppBarTitleStyle(context),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 用户信息卡片（现在包含健康档案信息）
            _buildUserProfileCard(context, user, userProfile, isLoggedIn),

            // 点赞收藏购物车按钮
            if (isLoggedIn) _buildInteractionButtons(context),

            // 减少间距，让卡片和菜单更紧密连接
            SizedBox(
              height: ResponsiveUtil.smallSpacing(context) / 2,
            ), // 从largeSpacing改为更小的间距
            // 功能菜单列表
            _buildMenuSection(context),
          ],
        ),
      ),
    );
  }

  /// 构建用户信息卡片
  Widget _buildUserProfileCard(
    BuildContext context,
    dynamic user,
    dynamic userProfile,
    bool isLoggedIn,
  ) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // 检查是否为VIP会员，如果是年度会员使用金色主题
    final isVipUser = userProfile?.vip == true || user?.vip == true;
    final isAnnualVip =
        (userProfile?.vipData?.type == 1) || (user?.vipData?.type == 1);

    // 根据VIP状态和主题模式选择颜色
    List<Color> gradientColors;

    if (isVipUser && isAnnualVip) {
      // 年度VIP - 豪华金色主题
      if (isDark) {
        gradientColors = [
          const Color(0xFF2D1B00), // 深棕色背景
          const Color(0xFF4A2C00), // 中棕色
          const Color(0xFF6B3E00), // 金棕色
        ];
      } else {
        gradientColors = [
          const Color(0xFFFFF8E1), // 浅金色背景
          const Color(0xFFFFECB3), // 金色
          const Color(0xFFFFD54F), // 深金色
        ];
      }
    } else if (isVipUser) {
      // 普通VIP - 优雅紫色主题
      if (isDark) {
        gradientColors = [
          const Color(0xFF1A0D2E), // 深紫色背景
          const Color(0xFF2D1B4E), // 中紫色
          const Color(0xFF4A2C7A), // 紫色
        ];
      } else {
        gradientColors = [
          const Color(0xFFF3E5F5), // 浅紫色背景
          const Color(0xFFE1BEE7), // 紫色
          const Color(0xFFCE93D8), // 深紫色
        ];
      }
    } else {
      // 普通用户 - 现代健康绿色主题
      if (isDark) {
        gradientColors = [
          const Color(0xFF0D2818), // 深绿色背景
          const Color(0xFF1B4332), // 中绿色
          const Color(0xFF2D5A3D), // 绿色
        ];
      } else {
        gradientColors = [
          const Color(0xFFE8F5E8), // 浅绿色背景
          const Color(0xFFC8E6C9), // 绿色
          const Color(0xFFA5D6A7), // 深绿色
        ];
      }
    }

    return Container(
      margin: ResponsiveUtil.cardPadding(context),
      padding: EdgeInsets.all(
        ResponsiveUtil.largeSpacing(context) * 1.5,
      ), // 增加padding
      constraints: BoxConstraints(
        minHeight: ResponsiveUtil.fromTextTheme(
          context,
          multiplier: 8.0, // 从8.0增加到12.0，使卡片更高
        ),
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
          stops: const [0.0, 0.5, 1.0], // 简化渐变停止点
        ),
        borderRadius: BorderRadius.circular(
          ResponsiveUtil.borderRadius(context, baseRadius: 24), // 更大的圆角
        ),
        boxShadow: isDark
            ? [
                // 暗色模式下的微妙灰色阴影
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.4),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                  spreadRadius: -2,
                ),
              ]
            : [
                // 亮色模式下的柔和灰色阴影
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                  spreadRadius: -3,
                ),
                // 微妙的外灰色阴影
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                  spreadRadius: -8,
                ),
              ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户基本信息行（头像 + 用户信息）
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 头像
                _buildUserAvatar(context, isLoggedIn),

                SizedBox(width: ResponsiveUtil.largeSpacing(context) * 2),
                // 用户信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      // 用户名和编辑按钮在同一行
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // 用户名或登录提示 - 占据可用空间
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                if (!isLoggedIn) {
                                  AppRoutes.navigateToLogin(context);
                                }
                              },
                              child: Text(
                                isLoggedIn
                                    ? (userProfile?.nickname ??
                                          user?.username ??
                                          AppLocalizations.of(context).user)
                                    : AppLocalizations.of(context).clickToLogin,
                                style: TextStyleUtil.getCustomStyle(
                                  context: context,
                                  baseFontSize: 20, // 从18增加到20
                                  fontWeight: FontWeight.bold,
                                  color: isLoggedIn
                                      ? ThemeHelper.getTextPrimary(context)
                                      : AppColors.primary,
                                ),
                              ),
                            ),
                          ),

                          // 编辑按钮 - 只在登录时显示
                          if (isLoggedIn) ...[
                            SizedBox(
                              width: ResponsiveUtil.smallSpacing(context),
                            ),
                            GestureDetector(
                              onTap: () {
                                _showEditProfile(context);
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal:
                                      ResponsiveUtil.smallSpacing(context) *
                                      1.8,
                                  vertical:
                                      ResponsiveUtil.smallSpacing(context) *
                                      1.2,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(
                                    ResponsiveUtil.borderRadius(
                                      context,
                                      baseRadius: 16,
                                    ),
                                  ),
                                  border: Border.all(
                                    color: AppColors.primary.withValues(
                                      alpha: 0.3,
                                    ),
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.edit,
                                      size: ResponsiveUtil.fromTextTheme(
                                        context,
                                        multiplier: 1.3,
                                      ),
                                      color: AppColors.primary,
                                    ),
                                    SizedBox(
                                      width:
                                          ResponsiveUtil.smallSpacing(context) *
                                          1.2,
                                    ),
                                    Text(
                                      AppLocalizations.of(
                                        context,
                                      ).editProfileButton,
                                      style: TextStyleUtil.getCustomStyle(
                                        context: context,
                                        baseFontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),

                      SizedBox(
                        height: ResponsiveUtil.mediumSpacing(context) * 0.8,
                      ),

                      // 手机号或提示 - 单独一行，有足够空间
                      SizedBox(
                        width: double.infinity, // 确保有足够宽度
                        child: Text(
                          isLoggedIn
                              ? (userProfile?.phone ??
                                    user?.phone ??
                                    '139****8877')
                              : AppLocalizations.of(
                                  context,
                                ).loginToEnjoyMoreFeatures,
                          style: TextStyleUtil.getCustomStyle(
                            context: context,
                            baseFontSize: 15, // 从14增加到15
                            color: ThemeHelper.getTextSecondary(context),
                          ),
                          overflow: TextOverflow.visible, // 允许文字显示完整
                          maxLines: 2, // 允许最多两行
                        ),
                      ),

                      SizedBox(
                        height: ResponsiveUtil.mediumSpacing(context) * 0.8,
                      ),

                      // VIP标识或状态 - 单独一行，有足够空间
                      if (isLoggedIn)
                        SizedBox(
                          width: double.infinity, // 确保有足够宽度
                          child: Row(
                            children: [
                              // VIP徽章
                              _buildVipBadge(context, userProfile, user),

                              // 显示分销等级（如果是分销员）
                              if (userProfile?.isReferrer == true ||
                                  user?.vipData != null) ...[
                                SizedBox(
                                  width: ResponsiveUtil.smallSpacing(context),
                                ),
                                _buildDistributorBadge(
                                  context,
                                  userProfile,
                                  user,
                                ),
                              ],
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 健康档案信息 - 放在用户基本信息下方
          CompactHealthProfile(
            userProfile: userProfile,
            isLoggedIn: isLoggedIn,
          ),
        ],
      ),
    );
  }

  /// 构建用户头像
  Widget _buildUserAvatar(BuildContext context, bool isLoggedIn) {
    return UserAvatarWidget(
      size: ResponsiveUtil.fromTextTheme(context, multiplier: 5.5),
      showBorder: true,
      borderWidth: ResponsiveUtil.smallSpacing(context) / 2,
      borderColor: AppColors.white,
    );
  }

  /// 获取VIP显示文本
  String _getVipDisplayText(
    BuildContext context,
    dynamic userProfile,
    dynamic user,
  ) {
    // 优先使用完整的用户资料信息
    if (userProfile?.vipData != null) {
      return _localizeVipName(
        context,
        userProfile.vipData.vipName,
        userProfile.vipData.type,
      );
    } else if (userProfile?.vip == true) {
      return AppLocalizations.of(context).vipMemberBadge;
    } else if (user?.vipData != null) {
      return _localizeVipName(context, user.vipData.vipName, user.vipData.type);
    } else if (user?.vip == true) {
      return AppLocalizations.of(context).vipMemberBadge;
    } else {
      return AppLocalizations.of(context).normalUserBadge;
    }
  }

  /// 本地化VIP名称
  String _localizeVipName(BuildContext context, String vipName, int? type) {
    // 根据VIP类型返回本地化的名称
    switch (type) {
      case 1: // 年度VIP
        return AppLocalizations.of(context).annualVipMember;
      case 2: // 月度VIP
        return AppLocalizations.of(context).monthlyVipMember;
      case 3: // 终身VIP
        return AppLocalizations.of(context).lifetimeVipMember;
      default:
        // 如果类型未知，尝试根据名称匹配
        if (vipName.contains('年度') || vipName.contains('Annual')) {
          return AppLocalizations.of(context).annualVipMember;
        } else if (vipName.contains('月度') || vipName.contains('Monthly')) {
          return AppLocalizations.of(context).monthlyVipMember;
        } else if (vipName.contains('终身') || vipName.contains('Lifetime')) {
          return AppLocalizations.of(context).lifetimeVipMember;
        } else {
          // 默认返回通用VIP会员
          return AppLocalizations.of(context).vipMemberBadge;
        }
    }
  }

  /// 获取分销员显示文本
  String _getReferrerDisplayText(
    BuildContext context,
    dynamic userProfile,
    dynamic user,
  ) {
    // 优先使用完整的用户资料信息
    if (userProfile?.isReferrer == true) {
      if (userProfile?.levelName != null && userProfile.levelName.isNotEmpty) {
        return userProfile.levelName;
      } else {
        return AppLocalizations.of(
          context,
        ).distributorLevelBadge(userProfile?.referrerLevel?.toString() ?? '0');
      }
    }
    return AppLocalizations.of(context).distributorBadge;
  }

  /// 构建VIP徽章
  Widget _buildVipBadge(
    BuildContext context,
    dynamic userProfile,
    dynamic user,
  ) {
    final vipText = _getVipDisplayText(context, userProfile, user);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // 检查是否为年度VIP
    final isAnnualVip =
        (userProfile?.vipData?.type == 1) || (user?.vipData?.type == 1);

    // 根据VIP类型和主题模式选择柔和的颜色
    Color backgroundColor;
    Color textColor;
    Color iconColor;
    IconData badgeIcon;

    if (isAnnualVip) {
      // 年度VIP - 高贵的金色主题
      if (isDark) {
        backgroundColor = const Color(
          0xFFB8860B,
        ).withValues(alpha: 0.7); // 暗金色，柔和
        textColor = const Color(0xFFFFF8DC); // 象牙白，温和对比
        iconColor = const Color(0xFFFFF8DC);
      } else {
        // 亮色模式下的高贵金色
        backgroundColor = const Color(0xFFCD853F); // 秘鲁金，更高贵的金色
        textColor = const Color(0xFFFFFFFF); // 纯白色，清晰对比
        iconColor = const Color(0xFFFFFFFF);
      }
      badgeIcon = Icons.star; // 年度VIP用星星图标
    } else {
      // 普通VIP - 高贵的紫色主题
      if (isDark) {
        backgroundColor = const Color(
          0xFF6B46C1,
        ).withValues(alpha: 0.7); // 柔和紫色
        textColor = const Color(0xFFF3E8FF); // 淡紫白色
        iconColor = const Color(0xFFF3E8FF);
      } else {
        // 亮色模式下的高贵紫色
        backgroundColor = const Color(0xFF7C3AED); // 皇家紫，更高贵的紫色
        textColor = const Color(0xFFFFFFFF); // 纯白色，清晰对比
        iconColor = const Color(0xFFFFFFFF);
      }
      badgeIcon = Icons.diamond; // 普通VIP用钻石图标
    }

    return Container(
      constraints: BoxConstraints(
        minWidth: ResponsiveUtil.fromTextTheme(
          context,
          multiplier: 4.0,
        ), // 确保最小宽度
        maxWidth: ResponsiveUtil.fromTextTheme(
          context,
          multiplier: 10.0,
        ), // 限制最大宽度防止过长
      ),
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveUtil.smallSpacing(context),
        vertical: ResponsiveUtil.smallSpacing(context) / 2,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(
          ResponsiveUtil.borderRadius(context, baseRadius: 12),
        ),
        border: Border.all(
          color: isDark
              ? textColor.withValues(alpha: 0.2)
              : Colors.white.withValues(alpha: 0.6),
          width: isDark ? 0.5 : 1.0,
        ),
        boxShadow: isDark
            ? []
            : [
                BoxShadow(
                  color: backgroundColor.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                  spreadRadius: 0,
                ),
              ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            badgeIcon,
            color: iconColor,
            size: ResponsiveUtil.fromTextTheme(context, multiplier: 0.9),
          ),
          SizedBox(width: ResponsiveUtil.smallSpacing(context) / 2),
          Flexible(
            child: Text(
              vipText,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 12,
                fontWeight: FontWeight.w600,
                color: textColor,
              ),
              overflow: TextOverflow.visible, // 改为visible，确保文本完整显示
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分销员徽章
  Widget _buildDistributorBadge(
    BuildContext context,
    dynamic userProfile,
    dynamic user,
  ) {
    final distributorText = _getReferrerDisplayText(context, userProfile, user);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // 分销员专用的橙色主题
    Color backgroundColor;
    Color textColor;
    Color iconColor;
    IconData badgeIcon = Icons.business_center; // 分销员使用公文包图标

    if (isDark) {
      backgroundColor = const Color(0xFFFF9500).withValues(alpha: 0.7); // 柔和橙色
      textColor = const Color(0xFFFFF3E0); // 象牙白色
      iconColor = const Color(0xFFFFF3E0);
    } else {
      // 亮色模式下的橙色
      backgroundColor = const Color(0xFFFF9500); // 橙色
      textColor = const Color(0xFFFFFFFF); // 纯白色，清晰对比
      iconColor = const Color(0xFFFFFFFF);
    }

    return Container(
      constraints: BoxConstraints(
        minWidth: ResponsiveUtil.fromTextTheme(
          context,
          multiplier: 4.0,
        ), // 确保最小宽度
        maxWidth: ResponsiveUtil.fromTextTheme(
          context,
          multiplier: 10.0,
        ), // 限制最大宽度防止过长
      ),
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveUtil.smallSpacing(context),
        vertical: ResponsiveUtil.smallSpacing(context) / 2,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(
          ResponsiveUtil.borderRadius(context, baseRadius: 12),
        ),
        border: Border.all(
          color: isDark
              ? textColor.withValues(alpha: 0.2)
              : Colors.white.withValues(alpha: 0.6),
          width: isDark ? 0.5 : 1.0,
        ),
        boxShadow: isDark
            ? []
            : [
                BoxShadow(
                  color: backgroundColor.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                  spreadRadius: 0,
                ),
              ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            badgeIcon,
            color: iconColor,
            size: ResponsiveUtil.fromTextTheme(context, multiplier: 0.9),
          ),
          SizedBox(width: ResponsiveUtil.smallSpacing(context) / 2),
          Flexible(
            child: Text(
              distributorText,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 12,
                fontWeight: FontWeight.w600,
                color: textColor,
              ),
              overflow: TextOverflow.visible, // 改为visible，确保文本完整显示
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建功能菜单区域
  Widget _buildMenuSection(BuildContext context) {
    // 获取用户信息以检查管理员权限
    final userInfoManager = UserInfoManagerService();
    final user = AuthService().currentUser;
    final userProfile = userInfoManager.currentUserProfile;

    // 首先检查用户是否已登录
    final isLoggedIn = AuthService().isLoggedIn;

    // 只有在已登录的情况下才检查管理员和医生权限
    final isAdmin =
        isLoggedIn && (userProfile?.isAdmin == true || user?.isAdmin == true);

    // 检查是否为医生 - 需要同时满足 is_doctor=true 和 doctor_id 不为空
    // 优先使用UserProfileModel中的信息，如果没有则使用UserModel中的信息
    final isDoctor = isLoggedIn && _isDoctorUser(user, userProfile);

    return Container(
      margin: ResponsiveUtil.cardPadding(context),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(
          ResponsiveUtil.borderRadius(context, baseRadius: 12),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.withAlpha(Colors.black, 13),
            blurRadius: ResponsiveUtil.mediumSpacing(context),
            offset: Offset(0, ResponsiveUtil.smallSpacing(context) / 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuItem(
            context,
            icon: Icons.receipt_long,
            iconColor: const Color(0xFF109D58),
            title: AppLocalizations.of(context).myOrders,
            onTap: () => _navigateToMyOrders(context),
          ),
          _buildMenuItem(
            context,
            icon: Icons.share,
            iconColor: AppColors.warning,
            title: AppLocalizations.of(context).shareApp,
            onTap: () => _handleShare(context),
          ),
          _buildMenuItem(
            context,
            icon: Icons.history,
            iconColor: AppColors.primary,
            title: AppLocalizations.of(context).chatHistory,
            onTap: () => _navigateToChatHistory(context),
          ),
          _buildMenuItem(
            context,
            icon: Icons.business_center,
            iconColor: AppColors.error,
            title: AppLocalizations.of(context).distributionManagement,
            onTap: () => _handleDistribution(context),
          ),
          // 医生选项 - 只有医生可见
          if (isDoctor)
            _buildMenuItem(
              context,
              icon: Icons.store_outlined,
              iconColor: const Color(0xFF27AE60),
              title: AppLocalizations.of(context).doctorManagement,
              onTap: () => _handleProductManagement(context),
            ),
          // 管理员选项 - 只有管理员可见
          if (isAdmin)
            _buildMenuItem(
              context,
              icon: Icons.admin_panel_settings,
              iconColor: AppColors.primary,
              title: AppLocalizations.of(context).adminManagement,
              onTap: () => _handleDoctorManagement(context),
            ),
          _buildMenuItem(
            context,
            icon: Icons.settings_outlined,
            iconColor: AppColors.textSecondaryStatic,
            title: AppLocalizations.of(context).bottomNavSettings,
            onTap: () => _navigateToSettings(context),
            isLast: true,
          ),
        ],
      ),
    );
  }

  /// 构建菜单项
  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required Color iconColor,
    required String title,
    required VoidCallback onTap,
    bool isLast = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(ResponsiveUtil.borderRadius(context)),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: ResponsiveUtil.largeSpacing(context), // 增加垂直padding
          horizontal: ResponsiveUtil.largeSpacing(context),
        ),
        constraints: BoxConstraints(
          minHeight: ResponsiveUtil.fromTextTheme(
            context,
            multiplier: 4.5, // 增加最小高度，使菜单项更高
          ),
        ),
        decoration: BoxDecoration(
          border: isLast
              ? null
              : Border(
                  bottom: BorderSide(
                    color: ThemeHelper.getDivider(context),
                    width: 0.5,
                  ),
                ),
        ),
        child: Row(
          children: [
            // 图标背景
            Container(
              width: ResponsiveUtil.fromTextTheme(
                context,
                multiplier: 3.2,
              ), // 从2.5增加到3.2
              height: ResponsiveUtil.fromTextTheme(context, multiplier: 3.2),
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(
                  ResponsiveUtil.borderRadius(context, baseRadius: 12), // 增加圆角
                ),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: ResponsiveUtil.fromTextTheme(
                  context,
                  multiplier: 1.8,
                ), // 从1.4增加到1.8
              ),
            ),

            SizedBox(width: ResponsiveUtil.largeSpacing(context) * 1.2), // 增加间距
            // 标题
            Expanded(
              child: Text(
                title,
                style: TextStyleUtil.getCustomStyle(
                  context: context,
                  baseFontSize: 18, // 增加字体大小，使用自定义样式以便更好控制
                  fontWeight: FontWeight.w500,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ),

            // 箭头
            Icon(
              Icons.arrow_forward_ios,
              color: ThemeHelper.getTextHint(context),
              size: ResponsiveUtil.fromTextTheme(
                context,
                multiplier: 1.2,
              ), // 从1.0增加到1.2
            ),
          ],
        ),
      ),
    );
  }

  /// 显示编辑个人资料
  void _showEditProfile(BuildContext context) {
    Navigator.pushNamed(context, AppRoutes.profileEdit);
  }

  /// 导航到聊天历史页面
  Future<void> _navigateToChatHistory(BuildContext context) async {
    // 检查用户是否已登录
    final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
      context,
      featureName: AppLocalizations.of(context).chatHistoryFeature,
    );
    if (!isLoggedIn) {
      return; // 用户未登录，已显示登录提示弹窗，不跳转页面
    }

    if (!context.mounted) return;

    // 用户已登录，跳转到聊天历史页面
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ChatHistoryPage()));
  }

  /// 导航到设置页面
  void _navigateToSettings(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const SettingsScreen()));
  }

  /// 导航到我的订单页面
  Future<void> _navigateToMyOrders(BuildContext context) async {
    // 检查用户是否已登录
    final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
      context,
      featureName: AppLocalizations.of(context).myOrdersFeature,
    );
    if (!isLoggedIn) {
      return; // 用户未登录，已显示登录提示弹窗，不跳转页面
    }

    if (!context.mounted) return;

    // 用户已登录，跳转到订单页面
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const MyOrdersPage()));
  }

  /// 导航到购物车页面
  void _navigateToCart(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const CartPage()));
  }

  /// 处理分享应用
  Future<void> _handleShare(BuildContext context) async {
    try {
      // 应用名称
      final String appName = AppLocalizations.of(context).appName;

      // 基础落地页URL (您需要根据实际情况替换这个URL)
      const String baseLandingPageUrl = 'https://minhan.com/download';

      // 初始分享URL
      String finalShareUrl = baseLandingPageUrl;

      // 检查用户是否为分销员并获取推荐码
      final userInfoManager = UserInfoManagerService();
      final userProfile = userInfoManager.currentUserProfile;
      final user = AuthService().currentUser;

      // 优先使用完整的用户资料信息检查分销员状态
      bool isUserDistributor = false;
      String? referralCode;

      if (userProfile?.isReferrer == true) {
        isUserDistributor = true;
        referralCode = userProfile?.disCode;
      } else if (user != null) {
        // 如果完整用户资料没有，检查基础用户信息
        // 注意：基础用户信息可能没有分销相关字段，这里做防御性检查
        try {
          final dynamic userData = user.toJson();
          if (userData['is_referrer'] == true || userData['is_referrer'] == 1) {
            isUserDistributor = true;
            referralCode = userData['dis_code']?.toString();
          }
        } catch (e) {
          // 忽略解析错误，继续使用默认URL
        }
      }

      // 如果用户是分销员且有推荐码，则添加到URL参数中
      if (isUserDistributor &&
          referralCode != null &&
          referralCode.isNotEmpty) {
        final uri = Uri.parse(baseLandingPageUrl);
        final newUri = uri.replace(
          queryParameters: {...uri.queryParameters, 'ref_code': referralCode},
        );
        finalShareUrl = newUri.toString();
      }

      // 构建分享文本
      String shareText;
      if (isUserDistributor &&
          referralCode != null &&
          referralCode.isNotEmpty) {
        shareText = AppLocalizations.of(
          context,
        ).shareContentWithReferral(appName, finalShareUrl);
      } else {
        shareText = AppLocalizations.of(
          context,
        ).shareContentNormal(appName, finalShareUrl);
      }

      // 构建分享主题
      final String subjectText = AppLocalizations.of(context).shareSubject;

      // 执行分享
      await SharePlus.instance.share(
        ShareParams(
          text: shareText,
          subject: subjectText.isNotEmpty ? subjectText : null,
        ),
      );

      // 检查上下文是否仍然有效
      if (!context.mounted) return;

      // 分享成功后的提示（可选）
      if (isUserDistributor) {
        ToastUtil.show(context, AppLocalizations.of(context).shareSuccess);
      }
    } catch (e) {
      // 检查上下文是否仍然有效
      if (!context.mounted) return;

      // 分享失败的处理
      ToastUtil.show(context, AppLocalizations.of(context).shareNotAvailable);

      // 在调试模式下打印错误信息
      assert(() {
        // ignore: avoid_print
        print('❌ 分享应用失败: $e');
        return true;
      }());
    }
  }

  /// 处理分销管理
  Future<void> _handleDistribution(BuildContext context) async {
    // 检查用户是否已登录
    final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
      context,
      featureName: AppLocalizations.of(context).distributionManagementFeature,
    );
    if (!isLoggedIn) {
      return;
    }

    if (!context.mounted) return;
    final user = AuthService().currentUser;
    if (user?.token != null) {
      DistributionHelper.handleDistributionManagement(context, user!.token);
    }
  }

  /// 处理医生管理
  Future<void> _handleDoctorManagement(BuildContext context) async {
    // 检查用户是否已登录
    final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
      context,
      featureName: AppLocalizations.of(context).adminManagementFeature,
    );
    if (!isLoggedIn) {
      return;
    }

    if (!context.mounted) return;

    // 导航到医生管理页面
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const DoctorManagementPage()),
    );
  }

  /// 处理产品管理
  Future<void> _handleProductManagement(BuildContext context) async {
    // 检查用户是否已登录
    final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
      context,
      featureName: AppLocalizations.of(context).doctorManagementFeature,
    );
    if (!isLoggedIn) {
      return;
    }

    if (!context.mounted) return;

    // 检查用户是否有医生权限 - 需要同时满足 is_doctor=true 和 doctor_id 不为空
    final user = AuthService().currentUser;
    final userInfoManager = UserInfoManagerService();
    final userProfile = userInfoManager.currentUserProfile;

    if (!_isDoctorUser(user, userProfile)) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).onlyDoctorUsersCanAccess,
      );
      return;
    }

    // 导航到医生管理页面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const doctor_mgmt.DoctorManagementPage(),
      ),
    );
  }

  /// 构建互动按钮（点赞、收藏、购物车）
  Widget _buildInteractionButtons(BuildContext context) {
    return Container(
      margin: ResponsiveUtil.cardPadding(context),
      padding: EdgeInsets.all(ResponsiveUtil.mediumSpacing(context)),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(
          ResponsiveUtil.borderRadius(context, baseRadius: 16),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.withAlpha(Colors.black, 13),
            blurRadius: ResponsiveUtil.mediumSpacing(context),
            offset: Offset(0, ResponsiveUtil.smallSpacing(context) / 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 点赞按钮
          _buildInteractionButton(
            context,
            icon: Icons.thumb_up_outlined,
            activeIcon: Icons.thumb_up,
            label: AppLocalizations.of(context).myLikes,
            color: AppColors.error,
            onTap: () => _navigateToLikedDoctors(context),
          ),

          // 分割线
          Container(
            height: 40,
            width: 1,
            margin: EdgeInsets.symmetric(
              horizontal: ResponsiveUtil.smallSpacing(context),
            ),
            color: ThemeHelper.getTextSecondary(context).withValues(alpha: 0.2),
          ),

          // 收藏按钮
          _buildInteractionButton(
            context,
            icon: Icons.favorite_border,
            activeIcon: Icons.favorite,
            label: AppLocalizations.of(context).myFavorites,
            color: AppColors.warning,
            onTap: () => _navigateToFavoriteDoctors(context),
          ),

          // 分割线
          Container(
            height: 40,
            width: 1,
            margin: EdgeInsets.symmetric(
              horizontal: ResponsiveUtil.smallSpacing(context),
            ),
            color: ThemeHelper.getTextSecondary(context).withValues(alpha: 0.2),
          ),

          // 购物车按钮
          _buildInteractionButton(
            context,
            icon: Icons.shopping_cart_outlined,
            activeIcon: Icons.shopping_cart,
            label: AppLocalizations.of(context).shoppingCart,
            color: AppColors.success,
            onTap: () => _navigateToCart(context),
          ),
        ],
      ),
    );
  }

  /// 构建单个互动按钮
  Widget _buildInteractionButton(
    BuildContext context, {
    required IconData icon,
    IconData? activeIcon,
    required String label,
    required VoidCallback onTap,
    Color? color,
  }) {
    final buttonColor = color ?? AppColors.primary;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: EdgeInsets.symmetric(
              vertical: ResponsiveUtil.mediumSpacing(context),
              horizontal: ResponsiveUtil.smallSpacing(context),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: buttonColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, size: 24, color: buttonColor),
                ),
                SizedBox(height: ResponsiveUtil.smallSpacing(context)),
                Text(
                  label,
                  style: TextStyleUtil.getCustomStyle(
                    context: context,
                    baseFontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 导航到点赞的医生页面
  void _navigateToLikedDoctors(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const LikedDoctorsPage()));
  }

  /// 导航到收藏的医生页面
  void _navigateToFavoriteDoctors(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const FavoriteDoctorsPage()),
    );
  }

  /// 检查用户是否为医生
  /// 优先使用UserProfileModel中的信息，如果没有则使用UserModel中的信息
  bool _isDoctorUser(UserModel? user, UserProfileModel? userProfile) {
    // 优先使用UserProfileModel中的医生信息（来自最新的API调用）
    if (userProfile != null) {
      return userProfile.isDoctor == true && userProfile.doctorId != null;
    }

    // 如果没有UserProfileModel，则使用UserModel中的信息
    if (user != null) {
      return user.isDoctor == true && user.doctorId != null;
    }

    // 都没有则返回false
    return false;
  }
}
