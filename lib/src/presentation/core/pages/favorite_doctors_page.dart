import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_model.dart';
import '../../../services/user_interaction_cache_service.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../product_list/pages/doctor_detail_page.dart';

/// 收藏的医生页面
class FavoriteDoctorsPage extends StatefulWidget {
  const FavoriteDoctorsPage({super.key});

  @override
  State<FavoriteDoctorsPage> createState() => _FavoriteDoctorsPageState();
}

class _FavoriteDoctorsPageState extends State<FavoriteDoctorsPage> {
  final UserInteractionCacheService _interactionCacheService =
      UserInteractionCacheService();
  List<DoctorModel> _favoriteDoctors = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFavoriteDoctorsInstantly();
  }

  /// 立即加载收藏的医生列表（优先使用缓存，避免刷新）
  void _loadFavoriteDoctorsInstantly() {
    // 先尝试同步获取缓存数据
    final cachedDoctors = _interactionCacheService
        .getCachedFavoriteDoctorsSync();

    if (cachedDoctors.isNotEmpty) {
      // 如果有缓存数据，立即显示
      setState(() {
        _favoriteDoctors = cachedDoctors;
        _isLoading = false;
      });

      // 后台异步检查更新（不阻塞UI）
      _backgroundRefreshFavoriteDoctors();
    } else {
      // 如果没有缓存数据，显示加载状态并异步加载
      setState(() {
        _isLoading = true;
      });
      _loadFavoriteDoctorsAsync();
    }
  }

  /// 后台刷新收藏医生数据（不阻塞UI）
  void _backgroundRefreshFavoriteDoctors() {
    // 异步后台刷新，不影响当前显示
    Future.microtask(() async {
      try {
        final doctors = await _interactionCacheService.getFavoriteDoctors();

        if (mounted && doctors.isNotEmpty) {
          // 只有数据真正变化时才更新UI
          if (_hasDataChanged(doctors)) {
            setState(() {
              _favoriteDoctors = doctors;
            });
          }
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
      }
    });
  }

  /// 检查数据是否发生变化
  bool _hasDataChanged(List<DoctorModel> newDoctors) {
    if (_favoriteDoctors.length != newDoctors.length) {
      return true;
    }

    // 比较医生ID
    for (int i = 0; i < _favoriteDoctors.length; i++) {
      if (_favoriteDoctors[i].id != newDoctors[i].id) {
        return true;
      }
    }

    return false;
  }

  /// 异步加载收藏的医生列表（用于首次加载）
  Future<void> _loadFavoriteDoctorsAsync() async {
    try {
      final doctors = await _interactionCacheService.getFavoriteDoctors();

      if (mounted) {
        setState(() {
          _favoriteDoctors = doctors;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ThemeHelper.getTextPrimary(context),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          AppLocalizations.of(context).myFavoritesTitle,
          style: TextStyle(
            color: ThemeHelper.getTextPrimary(context),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _favoriteDoctors.isEmpty
          ? _buildEmptyState()
          : _buildDoctorsList(),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.favorite_border, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).noFavoriteDoctors,
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).goFavoriteDoctors,
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建医生列表
  Widget _buildDoctorsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _favoriteDoctors.length,
      itemBuilder: (context, index) {
        final doctor = _favoriteDoctors[index];
        return _buildDoctorCard(doctor);
      },
    );
  }

  /// 构建医生卡片
  Widget _buildDoctorCard(DoctorModel doctor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToDoctorDetail(doctor),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 医生头像
                _buildDoctorAvatar(doctor),
                const SizedBox(width: 12),
                // 医生信息
                Expanded(child: _buildDoctorInfo(doctor)),
                // 收藏状态
                Icon(Icons.favorite, color: Colors.red, size: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建医生头像
  Widget _buildDoctorAvatar(DoctorModel doctor) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: ClipOval(
        child: doctor.fullAvatarUrl.isNotEmpty
            ? Image.network(
                doctor.fullAvatarUrl,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar();
                },
              )
            : _buildDefaultAvatar(),
      ),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Container(
      width: 50,
      height: 50,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF10B981), Color(0xFF059669)],
        ),
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.local_hospital, color: Colors.white, size: 24),
    );
  }

  /// 构建医生信息
  Widget _buildDoctorInfo(DoctorModel doctor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 医生姓名和专科
        Row(
          children: [
            Flexible(
              child: Text(
                doctor.name,
                style: TextStyle(
                  color: ThemeHelper.getTextPrimary(context),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            if (doctor.specialty != null) ...[
              const SizedBox(width: 8),
              Flexible(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    doctor.specialty!,
                    style: TextStyle(fontSize: 12, color: AppColors.primary),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 4),
        // 医生描述
        Text(
          doctor.safeDescription,
          style: TextStyle(
            color: ThemeHelper.getTextSecondary(context),
            fontSize: 14,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        // 点赞和收藏数量
        Row(
          children: [
            Icon(
              Icons.thumb_up_outlined,
              size: 14,
              color: ThemeHelper.getTextSecondary(context),
            ),
            const SizedBox(width: 4),
            Text(
              doctor.likesCount.toString(),
              style: TextStyle(
                color: ThemeHelper.getTextSecondary(context),
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 12),
            Icon(
              Icons.favorite_border,
              size: 14,
              color: ThemeHelper.getTextSecondary(context),
            ),
            const SizedBox(width: 4),
            Text(
              doctor.favoritesCount.toString(),
              style: TextStyle(
                color: ThemeHelper.getTextSecondary(context),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 导航到医生详情页面
  void _navigateToDoctorDetail(DoctorModel doctor) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => DoctorDetailPage(doctor: doctor)),
    );
  }
}
