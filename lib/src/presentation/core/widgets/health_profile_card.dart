import 'package:flutter/material.dart';
import '../../../common/utils/responsive_util.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../services/health_profile_cache_service.dart';
import '../../health/pages/health_profile_detail_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 紧凑型健康档案组件 - 用于集成到个人资料卡片中
class CompactHealthProfile extends StatefulWidget {
  final dynamic userProfile;
  final bool isLoggedIn;

  const CompactHealthProfile({
    super.key,
    this.userProfile,
    required this.isLoggedIn,
  });

  @override
  State<CompactHealthProfile> createState() => _CompactHealthProfileState();
}

/// 紧凑型健康档案状态类
class _CompactHealthProfileState extends State<CompactHealthProfile> {
  final HealthProfileCacheService _cacheService = HealthProfileCacheService();

  @override
  void initState() {
    super.initState();
    if (widget.isLoggedIn) {
      // 立即获取缓存数据（如果有的话）
      _cacheService.getHealthProfile();
    }
  }

  @override
  Widget build(BuildContext context) {
    // 只有用户登录时才显示健康档案组件
    if (!widget.isLoggedIn) {
      return const SizedBox.shrink(); // 未登录时返回空组件
    }

    return GestureDetector(
      onTap: () => _navigateToFullHealthProfile(context),
      child: Column(
        children: [
          // 虚线分割线
          Container(
            margin: EdgeInsets.only(
              top: ResponsiveUtil.mediumSpacing(context),
              bottom: ResponsiveUtil.mediumSpacing(context),
            ),
            child: _buildDashedLine(context),
          ),

          // 健康档案内容 - 使用AnimatedBuilder监听缓存服务变化
          Container(
            padding: EdgeInsets.only(
              left: ResponsiveUtil.smallSpacing(context),
              right: ResponsiveUtil.smallSpacing(context),
              bottom: ResponsiveUtil.smallSpacing(context),
              top: ResponsiveUtil.smallSpacing(context),
            ),
            child: AnimatedBuilder(
              animation: _cacheService,
              builder: (context, child) {
                return _cacheService.isLoading && !_cacheService.hasCachedData
                    ? _buildLoadingContent(context)
                    : _buildHealthContent(context);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建虚线分割线
  Widget _buildDashedLine(BuildContext context) {
    return CustomPaint(
      size: Size(double.infinity, 2),
      painter: DashedLinePainter(
        color: const Color(0xFF6B73FF).withValues(alpha: 0.4),
        strokeWidth: 2.0,
      ),
    );
  }

  /// 构建加载中内容
  Widget _buildLoadingContent(BuildContext context) {
    // 使用橙色加载指示器
    return SizedBox(
      height: 60,
      child: Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: const Color(0xFF6B73FF),
          ),
        ),
      ),
    );
  }

  /// 构建健康档案内容
  Widget _buildHealthContent(BuildContext context) {
    final metrics = _getCompactHealthMetrics(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 健康指标 - 一行三个
        Row(
          children: metrics.asMap().entries.map((entry) {
            final index = entry.key;
            final metric = entry.value;
            return Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  left: index == 0
                      ? 0
                      : ResponsiveUtil.smallSpacing(context) / 2,
                  right: index == metrics.length - 1
                      ? 0
                      : ResponsiveUtil.smallSpacing(context) / 2,
                ),
                child: _buildCompactMetricItem(context, metric, index),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建紧凑型健康指标项
  Widget _buildCompactMetricItem(
    BuildContext context,
    _CompactHealthMetric metric,
    int index,
  ) {
    // 为三个不同的指标设置现代化的渐变背景和颜色
    LinearGradient backgroundGradient;
    Color iconColor;
    Color textColor = Colors.white;

    switch (index) {
      case 0: // 体重 - 清新绿色渐变
        backgroundGradient = const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF56C596), // 薄荷绿
            Color(0xFF38A169), // 深绿
          ],
        );
        iconColor = Colors.white;
        break;
      case 1: // 身高 - 天空蓝渐变
        backgroundGradient = const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF4299E1), // 天空蓝
            Color(0xFF3182CE), // 深蓝
          ],
        );
        iconColor = Colors.white;
        break;
      case 2: // 血型 - 温暖橙红渐变
        backgroundGradient = const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFC8181), // 珊瑚红
            Color(0xFFE53E3E), // 深红
          ],
        );
        iconColor = Colors.white;
        break;
      default:
        backgroundGradient = const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF7C3AED), // 紫色
            Color(0xFF5B21B6), // 深紫
          ],
        );
        iconColor = Colors.white;
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      decoration: BoxDecoration(
        gradient: backgroundGradient,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: backgroundGradient.colors.first.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(metric.icon, size: 22, color: iconColor),
          ),
          const SizedBox(height: 8),
          Text(
            metric.value,
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 13,
              fontWeight: FontWeight.w700,
              color: textColor,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            metric.label,
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 10,
              color: textColor.withValues(alpha: 0.9),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 获取紧凑型健康指标数据
  List<_CompactHealthMetric> _getCompactHealthMetrics(BuildContext context) {
    // 从缓存服务中获取健康档案数据
    final healthProfile = _cacheService.cachedProfile;
    String weightValue = healthProfile?.weight?.toStringAsFixed(1) ?? '--';
    String heightValue = healthProfile?.height?.toStringAsFixed(0) ?? '--';
    String bloodTypeValue = healthProfile?.bloodType ?? '--';

    return [
      _CompactHealthMetric(
        icon: Icons.fitness_center,
        label: AppLocalizations.of(context).weight,
        value: '$weightValue kg',
      ),
      _CompactHealthMetric(
        icon: Icons.height,
        label: AppLocalizations.of(context).height,
        value: '$heightValue cm',
      ),
      _CompactHealthMetric(
        icon: Icons.bloodtype,
        label: AppLocalizations.of(context).bloodType,
        value: bloodTypeValue,
      ),
    ];
  }

  /// 导航到完整健康档案页面
  void _navigateToFullHealthProfile(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const HealthProfileDetailPage()),
    );
  }
}

/// 紧凑型健康指标数据类
class _CompactHealthMetric {
  final IconData icon;
  final String label;
  final String value;

  const _CompactHealthMetric({
    required this.icon,
    required this.label,
    required this.value,
  });
}

/// 虚线绘制器
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;

  DashedLinePainter({
    required this.color,
    required this.strokeWidth,
    this.dashWidth = 8.0,
    this.dashSpace = 4.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
