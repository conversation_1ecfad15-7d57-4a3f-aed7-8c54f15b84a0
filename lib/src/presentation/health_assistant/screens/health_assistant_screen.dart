import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';

import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../common/utils/font_util.dart';
import '../../../services/login_check_service.dart';
import '../../../utils/auth_helper.dart';
import '../../../utils/chat_navigation_helper.dart';
import '../../../utils/chat_state_manager.dart';

import '../../../../generated/l10n/app_localizations.dart';
import 'health_assistant_camera_screen.dart';

import '../widgets/health_assistant_top_card.dart';

import '../widgets/health_assistant_chat_tools.dart';
import '../widgets/chat_history_drawer.dart';

import '../../../services/global_audio_manager.dart';

import '../widgets/health_assistant_chat_bubble.dart';
import '../widgets/doctor_skeleton_widget.dart';
import '../../../services/chat_repository.dart';
import '../../../services/doctor_service.dart';
import '../../../models/conversation_model.dart';
import '../../../models/chat_message_model.dart';
import '../../../models/doctor_model.dart';
import '../widgets/doctor_detail_card.dart';

/// 健康助手页面 - 智能健康助手
class HealthAssistantScreen extends StatelessWidget {
  final String? initialConversationId;

  const HealthAssistantScreen({super.key, this.initialConversationId});

  @override
  Widget build(BuildContext context) {
    return _HealthAssistantScreenContent(
      initialConversationId: initialConversationId,
    );
  }
}

class _HealthAssistantScreenContent extends StatefulWidget {
  final String? initialConversationId;

  const _HealthAssistantScreenContent({this.initialConversationId});

  @override
  State<_HealthAssistantScreenContent> createState() =>
      _HealthAssistantScreenContentState();
}

class _HealthAssistantScreenContentState
    extends State<_HealthAssistantScreenContent>
    with
        TickerProviderStateMixin,
        AutomaticKeepAliveClientMixin,
        WidgetsBindingObserver {
  late TabController _tabController;

  // 地图服务
  // final MapService _mapService = MapService.instance;

  // 移除了全屏地图状态和动画控制器相关代码
  // 现在使用独立的FullscreenMapPage页面处理全屏地图

  // 双击检测相关状态（用于聊天功能）
  DateTime? _lastTapTime;
  int _tapCount = 0;

  // AI聊天工具栏控制器
  final HealthAssistantChatToolsController _chatToolsController =
      HealthAssistantChatToolsController();

  // 聊天状态
  bool _isChatMode = false; // 是否为聊天模式
  final List<HealthAssistantChatMessage> _chatMessages = []; // 聊天消息列表
  String? _currentConversationId; // 当前对话ID
  final ScrollController _chatScrollController = ScrollController();

  // 加载状态控制
  bool _showDrawerLoading = false;
  Timer? _loadingTimer;

  // 聊天历史缓存
  List<ConversationModel>? _cachedConversations;
  bool _hasInitializedConversations = false;

  // 医生选择状态
  DoctorModel? _selectedDoctor;

  // 医生滑动相关
  late PageController _doctorPageController;
  int _currentDoctorIndex = 0;
  List<DoctorModel> _allDoctors = [];
  bool _isDoctorsLoading = true; // 医生数据加载状态

  /// 保持页面状态，防止切换时被销毁
  @override
  bool get wantKeepAlive => true;

  /// 处理医生选择
  void _handleDoctorSelected(DoctorModel doctor) {
    // 找到医生在列表中的索引
    final doctorIndex = _allDoctors.indexWhere((d) => d.id == doctor.id);

    setState(() {
      _selectedDoctor = doctor;
      if (doctorIndex != -1) {
        _currentDoctorIndex = doctorIndex;
      }
    });

    // 如果找到了医生，滑动到对应页面
    if (doctorIndex != -1 && _doctorPageController.hasClients) {
      // 获取当前页面索引
      final baseMultiplier = 50000;
      final currentPage =
          _doctorPageController.page?.round() ??
          (baseMultiplier * _allDoctors.length);
      // 计算目标页面，保持在当前"循环"内
      final currentCycle = currentPage ~/ _allDoctors.length;
      final targetPage = currentCycle * _allDoctors.length + doctorIndex;

      _doctorPageController.animateToPage(
        targetPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }

    // 显示选择成功提示
    ToastUtil.show(
      context,
      AppLocalizations.of(context).doctorAiAssistantSelected(doctor.name),
    );
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this);

    // 初始化PageController（临时的，会在数据加载后重新初始化）
    _doctorPageController = PageController();
    // 初始化医生数据
    _loadDoctors();

    // 添加应用生命周期监听
    WidgetsBinding.instance.addObserver(this);

    // 移除了地图动画控制器相关代码，现在使用独立页面

    // 监听标签页切换
    _tabController.addListener(() {
      setState(() {
        // 强制重新构建以更新底部操作栏显示状态
      });
    });

    // 初始化地图服务并加载POI数据
    _initializeServices();

    // 如果有初始对话ID，加载该对话
    if (widget.initialConversationId != null) {
      _loadInitialConversation(widget.initialConversationId!);
    }

    // 注册聊天导航助手的对话加载回调
    ChatNavigationHelper().registerConversationLoadCallback(
      _loadConversationById,
    );

    // 注册聊天导航助手的医生选择回调
    ChatNavigationHelper().registerDoctorSelectionCallback((doctorId) async {
      print('🩺 HealthAssistantScreen: 收到医生选择请求，医生ID: $doctorId');
      await setDoctorById(doctorId);
      print('✅ HealthAssistantScreen: 医生选择完成');
    });

    // 注册聊天状态管理器的回调
    ChatStateManager().registerChatStateCallback(_handleChatStateChanged);
  }

  /// 处理聊天状态变化（当聊天历史被删除时）
  void _handleChatStateChanged() {
    // 清除聊天历史缓存，确保drawer显示最新数据
    _cachedConversations = null;

    // 检查当前对话是否还存在
    if (_currentConversationId != null) {
      _checkCurrentConversationExists();
    }

    // 如果drawer当前打开，触发重建以显示最新数据
    if (mounted) {
      setState(() {});
    }
  }

  /// 检查当前对话是否还存在
  Future<void> _checkCurrentConversationExists() async {
    if (_currentConversationId == null) return;

    try {
      final conversationsStream = ChatRepository().getConversations();
      final conversations = await conversationsStream.first;

      // 检查当前对话是否还在列表中
      final exists = conversations.any(
        (conv) => conv.id == _currentConversationId,
      );

      if (!exists && mounted) {
        // 当前对话已被删除，清空聊天状态
        setState(() {
          _isChatMode = false;
          _chatMessages.clear();
          _currentConversationId = null;
        });

        // 更新ChatStateManager
        ChatStateManager().setCurrentConversationId(null);

        ToastUtil.show(
          context,
          AppLocalizations.of(context).currentConversationDeleted,
        );
      }
    } catch (e) {
      print('检查对话存在性失败: $e');
    }
  }

  /// 应用生命周期变化监听 - 在应用退出时自动保存当前对话
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用进入后台、暂停、隐藏、分离状态时，保存当前对话状态并停止音频播放
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached ||
        state == AppLifecycleState.inactive ||
        state == AppLifecycleState.hidden) {
      _stopAllAudioPlayback();
      _saveCurrentChatState();
    }
  }

  /// 加载医生数据
  Future<void> _loadDoctors() async {
    setState(() {
      _isDoctorsLoading = true;
    });

    try {
      final doctors = await DoctorService().getDoctors();
      if (mounted) {
        setState(() {
          _allDoctors = doctors;
          _isDoctorsLoading = false;
          // 只有在有医生数据时才初始化PageController，不自动选中医生
          if (_allDoctors.isNotEmpty) {
            // 重新初始化PageController以支持无限滑动
            _initializePageController();
            // 不自动设置_selectedDoctor，需要用户手动选择或通过其他方式指定
          }
        });
      }
    } catch (e) {
      debugPrint('加载医生数据失败: $e');
      // 如果加载失败，尝试从缓存获取
      if (mounted) {
        setState(() {
          _isDoctorsLoading = false;
          // 如果没有缓存数据，_allDoctors保持为空列表
          if (_allDoctors.isEmpty) {
            _selectedDoctor = null;
          }
        });
      }
    }
  }

  /// 初始化PageController以支持无限滑动
  void _initializePageController() {
    if (_allDoctors.isNotEmpty) {
      // 释放旧的controller
      _doctorPageController.dispose();

      // 使用一个更大的数字作为起始点，确保可以向左右滑动
      // 使用50000作为基数，让初始页面从中间开始，这样一开始就可以左右滑动
      // 计算中间位置：50000 * 医生数量 + 当前医生索引
      final baseMultiplier = 50000;
      final initialPage =
          baseMultiplier * _allDoctors.length + _currentDoctorIndex;

      print(
        '初始化PageController: 医生数量=${_allDoctors.length}, 当前索引=$_currentDoctorIndex, 基数倍数=$baseMultiplier, 初始页面=$initialPage',
      );

      _doctorPageController = PageController(
        initialPage: initialPage,
        viewportFraction: 0.85, // 让卡片两侧露出一点，显示可滑动
      );

      // 初始化选中的医生，确保页面指示器能正确显示
      if (_selectedDoctor == null && _allDoctors.isNotEmpty) {
        _selectedDoctor = _allDoctors[_currentDoctorIndex];
      }
    }
  }

  /// 初始化地图服务并加载POI数据
  Future<void> _initializeServices() async {
    debugPrint('[健康助手页面] 开始初始化服务...');

    // 立即设置初始搜索状态，确保页面进入时显示正在搜索
    // POI相关功能已移除，不再需要初始化搜索状态

    // 添加超时保护
    bool initCompleted = false;
    Future.delayed(const Duration(seconds: 10), () {
      if (!initCompleted && mounted) {
        setState(() {}); // 强制刷新UI
      }
    });

    // 地图相关功能已移除
    initCompleted = true;
    if (mounted) setState(() {});
  }

  /// 保存当前聊天状态
  void _saveCurrentChatState() {
    // 如果有聊天内容，保存到本地存储或缓存
    if (_isChatMode && _chatMessages.isNotEmpty) {
      // 这里可以添加保存逻辑，类似翻译页面的保存机制
      print('保存健康助手聊天状态: ${_chatMessages.length}条消息');
    }
  }

  @override
  void dispose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);

    // 注销聊天导航助手的回调
    ChatNavigationHelper().unregisterConversationLoadCallback();
    ChatNavigationHelper().unregisterDoctorSelectionCallback();

    // 注销聊天状态管理器的回调
    ChatStateManager().unregisterChatStateCallback();

    // 停止所有音频播放
    _stopAllAudioPlayback();

    // 保存当前聊天状态
    _saveCurrentChatState();

    _tabController.dispose();
    _chatScrollController.dispose();
    _doctorPageController.dispose();

    // 清理加载计时器
    _loadingTimer?.cancel();

    // POI相关功能已移除，不再需要清理搜索状态

    super.dispose();
  }

  /// 停止所有音频播放
  void _stopAllAudioPlayback() {
    // 使用全局音频管理器停止所有音频播放
    GlobalAudioManager().stopAllAudio();
  }

  /// 处理拍照提问功能
  Future<void> _handlePhotoQuestion() async {
    // 检查用户是否已登录
    if (!mounted) return;
    final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
      context,
      featureName: AppLocalizations.of(context).aiGuidePhotoQuestion,
    );
    if (!isLoggedIn || !mounted) {
      return;
    }

    try {
      // 在导航前清除选择状态

      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => HealthAssistantCameraScreen(
            onPhotoResult: _handlePhotoResult,
            onAIGuideResult: _handleAIGuideResult,
          ),
        ),
      );
    } catch (e) {
      debugPrint('打开拍照提问页面失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).cameraAccessFailure,
        );
      }
    }
  }

  /// 处理健康助手结果回调
  void _handleAIGuideResult(
    String originalText,
    String translatedText,
    String audioUrl,
    String? inputLanguage, {
    String? imagePath, // 新增图片路径参数
  }) {
    // TODO: 这里应该调用健康助手的图片问答API
    // 目前先显示翻译结果
    if (mounted) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).aiTourGuideRecognitionResult(originalText),
      );
    }
  }

  /// 处理拍照结果 - 设置到聊天工具栏预览
  void _handlePhotoResult(String imagePath, {String? question}) {
    if (!mounted) return;

    // 设置照片到聊天工具栏预览
    _chatToolsController.setSelectedImage(imagePath);

    // 如果有问题文本，设置到输入框
    if (question != null && question.isNotEmpty) {
      _chatToolsController.setTextAndSwitchToInputMode(question);
    }
  }

  /// 发送图片消息到AI
  Future<void> _sendImageToAI(String imagePath, {String? question}) async {
    try {
      final imageFile = File(imagePath);

      if (_currentConversationId == null) {
        // 如果没有当前对话，直接发送图片消息创建新对话
        print('🆕 直接发送图片消息创建新对话');

        bool isFirstChunk = true;

        String accumulatedText = '';

        await for (final result
            in ChatRepository().createConversationWithImageStream(
              imageFile: imageFile,
              doctorId: _selectedDoctor?.id ?? 1, // 使用当前选中医生的ID
              description: question?.isNotEmpty == true ? question : null,
              title: AppLocalizations.of(context).newConversation,
            )) {
          // 处理conversation_complete类型的消息
          if (result['type'] == 'conversation_complete') {
            final conversationId = result['conversation_id'];
            if (conversationId != null &&
                conversationId.toString().isNotEmpty &&
                conversationId.toString() != 'null') {
              _currentConversationId = conversationId.toString();
            }

            // 完成流式响应
            setState(() {
              final streamingIndex = _chatMessages.indexWhere(
                (msg) => msg.type == HealthAssistantMessageType.streaming,
              );

              if (streamingIndex != -1) {
                _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
                    .copyWith(
                      type: HealthAssistantMessageType.assistant,
                      isStreaming: false,
                    );
              }
            });
            break;
          }

          // 确保conversation_id被正确设置
          final conversationId = result['conversation_id'];
          if (conversationId != null &&
              conversationId.toString().isNotEmpty &&
              conversationId.toString() != 'null') {
            _currentConversationId = conversationId.toString();
          }

          // 处理流式响应
          final message = result['message'] as ChatMessageModel?;
          if (message != null) {
            final newText = _extractTextFromContent(message.content);

            // 对于流式块，累积文本
            if (result['type'] == 'message') {
              if (isFirstChunk) {
                accumulatedText = newText;
                isFirstChunk = false;
              } else {
                accumulatedText += newText;
              }

              setState(() {
                final streamingIndex = _chatMessages.indexWhere(
                  (msg) => msg.type == HealthAssistantMessageType.streaming,
                );

                if (streamingIndex != -1) {
                  _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
                      .copyWith(text: accumulatedText, isStreaming: true);
                }
              });

              _scrollChatToBottom();
            }
          }
        }
      } else {
        await _sendImageToExistingConversation(imageFile, question: question);
      }

      // 滚动到底部
      _scrollChatToBottom();
    } catch (e) {
      setState(() {
        // 找到流式消息并更新为错误状态
        final streamingIndex = _chatMessages.indexWhere(
          (msg) => msg.type == HealthAssistantMessageType.streaming,
        );

        if (streamingIndex != -1) {
          _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
              .copyWith(
                text: '图片处理失败，请重试: $e',
                type: HealthAssistantMessageType.assistant,
                isStreaming: false,
              );
        }
      });

      print('发送图片消息失败: $e');
    }
  }

  /// 发送图片消息到现有对话
  Future<void> _sendImageToExistingConversation(
    File imageFile, {
    String? question,
  }) async {
    try {
      print('📤 发送图片消息到现有对话: $_currentConversationId');

      // 先上传图片获取URL
      print('🖼️ 开始上传图片获取URL');
      final imageUrl = await ChatRepository().uploadChatImage(imageFile);
      print('📸 图片上传完成，URL: $imageUrl');

      // 注意：不在这里添加流式消息，因为调用方法已经添加了

      bool isFirstChunk = true;

      await for (final aiMessage in ChatRepository().sendImageMessageStream(
        conversationId: _currentConversationId!,
        imageFile: imageFile,
        doctorId: _selectedDoctor?.id ?? 1, // 使用当前选中医生的ID
        description: question?.isNotEmpty == true ? question : null,
      )) {
        final newText = _extractTextFromContent(aiMessage.content);

        setState(() {
          final streamingIndex = _chatMessages.indexWhere(
            (msg) => msg.type == HealthAssistantMessageType.streaming,
          );

          if (streamingIndex != -1) {
            if (isFirstChunk) {
              _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
                  .copyWith(text: newText, isStreaming: true);
              isFirstChunk = false;
            } else {
              _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
                  .copyWith(text: newText, isStreaming: true);
            }
          }
        });

        _scrollChatToBottom();
      }

      // 完成流式响应
      setState(() {
        final streamingIndex = _chatMessages.indexWhere(
          (msg) => msg.type == HealthAssistantMessageType.streaming,
        );

        if (streamingIndex != -1) {
          _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
              .copyWith(
                type: HealthAssistantMessageType.assistant,
                isStreaming: false,
              );
        }
      });
    } catch (e) {
      setState(() {
        final streamingIndex = _chatMessages.indexWhere(
          (msg) => msg.type == HealthAssistantMessageType.streaming,
        );

        if (streamingIndex != -1) {
          _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
              .copyWith(
                text: '图片处理失败，请重试: $e',
                type: HealthAssistantMessageType.assistant,
                isStreaming: false,
              );
        }
      });

      print('发送图片消息失败: $e');
    }
  }

  /// 处理发送消息（从聊天工具栏）
  void _handleSendMessage(String message) {
    // 检查用户是否已登录（双重保险）
    if (!AuthHelper.isLoggedIn()) {
      return;
    }

    final text = message.trim();
    final imagePath = _chatToolsController.selectedImagePath;

    // 如果没有文本也没有图片，不发送
    if (text.isEmpty && imagePath == null) {
      return;
    }

    setState(() {
      // 切换到聊天模式
      _isChatMode = true;

      // 添加用户消息（可能包含图片）
      _chatMessages.add(
        HealthAssistantChatMessage(
          text: text,
          type: HealthAssistantMessageType.user,
          timestamp: DateTime.now(),
          shouldAutoPlay: false,
          imagePath: imagePath,
        ),
      );

      // 添加流式回复消息
      _chatMessages.add(
        HealthAssistantChatMessage(
          text: '',
          type: HealthAssistantMessageType.streaming,
          timestamp: DateTime.now(),
          shouldAutoPlay: false,
          isStreaming: true,
        ),
      );
    });

    // 清除聊天工具栏中的照片
    _chatToolsController.setSelectedImage(null);

    // 滚动到底部（发送新消息时强制滚动）
    _scrollChatToBottom(force: true);

    // 根据是否有图片选择不同的发送方式
    if (imagePath != null) {
      _sendImageToAI(imagePath, question: text.isEmpty ? null : text);
    } else {
      _sendMessageToAI(text);
    }
  }

  /// 滚动聊天到底部
  void _scrollChatToBottom({bool force = false}) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients) {
        // 如果用户正在手动滚动（不在底部），且不是强制滚动，则不自动滚动
        if (!force && !_isScrollAtBottom()) {
          return;
        }

        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 检查滚动是否在底部附近（允许一定误差）
  bool _isScrollAtBottom() {
    if (!_chatScrollController.hasClients) return true;

    final position = _chatScrollController.position;
    const threshold = 100.0; // 100像素的误差范围
    return (position.maxScrollExtent - position.pixels) <= threshold;
  }

  /// 发送消息到AI (流式响应)
  Future<void> _sendMessageToAI(String message) async {
    try {
      // 如果没有当前对话，先创建新对话
      if (_currentConversationId == null) {
        // 注意：不清除聊天历史缓存，避免触发不必要的API调用
        // _cachedConversations = null;

        final stream = ChatRepository().createConversationWithMessageStream(
          content: message,
          doctorId: _selectedDoctor?.id ?? 1, // 使用当前选中医生的ID
          title: '新对话',
        );

        await for (final result in stream.timeout(
          const Duration(seconds: 30),
          onTimeout: (sink) {
            sink.close();
          },
        )) {
          // 处理conversation_complete类型的消息
          if (result['type'] == 'conversation_complete') {
            final conversationId = result['conversation_id'];
            if (conversationId != null &&
                conversationId.toString().isNotEmpty &&
                conversationId.toString() != 'null') {
              _currentConversationId = conversationId.toString();
            }
            continue; // 跳过这个消息，不处理UI更新
          }

          // 确保conversation_id被正确设置
          final conversationId = result['conversation_id'];
          if (conversationId != null &&
              conversationId.toString().isNotEmpty &&
              conversationId.toString() != 'null') {
            _currentConversationId = conversationId.toString();
          }

          final aiMessage = result['message'] as ChatMessageModel?;
          if (aiMessage == null) {
            continue;
          }

          final newText = _extractTextFromContent(aiMessage.content);

          setState(() {
            // 找到流式消息并更新
            final streamingIndex = _chatMessages.indexWhere(
              (msg) => msg.type == HealthAssistantMessageType.streaming,
            );

            if (streamingIndex != -1) {
              // 直接使用API返回的累积文本，不再在UI层累积
              _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
                  .copyWith(text: newText, isStreaming: true);
            }
          });

          // 流式响应时，只在用户在底部时才自动滚动
          _scrollChatToBottom();
        }

        // 流式完成，更新最终状态
        setState(() {
          final streamingIndex = _chatMessages.indexWhere(
            (msg) => msg.type == HealthAssistantMessageType.streaming,
          );

          if (streamingIndex != -1) {
            _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
                .copyWith(
                  type: HealthAssistantMessageType.assistant,
                  isStreaming: false,
                );
          }
        });
      } else {
        // 发送消息到现有对话
        bool isFirstChunk = true;

        final messageStream = ChatRepository().sendTextMessageStream(
          conversationId: _currentConversationId!,
          content: message,
          doctorId: _selectedDoctor?.id ?? 1, // 使用当前选中医生的ID
        );

        await for (final aiMessage in messageStream.timeout(
          const Duration(seconds: 30),
          onTimeout: (sink) {
            sink.close();
          },
        )) {
          final newText = _extractTextFromContent(aiMessage.content);

          setState(() {
            // 找到流式消息并更新
            final streamingIndex = _chatMessages.indexWhere(
              (msg) => msg.type == HealthAssistantMessageType.streaming,
            );

            if (streamingIndex != -1) {
              if (isFirstChunk) {
                // 第一次收到数据，开始流式显示
                _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
                    .copyWith(text: newText, isStreaming: true);
                isFirstChunk = false;
              } else {
                _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
                    .copyWith(
                      text: newText, // API返回的是累积文本
                      isStreaming: true,
                    );
              }
            }
          });

          // 流式响应时，只在用户在底部时才自动滚动
          _scrollChatToBottom();
        }

        // 流式完成，更新最终状态
        setState(() {
          final streamingIndex = _chatMessages.indexWhere(
            (msg) => msg.type == HealthAssistantMessageType.streaming,
          );

          if (streamingIndex != -1) {
            _chatMessages[streamingIndex] = _chatMessages[streamingIndex]
                .copyWith(
                  type: HealthAssistantMessageType.assistant,
                  isStreaming: false,
                );
          }
        });
      }
    } catch (e) {
      // 移除流式消息并显示错误
      setState(() {
        _chatMessages.removeWhere(
          (msg) => msg.type == HealthAssistantMessageType.streaming,
        );

        _chatMessages.add(
          HealthAssistantChatMessage(
            text: '发送失败: $e',
            type: HealthAssistantMessageType.assistant,
            timestamp: DateTime.now(),
            shouldAutoPlay: false,
          ),
        );
      });

      print('发送消息失败: $e');
    }
  }

  /// 从消息内容中提取文本
  String _extractTextFromContent(List<MessageContent> content) {
    return content
        .where((c) => c.type == MessageContentType.text)
        .map((c) => c.content)
        .join(' ');
  }

  /// 从消息内容中提取Base64图片数据
  String? _extractImageFromContent(List<MessageContent> content) {
    try {
      // 过滤出所有图片类型且内容不为空的项
      final imageContents = content
          .where(
            (item) =>
                item.type == MessageContentType.image &&
                item.content.isNotEmpty &&
                item.content != 'null',
          )
          .toList();

      if (imageContents.isNotEmpty) {
        return imageContents.first.content;
      }

      return null;
    } catch (e) {
      print('提取图片内容失败: $e');
      return null;
    }
  }

  /// 构建聊天历史drawer - 使用FutureBuilder避免Stream重复监听问题
  Widget _buildChatHistoryDrawer() {
    return FutureBuilder<List<ConversationModel>>(
      future: _getConversationsData(),
      builder: (context, snapshot) {
        final conversations = snapshot.data ?? [];
        // 优化加载状态：只有在没有数据且正在等待时才显示加载，并且使用延迟显示
        final isLoading =
            snapshot.connectionState == ConnectionState.waiting &&
            conversations.isEmpty &&
            _showDrawerLoading;

        return ChatHistoryDrawer(
          conversations: conversations,
          isLoading: isLoading,
          onConversationSelected: _loadExistingConversation,
          onClearAllHistory: _clearAllChatHistory,
          onRefresh: _refreshConversations,
          onTitleUpdated: _updateConversationTitle,
        );
      },
    );
  }

  /// 获取对话数据 - 使用本地缓存优化用户体验
  Future<List<ConversationModel>> _getConversationsData() async {
    // 如果有缓存且已初始化，直接返回缓存
    if (_cachedConversations != null && _hasInitializedConversations) {
      return _cachedConversations!;
    }

    // 启动延迟加载状态显示
    _startLoadingTimer();

    try {
      // 首次加载或需要刷新时，从Repository获取数据
      List<ConversationModel> conversations;

      if (!_hasInitializedConversations) {
        // 首次加载，先尝试从本地缓存获取，避免网络请求
        try {
          final conversationsStream = ChatRepository().getConversations();
          conversations = await conversationsStream.first;
        } catch (e) {
          // 如果本地缓存失败，再尝试网络请求
          conversations = await ChatRepository().refreshConversations();
        }
        _hasInitializedConversations = true;
      } else {
        // 后续刷新，直接网络请求
        conversations = await ChatRepository().refreshConversations();
      }

      // 更新缓存
      _cachedConversations = conversations;
      _stopLoadingTimer();
      return conversations;
    } catch (e) {
      _stopLoadingTimer();
      // 如果所有方式都失败，返回缓存或空列表
      return _cachedConversations ?? [];
    }
  }

  /// 启动加载计时器，延迟显示加载状态
  void _startLoadingTimer() {
    _loadingTimer?.cancel();
    _loadingTimer = Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _showDrawerLoading = true;
        });
      }
    });
  }

  /// 停止加载计时器，隐藏加载状态
  void _stopLoadingTimer() {
    _loadingTimer?.cancel();
    if (mounted) {
      setState(() {
        _showDrawerLoading = false;
      });
    }
  }

  /// 刷新对话列表
  Future<void> _refreshConversations() async {
    try {
      // 重置加载状态
      _stopLoadingTimer();

      // 清除缓存，强制重新获取
      _cachedConversations = null;

      await ChatRepository().refreshConversations();
      if (mounted) {
        // 触发重建以显示最新数据
        setState(() {});
        ToastUtil.show(context, AppLocalizations.of(context).refreshSuccess);
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).refreshFailed(e.toString()),
        );
      }
    }
  }

  /// 更新对话标题
  Future<void> _updateConversationTitle(
    String conversationId,
    String newTitle,
  ) async {
    try {
      // 调用ChatRepository更新标题
      await ChatRepository().updateConversationTitle(conversationId, newTitle);

      // 显示成功提示
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).titleUpdateSuccess,
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).titleUpdateFailed(e.toString()),
        );
      }
      rethrow; // 重新抛出异常，让ChatHistoryDrawer处理
    }
  }

  /// 加载初始对话（从聊天历史页面跳转时使用）
  Future<void> _loadInitialConversation(String conversationId) async {
    try {
      // 获取对话信息 - 使用广播流避免重复监听问题
      final conversationsStream = ChatRepository()
          .getConversations()
          .asBroadcastStream();
      final conversations = await conversationsStream.first;

      // 查找指定的对话
      final conversation = conversations.firstWhere(
        (conv) => conv.id == conversationId,
        orElse: () => throw Exception('对话不存在'),
      );

      // 加载该对话（从外部页面调用，不关闭drawer）
      await _loadExistingConversation(conversation, fromDrawer: false);
    } catch (e) {
      print('加载初始对话失败: $e');
      if (mounted) {
        ToastUtil.show(context, '加载对话失败: $e');
      }
    }
  }

  /// 根据对话ID加载对话（ChatNavigationHelper回调使用）
  Future<void> _loadConversationById(String conversationId) async {
    await _loadInitialConversation(conversationId);
  }

  /// 根据医生ID设置医生信息（公共方法，可从外部调用）
  Future<void> setDoctorById(int doctorId) async {
    print('🎯 setDoctorById: 开始设置医生，ID: $doctorId');
    print(
      '🎯 setDoctorById: 当前状态 - _isChatMode: $_isChatMode, _allDoctors.length: ${_allDoctors.length}, _isDoctorsLoading: $_isDoctorsLoading',
    );

    // 确保医生数据已经加载
    if (_allDoctors.isEmpty || _isDoctorsLoading) {
      print('⏳ setDoctorById: 医生数据未加载，等待加载完成');
      await _loadDoctors();
    }

    await _setDoctorByIdFromConversation(doctorId);

    // 强制刷新UI状态，确保显示医生卡片而不是空白聊天
    if (mounted) {
      setState(() {
        // 如果当前是聊天模式但没有消息，切换回普通模式显示医生卡片
        if (_isChatMode && _chatMessages.isEmpty) {
          _isChatMode = false;
          _currentConversationId = null;
          print('🔄 setDoctorById: 检测到空聊天状态，切换回医生卡片模式');
        }
      });
      print('🔄 setDoctorById: 已强制刷新UI状态');
    }

    print('✅ setDoctorById: 医生设置完成');
  }

  /// 根据医生ID设置医生信息（从对话加载时使用）
  Future<void> _setDoctorByIdFromConversation(int doctorId) async {
    try {
      // 在已加载的医生列表中查找对应的医生
      final doctor = _allDoctors.firstWhere(
        (d) => d.id == doctorId,
        orElse: () => throw Exception('未找到医生ID为 $doctorId 的医生'),
      );

      // 设置选中的医生
      setState(() {
        _selectedDoctor = doctor;
        // 找到医生在列表中的索引并更新当前索引
        final doctorIndex = _allDoctors.indexWhere((d) => d.id == doctorId);
        if (doctorIndex != -1) {
          _currentDoctorIndex = doctorIndex;
        }
      });

      // 如果PageController已经初始化，滑动到对应的医生页面
      if (_doctorPageController.hasClients && _allDoctors.isNotEmpty) {
        final doctorIndex = _allDoctors.indexWhere((d) => d.id == doctorId);
        if (doctorIndex != -1) {
          // 获取当前页面索引
          final baseMultiplier = 50000;
          final currentPage =
              _doctorPageController.page?.round() ??
              (baseMultiplier * _allDoctors.length);
          // 计算目标页面，保持在当前"循环"内
          final currentCycle = currentPage ~/ _allDoctors.length;
          final targetPage = currentCycle * _allDoctors.length + doctorIndex;

          await _doctorPageController.animateToPage(
            targetPage,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    } catch (e) {
      debugPrint('设置医生失败: $e');
      // 如果找不到对应的医生，清空选中状态
      setState(() {
        _selectedDoctor = null;
        _currentDoctorIndex = 0;
      });
    }
  }

  /// 加载现有对话
  Future<void> _loadExistingConversation(
    ConversationModel conversation, {
    bool fromDrawer = true,
  }) async {
    try {
      // 只有从drawer调用时才关闭drawer
      if (fromDrawer) {
        try {
          Navigator.of(context).pop();
        } catch (e) {
          // 如果没有可关闭的页面，忽略错误
        }
      }

      // 立即切换到聊天模式，显示加载状态
      setState(() {
        _isChatMode = true;
        _currentConversationId = conversation.id;
        _chatMessages.clear();
      });

      // 根据对话的doctorId设置正确的医生信息
      if (conversation.doctorId != null) {
        await _setDoctorByIdFromConversation(conversation.doctorId!);
      }

      // 获取对话消息 - 使用Cache-then-Network策略确保获取最新数据
      final messagesStream = ChatRepository()
          .getMessages(conversation.id)
          .asBroadcastStream();

      // 监听消息流，处理缓存数据和网络数据
      bool hasReceivedNetworkData = false;
      await for (final messages in messagesStream) {
        setState(() {
          // 清空当前消息列表
          _chatMessages.clear();

          // 转换消息格式并添加到列表
          for (var message in messages) {
            _chatMessages.add(
              HealthAssistantChatMessage(
                text: _extractTextFromContent(message.content),
                type: message.role == MessageRole.user
                    ? HealthAssistantMessageType.user
                    : HealthAssistantMessageType.assistant,
                timestamp: message.timestamp,
                shouldAutoPlay: false, // 历史消息不自动播放
                imageBase64: _extractImageFromContent(
                  message.content,
                ), // 提取Base64图片
              ),
            );
          }
        });

        // 如果这是第二次数据（网络数据），则停止监听
        if (hasReceivedNetworkData) {
          break;
        }
        hasReceivedNetworkData = true;
      }

      // 更新ChatStateManager
      ChatStateManager().setCurrentConversationId(conversation.id);

      // 滚动到底部
      _scrollChatToBottom();
    } catch (e) {
      print('加载对话失败: $e');
      if (mounted) {
        ToastUtil.show(context, '加载对话失败: $e');
      }
    }
  }

  /// 导航到现有对话（已删除，现在使用_loadExistingConversation）

  /// 清空所有聊天历史
  void _clearAllChatHistory() async {
    // 显示确认对话框
    final shouldClear = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(context).clearHistoryTitle,
          style: FontUtil.createHeadingTextStyle(
            text: AppLocalizations.of(context).clearHistoryTitle,
          ),
        ),
        content: Text(
          AppLocalizations.of(context).clearHistoryConfirm,
          style: FontUtil.createBodyTextStyle(
            text: AppLocalizations.of(context).clearHistoryConfirm,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: FontUtil.createButtonTextStyle(
                text: AppLocalizations.of(context).cancel,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              AppLocalizations.of(context).clearHistory,
              style: FontUtil.createButtonTextStyle(
                text: AppLocalizations.of(context).clearHistory,
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );

    if (shouldClear == true && mounted) {
      try {
        // 调用删除所有对话的API，同时清空缓存
        await ChatRepository().deleteAllConversations();

        // 清空本地缓存
        _cachedConversations = null;

        // 清空当前聊天状态
        setState(() {
          _isChatMode = false;
          _chatMessages.clear();
          _currentConversationId = null;
        });

        // 关闭drawer
        if (mounted) {
          Navigator.pop(context);
        }

        // 显示成功消息
        if (mounted) {
          ToastUtil.show(context, AppLocalizations.of(context).historyCleared);
        }
      } catch (e) {
        if (mounted) {
          ToastUtil.show(context, '清空历史失败: $e');
        }
      }
    }
  }

  /// 导航到新建对话
  void _navigateToNewChat() {
    // 清空当前聊天并开始新对话
    setState(() {
      _isChatMode = false;
      _chatMessages.clear();
      _currentConversationId = null;
    });
  }

  /// 处理语音输入（中文）
  void _handleVoiceInputChinese() {
    // 保留原有功能，可以在这里添加特定的处理逻辑
  }

  /// 处理语音翻译结果
  void _handleVoiceTranslationResult(
    String originalText,
    String translatedText,
    String audioUrl,
    String? inputLanguage,
  ) {
    // 目前先显示识别结果
    if (mounted) {
      ToastUtil.show(
        context,
        AppLocalizations.of(
          context,
        ).healthAssistantVoiceRecognition(originalText),
      );
    }
  }

  /// 处理语音翻译开始
  void _handleVoiceTranslationStart() {
    // 可以在这里显示"处理中..."状态
  }

  /// 处理语音翻译结束
  void _handleVoiceTranslationEnd() {
    // 可以在这里移除"处理中..."状态
  }

  /// 处理语音翻译错误
  void _handleVoiceTranslationError(dynamic error) {
    if (mounted) {
      ToastUtil.show(
        context,
        AppLocalizations.of(
          context,
        ).healthAssistantVoiceProcessingFailed(error.toString()),
      );
    }
  }

  /// 处理空白区域点击 - 双击检测和关闭键盘
  void _handleEmptyAreaTap() {
    // 如果当前是输入模式，优先关闭键盘
    if (_chatToolsController.isInputMode) {
      _closeKeyboard();
      return;
    }

    final now = DateTime.now();
    if (_lastTapTime == null ||
        now.difference(_lastTapTime!) > const Duration(milliseconds: 500)) {
      // 重置计数
      _tapCount = 1;
    } else {
      // 连续点击
      _tapCount++;
    }
    _lastTapTime = now;

    if (_tapCount >= 2) {
      // 双击 - 触发键盘
      _switchToInputMode();
      _tapCount = 0; // 重置计数
    }
  }

  /// 切换到输入模式
  void _switchToInputMode() {
    _chatToolsController.switchToInputMode();
  }

  /// 关闭键盘并退出输入模式
  void _closeKeyboard() {
    FocusScope.of(context).unfocus(); // 关闭键盘
    _chatToolsController.switchToToolbarMode(); // 退出输入模式
  }

  @override
  Widget build(BuildContext context) {
    // 必须调用super.build以支持AutomaticKeepAliveClientMixin
    super.build(context);

    // 从Provider获取最新的控制器状态
    // final sharedMapController = context.watch<SharedMapController>();

    // 检查用户是否已登录
    final isLoggedIn = AuthHelper.isLoggedIn();

    // 显示正常的健康助手页面（移除了全屏地图的条件判断）
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      // 只有用户已登录时才显示聊天历史drawer
      drawer: isLoggedIn ? _buildChatHistoryDrawer() : null,
      body: Column(
        children: [
          // 智能导游内容
          Expanded(child: _buildSmartGuideTab()),

          // 底部健康助手聊天工具栏
          HealthAssistantChatTools(
            controller: _chatToolsController, // 传递控制器
            onSendMessage: _handleSendMessage,
            onVoiceInputChinese: _handleVoiceInputChinese,
            onCameraInput: _handlePhotoQuestion,
            onVoiceResult: _handleVoiceTranslationResult,
            onVoiceStart: _handleVoiceTranslationStart,
            onVoiceEnd: _handleVoiceTranslationEnd,
            onVoiceError: _handleVoiceTranslationError,
          ),
        ],
      ),
    );
  }

  // 移除了_buildFullscreenMapView方法，现在使用独立的FullscreenMapPage

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    // 检查用户是否已登录
    final isLoggedIn = AuthHelper.isLoggedIn();

    return PreferredSize(
      preferredSize: const Size.fromHeight(64), // 增加AppBar高度从默认56到64
      child: AppBar(
        title: Text(
          AppLocalizations.of(context).aiTourGuideTitle,
          style: FontUtil.createAppBarTitleStyle(
            context: context,
            text: AppLocalizations.of(context).aiTourGuideTitle,
            fontSize: 20,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
        automaticallyImplyLeading: false, // 不显示返回按钮，因为是主导航页面
        // 只有用户已登录时才显示聊天历史按钮
        leading: isLoggedIn
            ? Builder(
                builder: (context) => IconButton(
                  icon: const Icon(Icons.menu, size: 26), // 从默认24增加到26
                  onPressed: () {
                    // 直接打开抽屉，让SelectionHelper统一处理键盘关闭
                    Scaffold.of(context).openDrawer();
                  },
                  tooltip: '聊天历史',
                ),
              )
            : null,
        actions: isLoggedIn
            ? [
                // 新建对话按钮 - 右上角位置
                Container(
                  margin: const EdgeInsets.only(right: 16),
                  child: ElevatedButton(
                    onPressed: _navigateToNewChat,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      elevation: 2,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 14,
                        vertical: 10,
                      ), // 增加内边距
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      minimumSize: const Size(0, 40), // 从36增加到40
                    ),
                    child: Text(
                      AppLocalizations.of(context).newChat,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ), // 从14增加到15
                    ),
                  ),
                ),
              ]
            : null,
      ),
    );
  }

  /// 构建智能导游标签页
  Widget _buildSmartGuideTab() {
    return GestureDetector(
      onTap: _isChatMode
          ? () {
              // 聊天模式：点击聊天区域时，如果键盘打开则关闭
              if (_chatToolsController.isInputMode) {
                _closeKeyboard();
              }
            }
          : _handleEmptyAreaTap, // 普通模式：为整个标签页添加双击检测
      child: Column(
        children: [
          // 顶部固定内容 - 始终显示
          const SizedBox(height: 16),
          HealthAssistantTopCard(
            selectedDoctor: _selectedDoctor,
            isLoading: _isDoctorsLoading,
            onDoctorSelected: _handleDoctorSelected,
          ),
          const SizedBox(height: 16),

          // 根据模式显示不同内容
          Expanded(
            child: _isChatMode
                ? _buildChatMessagesList() // 聊天模式：显示聊天消息列表
                : _buildDoctorContentView(), // 普通模式：显示医生内容（卡片或骨架动画）
          ),
        ],
      ),
    );
  }

  /// 构建医生内容视图 - 根据加载状态显示骨架动画或医生卡片
  Widget _buildDoctorContentView() {
    return Column(
      children: [
        // 减少上方间距，避免卡片被挤压
        const SizedBox(height: 4),
        // 始终显示PageView形式的卡片
        Expanded(
          child: _allDoctors.isEmpty
              ? _buildSkeletonPageView()
              : _buildDoctorPageView(),
        ),
        // 减少下方间距，为页面指示器留出更多空间
        const SizedBox(height: 8),
        // 页面指示器
        _buildPageIndicator(),
        const SizedBox(height: 12),
      ],
    );
  }

  /// 构建骨架动画PageView
  Widget _buildSkeletonPageView() {
    return PageView.builder(
      itemCount: 3, // 显示3个骨架动画卡片
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 8),
          child: const SingleChildScrollView(
            padding: EdgeInsets.only(top: 2, bottom: 4),
            physics: BouncingScrollPhysics(),
            child: DoctorDetailSkeletonWidget(),
          ),
        );
      },
    );
  }

  /// 构建医生卡片PageView（支持无限滑动）
  Widget _buildDoctorPageView() {
    return PageView.builder(
      controller: _doctorPageController,
      onPageChanged: (index) {
        // 修复索引计算，确保正确的循环（处理负数情况）
        final actualIndex =
            ((index % _allDoctors.length) + _allDoctors.length) %
            _allDoctors.length;
        setState(() {
          _currentDoctorIndex = actualIndex;
          _selectedDoctor = _allDoctors[actualIndex];
        });
      },
      itemBuilder: (context, index) {
        // 实现无限滑动（处理负数情况）
        final doctorIndex =
            ((index % _allDoctors.length) + _allDoctors.length) %
            _allDoctors.length;
        final doctor = _allDoctors[doctorIndex];

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 8),
          child: SingleChildScrollView(
            padding: const EdgeInsets.only(top: 2, bottom: 4),
            physics: const BouncingScrollPhysics(),
            child: DoctorDetailCard(doctor: doctor),
          ),
        );
      },
    );
  }

  /// 构建页面指示器
  Widget _buildPageIndicator() {
    // 确定显示的数量：加载中时显示3个点，有数据时显示实际数量
    final displayCount = _allDoctors.isEmpty ? 3 : _allDoctors.length;
    final currentDoctor = _allDoctors.isNotEmpty ? _selectedDoctor : null;
    final hasData = _allDoctors.isNotEmpty;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
      child: Column(
        children: [
          // 医生名称
          Text(
            currentDoctor?.name ??
                (_isDoctorsLoading
                    ? AppLocalizations.of(context).loading
                    : AppLocalizations.of(context).noDoctorInfo),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),

          // 减少医生名称和指示器之间的间距
          const SizedBox(height: 8),

          // 页面指示点和左右箭头
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 左箭头
              GestureDetector(
                onTap: hasData ? () => _previousDoctor() : null,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: hasData
                        ? AppColors.primary.withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: hasData
                          ? AppColors.primary.withValues(alpha: 0.2)
                          : Colors.grey.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.chevron_left,
                    size: 16,
                    color: hasData ? AppColors.primary : Colors.grey,
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // 页面指示点
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(displayCount, (index) {
                  final isActive = index == _currentDoctorIndex;
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 3),
                    width: isActive ? 20 : 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: isActive
                          ? AppColors.primary
                          : AppColors.primary.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  );
                }),
              ),

              const SizedBox(width: 16),

              // 右箭头
              GestureDetector(
                onTap: hasData ? () => _nextDoctor() : null,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: hasData
                        ? AppColors.primary.withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: hasData
                          ? AppColors.primary.withValues(alpha: 0.2)
                          : Colors.grey.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.chevron_right,
                    size: 16,
                    color: hasData ? AppColors.primary : Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 切换到上一个医生
  void _previousDoctor() {
    if (_doctorPageController.hasClients) {
      _doctorPageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 切换到下一个医生
  void _nextDoctor() {
    if (_doctorPageController.hasClients) {
      _doctorPageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 构建聊天消息列表
  Widget _buildChatMessagesList() {
    return ListView.builder(
      controller: _chatScrollController,
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: _chatMessages.length,
      itemBuilder: (context, index) {
        final message = _chatMessages[index];
        return HealthAssistantChatBubble(
          message: message,
          onEditUserMessage: message.type == HealthAssistantMessageType.user
              ? () => _editUserMessage(message.text)
              : null,
          doctorAvatarPath: _selectedDoctor?.fullAvatarUrl, // 传入当前选中医生的完整头像URL
        );
      },
    );
  }

  /// 编辑用户消息
  void _editUserMessage(String text) {
    _chatToolsController.setTextAndSwitchToInputMode(text);
  }
}
