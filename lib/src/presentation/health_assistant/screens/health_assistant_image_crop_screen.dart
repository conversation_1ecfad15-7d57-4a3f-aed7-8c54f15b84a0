import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as image_lib;
import 'package:path_provider/path_provider.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 自定义绘制器，绘制剪裁区域的遮罩
class CropOverlayPainter extends CustomPainter {
  final Rect cropRect;
  final Color overlayColor;

  CropOverlayPainter({required this.cropRect, required this.overlayColor});

  @override
  void paint(Canvas canvas, Size size) {
    final overlayPaint = Paint()..color = overlayColor;
    final clearPaint = Paint()..blendMode = BlendMode.clear;

    // 保存画布状态
    canvas.saveLayer(Offset.zero & size, Paint());

    // 首先绘制完整的遮罩
    canvas.drawRect(Offset.zero & size, overlayPaint);

    // 然后清除剪裁区域，让底层图片显示出来
    canvas.drawRect(cropRect, clearPaint);

    // 恢复画布状态
    canvas.restore();
  }

  @override
  bool shouldRepaint(CropOverlayPainter oldDelegate) {
    return cropRect != oldDelegate.cropRect ||
        overlayColor != oldDelegate.overlayColor;
  }
}

/// 健康助手图片裁剪页面 - 支持用户选择要发送的区域
class HealthAssistantImageCropScreen extends StatefulWidget {
  final File imageFile;

  const HealthAssistantImageCropScreen({super.key, required this.imageFile});

  @override
  State<HealthAssistantImageCropScreen> createState() =>
      _HealthAssistantImageCropScreenState();
}

class _HealthAssistantImageCropScreenState
    extends State<HealthAssistantImageCropScreen> {
  // 裁剪框的位置和大小
  double _cropLeft = 50;
  double _cropTop = 50;
  double _cropWidth = 200;
  double _cropHeight = 200;

  // 图片显示的实际尺寸
  double _imageDisplayWidth = 0;
  double _imageDisplayHeight = 0;

  // 处理状态
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeCropArea();
    });
  }

  void _initializeCropArea() {
    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;
    final availableHeight =
        screenSize.height -
        MediaQuery.of(context).padding.top -
        kToolbarHeight -
        100; // 减去底部按钮区域

    // 设置初始裁剪区域为屏幕中央的正方形
    final initialSize = (screenSize.width * 0.6).clamp(150.0, 300.0);

    setState(() {
      _imageDisplayWidth = screenSize.width - 32; // 减去margin
      _imageDisplayHeight = availableHeight - 32;

      _cropWidth = initialSize;
      _cropHeight = initialSize;
      _cropLeft = (_imageDisplayWidth - _cropWidth) / 2;
      _cropTop = (_imageDisplayHeight - _cropHeight) / 2;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).selectSendArea,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        backgroundColor: isDarkMode ? const Color(0xFF1F1F1F) : Colors.white,
        foregroundColor: ThemeHelper.getTextPrimary(context),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // 图片裁剪区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildImageWithCropOverlay(),
              ),
            ),
          ),

          // 底部操作区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // 重新拍照按钮
                ElevatedButton.icon(
                  onPressed: _isProcessing
                      ? null
                      : () {
                          Navigator.of(context).pop();
                          Navigator.of(context).pop();
                        },
                  icon: const Icon(Icons.camera_alt, size: 20),
                  label: Text(AppLocalizations.of(context).retakePhoto),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isDarkMode
                        ? const Color(0xFF2C2C2C)
                        : Colors.grey[100],
                    foregroundColor: ThemeHelper.getTextPrimary(context),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                ),

                // 发送按钮
                ElevatedButton.icon(
                  onPressed: _isProcessing ? null : _processCroppedImage,
                  icon: _isProcessing
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : const Icon(Icons.send, size: 20),
                  label: Text(
                    _isProcessing
                        ? AppLocalizations.of(context).processing
                        : AppLocalizations.of(context).send,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageWithCropOverlay() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 更新图片显示尺寸
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_imageDisplayWidth != constraints.maxWidth ||
              _imageDisplayHeight != constraints.maxHeight) {
            setState(() {
              _imageDisplayWidth = constraints.maxWidth;
              _imageDisplayHeight = constraints.maxHeight;
              _validateCropBounds();
            });
          }
        });

        return Stack(
          children: [
            // 背景图片
            SizedBox(
              width: constraints.maxWidth,
              height: constraints.maxHeight,
              child: Image.file(
                widget.imageFile,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[300],
                    child: const Center(child: Icon(Icons.error, size: 64)),
                  );
                },
              ),
            ),

            // 暗色遮罩层，中间透明显示原图
            CustomPaint(
              size: Size(constraints.maxWidth, constraints.maxHeight),
              painter: CropOverlayPainter(
                cropRect: Rect.fromLTWH(
                  _cropLeft,
                  _cropTop,
                  _cropWidth,
                  _cropHeight,
                ),
                overlayColor: Colors.black.withValues(alpha: 0.5),
              ),
            ),

            // 可拖拽的裁剪框
            Positioned(left: _cropLeft, top: _cropTop, child: _buildCropBox()),
          ],
        );
      },
    );
  }

  Widget _buildCropBox() {
    return SizedBox(
      width: _cropWidth,
      height: _cropHeight,
      child: Stack(
        children: [
          // 裁剪框边框
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.white, width: 2.0),
              ),
            ),
          ),

          // 中心拖动区域（移动整个裁剪框）- 放在边框后面，覆盖中间区域
          Positioned(
            left: 25,
            top: 25,
            right: 25,
            bottom: 25,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque, // 确保透明区域也能接收触摸事件
              onPanUpdate: (details) {
                setState(() {
                  // 移动裁剪框，但保持在边界内
                  _cropLeft = (_cropLeft + details.delta.dx).clamp(
                    0.0,
                    _imageDisplayWidth - _cropWidth,
                  );
                  _cropTop = (_cropTop + details.delta.dy).clamp(
                    0.0,
                    _imageDisplayHeight - _cropHeight,
                  );
                });
              },
              child: Container(color: Colors.transparent),
            ),
          ),

          // 四条边框的拖动区域（用于单向调整大小）
          _buildEdgeHandle('top'),
          _buildEdgeHandle('bottom'),
          _buildEdgeHandle('left'),
          _buildEdgeHandle('right'),

          // 四个角的控制点（用于双向调整裁剪框大小）
          _buildCornerHandle(Alignment.topLeft),
          _buildCornerHandle(Alignment.topRight),
          _buildCornerHandle(Alignment.bottomLeft),
          _buildCornerHandle(Alignment.bottomRight),
        ],
      ),
    );
  }

  Widget _buildCornerHandle(Alignment alignment) {
    return Align(
      alignment: alignment,
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            final dx = details.delta.dx;
            final dy = details.delta.dy;

            // 修复拖动方向逻辑，使其符合直觉
            if (alignment == Alignment.topLeft) {
              _resizeCropBox(dx, dy, -dx, -dy);
            } else if (alignment == Alignment.topRight) {
              _resizeCropBox(0, dy, dx, -dy);
            } else if (alignment == Alignment.bottomLeft) {
              _resizeCropBox(dx, 0, -dx, dy);
            } else if (alignment == Alignment.bottomRight) {
              _resizeCropBox(0, 0, dx, dy);
            }
          });
        },
        child: SizedBox(
          width: 30, // 增大触摸区域
          height: 30,
          child: ColoredBox(
            color: Colors.transparent, // 透明背景增大触摸区域
            child: Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: Stack(
                  children: [
                    // 角部加粗线条 - L形状，增强视觉反馈
                    if (alignment == Alignment.topLeft) ...[
                      Positioned(
                        top: 0,
                        left: 0,
                        child: Container(
                          width: 4,
                          height: 14,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        left: 0,
                        child: Container(
                          width: 14,
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                    if (alignment == Alignment.topRight) ...[
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          width: 4,
                          height: 14,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          width: 14,
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                    if (alignment == Alignment.bottomLeft) ...[
                      Positioned(
                        bottom: 0,
                        left: 0,
                        child: Container(
                          width: 4,
                          height: 14,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        child: Container(
                          width: 14,
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                    if (alignment == Alignment.bottomRight) ...[
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 4,
                          height: 14,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 14,
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建边框拖动手柄（用于单向调整大小）
  Widget _buildEdgeHandle(String edge) {
    Widget handle;

    switch (edge) {
      case 'top':
        handle = Positioned(
          top: -10,
          left: 20,
          right: 20,
          child: GestureDetector(
            onPanUpdate: (details) {
              setState(() {
                final dy = details.delta.dy;
                _resizeCropBox(0, dy, 0, -dy);
              });
            },
            child: Container(height: 20, color: Colors.transparent),
          ),
        );
        break;
      case 'bottom':
        handle = Positioned(
          bottom: -10,
          left: 20,
          right: 20,
          child: GestureDetector(
            onPanUpdate: (details) {
              setState(() {
                final dy = details.delta.dy;
                _resizeCropBox(0, 0, 0, dy);
              });
            },
            child: Container(height: 20, color: Colors.transparent),
          ),
        );
        break;
      case 'left':
        handle = Positioned(
          left: -10,
          top: 20,
          bottom: 20,
          child: GestureDetector(
            onPanUpdate: (details) {
              setState(() {
                final dx = details.delta.dx;
                _resizeCropBox(dx, 0, -dx, 0);
              });
            },
            child: Container(width: 20, color: Colors.transparent),
          ),
        );
        break;
      case 'right':
        handle = Positioned(
          right: -10,
          top: 20,
          bottom: 20,
          child: GestureDetector(
            onPanUpdate: (details) {
              setState(() {
                final dx = details.delta.dx;
                _resizeCropBox(0, 0, dx, 0);
              });
            },
            child: Container(width: 20, color: Colors.transparent),
          ),
        );
        break;
      default:
        handle = const SizedBox.shrink();
    }

    return handle;
  }

  void _resizeCropBox(
    double leftDelta,
    double topDelta,
    double widthDelta,
    double heightDelta,
  ) {
    // 最小和最大尺寸限制
    const double minSize = 30.0;
    final double maxWidth = _imageDisplayWidth;
    final double maxHeight = _imageDisplayHeight;

    // 计算新的位置和尺寸
    double newLeft = _cropLeft + leftDelta;
    double newTop = _cropTop + topDelta;
    double newWidth = _cropWidth + widthDelta;
    double newHeight = _cropHeight + heightDelta;

    // 确保最小尺寸
    newWidth = newWidth.clamp(minSize, maxWidth);
    newHeight = newHeight.clamp(minSize, maxHeight);

    // 确保左边界
    if (newLeft < 0) {
      newWidth += newLeft; // 补偿宽度
      newLeft = 0;
    }

    // 确保上边界
    if (newTop < 0) {
      newHeight += newTop; // 补偿高度
      newTop = 0;
    }

    // 确保右边界
    if (newLeft + newWidth > maxWidth) {
      if (leftDelta != 0) {
        // 如果是从左边拖动，调整左边位置
        newLeft = maxWidth - newWidth;
      } else {
        // 如果是从右边拖动，调整宽度
        newWidth = maxWidth - newLeft;
      }
    }

    // 确保下边界
    if (newTop + newHeight > maxHeight) {
      if (topDelta != 0) {
        // 如果是从上边拖动，调整上边位置
        newTop = maxHeight - newHeight;
      } else {
        // 如果是从下边拖动，调整高度
        newHeight = maxHeight - newTop;
      }
    }

    // 最后验证所有约束
    if (newWidth >= minSize &&
        newHeight >= minSize &&
        newLeft >= 0 &&
        newTop >= 0 &&
        newLeft + newWidth <= maxWidth &&
        newTop + newHeight <= maxHeight) {
      _cropLeft = newLeft;
      _cropTop = newTop;
      _cropWidth = newWidth;
      _cropHeight = newHeight;
    }
  }

  void _validateCropBounds() {
    if (_imageDisplayWidth > 0 && _imageDisplayHeight > 0) {
      _cropLeft = _cropLeft.clamp(0.0, _imageDisplayWidth - _cropWidth);
      _cropTop = _cropTop.clamp(0.0, _imageDisplayHeight - _cropHeight);
      _cropWidth = _cropWidth.clamp(50.0, _imageDisplayWidth - _cropLeft);
      _cropHeight = _cropHeight.clamp(50.0, _imageDisplayHeight - _cropTop);
    }
  }

  Future<void> _processCroppedImage() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // 执行图片裁剪
      final croppedFile = await _cropImageFile();

      if (!mounted) return;

      // 返回裁剪后的图片路径
      Navigator.of(context).pop({'croppedImagePath': croppedFile.path});
    } catch (e) {
      if (mounted) {
        _showErrorDialog('处理图片时出错: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// 执行图片裁剪，返回裁剪后的图片文件
  Future<File> _cropImageFile() async {
    try {
      // 读取原始图片
      final originalBytes = await widget.imageFile.readAsBytes();
      final originalImage = image_lib.decodeImage(originalBytes);

      if (originalImage == null) {
        throw Exception('无法解码图片');
      }

      // 计算裁剪区域在原始图片中的实际位置
      final scaleX = originalImage.width / _imageDisplayWidth;
      final scaleY = originalImage.height / _imageDisplayHeight;

      final cropX = (_cropLeft * scaleX).round();
      final cropY = (_cropTop * scaleY).round();
      final cropW = (_cropWidth * scaleX).round();
      final cropH = (_cropHeight * scaleY).round();

      // 执行裁剪
      final croppedImage = image_lib.copyCrop(
        originalImage,
        x: cropX,
        y: cropY,
        width: cropW,
        height: cropH,
      );

      // 保存裁剪后的图片
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final croppedFile = File('${tempDir.path}/cropped_$timestamp.jpg');

      final croppedBytes = image_lib.encodeJpg(croppedImage, quality: 85);
      await croppedFile.writeAsBytes(croppedBytes);

      return croppedFile;
    } catch (e) {
      throw Exception('图片裁剪失败: $e');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context).error),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalizations.of(context).confirm),
          ),
        ],
      ),
    );
  }
}
