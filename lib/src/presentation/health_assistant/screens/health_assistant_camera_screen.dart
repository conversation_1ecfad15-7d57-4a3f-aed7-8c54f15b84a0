import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../generated/l10n/app_localizations.dart';

import '../../../utils/toast_util.dart';
import 'health_assistant_image_crop_screen.dart';

/// 健康助手拍照页面
class HealthAssistantCameraScreen extends StatefulWidget {
  final Function(String, String, String, String?, {String? imagePath})?
  onAIGuideResult;

  final Function(String imagePath, {String? question})?
  onPhotoResult; // 新增拍照结果回调

  const HealthAssistantCameraScreen({
    super.key,
    this.onAIGuideResult,
    this.onPhotoResult,
  });

  @override
  State<HealthAssistantCameraScreen> createState() =>
      _HealthAssistantCameraScreenState();
}

class _HealthAssistantCameraScreenState
    extends State<HealthAssistantCameraScreen>
    with WidgetsBindingObserver {
  CameraController? _cameraController;
  List<CameraDescription> _cameras = [];
  bool _isCameraInitialized = false;
  bool _isFlashOn = false;
  bool _isBackCamera = true;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final CameraController? cameraController = _cameraController;

    // App state changed before we got the chance to initialize.
    if (cameraController == null || !cameraController.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      cameraController.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    }
  }

  /// 初始化相机
  Future<void> _initializeCamera() async {
    try {
      // 检查相机权限
      final status = await Permission.camera.status;
      if (!status.isGranted) {
        final result = await Permission.camera.request();
        if (!result.isGranted) {
          if (mounted) {
            ToastUtil.show(
              context,
              AppLocalizations.of(context).cameraPermissionRequired,
            );
            Navigator.pop(context);
          }
          return;
        }
      }

      // 获取可用相机
      _cameras = await availableCameras();
      if (_cameras.isEmpty) {
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).noCameraDetected,
          );
          Navigator.pop(context);
        }
        return;
      }

      // 初始化相机控制器
      final camera = _cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => _cameras.first,
      );

      _cameraController = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _cameraController!.initialize();

      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
          _isBackCamera = camera.lensDirection == CameraLensDirection.back;
        });
      }
    } catch (e) {
      debugPrint('相机初始化失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).cameraInitializationFailed(e.toString()),
        );
        Navigator.pop(context);
      }
    }
  }

  /// 切换相机
  Future<void> _switchCamera() async {
    if (_cameras.length < 2) return;

    try {
      await _cameraController?.dispose();

      final newCamera = _cameras.firstWhere(
        (camera) =>
            camera.lensDirection !=
            (_isBackCamera
                ? CameraLensDirection.back
                : CameraLensDirection.front),
        orElse: () => _cameras.first,
      );

      _cameraController = CameraController(
        newCamera,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _cameraController!.initialize();

      if (mounted) {
        setState(() {
          _isBackCamera = newCamera.lensDirection == CameraLensDirection.back;
          _isFlashOn = false; // 切换相机时重置闪光灯状态
        });
      }
    } catch (e) {
      debugPrint('切换相机失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).switchCameraFailed,
        );
      }
    }
  }

  /// 切换闪光灯
  Future<void> _toggleFlash() async {
    if (_cameraController == null || !_isBackCamera) return;

    try {
      final newFlashMode = _isFlashOn ? FlashMode.off : FlashMode.torch;
      await _cameraController!.setFlashMode(newFlashMode);

      if (mounted) {
        setState(() {
          _isFlashOn = !_isFlashOn;
        });
      }
    } catch (e) {
      debugPrint('切换闪光灯失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).flashlightOperationFailed,
        );
      }
    }
  }

  /// 拍照
  Future<void> _takePicture() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    try {
      // 关闭闪光灯（如果开启）
      if (_isFlashOn) {
        await _cameraController!.setFlashMode(FlashMode.off);
      }

      final XFile image = await _cameraController!.takePicture();

      if (mounted) {
        // 导航到图片裁剪页面
        final result = await Navigator.push<Map<String, dynamic>>(
          context,
          MaterialPageRoute(
            builder: (context) =>
                HealthAssistantImageCropScreen(imageFile: File(image.path)),
          ),
        );

        if (result != null && result['croppedImagePath'] != null) {
          final croppedImagePath = result['croppedImagePath'] as String;

          // 使用新的拍照结果回调
          if (widget.onPhotoResult != null) {
            widget.onPhotoResult!(croppedImagePath);
          } else if (widget.onAIGuideResult != null) {
            // 保持向后兼容
            widget.onAIGuideResult!(
              '健康助手图片识别结果', // 原始文本
              'AI Guide Image Recognition Result', // 翻译文本
              '', // 音频URL
              'Chinese', // 输入语言
              imagePath: croppedImagePath,
            );
          }

          // 返回上一页
          if (mounted) {
            Navigator.pop(context);
          }
        }
      }
    } catch (e) {
      debugPrint('拍照失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).capturePhotoFailed(e.toString()),
        );
      }
    }
  }

  /// 从相册选择图片
  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        // 导航到图片裁剪页面
        final result = await Navigator.push<Map<String, dynamic>>(
          context,
          MaterialPageRoute(
            builder: (context) =>
                HealthAssistantImageCropScreen(imageFile: File(image.path)),
          ),
        );

        if (result != null && result['croppedImagePath'] != null) {
          final croppedImagePath = result['croppedImagePath'] as String;

          // 使用新的拍照结果回调
          if (widget.onPhotoResult != null) {
            widget.onPhotoResult!(croppedImagePath);
          } else if (widget.onAIGuideResult != null) {
            // 保持向后兼容
            widget.onAIGuideResult!(
              '健康助手图片识别结果', // 原始文本
              'AI Guide Image Recognition Result', // 翻译文本
              '', // 音频URL
              'Chinese', // 输入语言
              imagePath: croppedImagePath,
            );
          }

          // 返回上一页
          if (mounted) {
            Navigator.pop(context);
          }
        }
      }
    } catch (e) {
      debugPrint('选择图片失败: $e');
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).selectImageFailed(e.toString()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 相机预览
          if (_isCameraInitialized && _cameraController != null)
            Positioned.fill(
              child: FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                  width: _cameraController!.value.previewSize!.height,
                  height: _cameraController!.value.previewSize!.width,
                  child: CameraPreview(_cameraController!),
                ),
              ),
            )
          else
            const Positioned.fill(
              child: Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
            ),

          // 顶部控制栏
          Positioned(top: 0, left: 0, right: 0, child: _buildTopControls()),

          // 底部控制栏
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomControls(),
          ),
        ],
      ),
    );
  }

  /// 构建顶部控制栏
  Widget _buildTopControls() {
    final topPadding = MediaQuery.of(context).padding.top;

    return Container(
      padding: EdgeInsets.only(
        top: topPadding + 10,
        left: 20,
        right: 20,
        bottom: 20,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
        ),
      ),
      child: Row(
        children: [
          // 返回按钮
          _buildControlButton(
            icon: Icons.arrow_back,
            onTap: () => Navigator.pop(context),
          ),

          const Spacer(),

          // 标题
          Text(
            AppLocalizations.of(context).takePhotoAndSend,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),

          const Spacer(),

          // 切换相机按钮
          if (_cameras.length > 1)
            _buildControlButton(
              icon: Icons.flip_camera_ios,
              onTap: _switchCamera,
            )
          else
            const SizedBox(width: 60),
        ],
      ),
    );
  }

  /// 构建底部控制栏
  Widget _buildBottomControls() {
    final bottomPadding = MediaQuery.of(context).padding.bottom;

    return Container(
      padding: EdgeInsets.only(
        top: 20,
        left: 20,
        right: 20,
        bottom: bottomPadding + 20,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [Colors.black.withValues(alpha: 0.7), Colors.transparent],
        ),
      ),
      child: Row(
        children: [
          // 手电筒按钮 - 贴近左边缘
          if (_isBackCamera) // 只有后置摄像头才显示手电筒
            _buildControlButton(
              icon: _isFlashOn ? Icons.flash_on : Icons.flash_off,
              onTap: _toggleFlash,
              isActive: _isFlashOn,
            )
          else
            const SizedBox(width: 60),

          // 弹性间距
          const Spacer(),

          // 拍照按钮 - 居中
          GestureDetector(
            onTap: _takePicture,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 4),
                color: Colors.white.withValues(alpha: 0.2),
              ),
              child: const Icon(
                Icons.camera_alt,
                color: Colors.white,
                size: 40,
              ),
            ),
          ),

          // 弹性间距
          const Spacer(),

          // 相册按钮 - 贴近右边缘
          _buildControlButton(
            icon: Icons.photo_library,
            onTap: _pickImageFromGallery,
          ),
        ],
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isActive = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isActive
              ? Colors.yellow.withValues(alpha: 0.3)
              : Colors.black.withValues(alpha: 0.5),
          border: Border.all(
            color: isActive
                ? Colors.yellow
                : Colors.white.withValues(alpha: 0.5),
            width: 2,
          ),
        ),
        child: Icon(
          icon,
          color: isActive ? Colors.yellow : Colors.white,
          size: 28,
        ),
      ),
    );
  }
}
