import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_model.dart';
import '../../../services/doctor_service.dart';
import '../widgets/doctor_card_widget.dart';

/// 医生列表页面
class DoctorListScreen extends StatefulWidget {
  const DoctorListScreen({super.key});

  @override
  State<DoctorListScreen> createState() => _DoctorListScreenState();
}

class _DoctorListScreenState extends State<DoctorListScreen> {
  List<DoctorModel> _doctors = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDoctors();
  }

  /// 加载医生数据
  Future<void> _loadDoctors() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final doctors = await DoctorService().getDoctors();
      if (mounted) {
        setState(() {
          _doctors = doctors;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _doctors = [];
          _isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载医生列表失败: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          '专家团队',
          style: TextStyle(
            color: ThemeHelper.getTextPrimary(context),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ThemeHelper.getTextPrimary(context),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: AppColors.primary),
            onPressed: _loadDoctors,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// 构建页面主体
  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingView();
    }

    if (_doctors.isEmpty) {
      return _buildEmptyView();
    }

    return _buildDoctorList();
  }

  /// 构建加载视图
  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          const SizedBox(height: 16),
          Text(
            '正在加载专家信息...',
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空数据视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medical_services_outlined,
            size: 64,
            color: ThemeHelper.getTextHint(context),
          ),
          const SizedBox(height: 16),
          Text(
            '暂无专家信息',
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '请稍后再试',
            style: TextStyle(
              color: ThemeHelper.getTextHint(context),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadDoctors,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('重新加载'),
          ),
        ],
      ),
    );
  }

  /// 构建医生列表
  Widget _buildDoctorList() {
    return RefreshIndicator(
      onRefresh: _loadDoctors,
      color: AppColors.primary,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _doctors.length,
        itemBuilder: (context, index) {
          final doctor = _doctors[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: DoctorCardWidget(
              doctor: doctor,
              onTap: () => _onDoctorTap(doctor),
            ),
          );
        },
      ),
    );
  }

  /// 处理医生卡片点击
  void _onDoctorTap(DoctorModel doctor) {
    // TODO: 跳转到医生详情页面或开始咨询
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('点击了${doctor.name}'),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 1),
      ),
    );
  }
}
