import 'package:flutter/material.dart';

import '../../../config/themes/app_colors.dart';
import '../../../common/utils/app_responsive_sizes.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/conversation_model.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// AI导游聊天历史抽屉组件 - 与翻译页面历史抽屉设计完全一致
class ChatHistoryDrawer extends StatefulWidget {
  final List<ConversationModel> conversations;
  final bool isLoading;
  final Function(ConversationModel) onConversationSelected;
  final VoidCallback onClearAllHistory;
  final VoidCallback onRefresh;
  final Function(String conversationId, String newTitle)? onTitleUpdated;

  const ChatHistoryDrawer({
    super.key,
    required this.conversations,
    required this.isLoading,
    required this.onConversationSelected,
    required this.onClearAllHistory,
    required this.onRefresh,
    this.onTitleUpdated,
  });

  @override
  State<ChatHistoryDrawer> createState() => _ChatHistoryDrawerState();
}

class _ChatHistoryDrawerState extends State<ChatHistoryDrawer> {
  bool _isRefreshing = false;

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: ThemeHelper.getBackground(context), // 适配暗色模式背景
      width: MediaQuery.of(context).size.width * 0.75, // 屏幕宽度的75%
      child: SafeArea(
        child: Column(
          children: [
            // 抽屉头部
            _buildHeader(context),

            // 加载指示器
            if (widget.isLoading || _isRefreshing)
              Padding(
                padding: EdgeInsets.all(AppResponsiveSizes(context).spacingL),
                child: Center(
                  child: SizedBox(
                    width: AppResponsiveSizes(context).iconXL,
                    height: AppResponsiveSizes(context).iconXL,
                    child: const CircularProgressIndicator(
                      color: AppColors.primary, // 使用主题色
                    ),
                  ),
                ),
              ),

            // 聊天历史列表
            Expanded(
              child:
                  widget.conversations.isEmpty &&
                      !widget.isLoading &&
                      !_isRefreshing
                  ? _buildEmptyState(context)
                  : _buildConversationList(context),
            ),

            // 底部操作区
            _buildBottomActions(context),
          ],
        ),
      ),
    );
  }

  // 构建抽屉头部
  Widget _buildHeader(BuildContext context) {
    final responsiveSizes = AppResponsiveSizes(context);

    return Container(
      padding: EdgeInsets.fromLTRB(
        responsiveSizes.spacingL,
        responsiveSizes.spacingM,
        responsiveSizes.spacingM,
        responsiveSizes.spacingM,
      ),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context), // 适配暗色模式卡片背景
        border: Border(
          bottom: BorderSide(
            color: ThemeHelper.getDivider(context),
            width: 0.5,
          ),
        ),
      ),
      // 强制使用LTR方向，不受RTL影响
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Row(
          children: [
            // 标题
            Expanded(
              child: Text(
                AppLocalizations.of(context).chatHistory,
                style: TextStyle(
                  fontSize: responsiveSizes.getRelativeSize(1.1),
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ),

            // 刷新按钮
            IconButton(
              onPressed: _isRefreshing ? null : _handleRefresh,
              icon: Icon(
                Icons.refresh,
                color: _isRefreshing
                    ? ThemeHelper.getTextSecondary(context)
                    : AppColors.primary,
                size: responsiveSizes.iconM,
              ),
              tooltip: AppLocalizations.of(context).refresh,
            ),

            // 关闭按钮
            IconButton(
              onPressed: () => Navigator.pop(context),
              icon: Icon(
                Icons.close,
                color: ThemeHelper.getTextSecondary(context),
                size: responsiveSizes.iconM,
              ),
              tooltip: AppLocalizations.of(context).close,
            ),
          ],
        ),
      ),
    );
  }

  // 构建空状态
  Widget _buildEmptyState(BuildContext context) {
    final responsiveSizes = AppResponsiveSizes(context);

    return Center(
      child: Padding(
        padding: EdgeInsets.all(responsiveSizes.spacingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: responsiveSizes.iconXXL,
              color: ThemeHelper.getTextSecondary(
                context,
              ).withValues(alpha: 0.5),
            ),
            SizedBox(height: responsiveSizes.spacingL),
            Text(
              AppLocalizations.of(context).noChatRecords,
              style: TextStyle(
                fontSize: responsiveSizes.getRelativeSize(1.0),
                color: ThemeHelper.getTextSecondary(context),
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: responsiveSizes.spacingS),
            Text(
              AppLocalizations.of(context).startChatWithAiGuide,
              style: TextStyle(
                fontSize: responsiveSizes.getRelativeSize(0.85),
                color: ThemeHelper.getTextSecondary(
                  context,
                ).withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建对话列表
  Widget _buildConversationList(BuildContext context) {
    final responsiveSizes = AppResponsiveSizes(context);

    return ListView.separated(
      padding: EdgeInsets.symmetric(vertical: responsiveSizes.spacingS),
      itemCount: widget.conversations.length,
      separatorBuilder: (context, index) => Container(
        margin: EdgeInsets.symmetric(horizontal: responsiveSizes.spacingL),
        child: Divider(
          height: 1,
          thickness: 0.5,
          color: ThemeHelper.getDivider(context),
        ),
      ),
      itemBuilder: (context, index) {
        final conversation = widget.conversations[index];
        return _buildConversationItem(context, conversation);
      },
    );
  }

  // 构建对话项
  Widget _buildConversationItem(
    BuildContext context,
    ConversationModel conversation,
  ) {
    final responsiveSizes = AppResponsiveSizes(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () async => await widget.onConversationSelected(conversation),
        splashColor: AppColors.primary.withValues(alpha: 0.1),
        highlightColor: AppColors.primary.withValues(alpha: 0.05),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: responsiveSizes.spacingL,
            vertical: responsiveSizes.spacingM,
          ),
          // 强制使用LTR方向，不受RTL影响
          child: Directionality(
            textDirection: TextDirection.ltr,
            child: Row(
              children: [
                // 医生头像
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: ClipOval(
                    child: conversation.doctor?.fullAvatarUrl.isNotEmpty == true
                        ? Image.network(
                            conversation.doctor!.fullAvatarUrl,
                            width: 50,
                            height: 50,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return _buildDefaultAvatar();
                            },
                          )
                        : _buildDefaultAvatar(),
                  ),
                ),
                const SizedBox(width: 12),
                // 聊天信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 医生姓名和专科
                      Row(
                        children: [
                          Flexible(
                            child: Text(
                              conversation.doctor?.name ??
                                  AppLocalizations.of(context).unknownDoctor,
                              style: TextStyle(
                                color: ThemeHelper.getTextPrimary(context),
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (conversation.doctor?.specialty != null &&
                              conversation.doctor!.specialty!.isNotEmpty) ...[
                            const SizedBox(width: 8),
                            Flexible(
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  conversation.doctor!.specialty!,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: AppColors.primary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      // 聊天标题预览
                      Text(
                        conversation.title.isNotEmpty
                            ? conversation.title
                            : AppLocalizations.of(context).newConversation,
                        style: TextStyle(
                          color: ThemeHelper.getTextSecondary(context),
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                // 编辑按钮、时间和消息数量
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // 编辑按钮
                    IconButton(
                      onPressed: () =>
                          _showEditTitleDialog(context, conversation),
                      icon: Icon(
                        Icons.edit_outlined,
                        size: 16,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 24,
                        minHeight: 24,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatTime(conversation.updatedAt),
                      style: TextStyle(
                        color: ThemeHelper.getTextSecondary(context),
                        fontSize: 12,
                      ),
                    ),
                    if (conversation.messageCount > 0) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          '${conversation.messageCount}',
                          style: TextStyle(
                            fontSize: 10,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Container(
      width: 50,
      height: 50,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF10B981), Color(0xFF059669)],
        ),
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.local_hospital, color: Colors.white, size: 24),
    );
  }

  // 构建底部操作区
  Widget _buildBottomActions(BuildContext context) {
    final responsiveSizes = AppResponsiveSizes(context);

    return Container(
      padding: EdgeInsets.all(responsiveSizes.spacingL),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context), // 适配暗色模式卡片背景
        border: Border(
          top: BorderSide(color: ThemeHelper.getDivider(context), width: 0.5),
        ),
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: widget.conversations.isEmpty
              ? null
              : widget.onClearAllHistory,
          icon: Icon(Icons.delete_outline, size: responsiveSizes.iconS),
          label: Text(
            AppLocalizations.of(context).clearHistoryButton,
            style: TextStyle(
              fontSize: responsiveSizes.getRelativeSize(0.9),
              fontWeight: FontWeight.w500,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.error,
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(vertical: responsiveSizes.spacingM),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                responsiveSizes.cardBorderRadius,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 处理刷新
  Future<void> _handleRefresh() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      widget.onRefresh();
      // 等待一小段时间确保刷新完成
      await Future.delayed(const Duration(milliseconds: 500));
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  // 格式化时间
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return AppLocalizations.of(context).hoursAgo(difference.inHours);
    } else if (difference.inMinutes > 0) {
      return AppLocalizations.of(context).minutesAgo(difference.inMinutes);
    } else {
      return AppLocalizations.of(context).justNow;
    }
  }

  // 显示编辑标题对话框
  void _showEditTitleDialog(
    BuildContext context,
    ConversationModel conversation,
  ) {
    final TextEditingController titleController = TextEditingController(
      text: conversation.title,
    );
    final responsiveSizes = AppResponsiveSizes(context);

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: ThemeHelper.getCardBackground(context),
          title: Text(
            AppLocalizations.of(context).editConversationTitle,
            style: TextStyle(
              color: ThemeHelper.getTextPrimary(context),
              fontSize: responsiveSizes.getRelativeSize(1.1),
              fontWeight: FontWeight.w600,
            ),
          ),
          content: TextField(
            controller: titleController,
            autofocus: true,
            maxLength: 50,
            decoration: InputDecoration(
              hintText: AppLocalizations.of(context).enterNewTitle,
              hintStyle: TextStyle(color: ThemeHelper.getTextHint(context)),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: ThemeHelper.getDivider(context)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.primary,
                  width: 2,
                ),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: responsiveSizes.spacingM,
                vertical: responsiveSizes.spacingS,
              ),
            ),
            style: TextStyle(
              color: ThemeHelper.getTextPrimary(context),
              fontSize: responsiveSizes.getRelativeSize(0.9),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                AppLocalizations.of(context).cancel,
                style: TextStyle(
                  color: ThemeHelper.getTextSecondary(context),
                  fontSize: responsiveSizes.getRelativeSize(0.9),
                ),
              ),
            ),
            TextButton(
              onPressed: () async {
                final newTitle = titleController.text.trim();
                if (newTitle.isNotEmpty && newTitle != conversation.title) {
                  Navigator.of(dialogContext).pop();
                  await _handleTitleUpdate(conversation.id, newTitle);
                } else {
                  Navigator.of(dialogContext).pop();
                }
              },
              child: Text(
                AppLocalizations.of(context).confirm,
                style: TextStyle(
                  color: AppColors.primary,
                  fontSize: responsiveSizes.getRelativeSize(0.9),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // 处理标题更新
  Future<void> _handleTitleUpdate(
    String conversationId,
    String newTitle,
  ) async {
    try {
      // 调用回调函数更新标题
      if (widget.onTitleUpdated != null) {
        await widget.onTitleUpdated!(conversationId, newTitle);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context).updateTitleFailed}: $e',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
