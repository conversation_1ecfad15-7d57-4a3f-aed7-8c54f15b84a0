import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_model.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../common/widgets/cached_image_widget.dart';

/// 医生详细信息卡片组件 - 用于聊天页面中间显示
class DoctorDetailCard extends StatelessWidget {
  final DoctorModel doctor;

  const DoctorDetailCard({super.key, required this.doctor});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 医生头像和基本信息
          _buildDoctorHeader(context),

          const SizedBox(height: 16),

          // 分割线
          Container(
            height: 1,
            color: ThemeHelper.getBorder(context).withValues(alpha: 0.3),
          ),

          const SizedBox(height: 16),

          // 详细信息
          _buildDoctorDetails(context),

          const SizedBox(height: 16),

          // 经验和评分
          _buildExperienceAndRating(context),
        ],
      ),
    );
  }

  /// 构建医生头像和基本信息
  Widget _buildDoctorHeader(BuildContext context) {
    return Row(
      children: [
        // 头像
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
              width: 3,
            ),
          ),
          child: CachedImageWidget.avatar(
            imageUrl: doctor.fullAvatarUrl,
            size: 74,
          ),
        ),

        const SizedBox(width: 16),

        // 基本信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 医生姓名
              Text(
                doctor.name,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: ThemeHelper.getTextPrimary(context),
                ),
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 6),

              // AI标识
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary,
                      AppColors.primary.withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.smart_toy, size: 12, color: Colors.white),
                    const SizedBox(width: 3),
                    Text(
                      'AI',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 6),

              // 专科
              Text(
                doctor.specialization,
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建医生详细信息
  Widget _buildDoctorDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 专业描述
        Text(
          AppLocalizations.of(context).professionalIntroduction,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),

        const SizedBox(height: 8),

        Text(
          doctor.safeDescription,
          style: TextStyle(
            fontSize: 14,
            color: ThemeHelper.getTextSecondary(context),
            height: 1.4,
          ),
        ),

        // 擅长领域
        if (doctor.specialtiesList.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).specialtyField,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: doctor.specialtiesList.map((specialty) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  specialty,
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  /// 构建经验和评分信息
  Widget _buildExperienceAndRating(BuildContext context) {
    return Row(
      children: [
        // 工作经验
        Icon(
          Icons.work_outline,
          size: 16,
          color: ThemeHelper.getTextHint(context),
        ),
        const SizedBox(width: 4),
        Text(
          '${doctor.experience} ${AppLocalizations.of(context).years}',
          style: TextStyle(
            fontSize: 13,
            color: ThemeHelper.getTextHint(context),
          ),
        ),
        const SizedBox(width: 16),
        // 评分
        Icon(Icons.star, size: 16, color: Colors.amber),
        const SizedBox(width: 4),
        Text(
          AppLocalizations.of(context).ratingScore(doctor.rating.toString()),
          style: TextStyle(
            fontSize: 13,
            color: ThemeHelper.getTextHint(context),
          ),
        ),
      ],
    );
  }
}
