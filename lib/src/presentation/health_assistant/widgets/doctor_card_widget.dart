import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_model.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../common/widgets/cached_image_widget.dart';

/// 医生卡片组件
class DoctorCardWidget extends StatelessWidget {
  final DoctorModel doctor;
  final VoidCallback? onTap;

  const DoctorCardWidget({super.key, required this.doctor, this.onTap});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;

    // 根据屏幕宽度判断是否使用紧凑布局
    final isCompactLayout = screenWidth < 360;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(isCompactLayout ? 12 : 16),
        decoration: BoxDecoration(
          color: ThemeHelper.getCardBackground(context),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.black.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.1),
              blurRadius: isDarkMode ? 4 : 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: ThemeHelper.getBorder(context).withValues(alpha: 0.5),
            width: 0.5,
          ),
        ),
        child: isCompactLayout
            ? _buildCompactLayout(context)
            : _buildNormalLayout(context),
      ),
    );
  }

  /// 构建正常布局（横向布局）
  Widget _buildNormalLayout(BuildContext context) {
    return Row(
      children: [
        // 左侧头像
        _buildAvatar(),
        const SizedBox(width: 12),
        // 中间信息
        Expanded(child: _buildDoctorInfo(context)),
        const SizedBox(width: 8),
        // 右侧AI标识
        _buildAIBadge(context),
      ],
    );
  }

  /// 构建紧凑布局（纵向布局）
  Widget _buildCompactLayout(BuildContext context) {
    return Column(
      children: [
        // 上半部分：头像和基本信息
        Row(
          children: [
            _buildAvatar(isCompact: true),
            const SizedBox(width: 10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 医生姓名和AI标识
                  Row(
                    children: [
                      Flexible(
                        child: Text(
                          doctor.name,
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: ThemeHelper.getTextPrimary(context),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 6),
                      _buildCompactAIBadge(context),
                    ],
                  ),
                  const SizedBox(height: 2),
                  // 专科和评分
                  Row(
                    children: [
                      Flexible(
                        child: Text(
                          doctor.specialization,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 8),
                      _buildCompactRating(),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // 下半部分：描述和擅长领域
        _buildCompactDescription(context),
      ],
    );
  }

  /// 构建头像
  Widget _buildAvatar({bool isCompact = false}) {
    final size = isCompact ? 44.0 : 56.0;

    return CachedImageWidget.avatar(imageUrl: doctor.fullAvatarUrl, size: size);
  }

  /// 构建紧凑AI标识
  Widget _buildCompactAIBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.smart_toy, size: 10, color: Colors.white),
          const SizedBox(width: 2),
          Text(
            AppLocalizations.of(context).aiAssistant,
            style: TextStyle(
              fontSize: 8,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建紧凑评分
  Widget _buildCompactRating() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.amber.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star, size: 10, color: Colors.amber),
          const SizedBox(width: 2),
          Text(
            doctor.rating.toString(),
            style: TextStyle(
              fontSize: 9,
              color: Colors.amber.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建紧凑描述和擅长领域
  Widget _buildCompactDescription(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 描述
        Text(
          doctor.safeDescription,
          style: TextStyle(
            fontSize: 11,
            color: ThemeHelper.getTextSecondary(context),
            height: 1.2,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        // 擅长领域
        if (doctor.specialtiesList.isNotEmpty) ...[
          const SizedBox(height: 6),
          SizedBox(
            height: 24,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: doctor.specialtiesList.length,
              separatorBuilder: (context, index) => const SizedBox(width: 4),
              itemBuilder: (context, index) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    doctor.specialtiesList[index],
                    style: TextStyle(
                      fontSize: 10,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              },
            ),
          ),
        ],

        const SizedBox(height: 6),
        // 经验和评分
        Row(
          children: [
            // 经验
            Icon(
              Icons.work_outline,
              size: 12,
              color: ThemeHelper.getTextHint(context),
            ),
            const SizedBox(width: 2),
            Text(
              '${doctor.experience} ${AppLocalizations.of(context).years}',
              style: TextStyle(
                fontSize: 10,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
            const SizedBox(width: 8),
            // 评分
            Icon(Icons.star, size: 12, color: Colors.amber),
            const SizedBox(width: 2),
            Text(
              doctor.rating.toString(),
              style: TextStyle(
                fontSize: 10,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建医生信息
  Widget _buildDoctorInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 医生姓名和职称
        Row(
          children: [
            Flexible(
              child: Text(
                doctor.name,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  AppLocalizations.of(context).doctorTitle,
                  style: TextStyle(
                    fontSize: 10,
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 4),

        // 专科
        Text(
          doctor.specialization,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.primary,
            fontWeight: FontWeight.w500,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),

        const SizedBox(height: 6),

        // 描述
        Text(
          doctor.safeDescription,
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextSecondary(context),
            height: 1.3,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        // 擅长领域
        if (doctor.specialtiesList.isNotEmpty) ...[
          const SizedBox(height: 8),
          SizedBox(
            height: 26,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: doctor.specialtiesList.length,
              separatorBuilder: (context, index) => const SizedBox(width: 6),
              itemBuilder: (context, index) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    doctor.specialtiesList[index],
                    style: TextStyle(
                      fontSize: 11,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              },
            ),
          ),
        ],

        const SizedBox(height: 8),

        // 经验和评分
        Row(
          children: [
            Icon(
              Icons.work_outline,
              size: 14,
              color: ThemeHelper.getTextHint(context),
            ),
            const SizedBox(width: 4),
            Text(
              AppLocalizations.of(context).yearsExperience(doctor.experience),
              style: TextStyle(
                fontSize: 12,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
            const SizedBox(width: 12),
            Icon(Icons.star, size: 14, color: Colors.amber),
            const SizedBox(width: 2),
            Text(
              doctor.rating.toString(),
              style: TextStyle(
                fontSize: 12,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建AI标识
  Widget _buildAIBadge(BuildContext context) {
    return Column(
      children: [
        // AI智能体标识
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.primary,
                AppColors.primary.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.smart_toy, size: 12, color: Colors.white),
              const SizedBox(width: 4),
              Text(
                AppLocalizations.of(context).aiAssistant,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // 评分显示
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.amber.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.star, size: 12, color: Colors.amber),
              const SizedBox(width: 2),
              Text(
                doctor.rating.toString(),
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.amber.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
