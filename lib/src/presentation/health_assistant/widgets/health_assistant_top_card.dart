import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_model.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../common/widgets/cached_image_widget.dart';

/// 健康助手顶部卡片组件
class HealthAssistantTopCard extends StatelessWidget {
  final DoctorModel? selectedDoctor;
  final Function(DoctorModel)? onDoctorSelected;
  final bool isLoading;

  const HealthAssistantTopCard({
    super.key,
    this.selectedDoctor,
    this.onDoctorSelected,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: isLoading || selectedDoctor == null
          ? _buildSkeletonContent(context)
          : _buildDoctorContent(context),
    );
  }

  /// 构建医生内容
  Widget _buildDoctorContent(BuildContext context) {
    return Row(
      children: [
        // 左侧头像
        _buildDoctorAvatar(),

        const SizedBox(width: 12),

        // 中间信息
        Expanded(child: _buildDoctorInfo(context)),
        // 右侧全屏按钮
        _buildFullscreenButton(context),
      ],
    );
  }

  /// 构建骨架动画内容
  Widget _buildSkeletonContent(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Shimmer.fromColors(
      baseColor: isDark ? Colors.grey[800]! : Colors.grey[300]!,
      highlightColor: isDark ? Colors.grey[700]! : Colors.grey[100]!,
      period: const Duration(milliseconds: 1500),
      child: Row(
        children: [
          // 头像骨架
          Container(
            width: 50,
            height: 50,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),

          const SizedBox(width: 12),

          // 信息骨架
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 医生姓名
                Container(
                  width: 100,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(height: 6),
                // 专业领域
                Container(
                  width: 80,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ],
            ),
          ),

          // 按钮骨架
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建全屏按钮
  Widget _buildFullscreenButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _onFullscreenTap(context),
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          shape: BoxShape.circle,
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Icon(Icons.fullscreen, size: 20, color: AppColors.primary),
      ),
    );
  }

  /// 处理全屏按钮点击 - 跳转到数字人AI聊天页面
  void _onFullscreenTap(BuildContext context) {
    // TODO: 跳转到数字人AI聊天页面
    // 暂时显示提示信息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          AppLocalizations.of(context).digitalHumanChatInDevelopment,
        ),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 构建医生头像
  Widget _buildDoctorAvatar() {
    final avatarUrl = selectedDoctor?.fullAvatarUrl ?? '';

    return CachedImageWidget.avatar(imageUrl: avatarUrl, size: 48);
  }

  /// 构建医生信息
  Widget _buildDoctorInfo(BuildContext context) {
    if (selectedDoctor != null) {
      // 显示选中的医生信息
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Flexible(
                child: Text(
                  selectedDoctor!.name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 6),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary,
                      AppColors.primary.withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.smart_toy, size: 10, color: Colors.white),
                    const SizedBox(width: 2),
                    Text(
                      AppLocalizations.of(context).aiAssistant,
                      style: TextStyle(
                        fontSize: 8,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${AppLocalizations.of(context).doctorTitle} • ${selectedDoctor!.specialization}',
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
        ],
      );
    } else {
      // 没有医生信息时显示提示
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).noDoctorInfo,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
        ],
      );
    }
  }
}
