import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../common/utils/app_responsive_sizes.dart';
import '../../../common/utils/rtl_util.dart';
import '../../../utils/toast_util.dart';
import '../../../services/login_check_service.dart';
import '../../../services/audio_recording_service.dart';
import '../../../services/speech_to_text_service.dart';

import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';

import 'health_assistant_voice_recording_overlay.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 健康助手聊天工具栏组件 - 用户与健康助手对话的输入界面
class HealthAssistantChatTools extends StatefulWidget {
  final VoidCallback onVoiceInputChinese;
  final VoidCallback onCameraInput;
  final Function(String) onSendMessage;
  final String? sourceLanguage; // 健康助手不需要源语言，设为可选
  final String? targetLanguage; // 健康助手不需要目标语言，设为可选
  final Function(String, String, String, String?)?
  onVoiceResult; // 语音识别结果回调：原始文本，处理后文本，音频URL，输入语言等

  final VoidCallback? onVoiceStart; // 语音处理开始回调（用于显示"处理中..."状态）
  final VoidCallback? onVoiceEnd; // 语音处理结束回调（用于移除"处理中..."状态）
  final Function(dynamic)? onVoiceError; // 语音处理错误回调
  final HealthAssistantChatToolsController? controller; // 添加控制器参数

  const HealthAssistantChatTools({
    super.key,
    required this.onVoiceInputChinese,
    required this.onCameraInput,
    required this.onSendMessage,
    this.sourceLanguage, // 健康助手不需要源语言，设为可选
    this.targetLanguage, // 健康助手不需要目标语言，设为可选
    this.onVoiceResult,
    this.onVoiceStart,
    this.onVoiceEnd,
    this.onVoiceError,
    this.controller, // 添加控制器参数
  });

  @override
  State<HealthAssistantChatTools> createState() =>
      _HealthAssistantChatToolsState();
}

/// 健康助手聊天工具栏控制器 - 类似于翻译页面的TranslationToolsController
class HealthAssistantChatToolsController {
  _HealthAssistantChatToolsState? _state;

  /// 绑定状态
  void _bindState(_HealthAssistantChatToolsState state) {
    _state = state;
  }

  /// 解绑状态
  void _unbindState() {
    _state = null;
  }

  /// 切换到输入模式
  void switchToInputMode() {
    _state?._switchToInputMode();
  }

  /// 切换到工具栏模式
  void switchToToolbarMode() {
    _state?._switchToToolbarMode();
  }

  /// 检查是否处于输入模式
  bool get isInputMode => _state?._isInputMode ?? false;

  /// 设置输入框文本并切换到输入模式 - 用于编辑功能
  void setTextAndSwitchToInputMode(String text) {
    _state?._setTextAndSwitchToInputMode(text);
  }

  /// 设置选中的照片
  void setSelectedImage(String? imagePath) {
    _state?._setSelectedImage(imagePath);
  }

  /// 获取选中的照片路径
  String? get selectedImagePath => _state?._selectedImagePath;
}

class _HealthAssistantChatToolsState extends State<HealthAssistantChatTools> {
  bool _isInputMode = false; // 是否处于输入模式
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  // 文本方向状态
  TextDirection _inputTextDirection = TextDirection.ltr;
  TextAlign _inputTextAlign = TextAlign.left;

  // 录音相关状态
  final AudioRecordingService _recordingService = AudioRecordingService();
  bool _isRecording = false;
  OverlayEntry? _recordingOverlay;
  String? _currentRecordingLanguage;
  HealthAssistantVoiceRecordingState _recordingState =
      HealthAssistantVoiceRecordingState.recording;

  // 滑动取消相关状态
  Offset _initialPanPosition = Offset.zero; // Pan手势的初始位置
  Timer? _tapTimer; // 用于区分点击和长按的定时器
  bool _isPanRecording = false; // 标记是否通过Pan手势录音

  // 照片预览相关状态
  String? _selectedImagePath; // 选中的照片路径

  @override
  void initState() {
    super.initState();
    // 绑定控制器
    widget.controller?._bindState(this);

    // 监听文本变化，自动调整文本方向
    _textController.addListener(_onTextChanged);
  }

  /// 文本变化监听器 - 根据输入内容自动调整文本方向
  void _onTextChanged() {
    final text = _textController.text;
    final newDirection = RTLUtil.detectTextDirection(text);
    final newAlign = RTLUtil.detectTextAlign(text);

    if (newDirection != _inputTextDirection || newAlign != _inputTextAlign) {
      setState(() {
        _inputTextDirection = newDirection;
        _inputTextAlign = newAlign;
      });
    }
  }

  @override
  void dispose() {
    _textController.removeListener(_onTextChanged);
    _textController.dispose();
    _focusNode.dispose();
    _hideRecordingOverlay();
    _tapTimer?.cancel(); // 清理定时器
    // 解绑控制器
    widget.controller?._unbindState();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    // 录音界面通过 Overlay 显示，因此这里只需要构建工具栏
    return Container(
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1E1E1E) : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, -1),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 照片预览区域
          if (_selectedImagePath != null) _buildImagePreview(),

          // 工具栏区域
          Container(
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
            child: _isInputMode ? _buildInputMode() : _buildToolbarMode(),
          ),
        ],
      ),
    );
  }

  /// 构建工具栏模式 - AI导游专用版本（只有一个语音按钮）
  Widget _buildToolbarMode() {
    // 使用响应式尺寸管理
    final responsiveSizes = AppResponsiveSizes(context);

    // 根据字体档位调整间距
    final sideSpacing = responsiveSizes.spacingM;

    // 强制使用LTR方向，不受RTL影响
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Row(
        children: [
          // 左侧圆形按钮（键盘）
          _buildCircleButton(
            icon: Icons.keyboard_outlined,
            onTap: _switchToInputMode,
            responsiveSizes: responsiveSizes,
          ),

          SizedBox(width: sideSpacing), // 左侧按钮与语音按钮间距
          // 中间区域 - 只显示一个语音按钮（AI导游聊天）
          Expanded(
            child: _buildVoiceButton(
              icon: Icons.mic_outlined,
              onTap: widget.onVoiceInputChinese,
              onLongPress: () => _startVoiceRecording(false), // false表示目标语言
              language: widget.targetLanguage ?? 'Chinese', // AI导游默认使用中文
              isSourceLanguage: false,
            ),
          ),

          SizedBox(width: sideSpacing), // 语音按钮与右侧按钮间距
          // 右侧圆形按钮（相机）
          _buildCircleButton(
            icon: Icons.camera_alt_outlined,
            onTap: widget.onCameraInput,
            responsiveSizes: responsiveSizes,
          ),
        ],
      ),
    );
  }

  /// 构建输入模式（聊天框）
  Widget _buildInputMode() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final responsiveSizes = AppResponsiveSizes(context);

    // 强制使用LTR方向，不受RTL影响
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Row(
        children: [
          // 麦克风按钮 - 与翻译页面保持一致
          _buildCircleButton(
            icon: Icons.mic_outlined,
            onTap: _switchToToolbarMode,
            responsiveSizes: responsiveSizes,
          ),

          const SizedBox(width: 12),

          // 输入框
          Expanded(
            child: Container(
              constraints: BoxConstraints(
                minHeight: responsiveSizes.getScaledSize(48.0),
                maxHeight: responsiveSizes.getScaledSize(120.0),
              ),
              decoration: BoxDecoration(
                color: isDark
                    ? const Color(0xFF2A2A2A)
                    : const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: isDark
                      ? const Color(0xFF404040)
                      : const Color(0xFFE0E0E0),
                  width: 1,
                ),
              ),
              child: TextField(
                controller: _textController,
                focusNode: _focusNode,
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (text) async => await _sendMessage(text),
                // 根据输入内容动态调整文本方向和对齐方式
                textDirection: _inputTextDirection,
                textAlign: _inputTextAlign,
                style: TextStyle(
                  fontSize: responsiveSizes.getScaledSize(16.0),
                  color: ThemeHelper.getTextPrimary(context),
                  // 如果是维吾尔语，使用专用字体
                  fontFamily:
                      RTLUtil.containsUyghurCharacters(_textController.text)
                      ? 'UKIJTor'
                      : null,
                ),
                decoration: InputDecoration(
                  hintText: AppLocalizations.of(context).inputHint,
                  hintStyle: TextStyle(
                    color: ThemeHelper.getTextSecondary(context),
                    fontSize: responsiveSizes.getScaledSize(16.0),
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: responsiveSizes.getScaledSize(16.0),
                    vertical: responsiveSizes.getScaledSize(12.0),
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // 发送按钮 - 与翻译页面样式保持一致
          _buildSendButton(
            onTap: () => _sendMessage(_textController.text),
            responsiveSizes: responsiveSizes,
          ),
        ],
      ),
    );
  }

  /// 构建发送按钮 - 与翻译页面样式保持一致
  Widget _buildSendButton({
    required Future<void> Function() onTap,
    required AppResponsiveSizes responsiveSizes,
  }) {
    final size = responsiveSizes.getScaledSize(48.0);
    final iconSize = responsiveSizes.iconM;

    return GestureDetector(
      onTap: () async => await onTap(),
      child: Container(
        width: size,
        height: size,
        decoration: const BoxDecoration(
          color: Color(0xFF4A80F0), // 与翻译页面相同的蓝色
          shape: BoxShape.circle,
        ),
        child: Icon(Icons.send, color: Colors.white, size: iconSize),
      ),
    );
  }

  /// 构建圆形按钮 - 与翻译页面样式保持一致
  Widget _buildCircleButton({
    required IconData icon,
    required VoidCallback onTap,
    required AppResponsiveSizes responsiveSizes,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final buttonSize = responsiveSizes.getScaledSize(48.0);
    final iconSize = responsiveSizes.iconM;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(25),
      child: Container(
        width: buttonSize,
        height: buttonSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isDark ? const Color(0xFF2A2A2A) : Colors.white,
          border: Border.all(
            color: isDark ? const Color(0xFF404040) : Colors.grey.shade300,
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: isDark ? Colors.grey.shade300 : Colors.grey.shade600,
          size: iconSize,
        ),
      ),
    );
  }

  /// 构建语音输入按钮
  Widget _buildVoiceButton({
    required IconData icon,
    required VoidCallback onTap,
    required VoidCallback onLongPress,
    required String language,
    required bool isSourceLanguage,
  }) {
    // AI导游聊天不需要检查语言支持，直接启用

    // 使用响应式尺寸管理
    final responsiveSizes = AppResponsiveSizes(context);
    final iconSize = responsiveSizes.getScaledSize(20.0);
    final fontSize = responsiveSizes.getScaledSize(14.0);

    // 动态计算水平内边距，确保文字不会被截断
    final horizontalPadding = responsiveSizes.getScaledSize(12.0);

    return Listener(
      onPointerDown: (event) {
        // 记录初始位置用于滑动检测
        _initialPanPosition = event.position;
        _isPanRecording = true;

        // 立即开始录音但延迟很短（100ms），区分点击和长按
        _tapTimer = Timer(const Duration(milliseconds: 100), () {
          if (_isPanRecording) {
            debugPrint('🎤 确认长按，开始录音');
            onLongPress();
          }
        });
      },
      onPointerUp: (event) {
        _isPanRecording = false;
        _tapTimer?.cancel();

        // 如果录音界面存在，说明已开始录音
        if (_recordingOverlay != null) {
          // 检查是否处于取消状态
          if (_recordingState == HealthAssistantVoiceRecordingState.canceling) {
            debugPrint('🎤 取消录音 - 向上滑动取消');
            _cancelRecording();
          } else {
            debugPrint('🎤 结束录音并发送');
            _stopRecording();
          }
        } else {
          // 录音界面不存在，说明是点击而非长按，触发点击事件
          debugPrint('🎤 执行点击事件');
          onTap();
        }
      },
      onPointerMove: (event) {
        if (_recordingOverlay != null && _isPanRecording) {
          // 检查是否滑动超出取消阈值
          final dy = event.position.dy - _initialPanPosition.dy;

          // 如果向上滑动超过100像素
          if (dy < -100) {
            if (_recordingState !=
                HealthAssistantVoiceRecordingState.canceling) {
              _recordingState = HealthAssistantVoiceRecordingState.canceling;
              _showRecordingOverlay();
            }
          } else {
            if (_recordingState !=
                HealthAssistantVoiceRecordingState.recording) {
              _recordingState = HealthAssistantVoiceRecordingState.recording;
              _showRecordingOverlay();
            }
          }
        }
      },
      child: GestureDetector(
        onTap: onTap,
        behavior: HitTestBehavior.opaque,
        child: Container(
          height: responsiveSizes.getScaledSize(48.0), // 与键盘、相机按钮统一高度
          constraints: BoxConstraints(
            minHeight: responsiveSizes.getScaledSize(48.0), // 确保最小高度
          ),
          padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding,
            vertical: 0, // 移除垂直内边距，让文字垂直居中
          ),
          decoration: BoxDecoration(
            // 使用蓝色背景
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(color: AppColors.primary, width: 1),
          ),
          // 强制使用LTR方向，不受RTL影响
          child: Directionality(
            textDirection: TextDirection.ltr,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center, // 强制居中对齐
              crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中
              mainAxisSize: MainAxisSize.min,
              children: [
                // 麦克风图标
                Icon(
                  icon,
                  color: Colors.white.withValues(alpha: 0.85),
                  size: iconSize,
                ),
                SizedBox(width: responsiveSizes.getScaledSize(6.0)),
                // 按住说话文字
                Flexible(
                  child: Text(
                    AppLocalizations.of(context).holdToSpeak,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.85),
                      fontSize: fontSize,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 切换到输入模式
  void _switchToInputMode() {
    setState(() {
      _isInputMode = true;
    });
    // 延迟聚焦，确保界面已经切换完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  /// 切换到工具栏模式
  void _switchToToolbarMode() {
    setState(() {
      _isInputMode = false;
    });
    _focusNode.unfocus();
  }

  /// 设置输入框文本并切换到输入模式 - 用于编辑功能
  void _setTextAndSwitchToInputMode(String text) {
    _textController.text = text;
    // 根据设置的文本更新方向
    final newDirection = RTLUtil.detectTextDirection(text);
    final newAlign = RTLUtil.detectTextAlign(text);
    setState(() {
      _isInputMode = true;
      _inputTextDirection = newDirection;
      _inputTextAlign = newAlign;
    });
    // 延迟聚焦以确保输入框已构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
      // 将光标移动到文本末尾
      _textController.selection = TextSelection.fromPosition(
        TextPosition(offset: _textController.text.length),
      );
    });
  }

  /// 设置选中的照片
  void _setSelectedImage(String? imagePath) {
    setState(() {
      _selectedImagePath = imagePath;
      if (imagePath != null) {
        // 有照片时自动切换到输入模式
        _isInputMode = true;
      }
    });

    // 如果有照片且切换到输入模式，聚焦输入框
    if (imagePath != null && _isInputMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  /// 发送消息
  Future<void> _sendMessage(String message) async {
    final text = message.trim();
    final imagePath = _selectedImagePath;

    // 如果有照片或有文本内容才发送
    if (text.isNotEmpty || imagePath != null) {
      // 检查用户是否已登录
      if (!mounted) return;
      final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
        context,
        featureName: AppLocalizations.of(context).chatFeature,
      );
      if (!isLoggedIn) {
        return;
      }

      // 发送消息（主页面会检查是否有选中的图片）
      widget.onSendMessage(text);

      _textController.clear();
      // 注意：不在这里清除 _selectedImagePath，让主页面来清除
      _switchToToolbarMode();
    }
  }

  /// 开始语音录音
  void _startVoiceRecording(bool isSourceLanguage) async {
    debugPrint('🎤 开始语音录制: isSourceLanguage=$isSourceLanguage');

    // 检查用户是否已登录
    if (!mounted) return;
    final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
      context,
      featureName: AppLocalizations.of(context).voiceFeature,
    );
    if (!isLoggedIn) {
      return;
    }

    // 立即触觉反馈，给用户即时响应
    HapticFeedback.mediumImpact();

    _currentRecordingLanguage = isSourceLanguage
        ? (widget.sourceLanguage ?? 'Chinese')
        : (widget.targetLanguage ?? 'Chinese');

    debugPrint('🎤 录制语言: $_currentRecordingLanguage');

    // 重置录音状态为正常录音状态
    _recordingState = HealthAssistantVoiceRecordingState.recording;

    // 先检查权限状态
    final hasPermission = await _recordingService.checkPermissionStatus();
    if (!hasPermission) {
      // 没有权限，先请求权限
      debugPrint('🎤 没有麦克风权限，请求权限');
      final granted = await _recordingService.requestPermission();
      if (!granted) {
        if (mounted) {
          ToastUtil.show(context, '无法录音：需要麦克风权限才能使用语音功能');
        }
        return;
      }
    }

    // 有权限后显示录音界面并开始录音
    _showRecordingOverlay();
    _attemptRecordingStart();
  }

  /// 显示录音界面
  void _showRecordingOverlay() {
    _hideRecordingOverlay(); // 确保没有重复的overlay

    _recordingOverlay = OverlayEntry(
      builder: (context) => HealthAssistantVoiceRecordingOverlay(
        state: _recordingState,
        getDuration: () => _recordingService.recordingDuration,
        language: _currentRecordingLanguage ?? 'zh',
        getAudioLevel: () => _recordingService.currentAudioLevel,
        onCancel: _cancelRecording,
      ),
    );

    Overlay.of(context).insert(_recordingOverlay!);
  }

  /// 隐藏录音界面
  void _hideRecordingOverlay() {
    _recordingOverlay?.remove();
    _recordingOverlay = null;
  }

  /// 尝试开始录音（权限已检查）
  void _attemptRecordingStart() async {
    try {
      final success = await _recordingService.startRecording(
        _currentRecordingLanguage,
      );
      if (success && mounted) {
        setState(() {
          _isRecording = true;
        });
        debugPrint('🎤 录音启动成功');
      } else if (mounted) {
        _hideRecordingOverlay();
        ToastUtil.show(context, '录音启动失败，请重试');
      }
    } catch (e) {
      debugPrint('🎤 录音启动失败: $e');
      _hideRecordingOverlay();
      if (mounted) {
        ToastUtil.show(context, '录音启动失败，请重试');
      }
    }
  }

  /// 停止录音并处理
  void _stopRecording() async {
    try {
      // 在停止录音前获取时长
      final duration = _recordingService.recordingDuration;
      final audioPath = await _recordingService.stopRecording();

      if (mounted) {
        _hideRecordingOverlay();

        if (audioPath == null) {
          ToastUtil.show(context, '录音失败，请重试');
          setState(() {
            _isRecording = false;
          });
          return;
        }
      } else {
        return;
      }

      // 检查录音时长 - 统一标准
      if (duration < 800) {
        // 少于0.8秒
        _recordingState = HealthAssistantVoiceRecordingState.tooShort;
        _showRecordingOverlay();

        // 2秒后自动关闭提示
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            _hideRecordingOverlay();
          }
        });
        debugPrint('🎤 录音时间太短: ${duration}ms < 800ms');
        setState(() {
          _isRecording = false;
        });
        return;
      }

      setState(() {
        _isRecording = false;
      });

      debugPrint('🎤 录音完成，路径: $audioPath，时长: ${duration}ms');

      // 调用语音转文字服务
      try {
        // 显示处理中提示
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).voiceRecognizing,
          );
        }

        final audioFile = File(audioPath);
        final result = await SpeechToTextService.convertSpeechToText(
          audioFile: audioFile,
        );

        if (mounted) {
          if (result.success && result.text.isNotEmpty) {
            debugPrint('🎤 语音识别成功: ${result.text}');

            // 发送识别的文字到聊天（通过_sendMessage确保登录检查）
            await _sendMessage(result.text);

            if (mounted) {
              ToastUtil.show(
                context,
                AppLocalizations.of(context).voiceRecognitionSuccess,
              );
            }
          } else {
            debugPrint('🎤 语音识别失败: ${result.errorMessage}');
            ToastUtil.show(
              context,
              result.errorMessage ??
                  AppLocalizations.of(context).voiceRecognitionFailed,
            );
          }
        }
      } catch (e) {
        debugPrint('🎤 语音识别异常: $e');
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).voiceRecognitionRetry,
          );
        }
      }
    } catch (e) {
      debugPrint('🎤 录音处理失败: $e');
      if (mounted) {
        _hideRecordingOverlay();
        ToastUtil.show(context, '录音处理失败，请重试');
        setState(() {
          _isRecording = false;
        });
      }
    }
  }

  /// 取消录音
  void _cancelRecording() async {
    debugPrint('🎤 取消录音');
    _hideRecordingOverlay();

    if (_isRecording) {
      try {
        await _recordingService.cancelRecording();
      } catch (e) {
        debugPrint('停止录音失败: $e');
      }
      setState(() {
        _isRecording = false;
      });
    }
  }

  /// 构建照片预览区域
  Widget _buildImagePreview() {
    if (_selectedImagePath == null) return const SizedBox.shrink();

    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? const Color(0xFF1E1E1E) : Colors.white,
        border: Border(
          bottom: BorderSide(
            color: isDark ? const Color(0xFF404040) : const Color(0xFFE0E0E0),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 照片缩略图
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isDark
                    ? const Color(0xFF404040)
                    : const Color(0xFFE0E0E0),
                width: 1,
              ),
            ),
            child: Stack(
              children: [
                // 图片
                ClipRRect(
                  borderRadius: BorderRadius.circular(7),
                  child: Image.file(
                    File(_selectedImagePath!),
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 60,
                        height: 60,
                        color: isDark ? Colors.grey[800] : Colors.grey[200],
                        child: Icon(
                          Icons.broken_image,
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                          size: 24,
                        ),
                      );
                    },
                  ),
                ),
                // 删除按钮
                Positioned(
                  top: -4,
                  right: -4,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedImagePath = null;
                      });
                    },
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 14,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 12),

          // 提示文字
          Expanded(
            child: Text(
              AppLocalizations.of(context).addTextDescriptionOrSendImage,
              style: TextStyle(
                color: ThemeHelper.getTextSecondary(context),
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
