import 'dart:math';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:audioplayers/audioplayers.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/widgets/user_avatar_widget.dart';
import '../../../services/global_audio_manager.dart';
import '../../../services/text_to_speech_service.dart';
import '../../../services/language_service.dart';
import '../../../services/language_detection_service.dart';
import '../../../utils/toast_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 健康助手聊天消息类型
enum HealthAssistantMessageType {
  user, // 用户消息
  assistant, // AI助手回复
  loading, // 加载中
  streaming, // 流式回复中
}

/// 健康助手消息数据模型
class HealthAssistantChatMessage {
  final String text;
  final HealthAssistantMessageType type;
  final DateTime timestamp;
  final String? audioUrl; // 语音文件URL（可选）
  final bool shouldAutoPlay; // 是否应该自动播放音频
  final bool isStreaming; // 是否正在流式接收
  final String? imagePath; // 本地图片路径（可选）
  final String? imageBase64; // Base64图片数据（可选）

  HealthAssistantChatMessage({
    required this.text,
    required this.type,
    required this.timestamp,
    this.audioUrl,
    this.shouldAutoPlay = false,
    this.isStreaming = false,
    this.imagePath,
    this.imageBase64,
  });

  /// 复制并更新部分字段
  HealthAssistantChatMessage copyWith({
    String? text,
    HealthAssistantMessageType? type,
    DateTime? timestamp,
    String? audioUrl,
    bool? shouldAutoPlay,
    bool? isStreaming,
    String? imagePath,
    String? imageBase64,
  }) {
    return HealthAssistantChatMessage(
      text: text ?? this.text,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      audioUrl: audioUrl ?? this.audioUrl,
      shouldAutoPlay: shouldAutoPlay ?? this.shouldAutoPlay,
      isStreaming: isStreaming ?? this.isStreaming,
      imagePath: imagePath ?? this.imagePath,
      imageBase64: imageBase64 ?? this.imageBase64,
    );
  }

  /// 是否包含图片
  bool get hasImage =>
      (imagePath != null && imagePath!.isNotEmpty) ||
      (imageBase64 != null && imageBase64!.isNotEmpty);
}

/// 健康助手聊天气泡组件 - 支持流式响应
class HealthAssistantChatBubble extends StatefulWidget {
  final HealthAssistantChatMessage message;
  final VoidCallback? onEditUserMessage; // 编辑用户消息的回调
  final String? doctorAvatarPath; // 医生头像路径

  const HealthAssistantChatBubble({
    super.key,
    required this.message,
    this.onEditUserMessage,
    this.doctorAvatarPath,
  });

  @override
  State<HealthAssistantChatBubble> createState() =>
      _HealthAssistantChatBubbleState();
}

class _HealthAssistantChatBubbleState extends State<HealthAssistantChatBubble>
    with TickerProviderStateMixin {
  final AudioPlayer _audioPlayer = AudioPlayer();
  late AnimationController _loadingController;
  bool _isPlayingAudio = false;
  late String _audioKey; // 用于全局音频管理的唯一标识

  // 内部渲染优化：缓存上次渲染的文本，避免频繁重建复杂组件
  String? _lastRenderedText;
  Widget? _cachedMarkdownWidget;

  @override
  void initState() {
    super.initState();

    // 生成唯一的音频标识
    _audioKey =
        'ai_chat_${widget.message.hashCode}_${DateTime.now().millisecondsSinceEpoch}';

    // 注册到全局音频管理器
    GlobalAudioManager().registerAudioPlayer(_audioKey, _audioPlayer);

    // 初始化加载动画控制器 - 与翻译页面一致
    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // 如果是流式消息或加载中，启动动画
    if (widget.message.isStreaming ||
        widget.message.type == HealthAssistantMessageType.loading ||
        widget.message.type == HealthAssistantMessageType.streaming) {
      _loadingController.repeat();
    }

    // 只监听播放完成事件
    _audioPlayer.onPlayerComplete.listen((_) {
      if (mounted) {
        setState(() {
          _isPlayingAudio = false;
        });
        GlobalAudioManager().stopPlaying(_audioKey);
      }
    });
  }

  @override
  void didUpdateWidget(HealthAssistantChatBubble oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 检查流式状态变化
    if (oldWidget.message.isStreaming != widget.message.isStreaming ||
        oldWidget.message.type != widget.message.type) {
      if (widget.message.isStreaming ||
          widget.message.type == HealthAssistantMessageType.loading ||
          widget.message.type == HealthAssistantMessageType.streaming) {
        _loadingController.repeat();
      } else {
        _loadingController.stop();
        _loadingController.reset();
      }
    }
  }

  @override
  void dispose() {
    // 停止当前音频播放
    if (_isPlayingAudio) {
      _audioPlayer.stop();
    }

    // 释放音频播放器资源
    _audioPlayer.dispose();

    // 从全局音频管理器注销
    GlobalAudioManager().unregisterAudioPlayer(_audioKey);

    _loadingController.dispose();
    super.dispose();
  }

  /// 播放AI回复的语音 - 使用新的TTS接口
  Future<void> _playAudio() async {
    print('🔊 点击播放按钮，当前播放状态: $_isPlayingAudio');

    // 如果正在播放，则停止播放
    if (_isPlayingAudio) {
      print('🔊 停止当前播放');
      await _stopAudio();
      return;
    }

    print('🔊 开始播放音频');

    // 停止其他音频播放（但不包括当前播放器）
    GlobalAudioManager().stopOtherAudio(_audioKey);

    setState(() {
      _isPlayingAudio = true;
    });

    // 通知全局管理器开始播放
    GlobalAudioManager().startPlaying(_audioKey);

    try {
      // 限制TTS文本长度，避免过长文本导致服务失败
      String ttsText = widget.message.text;
      if (ttsText.length > 500) {
        ttsText = '${ttsText.substring(0, 500)}...';
        print('🔊 文本过长，已截断到500字符');
      }

      print('🔊 调用TTS服务，文本长度: ${ttsText.length}');

      // 自动检测文本语言
      final detectedLanguage = LanguageDetectionService.detectLanguage(ttsText);
      print('🔊 检测到的文本语言: $detectedLanguage');

      // 获取当前界面语言作为备选
      final currentLanguage = LanguageService().getCurrentLanguageCode();

      // 智能选择TTS语言
      final ttsLanguage = LanguageDetectionService.selectTtsLanguage(
        ttsText,
        userPreference: currentLanguage,
      );
      print('🔊 最终选择的TTS语言: $ttsLanguage');

      // 调用TTS服务
      final ttsResult = await TextToSpeechService.convertTextToSpeech(
        text: ttsText,
        language: ttsLanguage,
        speed: 5, // 默认语速
      );

      if (!mounted) return;

      if (ttsResult.success && ttsResult.audioUrl.isNotEmpty) {
        print('🔊 TTS转换成功，音频URL: ${ttsResult.audioUrl}');

        // 播放音频
        await _audioPlayer.play(UrlSource(ttsResult.audioUrl));
        print('🔊 开始播放音频');
      } else {
        // TTS转换失败
        print('🔊 TTS转换失败: ${ttsResult.errorMessage}');
        setState(() {
          _isPlayingAudio = false;
        });
        GlobalAudioManager().stopPlaying(_audioKey);

        // 显示错误提示
        if (mounted) {
          ToastUtil.show(context, ttsResult.errorMessage ?? '语音播放失败');
        }
      }
    } catch (e) {
      print('🔊 播放音频异常: $e');
      if (mounted) {
        setState(() {
          _isPlayingAudio = false;
        });
        GlobalAudioManager().stopPlaying(_audioKey);

        // 显示错误提示
        ToastUtil.show(context, '语音播放失败');
      }
    }
  }

  /// 停止音频播放
  Future<void> _stopAudio() async {
    try {
      print('🔊 正在停止音频播放');

      // 停止播放器
      await _audioPlayer.stop();

      if (mounted) {
        setState(() {
          _isPlayingAudio = false;
        });
      }
      GlobalAudioManager().stopPlaying(_audioKey);
      print('🔊 音频播放已停止');
    } catch (e) {
      print('停止音频播放失败: $e');
      if (mounted) {
        setState(() {
          _isPlayingAudio = false;
        });
      }
      GlobalAudioManager().stopPlaying(_audioKey);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isUser = widget.message.type == HealthAssistantMessageType.user;
    final isLoading = widget.message.type == HealthAssistantMessageType.loading;
    final isStreaming =
        widget.message.type == HealthAssistantMessageType.streaming ||
        widget.message.isStreaming;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
      // 强制使用LTR方向，不受RTL影响
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Row(
          mainAxisAlignment: isUser
              ? MainAxisAlignment.end
              : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!isUser) ...[
              // AI助手头像
              _buildAvatar(isUser: false, isDarkMode: isDarkMode),
              const SizedBox(width: 12),
            ],

            // 用户消息的编辑图标
            if (isUser && widget.onEditUserMessage != null) ...[
              GestureDetector(
                onTap: widget.onEditUserMessage,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    Icons.edit,
                    size: 18,
                    color: isDarkMode
                        ? Colors.grey.shade400
                        : Colors.grey.shade600,
                  ),
                ),
              ),
              const SizedBox(width: 2),
            ],

            // 消息气泡
            Flexible(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: _getBubbleColor(isUser, isDarkMode),
                    borderRadius: _getBubbleBorderRadius(isUser),
                    boxShadow: _getBubbleShadow(isDarkMode),
                  ),
                  child: Column(
                    crossAxisAlignment: isUser
                        ? CrossAxisAlignment.end
                        : CrossAxisAlignment.start,
                    children: [
                      // 图片显示（如果有）
                      if (widget.message.hasImage) ...[
                        _buildImageContent(isUser, isDarkMode),
                        if (widget.message.text.isNotEmpty ||
                            isLoading ||
                            isStreaming)
                          const SizedBox(height: 8),
                      ],

                      // 文本内容（如果有）
                      if (widget.message.text.isNotEmpty ||
                          isLoading ||
                          isStreaming)
                        isLoading
                            ? _buildLoadingContent()
                            : isStreaming
                            ? _buildStreamingContent(isUser, isDarkMode)
                            : _buildMessageContent(isUser, isDarkMode),
                    ],
                  ),
                ),
              ),
            ),

            // AI消息的语音播放按钮（放在气泡右下角）
            if (!isUser && _shouldShowAudioButton()) ...[
              const SizedBox(width: 4),
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 12), // 与气泡底部对齐
                  child: _buildAudioIcon(isUser, isDarkMode),
                ),
              ),
            ],

            if (isUser) ...[
              const SizedBox(width: 12),
              // 用户头像
              _buildAvatar(isUser: true, isDarkMode: isDarkMode),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建头像 - AI导游使用专用头像
  Widget _buildAvatar({required bool isUser, required bool isDarkMode}) {
    if (isUser) {
      // 用户头像：使用小尺寸用户头像组件
      return const UserAvatarWidget(size: 36);
    } else {
      // AI导游头像：使用专用的AI导游头像图片
      return Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.black.withValues(alpha: 0.3)
                  : Colors.black.withValues(alpha: 0.08),
              blurRadius: isDarkMode ? 4.0 : 8.0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipOval(child: _buildDoctorAvatarImage()),
      );
    }
  }

  /// 构建医生头像图片
  Widget _buildDoctorAvatarImage() {
    final avatarPath = widget.doctorAvatarPath;

    // 如果没有头像路径，显示默认头像
    if (avatarPath == null || avatarPath.isEmpty) {
      return _buildDefaultDoctorAvatar();
    }

    // 判断是网络图片还是本地资源
    if (avatarPath.startsWith('http://') || avatarPath.startsWith('https://')) {
      // 网络图片
      return Image.network(
        avatarPath,
        width: 36,
        height: 36,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              shape: BoxShape.circle,
            ),
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                    : null,
                strokeWidth: 2,
                color: Colors.white,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultDoctorAvatar();
        },
      );
    } else {
      // 本地资源图片
      return Image.asset(
        avatarPath,
        width: 36,
        height: 36,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultDoctorAvatar();
        },
      );
    }
  }

  /// 构建默认医生头像
  Widget _buildDefaultDoctorAvatar() {
    return Container(
      width: 36,
      height: 36,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF10B981), Color(0xFF059669)],
        ),
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.smart_toy_rounded, color: Colors.white, size: 18),
    );
  }

  /// 获取气泡颜色 - 使用健康助手绿色主题
  Color _getBubbleColor(bool isUser, bool isDarkMode) {
    if (isUser) {
      // 用户消息气泡 - 使用绿色主题色
      return isDarkMode
          ? const Color(0xFF0D7A47) // 更深的绿色
          : const Color(0xFF109D58); // 健康助手主题绿色
    } else {
      // AI回复气泡 - 使用精致的背景色
      return isDarkMode
          ? const Color(0xFF1F2937) // 深灰蓝
          : const Color(0xFFF8FAFC); // 浅灰白
    }
  }

  /// 获取气泡圆角 - 与翻译页面一致的现代圆角设计
  BorderRadius _getBubbleBorderRadius(bool isUser) {
    return BorderRadius.only(
      topLeft: const Radius.circular(20),
      topRight: const Radius.circular(20),
      bottomLeft: Radius.circular(isUser ? 20 : 6),
      bottomRight: Radius.circular(isUser ? 6 : 20),
    );
  }

  /// 获取气泡阴影 - 与翻译页面一致的精致阴影
  List<BoxShadow> _getBubbleShadow(bool isDarkMode) {
    return [
      BoxShadow(
        color: isDarkMode
            ? Colors.black.withValues(alpha: 0.3)
            : Colors.black.withValues(alpha: 0.08),
        blurRadius: isDarkMode ? 4.0 : 8.0,
        offset: const Offset(0, 2),
      ),
    ];
  }

  /// 构建加载内容 - 与翻译页面一致的优雅跳动点动画
  Widget _buildLoadingContent() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 三个优雅的跳动点
        AnimatedBuilder(
          animation: _loadingController,
          builder: (context, child) {
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(3, (index) {
                final delay = index * 0.3;
                final animationValue = (_loadingController.value + delay) % 1.0;
                final scale =
                    0.6 + (sin(animationValue * pi * 2) + 1) / 2 * 0.4;

                return Container(
                  margin: EdgeInsets.only(right: index < 2 ? 6 : 0),
                  child: Transform.scale(
                    scale: scale,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: ThemeHelper.getTextSecondary(
                          context,
                        ).withValues(alpha: 0.6),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                );
              }),
            );
          },
        ),
        const SizedBox(width: 12),
        Text(
          AppLocalizations.of(context).replying,
          style: TextStyle(
            color: ThemeHelper.getTextSecondary(context),
            fontSize: 14,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  /// 构建流式回复内容 - 显示文本内容和"回复中"动画
  Widget _buildStreamingContent(bool isUser, bool isDarkMode) {
    // 如果没有文本内容，显示"回复中"动画
    if (widget.message.text.isEmpty) {
      return _buildLoadingContent();
    }

    // 有文本内容时，直接显示文本内容（不再显示光标）
    if (isUser) {
      // 用户消息：使用普通文本，针对维吾尔语优化
      final containsUyghur = _containsUyghurText(widget.message.text);
      return SelectableText(
        widget.message.text,
        style: TextStyle(
          color: Colors.white,
          fontSize: containsUyghur ? 16 : 15,
          height: containsUyghur ? 1.8 : 1.6,
          fontWeight: FontWeight.w400,
          letterSpacing: containsUyghur ? 0.5 : 0.2,
          fontFamily: containsUyghur ? 'UKIJTor' : null,
        ),
      );
    } else {
      // AI回复：使用Markdown渲染，支持维吾尔语RTL方向
      return _buildMarkdownContent(widget.message.text, isDarkMode);
    }
  }

  /// 检测文本是否包含维吾尔语字符
  bool _containsUyghurText(String text) {
    // 维吾尔语Unicode范围：U+0600-U+06FF (阿拉伯文字块)
    // 维吾尔语扩展范围：U+FB50-U+FDFF, U+FE70-U+FEFF
    final uyghurRegex = RegExp(r'[\u0600-\u06FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return uyghurRegex.hasMatch(text);
  }

  /// 获取Markdown样式表
  MarkdownStyleSheet _getMarkdownStyleSheet(bool isDarkMode) {
    // 检测消息文本是否包含维吾尔语
    final containsUyghur = _containsUyghurText(widget.message.text);

    return MarkdownStyleSheet(
      // 基础文本样式 - 针对维吾尔语优化
      p: TextStyle(
        color: ThemeHelper.getTextPrimary(context),
        fontSize: containsUyghur ? 16 : 15, // 维吾尔语字体稍大
        height: containsUyghur ? 1.8 : 1.6, // 维吾尔语行高稍大
        fontWeight: FontWeight.w400,
        letterSpacing: containsUyghur ? 0.5 : 0.2, // 维吾尔语字符间距更大
        fontFamily: containsUyghur ? 'UKIJTor' : null, // 维吾尔语使用专用字体
      ),
      // 标题样式 - 针对维吾尔语优化
      h1: TextStyle(
        color: ThemeHelper.getTextPrimary(context),
        fontSize: containsUyghur ? 21 : 20,
        fontWeight: FontWeight.bold,
        height: containsUyghur ? 1.6 : 1.4,
        letterSpacing: containsUyghur ? 0.5 : 0,
        fontFamily: containsUyghur ? 'UKIJTor' : null,
      ),
      h2: TextStyle(
        color: ThemeHelper.getTextPrimary(context),
        fontSize: containsUyghur ? 19 : 18,
        fontWeight: FontWeight.bold,
        height: containsUyghur ? 1.6 : 1.4,
        letterSpacing: containsUyghur ? 0.5 : 0,
        fontFamily: containsUyghur ? 'UKIJTor' : null,
      ),
      h3: TextStyle(
        color: ThemeHelper.getTextPrimary(context),
        fontSize: containsUyghur ? 17 : 16,
        fontWeight: FontWeight.bold,
        height: containsUyghur ? 1.6 : 1.4,
        letterSpacing: containsUyghur ? 0.5 : 0,
        fontFamily: containsUyghur ? 'UKIJTor' : null,
      ),
      // 列表样式 - 针对维吾尔语优化
      listBullet: TextStyle(
        color: ThemeHelper.getTextPrimary(context),
        fontSize: containsUyghur ? 16 : 15,
        height: containsUyghur ? 1.8 : 1.6,
        letterSpacing: containsUyghur ? 0.5 : 0,
        fontFamily: containsUyghur ? 'UKIJTor' : null,
      ),
      // 代码样式
      code: TextStyle(
        color: isDarkMode ? Colors.green.shade300 : Colors.green.shade700,
        fontSize: 14,
        fontFamily: 'monospace',
        backgroundColor: isDarkMode
            ? Colors.grey.shade800.withValues(alpha: 0.3)
            : Colors.grey.shade200.withValues(alpha: 0.5),
      ),
      codeblockDecoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withValues(alpha: 0.3)
            : Colors.grey.shade200.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      // 强调样式 - 针对维吾尔语优化
      strong: TextStyle(
        color: ThemeHelper.getTextPrimary(context),
        fontWeight: FontWeight.bold,
        letterSpacing: containsUyghur ? 0.5 : 0,
        fontFamily: containsUyghur ? 'UKIJTor' : null,
      ),
      em: TextStyle(
        color: ThemeHelper.getTextPrimary(context),
        fontStyle: FontStyle.italic,
        letterSpacing: containsUyghur ? 0.5 : 0,
        fontFamily: containsUyghur ? 'UKIJTor' : null,
      ),
      // 链接样式
      a: TextStyle(
        color: isDarkMode ? Colors.blue.shade300 : Colors.blue.shade700,
        decoration: TextDecoration.underline,
      ),
      // 引用样式 - 针对维吾尔语优化
      blockquote: TextStyle(
        color: ThemeHelper.getTextSecondary(context),
        fontSize: containsUyghur ? 16 : 15,
        fontStyle: FontStyle.italic,
        height: containsUyghur ? 1.8 : 1.6,
        letterSpacing: containsUyghur ? 0.5 : 0,
        fontFamily: containsUyghur ? 'UKIJTor' : null,
      ),
      blockquoteDecoration: BoxDecoration(
        border: Border(
          left: BorderSide(
            color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade400,
            width: 4,
          ),
        ),
      ),
    );
  }

  /// 检查是否应该显示语音播放按钮
  bool _shouldShowAudioButton() {
    // 只有AI回复且有文本内容时才显示播放按钮
    return widget.message.type == HealthAssistantMessageType.assistant &&
        widget.message.text.trim().isNotEmpty &&
        !widget.message.isStreaming;
  }

  /// 构建语音播放图标 - 增大点击区域
  Widget _buildAudioIcon(bool isUser, bool isDarkMode) {
    return GestureDetector(
      onTap: _playAudio,
      child: Container(
        // 增大点击区域
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: _isPlayingAudio
              ? (isDarkMode ? Colors.blue.shade700 : Colors.blue.shade100)
              : (isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(20),
          // 添加轻微阴影提升视觉效果
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: RotatedBox(
          quarterTurns: 1, // 将Wi-Fi图标旋转90度使其横向
          child: Icon(
            _isPlayingAudio ? Icons.wifi : Icons.wifi_outlined,
            size: 18, // 稍微增大图标
            color: _getAudioIconColor(isUser, isDarkMode),
          ),
        ),
      ),
    );
  }

  /// 获取语音播放图标颜色
  Color _getAudioIconColor(bool isUser, bool isDarkMode) {
    if (_isPlayingAudio) {
      return isDarkMode ? Colors.blue.shade300 : Colors.blue.shade600;
    }
    return isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600;
  }

  /// 构建图片内容 - 支持本地文件和Base64图片
  Widget _buildImageContent(bool isUser, bool isDarkMode) {
    if (!widget.message.hasImage) return const SizedBox.shrink();

    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isUser
              ? Colors.white.withValues(alpha: 0.3)
              : (isDarkMode
                    ? const Color(0xFF404040)
                    : const Color(0xFFE0E0E0)),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(7),
        child: GestureDetector(
          onTap: () => _showFullScreenImage(),
          child: _buildImageWidget(isUser, isDarkMode),
        ),
      ),
    );
  }

  /// 构建图片组件 - 根据图片类型选择合适的显示方式
  Widget _buildImageWidget(bool isUser, bool isDarkMode) {
    // 优先使用Base64图片（来自聊天历史）
    if (widget.message.imageBase64 != null &&
        widget.message.imageBase64!.isNotEmpty) {
      return _buildBase64Image(isUser, isDarkMode);
    }

    // 其次使用本地文件图片（来自当前会话）
    if (widget.message.imagePath != null &&
        widget.message.imagePath!.isNotEmpty) {
      return _buildLocalFileImage(isUser, isDarkMode);
    }

    // 如果都没有，显示错误占位符
    return _buildImageErrorPlaceholder(isUser, isDarkMode);
  }

  /// 构建Base64图片
  Widget _buildBase64Image(bool isUser, bool isDarkMode) {
    try {
      // 解析Base64数据
      String base64Data = widget.message.imageBase64!;

      // 如果包含data URL前缀，提取纯Base64数据
      if (base64Data.startsWith('data:image/')) {
        final commaIndex = base64Data.indexOf(',');
        if (commaIndex != -1) {
          base64Data = base64Data.substring(commaIndex + 1);
        }
      }

      return Image.memory(
        Uri.parse('data:image/jpeg;base64,$base64Data').data!.contentAsBytes(),
        width: 120,
        height: 120,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          print('❌ Base64图片解析失败: $error');
          return _buildImageErrorPlaceholder(isUser, isDarkMode);
        },
      );
    } catch (e) {
      print('❌ Base64图片处理异常: $e');
      return _buildImageErrorPlaceholder(isUser, isDarkMode);
    }
  }

  /// 构建本地文件图片
  Widget _buildLocalFileImage(bool isUser, bool isDarkMode) {
    return Image.file(
      File(widget.message.imagePath!),
      width: 120,
      height: 120,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        print('❌ 本地图片加载失败: $error');
        return _buildImageErrorPlaceholder(isUser, isDarkMode);
      },
    );
  }

  /// 构建图片错误占位符
  Widget _buildImageErrorPlaceholder(bool isUser, bool isDarkMode) {
    return Container(
      width: 120,
      height: 120,
      color: isUser
          ? Colors.white.withValues(alpha: 0.2)
          : (isDarkMode ? Colors.grey[700] : Colors.grey[100]),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image,
            size: 24,
            color: isUser
                ? Colors.white.withValues(alpha: 0.7)
                : (isDarkMode ? Colors.grey[400] : Colors.grey[600]),
          ),
          const SizedBox(height: 4),
          Text(
            AppLocalizations.of(context).imageLoadFailed,
            style: TextStyle(
              color: isUser
                  ? Colors.white.withValues(alpha: 0.7)
                  : (isDarkMode ? Colors.grey[400] : Colors.grey[600]),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示全屏图片
  void _showFullScreenImage() {
    if (!widget.message.hasImage) return;

    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(20),
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(child: _buildFullScreenImageWidget()),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close, color: Colors.white, size: 30),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建全屏图片组件
  Widget _buildFullScreenImageWidget() {
    // 优先使用Base64图片（来自聊天历史）
    if (widget.message.imageBase64 != null &&
        widget.message.imageBase64!.isNotEmpty) {
      try {
        String base64Data = widget.message.imageBase64!;

        // 如果包含data URL前缀，提取纯Base64数据
        if (base64Data.startsWith('data:image/')) {
          final commaIndex = base64Data.indexOf(',');
          if (commaIndex != -1) {
            base64Data = base64Data.substring(commaIndex + 1);
          }
        }

        return Image.memory(
          Uri.parse(
            'data:image/jpeg;base64,$base64Data',
          ).data!.contentAsBytes(),
          fit: BoxFit.contain,
        );
      } catch (e) {
        print('❌ 全屏Base64图片处理异常: $e');
        return Center(
          child: Text(
            AppLocalizations.of(context).imageLoadFailed,
            style: const TextStyle(color: Colors.white, fontSize: 16),
          ),
        );
      }
    }

    // 其次使用本地文件图片（来自当前会话）
    if (widget.message.imagePath != null &&
        widget.message.imagePath!.isNotEmpty) {
      return Image.file(File(widget.message.imagePath!), fit: BoxFit.contain);
    }

    // 如果都没有，显示错误信息
    return Center(
      child: Text(
        AppLocalizations.of(context).imageNotAvailable,
        style: const TextStyle(color: Colors.white, fontSize: 16),
      ),
    );
  }

  /// 构建消息内容 - AI回复使用Markdown，用户消息使用普通文本
  Widget _buildMessageContent(bool isUser, bool isDarkMode) {
    final text = widget.message.text;

    if (isUser) {
      // 用户消息使用普通文本，针对维吾尔语优化
      final containsUyghur = _containsUyghurText(text);
      return SelectableText(
        text,
        style: TextStyle(
          color: Colors.white,
          fontSize: containsUyghur ? 16 : 15,
          height: containsUyghur ? 1.8 : 1.6,
          fontWeight: FontWeight.w400,
          letterSpacing: containsUyghur ? 0.5 : 0.2,
          fontFamily: containsUyghur ? 'UKIJTor' : null,
        ),
      );
    } else {
      // AI回复使用Markdown，支持维吾尔语RTL方向
      return _buildMarkdownContent(text, isDarkMode);
    }
  }

  /// 构建Markdown内容 - 专门用于AI回复（带缓存优化）
  Widget _buildMarkdownContent(String text, bool isDarkMode) {
    // 如果文本没有变化且有缓存，直接返回缓存的组件
    if (text == _lastRenderedText && _cachedMarkdownWidget != null) {
      return _cachedMarkdownWidget!;
    }

    // 检测是否包含维吾尔语文本
    final containsUyghur = _containsUyghurText(text);

    // 文本发生变化，重新构建并缓存
    _lastRenderedText = text;

    Widget markdownWidget = MarkdownBody(
      data: text,
      selectable: true,
      styleSheet: _getMarkdownStyleSheet(isDarkMode),
    );

    // 如果包含维吾尔语，使用RTL文本方向包装
    if (containsUyghur) {
      markdownWidget = Directionality(
        textDirection: TextDirection.rtl,
        child: markdownWidget,
      );
    }

    _cachedMarkdownWidget = markdownWidget;
    return _cachedMarkdownWidget!;
  }
}
