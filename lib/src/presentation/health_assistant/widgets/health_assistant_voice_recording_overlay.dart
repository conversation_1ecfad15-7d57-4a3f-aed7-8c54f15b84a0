import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 健康助手语音录制状态
enum HealthAssistantVoiceRecordingState {
  recording, // 正在录音
  canceling, // 取消状态（手指向上滑动）
  tooShort, // 录音时间太短
}

/// 健康助手语音录制悬浮界面 - 类似 Telegram 的录音效果
class HealthAssistantVoiceRecordingOverlay extends StatefulWidget {
  final HealthAssistantVoiceRecordingState state;
  final Function() getDuration; // 获取实时录音时长
  final String language; // 录音语言
  final Function()? getAudioLevel; // 获取实时音量等级
  final VoidCallback? onCancel; // 取消录音回调

  const HealthAssistantVoiceRecordingOverlay({
    super.key,
    required this.state,
    required this.getDuration,
    required this.language,
    this.getAudioLevel,
    this.onCancel,
  });

  @override
  State<HealthAssistantVoiceRecordingOverlay> createState() =>
      _HealthAssistantVoiceRecordingOverlayState();
}

class _HealthAssistantVoiceRecordingOverlayState
    extends State<HealthAssistantVoiceRecordingOverlay>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late AnimationController _audioWaveController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _slideAnimation;
  Timer? _updateTimer;
  double _smoothedAudioLevel = 0.0; // 平滑过渡的音量值
  double _rawAudioLevel = 0.0; // 原始音量值

  @override
  void initState() {
    super.initState();

    // 脉冲动画控制器 - 麦克风图标的呼吸效果
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.08).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // 滑动动画控制器 - 取消状态的滑动效果
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    // 音频波浪动画控制器
    _audioWaveController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    // 立即开始脉冲动画，提供即时视觉反馈
    _pulseController.repeat(reverse: true);

    // 优化计时器，使用更快的更新频率，确保流畅的反馈
    _updateTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (mounted) {
        // 从音频服务获取实时音量
        final newLevel = widget.getAudioLevel?.call() ?? 0.0;
        _rawAudioLevel = newLevel;

        // 应用双层平滑算法：快速响应峰值，平滑处理低值
        final smoothingFactor = newLevel > _smoothedAudioLevel
            ? 0.7
            : 0.3; // 峰值快速响应，下降缓慢
        _smoothedAudioLevel =
            _smoothedAudioLevel * (1 - smoothingFactor) +
            newLevel * smoothingFactor;

        // 确保音量数据在有效范围内
        _smoothedAudioLevel = _smoothedAudioLevel.clamp(0.0, 1.0);

        // 打印调试信息（每500ms一次）
        if (DateTime.now().millisecondsSinceEpoch % 500 < 50) {
          debugPrint(
            '🎤 健康助手录音界面音量 - 原始: ${_rawAudioLevel.toStringAsFixed(3)}, 平滑: ${_smoothedAudioLevel.toStringAsFixed(3)}',
          );
        }

        setState(() {
          // 触发重绘，确保UI更新流畅
        });
      }
    });

    // 使用postFrameCallback确保界面立即渲染
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // 界面首次渲染完成，确保所有动画已就绪
        debugPrint('🎤 健康助手录音界面渲染完成，开始监控');
      }
    });
  }

  @override
  void didUpdateWidget(HealthAssistantVoiceRecordingOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.state != oldWidget.state) {
      if (widget.state == HealthAssistantVoiceRecordingState.canceling) {
        _slideController.forward();
      } else {
        _slideController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    _pulseController.dispose();
    _slideController.dispose();
    _audioWaveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;
    final bottomPadding = mediaQuery.padding.bottom;

    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onTap: () {
          // 点击录音界面任意位置取消录音
          widget.onCancel?.call();
        },
        child: Container(
          width: screenWidth,
          height: screenHeight,
          // Telegram 风格的背景暗化
          color: Colors.black.withValues(alpha: 0.4),
          child: Stack(
            children: [
              // 录音界面 - 位置调整：避免覆盖底部导航，覆盖输入辅助按钮
              Positioned(
                left: 0,
                right: 0,
                bottom: bottomPadding + 80, // 为底部导航留出空间
                child: _buildRecordingInterface(isDark, screenWidth),
              ),

              // 向上滑动取消提示（界面上方）
              if (widget.state == HealthAssistantVoiceRecordingState.canceling)
                Positioned(
                  top: screenHeight * 0.3,
                  left: 0,
                  right: 0,
                  child: _buildUpSlideHint(isDark),
                ),

              // 录音时间太短提示（居中）
              if (widget.state == HealthAssistantVoiceRecordingState.tooShort)
                Center(child: _buildTooShortHint(isDark)),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建主要录音界面
  Widget _buildRecordingInterface(bool isDark, double screenWidth) {
    return Container(
      height: 140,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      decoration: BoxDecoration(
        color: _getBackgroundColor(isDark),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 15,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: widget.state == HealthAssistantVoiceRecordingState.tooShort
          ? const SizedBox.shrink()
          : Column(
              children: [
                // 上方：录音信息和时间
                Row(
                  children: [
                    // 麦克风图标
                    _buildMicrophoneIcon(isDark),

                    const SizedBox(width: 16),

                    // 录音信息
                    Expanded(child: _buildRecordingInfo(isDark)),

                    // 向上滑动提示
                    _buildUpSlideIndicator(isDark),
                  ],
                ),

                const SizedBox(height: 16),

                // 下方：音量波浪动画
                Expanded(child: _buildAudioWaveform(isDark)),
              ],
            ),
    );
  }

  /// 构建麦克风图标
  Widget _buildMicrophoneIcon(bool isDark) {
    return AnimatedBuilder(
      animation: Listenable.merge([_pulseAnimation, _slideAnimation]),
      builder: (context, child) {
        final isCanceling =
            widget.state == HealthAssistantVoiceRecordingState.canceling;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.identity()
            ..scale(
              isCanceling
                  ? 1.0 - _slideAnimation.value * 0.15
                  : _pulseAnimation.value,
            )
            ..translate(0.0, isCanceling ? -_slideAnimation.value * 15 : 0.0),
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isCanceling ? Colors.red : _getPrimaryColor(),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: (isCanceling ? Colors.red : _getPrimaryColor())
                      .withValues(alpha: 0.3),
                  blurRadius: 10,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Icon(
              isCanceling ? Icons.delete_outline : Icons.mic,
              color: Colors.white,
              size: 24,
            ),
          ),
        );
      },
    );
  }

  /// 构建录音信息
  Widget _buildRecordingInfo(bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 录音时长
        Text(
          _formatDuration(widget.getDuration()),
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: _getTextPrimaryColor(isDark),
          ),
        ),

        const SizedBox(height: 2),

        // 状态指示
        Row(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    widget.state == HealthAssistantVoiceRecordingState.canceling
                    ? Colors.red
                    : _getPrimaryColor(),
              ),
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                widget.state == HealthAssistantVoiceRecordingState.canceling
                    ? AppLocalizations.of(context).releaseToCancel
                    : AppLocalizations.of(context).recording,
                style: TextStyle(
                  fontSize: 14,
                  color: _getTextSecondaryColor(isDark),
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建向上滑动指示器
  Widget _buildUpSlideIndicator(bool isDark) {
    return AnimatedOpacity(
      opacity: widget.state == HealthAssistantVoiceRecordingState.canceling
          ? 0.0
          : 1.0,
      duration: const Duration(milliseconds: 200),
      child: SizedBox(
        width: 80, // 固定宽度避免布局冲突
        child: Column(
          children: [
            Icon(
              Icons.keyboard_arrow_up,
              color: _getTextSecondaryColor(isDark),
              size: 20,
            ),
            Text(
              AppLocalizations.of(context).slideUpToCancel,
              style: TextStyle(
                fontSize: 11,
                color: _getTextSecondaryColor(isDark),
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建音频波形动画
  Widget _buildAudioWaveform(bool isDark) {
    return CustomPaint(
      size: const Size(double.infinity, 40),
      painter: HealthAssistantAudioWaveformPainter(
        audioLevel: _smoothedAudioLevel,
        waveColor: widget.state == HealthAssistantVoiceRecordingState.canceling
            ? Colors.red.withValues(alpha: 0.6)
            : _getPrimaryColor().withValues(alpha: 0.8),
        backgroundColor: _getBackgroundColor(isDark),
        isDark: isDark,
      ),
    );
  }

  /// 构建向上滑动取消提示
  Widget _buildUpSlideHint(bool isDark) {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 300), // 限制最大宽度
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(25),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.keyboard_arrow_up, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                AppLocalizations.of(context).continueSlideUpToCancel,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建录音时间太短提示
  Widget _buildTooShortHint(bool isDark) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      margin: const EdgeInsets.symmetric(horizontal: 40),
      decoration: BoxDecoration(
        color: _getBackgroundColor(isDark),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.access_time, color: Colors.orange, size: 24),
          const SizedBox(width: 12),
          Text(
            AppLocalizations.of(context).recordingTooShort,
            style: TextStyle(
              color: _getTextPrimaryColor(isDark),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取背景颜色
  Color _getBackgroundColor(bool isDark) {
    return isDark ? const Color(0xFF1E1E1E) : Colors.white;
  }

  /// 获取主色调
  Color _getPrimaryColor() {
    return AppColors.primary;
  }

  /// 获取主要文本颜色
  Color _getTextPrimaryColor(bool isDark) {
    return isDark ? Colors.white : Colors.black87;
  }

  /// 获取次要文本颜色
  Color _getTextSecondaryColor(bool isDark) {
    return isDark ? Colors.grey.shade400 : Colors.grey.shade600;
  }

  /// 格式化录音时长
  String _formatDuration(int milliseconds) {
    final seconds = (milliseconds / 1000).round();
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      return '0:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }
}

/// 健康助手音频波形绘制器
class HealthAssistantAudioWaveformPainter extends CustomPainter {
  final double audioLevel;
  final Color waveColor;
  final Color backgroundColor;
  final bool isDark;

  HealthAssistantAudioWaveformPainter({
    required this.audioLevel,
    required this.waveColor,
    required this.backgroundColor,
    required this.isDark,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = waveColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final centerY = size.height / 2;
    final waveCount = 30;
    final waveSpacing = size.width / waveCount;

    // 调整音量阈值 - 更低的静音阈值，实现更平滑的过渡
    // 低于 0.014 几乎静音，只显示基线
    if (audioLevel < 0.014) {
      // 绘制静止基线，透明度根据接近阈值的程度调整
      final proximityToThreshold = (audioLevel / 0.014).clamp(0.0, 1.0);
      final baseLinePaint = Paint()
        ..color = waveColor.withValues(
          alpha: 0.15 + proximityToThreshold * 0.15,
        )
        ..strokeWidth = 1.0;

      canvas.drawLine(
        Offset(0, centerY),
        Offset(size.width, centerY),
        baseLinePaint,
      );
      return;
    }

    // 重新设计音量到振幅的映射关系 - 更平滑的渐变
    // 0.014-0.08: 微弱波浪 (高度: 0-15%) - 渐入期
    // 0.08-0.25: 小波浪 (高度: 15-35%)
    // 0.25-0.55: 中波浪 (高度: 35-65%)
    // 0.55-1.0: 大波浪 (高度: 65-100%)

    double amplitudeScale;
    if (audioLevel <= 0.08) {
      // 微弱音量: 从0.014开始的平滑映射到 0-0.15
      amplitudeScale = ((audioLevel - 0.014) / (0.08 - 0.014)) * 0.15;
    } else if (audioLevel <= 0.25) {
      // 小音量: 映射到 0.15-0.35
      amplitudeScale = 0.15 + (audioLevel - 0.08) / (0.25 - 0.08) * 0.2;
    } else if (audioLevel <= 0.55) {
      // 中音量: 映射到 0.35-0.65
      amplitudeScale = 0.35 + (audioLevel - 0.25) / (0.55 - 0.25) * 0.3;
    } else {
      // 大音量: 映射到 0.65-1.0
      amplitudeScale = 0.65 + (audioLevel - 0.55) / (1.0 - 0.55) * 0.35;
    }

    // 确保振幅比例在合理范围内
    amplitudeScale = amplitudeScale.clamp(0.0, 1.0);

    // 绘制基于实际音量的动态波形
    final activePath = Path();
    bool firstPoint = true;

    // 使用时间偏移创建波浪动画 - 调整动画速度
    final timeOffset = DateTime.now().millisecondsSinceEpoch * 0.004;

    for (int i = 0; i <= waveCount; i++) {
      final x = i * waveSpacing;

      // 基于调整后的振幅比例计算实际振幅
      final baseWave = math.sin(i * 0.5 + timeOffset);
      final volumeWave = math.sin(i * 0.9 + timeOffset * 1.2);

      // 主要基于调整后的振幅比例，增加更自然的波动
      final amplitude =
          amplitudeScale *
          size.height *
          0.35 *
          (0.85 + 0.15 * baseWave) *
          (0.9 + 0.1 * volumeWave);

      final y = centerY + amplitude * math.sin(i * 0.35 + timeOffset);

      if (firstPoint) {
        activePath.moveTo(x, y);
        firstPoint = false;
      } else {
        activePath.lineTo(x, y);
      }
    }

    canvas.drawPath(activePath, paint);

    // 绘制中心线（音量越高越淡），加入渐变效果
    final centerLineAlpha = math.max(0.08, 0.4 - amplitudeScale * 0.35);
    final centerLinePaint = Paint()
      ..color = waveColor.withValues(alpha: centerLineAlpha)
      ..strokeWidth = 1.0;

    canvas.drawLine(
      Offset(0, centerY),
      Offset(size.width, centerY),
      centerLinePaint,
    );

    // 当音量较高时（> 0.3），添加额外的波形层，使用更平滑的阈值
    if (amplitudeScale > 0.3) {
      final layerAlpha = ((amplitudeScale - 0.3) / 0.7).clamp(0.0, 1.0) * 0.25;
      final secondLayerPaint = Paint()
        ..color = waveColor.withValues(alpha: layerAlpha)
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke;

      final secondPath = Path();
      firstPoint = true;

      for (int i = 0; i <= waveCount; i++) {
        final x = i * waveSpacing;
        final secondAmplitude =
            amplitudeScale *
            size.height *
            0.12 *
            math.sin(i * 0.7 + timeOffset * 1.8);
        final y = centerY + secondAmplitude;

        if (firstPoint) {
          secondPath.moveTo(x, y);
          firstPoint = false;
        } else {
          secondPath.lineTo(x, y);
        }
      }

      canvas.drawPath(secondPath, secondLayerPaint);
    }
  }

  @override
  bool shouldRepaint(
    covariant HealthAssistantAudioWaveformPainter oldDelegate,
  ) {
    // 始终重绘以实现流畅的时间动画效果
    // 同时检查属性变化以优化性能
    return true;
  }
}
