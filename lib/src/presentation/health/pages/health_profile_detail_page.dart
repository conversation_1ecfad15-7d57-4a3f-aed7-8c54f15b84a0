import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/responsive_util.dart';
import '../../../common/utils/text_style_util.dart';
import '../../../models/health_profile_model.dart';
import '../../../services/health_profile_cache_service.dart';
import '../../../utils/toast_util.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 完整健康档案详情页面
class HealthProfileDetailPage extends StatefulWidget {
  const HealthProfileDetailPage({super.key});

  @override
  State<HealthProfileDetailPage> createState() =>
      _HealthProfileDetailPageState();
}

class _HealthProfileDetailPageState extends State<HealthProfileDetailPage> {
  final HealthProfileCacheService _healthCacheService =
      HealthProfileCacheService();
  HealthProfileModel? _healthProfile;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadHealthProfileInstantly();
  }

  /// 立即加载健康档案（优先使用缓存，避免刷新）
  void _loadHealthProfileInstantly() {
    // 先尝试同步获取缓存数据
    final cachedProfile = _healthCacheService.getCachedHealthProfileSync();

    if (cachedProfile != null) {
      // 如果有缓存数据，立即显示
      setState(() {
        _healthProfile = cachedProfile;
        _isLoading = false;
      });

      // 后台异步检查更新（不阻塞UI）
      _backgroundRefreshHealthProfile();
    } else {
      // 如果没有缓存数据，显示加载状态并异步加载
      setState(() {
        _isLoading = true;
      });
      _loadHealthProfileAsync();
    }
  }

  /// 后台刷新健康档案数据（不阻塞UI）
  void _backgroundRefreshHealthProfile() {
    // 异步后台刷新，不影响当前显示
    Future.microtask(() async {
      try {
        final healthProfile = await _healthCacheService.getHealthProfile();

        if (mounted && healthProfile != null) {
          // 只有数据真正变化时才更新UI
          if (_hasDataChanged(healthProfile)) {
            setState(() {
              _healthProfile = healthProfile;
            });
          }
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
      }
    });
  }

  /// 检查数据是否发生变化
  bool _hasDataChanged(HealthProfileModel newProfile) {
    if (_healthProfile == null) return true;

    // 比较关键字段
    return _healthProfile!.height != newProfile.height ||
        _healthProfile!.weight != newProfile.weight ||
        _healthProfile!.bloodType != newProfile.bloodType ||
        _healthProfile!.hasChronicDiseases != newProfile.hasChronicDiseases ||
        _healthProfile!.chronicDiseases.length !=
            newProfile.chronicDiseases.length;
  }

  /// 异步加载健康档案（用于首次加载）
  Future<void> _loadHealthProfileAsync() async {
    try {
      final healthProfile = await _healthCacheService.getHealthProfile();
      if (mounted) {
        setState(() {
          _healthProfile = healthProfile;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(context, '加载健康档案失败: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).healthProfile,
          style: TextStyleUtil.getAppBarTitleStyle(context),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _healthProfile == null
          ? _buildEmptyState(context)
          : _buildHealthProfileContent(context),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.health_and_safety_outlined,
            size: 80,
            color: ThemeHelper.getTextSecondary(context),
          ),
          SizedBox(height: ResponsiveUtil.mediumSpacing(context)),
          Text(
            AppLocalizations.of(context).noHealthProfileYet,
            style: TextStyleUtil.getCustomStyle(
              context: context,
              baseFontSize: 16,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
          SizedBox(height: ResponsiveUtil.largeSpacing(context)),
          ElevatedButton(
            onPressed: () {
              // TODO: 导航到健康档案编辑页面
              ToastUtil.show(
                context,
                AppLocalizations.of(context).healthProfileEditFeatureComingSoon,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveUtil.largeSpacing(context) * 2,
                vertical: ResponsiveUtil.mediumSpacing(context),
              ),
            ),
            child: Text(AppLocalizations.of(context).createHealthProfile),
          ),
        ],
      ),
    );
  }

  /// 构建健康档案内容
  Widget _buildHealthProfileContent(BuildContext context) {
    return SingleChildScrollView(
      padding: ResponsiveUtil.cardPadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 基础信息
          _buildSection(
            context,
            title: AppLocalizations.of(context).basicInfo,
            icon: Icons.person_outline,
            children: [
              _buildInfoRow(
                context,
                AppLocalizations.of(context).height,
                '${_healthProfile!.height?.toStringAsFixed(0) ?? '--'} cm',
              ),
              _buildInfoRow(
                context,
                AppLocalizations.of(context).weight,
                '${_healthProfile!.weight?.toStringAsFixed(1) ?? '--'} kg',
              ),
              _buildInfoRow(
                context,
                AppLocalizations.of(context).bloodType,
                _healthProfile!.bloodType ?? '--',
              ),
            ],
          ),

          // 过敏史
          _buildSection(
            context,
            title: AppLocalizations.of(context).allergyHistory,
            icon: Icons.warning_outlined,
            children: [
              _buildInfoRow(
                context,
                AppLocalizations.of(context).hasAllergies,
                _healthProfile!.hasAllergies
                    ? AppLocalizations.of(context).yes
                    : AppLocalizations.of(context).no,
              ),
              if (_healthProfile!.hasAllergies) ...[
                if (_healthProfile!.allergyDrugs.isNotEmpty)
                  _buildInfoRow(
                    context,
                    AppLocalizations.of(context).drugAllergies,
                    _healthProfile!.allergyDrugs.join(', '),
                  ),
                if (_healthProfile!.allergyFoods.isNotEmpty)
                  _buildInfoRow(
                    context,
                    AppLocalizations.of(context).foodAllergies,
                    _healthProfile!.allergyFoods.join(', '),
                  ),
                if (_healthProfile!.allergyOthers?.isNotEmpty == true)
                  _buildInfoRow(
                    context,
                    AppLocalizations.of(context).otherAllergies,
                    _healthProfile!.allergyOthers!,
                  ),
              ],
            ],
          ),

          // 慢性病史
          _buildSection(
            context,
            title: AppLocalizations.of(context).chronicDiseaseHistory,
            icon: Icons.medical_information_outlined,
            children: [
              _buildInfoRow(
                context,
                AppLocalizations.of(context).hasChronicDiseases,
                _healthProfile!.hasChronicDiseases
                    ? AppLocalizations.of(context).yes
                    : AppLocalizations.of(context).no,
              ),
              if (_healthProfile!.hasChronicDiseases) ...[
                if (_healthProfile!.chronicDiseases.isNotEmpty)
                  _buildInfoRow(
                    context,
                    AppLocalizations.of(context).chronicDiseasesList,
                    _healthProfile!.chronicDiseases.join(', '),
                  ),
                if (_healthProfile!.bloodPressureRange?.isNotEmpty == true)
                  _buildInfoRow(
                    context,
                    AppLocalizations.of(context).bloodPressureRange,
                    _healthProfile!.bloodPressureRange!,
                  ),
                if (_healthProfile!.bloodSugarRange?.isNotEmpty == true)
                  _buildInfoRow(
                    context,
                    AppLocalizations.of(context).bloodSugarRange,
                    _healthProfile!.bloodSugarRange!,
                  ),
              ],
            ],
          ),

          // 当前用药
          _buildSection(
            context,
            title: AppLocalizations.of(context).currentMedication,
            icon: Icons.medication_outlined,
            children: [
              _buildInfoRow(
                context,
                AppLocalizations.of(context).hasCurrentMedication,
                _healthProfile!.hasCurrentMedication
                    ? AppLocalizations.of(context).yes
                    : AppLocalizations.of(context).no,
              ),
              if (_healthProfile!.hasCurrentMedication &&
                  _healthProfile!.currentMedications?.isNotEmpty == true)
                _buildInfoRow(
                  context,
                  AppLocalizations.of(context).medicationDetails,
                  _healthProfile!.currentMedications!,
                ),
            ],
          ),

          // 生活方式
          _buildSection(
            context,
            title: AppLocalizations.of(context).lifestyle,
            icon: Icons.fitness_center_outlined,
            children: [
              _buildInfoRow(
                context,
                AppLocalizations.of(context).exerciseFrequency,
                _getExerciseFrequencyText(_healthProfile!.exerciseFrequency),
              ),
              _buildInfoRow(
                context,
                AppLocalizations.of(context).smokingStatus,
                _getSmokingStatusText(_healthProfile!.smokingStatus),
              ),
              _buildInfoRow(
                context,
                AppLocalizations.of(context).drinkingStatus,
                _getDrinkingStatusText(_healthProfile!.drinkingStatus),
              ),
              _buildInfoRow(
                context,
                AppLocalizations.of(context).sleepDuration,
                _getSleepDurationText(_healthProfile!.sleepDuration),
              ),
              _buildInfoRow(
                context,
                AppLocalizations.of(context).sleepQuality,
                _getSleepQualityText(_healthProfile!.sleepQuality),
              ),
              _buildInfoRow(
                context,
                AppLocalizations.of(context).stressLevel,
                _getStressLevelText(_healthProfile!.stressLevel),
              ),
            ],
          ),

          SizedBox(height: ResponsiveUtil.largeSpacing(context)),
        ],
      ),
    );
  }

  /// 构建信息区块
  Widget _buildSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: ResponsiveUtil.mediumSpacing(context)),
      padding: EdgeInsets.all(ResponsiveUtil.largeSpacing(context)),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(
          ResponsiveUtil.borderRadius(context, baseRadius: 12),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.withAlpha(Colors.black, 13),
            blurRadius: ResponsiveUtil.mediumSpacing(context),
            offset: Offset(0, ResponsiveUtil.smallSpacing(context) / 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary, size: 20),
              SizedBox(width: ResponsiveUtil.smallSpacing(context)),
              Text(
                title,
                style: TextStyleUtil.getCustomStyle(
                  context: context,
                  baseFontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),
          SizedBox(height: ResponsiveUtil.mediumSpacing(context)),
          ...children,
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: ResponsiveUtil.smallSpacing(context)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyleUtil.getCustomStyle(
                context: context,
                baseFontSize: 14,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取运动频率文本
  String _getExerciseFrequencyText(String? frequency) {
    if (frequency == null) return '--';
    switch (frequency) {
      case 'sedentary':
        return AppLocalizations.of(context).sedentary;
      case 'light':
        return AppLocalizations.of(context).lightExercise;
      case 'moderate':
        return AppLocalizations.of(context).moderateExercise;
      case 'very_active':
        return AppLocalizations.of(context).activeExercise;
      default:
        return frequency;
    }
  }

  /// 获取吸烟状况文本
  String _getSmokingStatusText(String? status) {
    if (status == null) return '--';
    switch (status) {
      case 'never':
        return AppLocalizations.of(context).never;
      case 'quit':
        return AppLocalizations.of(context).quit;
      case 'occasional':
        return AppLocalizations.of(context).occasional;
      case 'daily':
        return AppLocalizations.of(context).daily;
      default:
        return status;
    }
  }

  /// 获取饮酒状况文本
  String _getDrinkingStatusText(String? status) {
    if (status == null) return '--';
    switch (status) {
      case 'never':
        return AppLocalizations.of(context).never;
      case 'quit':
        return AppLocalizations.of(context).quit;
      case 'social':
        return AppLocalizations.of(context).social;
      case 'weekly':
        return AppLocalizations.of(context).weekly;
      case 'daily':
        return AppLocalizations.of(context).daily;
      default:
        return status;
    }
  }

  /// 获取睡眠时长文本
  String _getSleepDurationText(String? duration) {
    if (duration == null) return '--';
    switch (duration) {
      case 'less_6':
        return AppLocalizations.of(context).lessThan6Hours;
      case '6_7':
        return AppLocalizations.of(context).sixToSevenHours;
      case '7_8':
        return AppLocalizations.of(context).sevenToEightHours;
      case 'more_8':
        return AppLocalizations.of(context).moreThan8Hours;
      default:
        return duration;
    }
  }

  /// 获取睡眠质量文本
  String _getSleepQualityText(String? quality) {
    if (quality == null) return '--';
    switch (quality) {
      case 'good':
        return AppLocalizations.of(context).good;
      case 'fair':
        return AppLocalizations.of(context).fair;
      case 'poor':
        return AppLocalizations.of(context).poor;
      default:
        return quality;
    }
  }

  /// 获取压力水平文本
  String _getStressLevelText(String? level) {
    if (level == null) return '--';
    switch (level) {
      case 'very_low':
        return AppLocalizations.of(context).veryLow;
      case 'low':
        return AppLocalizations.of(context).low;
      case 'moderate':
        return AppLocalizations.of(context).moderate;
      case 'high':
        return AppLocalizations.of(context).high;
      case 'very_high':
        return AppLocalizations.of(context).veryHigh;
      default:
        return level;
    }
  }
}
