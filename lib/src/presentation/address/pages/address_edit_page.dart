import 'package:flutter/material.dart';
import 'package:city_pickers/city_pickers.dart';
import '../../../models/address_model.dart';
import '../../../services/address_service.dart';
import '../../../services/location_service.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../config/themes/app_colors.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 地址编辑页面
class AddressEditPage extends StatefulWidget {
  /// 要编辑的地址（为空则为新增）
  final AddressModel? address;

  const AddressEditPage({super.key, this.address});

  @override
  State<AddressEditPage> createState() => _AddressEditPageState();
}

class _AddressEditPageState extends State<AddressEditPage> {
  final AddressService _addressService = AddressService();
  final LocationService _locationService = LocationService();

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _detailedAddressController = TextEditingController();
  final _postalCodeController = TextEditingController();
  final _labelController = TextEditingController();

  bool _isLoading = false;
  bool _isGettingLocation = false;
  bool _isDefault = false;

  // 地区选择相关
  String _selectedProvince = '';
  String _selectedCity = '';
  String _selectedArea = '';
  String get _selectedRegion {
    if (_selectedProvince.isEmpty) return '';
    String region = _selectedProvince;
    if (_selectedCity.isNotEmpty) region += ' $_selectedCity';
    if (_selectedArea.isNotEmpty) region += ' $_selectedArea';
    return region;
  }

  /// 是否为编辑模式
  bool get _isEditMode => widget.address != null;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _detailedAddressController.dispose();
    _postalCodeController.dispose();
    _labelController.dispose();
    super.dispose();
  }

  /// 初始化数据
  void _initializeData() {
    if (_isEditMode) {
      final address = widget.address!;
      _nameController.text = address.receiverName;
      _phoneController.text = address.receiverPhone;
      _selectedProvince = address.province;
      _selectedCity = address.city;
      _selectedArea = address.district;
      _detailedAddressController.text = address.detailedAddress;
      _postalCodeController.text = address.postalCode ?? '';
      _labelController.text = address.addressLabel ?? '';
      _isDefault = address.isDefault;
    } else {
      // 新增地址时自动获取当前位置
      _getCurrentLocationDirect();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          _isEditMode
              ? AppLocalizations.of(context).editAddress
              : AppLocalizations.of(context).addAddress,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ThemeHelper.getTextPrimary(context),
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          // 删除按钮（仅在编辑模式下显示）
          if (_isEditMode)
            TextButton(
              onPressed: _isLoading ? null : _deleteAddress,
              child: Text(
                AppLocalizations.of(context).deleteAddress,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(height: 1, color: ThemeHelper.getBorder(context)),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [_buildFormCard(), const SizedBox(height: 100)],
                ),
              ),
            ),
          ),
          _buildBottomActions(),
        ],
      ),
    );
  }

  /// 构建表单卡片
  Widget _buildFormCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 收货人姓名
          _buildInputField(
            controller: _nameController,
            label: AppLocalizations.of(context).receiverName,
            hint: AppLocalizations.of(context).enterReceiverName,
            icon: Icons.person_outline,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return AppLocalizations.of(context).enterReceiverName;
              }
              if (value.trim().length > 50) {
                return '姓名不能超过50个字符';
              }
              return null;
            },
          ),
          const SizedBox(height: 20),

          // 联系电话
          _buildInputField(
            controller: _phoneController,
            label: AppLocalizations.of(context).contactPhone,
            hint: AppLocalizations.of(context).enterContactPhone,
            icon: Icons.phone_outlined,
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return AppLocalizations.of(context).enterContactPhone;
              }
              if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value.trim())) {
                return AppLocalizations.of(context).enterCorrectPhoneNumber;
              }
              return null;
            },
          ),
          const SizedBox(height: 20),

          // 所在地区选择
          _buildRegionSelector(),
          const SizedBox(height: 20),

          // 详细地址
          _buildInputField(
            controller: _detailedAddressController,
            label: AppLocalizations.of(context).detailedAddress,
            hint: AppLocalizations.of(context).enterDetailedAddress,
            icon: Icons.location_on_outlined,
            maxLines: 3,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return AppLocalizations.of(context).enterDetailedAddress;
              }
              if (value.trim().length < 5) {
                return AppLocalizations.of(context).addressTooShort;
              }
              if (value.trim().length > 200) {
                return AppLocalizations.of(context).addressTooLong;
              }
              return null;
            },
          ),
          const SizedBox(height: 20),

          // 邮政编码（可选）
          _buildInputField(
            controller: _postalCodeController,
            label: AppLocalizations.of(context).postalCodeOptional,
            hint: AppLocalizations.of(context).enterPostalCode,
            icon: Icons.local_post_office_outlined,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                if (!RegExp(r'^\d{6}$').hasMatch(value.trim())) {
                  return '请输入正确的邮政编码';
                }
              }
              return null;
            },
          ),
          const SizedBox(height: 20),

          // 地址标签（可选）
          _buildInputField(
            controller: _labelController,
            label: AppLocalizations.of(context).addressLabelOptional,
            hint: AppLocalizations.of(context).enterAddressLabel,
            icon: Icons.label_outline,
            validator: (value) {
              if (value != null && value.trim().length > 20) {
                return '地址标签不能超过20个字符';
              }
              return null;
            },
          ),
          const SizedBox(height: 20),

          // 设为默认地址
          Row(
            children: [
              Text(
                AppLocalizations.of(context).setAsDefaultAddress,
                style: TextStyle(
                  color: ThemeHelper.getTextPrimary(context),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              Switch(
                value: _isDefault,
                onChanged: (value) {
                  setState(() {
                    _isDefault = value;
                  });
                },
                activeColor: AppColors.primary,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建输入框
  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: ThemeHelper.getTextPrimary(context),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          style: TextStyle(color: ThemeHelper.getTextPrimary(context)),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: ThemeHelper.getTextHint(context),
              fontSize: 14,
            ),
            prefixIcon: Icon(
              icon,
              color: ThemeHelper.getTextSecondary(context),
              size: 20,
            ),
            filled: true,
            fillColor: ThemeHelper.getInputBackground(context),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: ThemeHelper.getBorder(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.primary, width: 1.5),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFFF4757)),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: Color(0xFFFF4757),
                width: 1.5,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建地区选择器
  Widget _buildRegionSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              AppLocalizations.of(context).region,
              style: TextStyle(
                color: ThemeHelper.getTextPrimary(context),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            GestureDetector(
              onTap: _getCurrentLocationDirect,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: _isGettingLocation
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: _isGettingLocation
                    ? SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: AppColors.primary,
                        ),
                      )
                    : Icon(
                        Icons.my_location,
                        color: AppColors.primary,
                        size: 16,
                      ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _showSystemCityPicker,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: ThemeHelper.getInputBackground(context),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: ThemeHelper.getBorder(context)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.location_city_outlined,
                  color: ThemeHelper.getTextSecondary(context),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedRegion.isEmpty
                        ? AppLocalizations.of(context).selectRegion
                        : _selectedRegion,
                    style: TextStyle(
                      color: _selectedRegion.isEmpty
                          ? ThemeHelper.getTextHint(context)
                          : ThemeHelper.getTextPrimary(context),
                      fontSize: 14,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: ThemeHelper.getTextHint(context),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
        if (_selectedRegion.isEmpty)
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: const Text(
              '请选择所在地区',
              style: TextStyle(color: Color(0xFFFF4757), fontSize: 12),
            ),
          ),
      ],
    );
  }

  /// 直接获取当前位置
  Future<void> _getCurrentLocationDirect() async {
    if (_isGettingLocation) return;

    setState(() {
      _isGettingLocation = true;
    });

    try {
      bool success = await _locationService.getCurrentLocation();

      if (success && _locationService.hasCurrentLocation()) {
        setState(() {
          _selectedProvince = _locationService.currentProvince ?? '';
          _selectedCity = _locationService.currentCity ?? '';
          _selectedArea = _locationService.currentArea ?? '';
        });
      }
    } catch (e) {
      // 静默失败
    } finally {
      if (mounted) {
        setState(() {
          _isGettingLocation = false;
        });
      }
    }
  }

  /// 显示系统城市选择器
  Future<void> _showSystemCityPicker() async {
    try {
      final result = await CityPickers.showCityPicker(context: context);

      if (result != null) {
        setState(() {
          _selectedProvince = result.provinceName ?? '';
          _selectedCity = result.cityName ?? '';
          _selectedArea = result.areaName ?? '';
        });
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '地区选择失败，请重试');
      }
    }
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        boxShadow: [
          BoxShadow(
            color: const Color(0x0A000000),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveAddress,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
              elevation: 0,
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : Text(
                    _isEditMode
                        ? AppLocalizations.of(context).saveChanges
                        : AppLocalizations.of(context).saveAddress,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  /// 保存地址
  Future<void> _saveAddress() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 验证地区选择
    if (_selectedRegion.isEmpty) {
      ToastUtil.show(context, '请选择所在地区');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = AddressRequestModel(
        receiverName: _nameController.text.trim(),
        receiverPhone: _phoneController.text.trim(),
        province: _selectedProvince,
        city: _selectedCity,
        district: _selectedArea,
        detailedAddress: _detailedAddressController.text.trim(),
        postalCode: _postalCodeController.text.trim().isEmpty
            ? null
            : _postalCodeController.text.trim(),
        addressLabel: _labelController.text.trim().isEmpty
            ? null
            : _labelController.text.trim(),
        isDefault: _isDefault,
      );

      if (_isEditMode) {
        await _addressService.updateAddress(widget.address!.id, request);
        if (mounted) {
          ToastUtil.show(context, '修改成功');
        }
      } else {
        await _addressService.createAddress(request);
        if (mounted) {
          ToastUtil.show(context, '添加成功');
        }
      }

      if (mounted) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '保存失败：$e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 删除地址
  Future<void> _deleteAddress() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这个地址吗？删除后无法恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        setState(() {
          _isLoading = true;
        });

        await _addressService.deleteAddress(widget.address!.id);

        if (mounted) {
          ToastUtil.show(context, '删除成功');
          Navigator.pop(context, true);
        }
      } catch (e) {
        if (mounted) {
          ToastUtil.show(context, '删除失败：$e');
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}
