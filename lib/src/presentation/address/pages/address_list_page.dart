import 'package:flutter/material.dart';
import '../../../models/address_model.dart';
import '../../../services/address_service.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../config/themes/app_colors.dart';
import 'address_edit_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 地址管理页面
class AddressListPage extends StatefulWidget {
  /// 是否为选择模式（从订单页面跳转）
  final bool isSelectMode;

  const AddressListPage({super.key, this.isSelectMode = false});

  @override
  State<AddressListPage> createState() => _AddressListPageState();
}

class _AddressListPageState extends State<AddressListPage> {
  final AddressService _addressService = AddressService();

  List<AddressModel> _addresses = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  /// 加载地址列表
  Future<void> _loadAddresses() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final addresses = await _addressService.getAddressList();

      if (mounted) {
        setState(() {
          _addresses = addresses;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).loadAddressFailed}：$e',
        );
      }
    }
  }

  /// 设置默认地址
  Future<void> _setDefaultAddress(AddressModel address) async {
    try {
      await _addressService.setDefaultAddress(address.id);
      if (mounted) {
        ToastUtil.show(context, AppLocalizations.of(context).setDefaultSuccess);
        _loadAddresses();
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).setDefaultFailed}：$e',
        );
      }
    }
  }

  /// 选择地址（选择模式）
  void _selectAddress(AddressModel address) {
    Navigator.pop(context, address);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          widget.isSelectMode
              ? AppLocalizations.of(context).selectAddress
              : AppLocalizations.of(context).addressManagement,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: ThemeHelper.getTextPrimary(context),
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(height: 1, color: ThemeHelper.getBorder(context)),
        ),
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: ThemeHelper.getTextPrimary(context),
              ),
            )
          : _addresses.isEmpty
          ? _buildEmptyState()
          : _buildAddressList(),
      floatingActionButton: FloatingActionButton(
        onPressed: _addAddress,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off_outlined,
            size: 80,
            color: ThemeHelper.getTextHint(context),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).noAddressesYet,
            style: TextStyle(
              fontSize: 16,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).clickToAddAddress,
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextHint(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建地址列表
  Widget _buildAddressList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _addresses.length,
      itemBuilder: (context, index) {
        final address = _addresses[index];
        return _buildAddressCard(address);
      },
    );
  }

  /// 构建地址卡片
  Widget _buildAddressCard(AddressModel address) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.isSelectMode ? () => _selectAddress(address) : null,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 姓名、手机号和默认标签在一行
                Row(
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Text(
                            address.receiverName,
                            style: TextStyle(
                              fontSize: 17,
                              fontWeight: FontWeight.bold,
                              color: ThemeHelper.getTextPrimary(context),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            address.receiverPhone,
                            style: TextStyle(
                              fontSize: 17,
                              fontWeight: FontWeight.bold,
                              color: ThemeHelper.getTextPrimary(context),
                            ),
                          ),
                          const SizedBox(width: 12),
                          // 默认标签
                          if (address.isDefault)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 3,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                AppLocalizations.of(context).defaultAddress,
                                style: const TextStyle(
                                  fontSize: 11,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          // 地址标签
                          if (address.addressLabel?.isNotEmpty == true)
                            Container(
                              margin: EdgeInsets.only(
                                left: address.isDefault ? 6 : 0,
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 3,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                address.displayLabel,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // 地址信息和编辑按钮
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        address.fullAddress,
                        style: TextStyle(
                          fontSize: 13,
                          color: ThemeHelper.getTextSecondary(context),
                          height: 1.4,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 编辑按钮
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => _editAddress(address),
                          borderRadius: BorderRadius.circular(8),
                          child: Icon(
                            Icons.edit_outlined,
                            size: 16,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                // 设为默认按钮（仅在非默认地址且非选择模式下显示）
                if (!address.isDefault && !widget.isSelectMode) ...[
                  const SizedBox(height: 12),
                  _buildDottedDivider(),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Spacer(),
                      _buildActionChip(
                        icon: Icons.star_outline,
                        label: AppLocalizations.of(context).setAsDefault,
                        onTap: () => _setDefaultAddress(address),
                        color: AppColors.primary,
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建虚线分隔符
  Widget _buildDottedDivider() {
    return Row(
      children: List.generate(
        50,
        (index) => Expanded(
          child: Container(
            height: 1,
            color: index % 2 == 0
                ? ThemeHelper.getBorder(context)
                : Colors.transparent,
          ),
        ),
      ),
    );
  }

  /// 构建操作芯片
  Widget _buildActionChip({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 添加地址
  void _addAddress() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (context) => const AddressEditPage()),
    );

    if (result == true) {
      _loadAddresses();
    }
  }

  /// 编辑地址
  void _editAddress(AddressModel address) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => AddressEditPage(address: address),
      ),
    );

    if (result == true) {
      _loadAddresses();
    }
  }
}
