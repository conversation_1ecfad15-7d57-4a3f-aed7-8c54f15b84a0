import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../doctor_product/pages/doctor_product_management_page.dart';
import '../widgets/order_management_tab.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 医生管理页面 - 包含产品管理和订单管理两个标签页
class DoctorManagementPage extends StatefulWidget {
  const DoctorManagementPage({super.key});

  @override
  State<DoctorManagementPage> createState() => _DoctorManagementPageState();
}

class _DoctorManagementPageState extends State<DoctorManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // 移除硬编码的标签列表，改为在build方法中动态获取

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this); // 固定为2个标签页
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        AppLocalizations.of(context).doctorManagementTitle,
        style: FontUtil.createAppBarTitleStyle(
          context: context,
          text: AppLocalizations.of(context).doctorManagementTitle,
          fontSize: 18,
          color: ThemeHelper.getTextPrimary(context),
        ),
      ),
      backgroundColor: ThemeHelper.getCardBackground(context),
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: ThemeHelper.getTextPrimary(context),
        ),
        onPressed: () => Navigator.pop(context),
      ),
      bottom: _buildTabBar(),
    );
  }

  /// 构建标签栏
  PreferredSizeWidget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      labelColor: AppColors.primary,
      unselectedLabelColor: ThemeHelper.getTextSecondary(context),
      indicatorColor: AppColors.primary,
      indicatorWeight: 2,
      labelStyle: FontUtil.createTabLabelStyle(
        text: AppLocalizations.of(context).productManagementTab,
        fontSize: 16,
      ),
      unselectedLabelStyle: FontUtil.createTabUnselectedLabelStyle(
        text: AppLocalizations.of(context).productManagementTab,
        fontSize: 16,
      ),
      dividerColor: Colors.transparent, // 移除下方的白色分割线
      tabs: [
        Tab(text: AppLocalizations.of(context).productManagementTab),
        Tab(text: AppLocalizations.of(context).orderManagementTab),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return TabBarView(
      controller: _tabController,
      children: [
        // 产品管理标签页 - 复用现有的产品管理页面内容
        const ProductManagementTab(),
        // 订单管理标签页 - 新建的订单管理页面
        const OrderManagementTab(),
      ],
    );
  }
}

/// 产品管理标签页 - 复用现有产品管理页面的内容
class ProductManagementTab extends StatelessWidget {
  const ProductManagementTab({super.key});

  @override
  Widget build(BuildContext context) {
    // 直接嵌入现有的产品管理页面内容，但不包含AppBar
    return const DoctorProductManagementContent();
  }
}
