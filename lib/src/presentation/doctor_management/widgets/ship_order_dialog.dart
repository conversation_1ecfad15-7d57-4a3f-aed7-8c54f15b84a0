import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_product_model.dart';
import '../../../models/shipping_model.dart';
import '../../../services/shipping_service.dart';
import '../../../services/language_service.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/shipping_error_messages.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 发货对话框
class ShipOrderDialog extends StatefulWidget {
  final ProductOrderModel order;
  final VoidCallback? onShipped;

  const ShipOrderDialog({super.key, required this.order, this.onShipped});

  @override
  State<ShipOrderDialog> createState() => _ShipOrderDialogState();
}

class _ShipOrderDialogState extends State<ShipOrderDialog> {
  final _formKey = GlobalKey<FormState>();
  final _trackingNumberController = TextEditingController();
  final _shippingNoteController = TextEditingController();

  String? _selectedCompany;
  bool _isLoading = false;

  @override
  void dispose() {
    _trackingNumberController.dispose();
    _shippingNoteController.dispose();
    super.dispose();
  }

  /// 获取本地化的产品名称
  String _getLocalizedProductName() {
    final languageCode = LanguageService().getCurrentLanguageCode();
    return widget.order.getProductName(languageCode);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 400,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        decoration: BoxDecoration(
          color: ThemeHelper.getCardBackground(context),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            _buildHeader(),

            // 内容区域
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 订单信息卡片
                      _buildOrderInfoCard(),
                      const SizedBox(height: 24),

                      // 发货信息标题
                      _buildSectionTitle(
                        AppLocalizations.of(context).shippingInfo,
                        Icons.local_shipping_outlined,
                      ),
                      const SizedBox(height: 16),

                      // 快递单号输入
                      _buildTrackingNumberField(),
                      const SizedBox(height: 20),

                      // 快递公司选择
                      _buildShippingCompanyField(),
                      const SizedBox(height: 20),

                      // 发货备注
                      _buildShippingNoteField(),
                      const SizedBox(height: 32),

                      // 操作按钮
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建标题栏
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.local_shipping,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              AppLocalizations.of(context).shipOrder,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
          ),
          IconButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            icon: Icon(
              Icons.close,
              color: ThemeHelper.getTextSecondary(context),
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.transparent,
              padding: EdgeInsets.zero,
              minimumSize: const Size(32, 32),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建区域标题
  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppColors.primary, size: 18),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
      ],
    );
  }

  /// 构建订单信息卡片
  Widget _buildOrderInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.receipt_long, color: AppColors.primary, size: 16),
              const SizedBox(width: 6),
              Text(
                AppLocalizations.of(context).orderInfoTitle,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildOrderInfoRow(
            AppLocalizations.of(context).orderNumber,
            widget.order.orderSn,
          ),
          _buildOrderInfoRow(
            AppLocalizations.of(context).productLabel,
            _getLocalizedProductName(),
          ),
          _buildOrderInfoRow(
            AppLocalizations.of(context).customer,
            widget.order.userNickname,
          ),
          _buildOrderInfoRow(
            AppLocalizations.of(context).shippingAddress,
            widget.order.shippingAddress ?? '',
            isAddress: true,
          ),
        ],
      ),
    );
  }

  /// 构建订单信息行
  Widget _buildOrderInfoRow(
    String label,
    String value, {
    bool isAddress = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 70,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: ThemeHelper.getTextSecondary(context),
                fontWeight: FontWeight.w500,
              ),
              maxLines: isAddress ? 2 : 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建快递单号输入框
  Widget _buildTrackingNumberField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _trackingNumberController,
        style: TextStyle(
          fontSize: 14,
          color: ThemeHelper.getTextPrimary(context),
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: '${AppLocalizations.of(context).trackingNumber} *',
          labelStyle: TextStyle(
            color: ThemeHelper.getTextHint(context),
            fontSize: 14,
          ),
          hintText: AppLocalizations.of(context).enterTrackingNumber,
          hintStyle: TextStyle(
            color: ThemeHelper.getTextHint(context),
            fontSize: 13,
            overflow: TextOverflow.ellipsis,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.local_shipping_outlined,
              color: AppColors.primary,
              size: 18,
            ),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getTextHint(context).withValues(alpha: 0.2),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getTextHint(context).withValues(alpha: 0.2),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.primary, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.red),
          ),
          filled: true,
          fillColor: ThemeHelper.getCardBackground(context),
          contentPadding: const EdgeInsets.only(
            left: 72,
            right: 16,
            top: 16,
            bottom: 16,
          ),
        ),
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return AppLocalizations.of(context).trackingNumberRequired;
          }
          return null;
        },
      ),
    );
  }

  /// 构建快递公司选择框
  Widget _buildShippingCompanyField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedCompany,
        style: TextStyle(
          fontSize: 14,
          color: ThemeHelper.getTextPrimary(context),
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).shippingCompanyLabel,
          labelStyle: TextStyle(
            color: ThemeHelper.getTextHint(context),
            fontSize: 14,
          ),
          // 移除hintText避免溢出，label已经足够说明用途
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.business_outlined,
              color: AppColors.primary,
              size: 18,
            ),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getTextHint(context).withValues(alpha: 0.2),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getTextHint(context).withValues(alpha: 0.2),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.primary, width: 2),
          ),
          filled: true,
          fillColor: ThemeHelper.getCardBackground(context),
          contentPadding: const EdgeInsets.only(
            left: 72,
            right: 40,
            top: 16,
            bottom: 16,
          ),
        ),
        items: ShippingCompanies.companies.map((company) {
          return DropdownMenuItem<String>(
            value: company,
            child: Text(
              company,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextPrimary(context),
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          );
        }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedCompany = value;
          });
        },
        icon: Icon(
          Icons.keyboard_arrow_down,
          color: ThemeHelper.getTextHint(context),
        ),
        dropdownColor: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  /// 构建发货备注输入框
  Widget _buildShippingNoteField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _shippingNoteController,
        style: TextStyle(
          fontSize: 14,
          color: ThemeHelper.getTextPrimary(context),
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context).shippingNoteLabel,
          labelStyle: TextStyle(
            color: ThemeHelper.getTextHint(context),
            fontSize: 14,
          ),
          hintText: AppLocalizations.of(context).enterShippingNote,
          hintStyle: TextStyle(
            color: ThemeHelper.getTextHint(context),
            fontSize: 13,
            overflow: TextOverflow.ellipsis,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.note_outlined,
              color: AppColors.primary,
              size: 18,
            ),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getTextHint(context).withValues(alpha: 0.2),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: ThemeHelper.getTextHint(context).withValues(alpha: 0.2),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.primary, width: 2),
          ),
          filled: true,
          fillColor: ThemeHelper.getCardBackground(context),
          contentPadding: const EdgeInsets.only(
            left: 72,
            right: 16,
            top: 16,
            bottom: 16,
          ),
        ),
        maxLines: 3,
        maxLength: 200,
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: ThemeHelper.getTextHint(context).withValues(alpha: 0.3),
              ),
            ),
            child: TextButton(
              onPressed: _isLoading ? null : () => Navigator.pop(context),
              style: TextButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: ThemeHelper.getTextSecondary(context),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                AppLocalizations.of(context).cancel,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [
                  AppColors.primary,
                  AppColors.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _handleShip,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      AppLocalizations.of(context).confirmShip,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ),
      ],
    );
  }

  /// 处理发货
  Future<void> _handleShip() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = ShipOrderRequest(
        trackingNumber: _trackingNumberController.text.trim(),
        shippingCompany: _selectedCompany,
        shippingNote: _shippingNoteController.text.trim().isNotEmpty
            ? _shippingNoteController.text.trim()
            : null,
      );

      await ShippingService().shipOrder(
        orderId: widget.order.id,
        request: request,
      );

      if (mounted) {
        Navigator.pop(context);
        ToastUtil.show(context, AppLocalizations.of(context).shipSuccess);
        if (widget.onShipped != null) {
          widget.onShipped!();
        }
      }
    } catch (e) {
      if (mounted) {
        final errorMessage = ShippingErrorMessages.getLocalizedErrorMessage(
          context,
          e is Exception ? e : Exception(e.toString()),
        );
        ToastUtil.show(
          context,
          AppLocalizations.of(context).shipFailed(errorMessage),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
