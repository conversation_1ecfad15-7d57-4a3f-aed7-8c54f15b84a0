import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import 'order_management_tab.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 订单统计信息卡片
class OrderStatisticsCard extends StatefulWidget {
  final OrderStatisticsModel statistics;

  const OrderStatisticsCard({super.key, required this.statistics});

  @override
  State<OrderStatisticsCard> createState() => _OrderStatisticsCardState();
}

class _OrderStatisticsCardState extends State<OrderStatisticsCard> {
  bool _isExpanded = false;

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 可点击的标题栏
          InkWell(
            onTap: _toggleExpanded,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    color: AppColors.primary,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context).orderOverview,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: ThemeHelper.getTextPrimary(context),
                    ),
                  ),
                  const Spacer(),
                  // 核心数据预览（折叠时显示）
                  if (!_isExpanded) ...[
                    _buildCompactStats(context),
                    const SizedBox(width: 8),
                  ],
                  // 展开/折叠图标
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: ThemeHelper.getTextSecondary(context),
                    size: 20,
                  ),
                ],
              ),
            ),
          ),

          // 详细统计信息（展开时显示）
          if (_isExpanded) ...[
            Container(
              width: double.infinity,
              height: 1,
              color: ThemeHelper.getDivider(context),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: _buildDetailedStatistics(context),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建紧凑统计信息（折叠时显示）
  Widget _buildCompactStats(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          AppLocalizations.of(
            context,
          ).orderCount(widget.statistics.totalOrders),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '¥${widget.statistics.totalSales.toStringAsFixed(0)}',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF27AE60),
          ),
        ),
      ],
    );
  }

  /// 构建详细统计信息（展开时显示）
  Widget _buildDetailedStatistics(BuildContext context) {
    return Column(
      children: [
        // 使用2x3网格布局，更紧凑
        Row(
          children: [
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).totalOrders,
                value: widget.statistics.totalOrders.toString(),
                icon: Icons.receipt_long_outlined,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).totalSales,
                value: '¥${widget.statistics.totalSales.toStringAsFixed(0)}',
                icon: Icons.monetization_on_outlined,
                color: const Color(0xFF27AE60),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).completed,
                value: widget.statistics.completed.toString(),
                icon: Icons.check_circle_outline,
                color: const Color(0xFF27AE60),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).pendingPayment,
                value: widget.statistics.pendingPayment.toString(),
                icon: Icons.payment_outlined,
                color: const Color(0xFFF39C12),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).pendingShipment,
                value: widget.statistics.pendingShipment.toString(),
                icon: Icons.local_shipping_outlined,
                color: const Color(0xFF3498DB),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactStatItem(
                context,
                title: AppLocalizations.of(context).cancelled,
                value: widget.statistics.cancelled.toString(),
                icon: Icons.cancel_outlined,
                color: const Color(0xFFE74C3C),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建紧凑统计项
  Widget _buildCompactStatItem(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: ThemeHelper.getTextSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
