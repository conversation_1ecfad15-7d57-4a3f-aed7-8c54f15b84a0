import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../models/doctor_product_model.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../services/language_service.dart';
import '../../../config/api/api_config.dart';

import 'ship_order_dialog.dart';

/// 订单列表项
class OrderListItem extends StatelessWidget {
  final ProductOrderModel order;
  final VoidCallback? onTap;
  final VoidCallback? onStatusUpdate;

  const OrderListItem({
    super.key,
    required this.order,
    this.onTap,
    this.onStatusUpdate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 订单信息行
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 产品图片
                  _buildProductImage(),
                  const SizedBox(width: 12),

                  // 订单信息
                  Expanded(child: _buildOrderInfo(context)),

                  // 操作按钮
                  _buildActionButtons(context),
                ],
              ),

              const SizedBox(height: 12),

              // 价格信息 - 跨越整个条目宽度
              _buildPriceInfo(context),

              const SizedBox(height: 8),

              // 底部信息
              _buildBottomInfo(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建产品图片
  Widget _buildProductImage() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: order.productImage?.isNotEmpty == true
            ? Image.network(
                _getFullImageUrl(order.productImage!),
                width: 60,
                height: 60,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultImage();
                },
              )
            : _buildDefaultImage(),
      ),
    );
  }

  /// 构建默认图片
  Widget _buildDefaultImage() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: AppColors.primary.withValues(alpha: 0.1),
      ),
      child: Icon(
        Icons.inventory_2_outlined,
        color: AppColors.primary,
        size: 24,
      ),
    );
  }

  /// 获取完整的图片URL
  String _getFullImageUrl(String imageUrl) {
    // 使用ApiConfig中的buildImageUrl方法来处理URL
    return ApiConfig.buildImageUrl(imageUrl);
  }

  /// 构建订单信息
  Widget _buildOrderInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 产品名称
        Text(
          _getLocalizedProductName(),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),

        // 订单号
        Text(
          '${AppLocalizations.of(context).orderNumber}: ${order.orderSn}',
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextSecondary(context),
          ),
        ),
        const SizedBox(height: 4),

        // 客户信息
        Text(
          '${AppLocalizations.of(context).customer}: ${order.userNickname}',
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextSecondary(context),
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  /// 构建价格信息
  Widget _buildPriceInfo(BuildContext context) {
    return Row(
      children: [
        // 左侧：单价和数量
        Text(
          '¥${order.unitPrice.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: const Color(0xFF27AE60),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          'x${order.quantity}',
          style: TextStyle(
            fontSize: 14,
            color: ThemeHelper.getTextSecondary(context),
          ),
        ),
        // 中间弹性空间
        const Spacer(),
        // 右侧：总计，贴在最右边
        Text(
          '${AppLocalizations.of(context).total}: ¥${order.totalAmount.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: ThemeHelper.getTextPrimary(context),
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 订单状态标签
        _buildStatusChip(context),
        const SizedBox(height: 8),
        // 操作按钮
        if (_canPerformAction()) _buildActionButton(context),
      ],
    );
  }

  /// 构建状态标签
  Widget _buildStatusChip(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    String statusText;

    switch (order.orderStatus) {
      case 0:
        backgroundColor = const Color(0xFFF39C12).withValues(alpha: 0.1);
        textColor = const Color(0xFFF39C12);
        statusText = AppLocalizations.of(context).orderStatusPending;
        break;
      case 1:
        backgroundColor = const Color(0xFF3498DB).withValues(alpha: 0.1);
        textColor = const Color(0xFF3498DB);
        statusText = AppLocalizations.of(context).pendingShipment;
        break;
      case 2:
        backgroundColor = AppColors.primary.withValues(alpha: 0.1);
        textColor = AppColors.primary;
        statusText = AppLocalizations.of(context).shipped;
        break;
      case 3:
        backgroundColor = const Color(0xFF27AE60).withValues(alpha: 0.1);
        textColor = const Color(0xFF27AE60);
        statusText = AppLocalizations.of(context).completed;
        break;
      case 4:
        backgroundColor = const Color(0xFFE74C3C).withValues(alpha: 0.1);
        textColor = const Color(0xFFE74C3C);
        statusText = AppLocalizations.of(context).cancelled;
        break;
      default:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
        statusText = AppLocalizations.of(context).orderStatusUnknown;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
    );
  }

  /// 是否可以执行操作
  bool _canPerformAction() {
    return order.canShip; // 使用模型中的方法判断是否可以发货
  }

  /// 构建操作按钮
  Widget _buildActionButton(BuildContext context) {
    return SizedBox(
      width: 60,
      height: 28,
      child: ElevatedButton(
        onPressed: () => _handleShipOrder(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(14),
          ),
        ),
        child: Text(
          AppLocalizations.of(context).ship,
          style: const TextStyle(fontSize: 12),
        ),
      ),
    );
  }

  /// 构建底部信息
  Widget _buildBottomInfo(BuildContext context) {
    return Row(
      children: [
        // 创建时间
        Icon(
          Icons.access_time,
          size: 14,
          color: ThemeHelper.getTextHint(context),
        ),
        const SizedBox(width: 4),
        Text(
          _formatDateTime(order.createdAt),
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextHint(context),
          ),
        ),
        const Spacer(),
        // 快递单号或支付状态
        if (order.trackingNumber?.isNotEmpty == true) ...[
          Icon(Icons.local_shipping, size: 14, color: AppColors.primary),
          const SizedBox(width: 4),
          Text(
            '${AppLocalizations.of(context).trackingNumber}: ${order.trackingNumber}',
            style: TextStyle(fontSize: 12, color: AppColors.primary),
          ),
        ] else if (order.payStatus == 1 && order.payTime != null) ...[
          Icon(Icons.payment, size: 14, color: const Color(0xFF27AE60)),
          const SizedBox(width: 4),
          Text(
            '${AppLocalizations.of(context).paidStatus} ${_formatDateTime(order.payTime!)}',
            style: TextStyle(fontSize: 12, color: const Color(0xFF27AE60)),
          ),
        ],
      ],
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 处理发货操作
  void _handleShipOrder(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ShipOrderDialog(
        order: order,
        onShipped: () {
          if (onStatusUpdate != null) {
            onStatusUpdate!();
          }
        },
      ),
    );
  }

  /// 获取本地化的产品名称
  String _getLocalizedProductName() {
    final languageCode = LanguageService().getCurrentLanguageCode();
    return order.getProductName(languageCode);
  }
}
