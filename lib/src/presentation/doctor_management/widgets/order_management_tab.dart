import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../../../models/doctor_product_model.dart';
import '../../../services/doctor_product_service.dart';
import '../../../utils/toast_util.dart';
import 'order_statistics_card.dart';
import 'order_list_item.dart';
import '../../shipping/pages/shipping_status_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 订单管理标签页
class OrderManagementTab extends StatefulWidget {
  const OrderManagementTab({super.key});

  @override
  State<OrderManagementTab> createState() => _OrderManagementTabState();
}

class _OrderManagementTabState extends State<OrderManagementTab>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<ProductOrderModel> _allOrders = [];
  List<ProductOrderModel> _filteredOrders = [];
  OrderStatisticsModel? _statistics;
  bool _isLoading = true;
  int _currentStatusFilter = -1; // -1表示全部

  // 移除硬编码的状态标签列表，改为在build方法中动态获取

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this); // 固定为6个状态标签
    _tabController.addListener(_onTabChanged);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) return;

    setState(() {
      _currentStatusFilter = _tabController.index == 0
          ? -1
          : _tabController.index - 1;
      _filterOrders();
    });
  }

  /// 加载数据
  Future<void> _loadData() async {
    try {
      print('OrderManagementTab: 开始加载数据');
      setState(() {
        _isLoading = true;
      });

      print('OrderManagementTab: 开始加载订单列表');
      // 加载订单列表
      final orders = await DoctorProductService().getOrders();
      print('OrderManagementTab: 订单列表加载完成，数量: ${orders.length}');

      // 计算统计信息
      final statistics = _calculateStatistics(orders);
      print('OrderManagementTab: 统计信息计算完成');

      if (mounted) {
        setState(() {
          _allOrders = orders;
          _statistics = statistics;
          _filterOrders();
          _isLoading = false;
        });
        print('OrderManagementTab: 数据加载完成并更新UI');
      }
    } catch (e, stackTrace) {
      print('OrderManagementTab: 加载数据失败: $e');
      print('OrderManagementTab: 堆栈跟踪: $stackTrace');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(context, '加载数据失败: $e');
      }
    }
  }

  /// 计算统计信息
  OrderStatisticsModel _calculateStatistics(List<ProductOrderModel> orders) {
    int totalOrders = orders.length;
    double totalSales = 0;
    int pendingPayment = 0;
    int pendingShipment = 0;
    int shipped = 0;
    int completed = 0;
    int cancelled = 0;

    for (final order in orders) {
      totalSales += order.totalAmount;

      switch (order.orderStatus) {
        case 0:
          pendingPayment++;
          break;
        case 1:
          pendingShipment++;
          break;
        case 2:
          shipped++;
          break;
        case 3:
          completed++;
          break;
        case 4:
          cancelled++;
          break;
      }
    }

    return OrderStatisticsModel(
      totalOrders: totalOrders,
      totalSales: totalSales,
      pendingPayment: pendingPayment,
      pendingShipment: pendingShipment,
      shipped: shipped,
      completed: completed,
      cancelled: cancelled,
    );
  }

  /// 根据状态筛选订单
  void _filterOrders() {
    if (_currentStatusFilter == -1) {
      _filteredOrders = List.from(_allOrders);
    } else {
      _filteredOrders = _allOrders
          .where((order) => order.orderStatus == _currentStatusFilter)
          .toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    return _isLoading ? _buildLoadingView() : _buildContent();
  }

  /// 构建加载视图
  Widget _buildLoadingView() {
    return const Center(child: CircularProgressIndicator());
  }

  /// 构建主要内容
  Widget _buildContent() {
    return Column(
      children: [
        // 统计信息卡片
        if (_statistics != null) OrderStatisticsCard(statistics: _statistics!),

        // 状态筛选标签
        _buildStatusTabs(),

        // 订单列表
        Expanded(child: _buildOrderList()),
      ],
    );
  }

  /// 构建状态筛选标签
  Widget _buildStatusTabs() {
    return Container(
      color: ThemeHelper.getCardBackground(context),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start, // 让标签从左边开始
        labelColor: AppColors.primary,
        unselectedLabelColor: ThemeHelper.getTextSecondary(context),
        indicatorColor: AppColors.primary,
        indicatorWeight: 2,
        indicatorSize: TabBarIndicatorSize.label, // 指示器大小跟随标签
        labelStyle: FontUtil.createTabLabelStyle(fontSize: 14),
        unselectedLabelStyle: FontUtil.createTabUnselectedLabelStyle(
          fontSize: 14,
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 16), // 调整标签内边距
        padding: const EdgeInsets.symmetric(horizontal: 16), // 整体内边距
        dividerColor: Colors.transparent, // 移除下方的白色分割线
        tabs: [
          Tab(text: AppLocalizations.of(context).allStatus),
          Tab(text: AppLocalizations.of(context).pendingPayment),
          Tab(text: AppLocalizations.of(context).pendingShipment),
          Tab(text: AppLocalizations.of(context).shipped),
          Tab(text: AppLocalizations.of(context).completed),
          Tab(text: AppLocalizations.of(context).cancelled),
        ],
      ),
    );
  }

  /// 构建订单列表
  Widget _buildOrderList() {
    if (_filteredOrders.isEmpty) {
      return _buildEmptyView();
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredOrders.length,
        itemBuilder: (context, index) {
          final order = _filteredOrders[index];
          return OrderListItem(
            order: order,
            onTap: () => _viewOrderDetail(order),
            onStatusUpdate: () => _loadData(), // 状态更新后重新加载数据
          );
        },
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: ThemeHelper.getTextHint(context),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).noOrdersMessage,
            style: TextStyle(
              fontSize: 16,
              color: ThemeHelper.getTextSecondary(context),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).ordersWillShowHere,
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextHint(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 查看订单详情
  void _viewOrderDetail(ProductOrderModel order) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            ShippingStatusPage(order: order, isDoctorView: true),
      ),
    );
  }
}

/// 订单统计信息模型
class OrderStatisticsModel {
  final int totalOrders;
  final double totalSales;
  final int pendingPayment;
  final int pendingShipment;
  final int shipped;
  final int completed;
  final int cancelled;

  const OrderStatisticsModel({
    this.totalOrders = 0,
    this.totalSales = 0.0,
    this.pendingPayment = 0,
    this.pendingShipment = 0,
    this.shipped = 0,
    this.completed = 0,
    this.cancelled = 0,
  });
}
