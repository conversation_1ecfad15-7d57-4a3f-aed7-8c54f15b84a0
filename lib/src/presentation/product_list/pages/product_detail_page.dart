import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';

import '../../../models/user_product_model.dart';
import '../../../services/user_product_service.dart';
import '../../../services/cart_service.dart';
import '../../../services/login_check_service.dart';
import '../../../services/auth_service.dart';
import '../../../utils/toast_util.dart';
import '../../orders/pages/order_confirm_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 产品详情页面
class ProductDetailPage extends StatefulWidget {
  final int productId;

  const ProductDetailPage({super.key, required this.productId});

  @override
  State<ProductDetailPage> createState() => _ProductDetailPageState();
}

class _ProductDetailPageState extends State<ProductDetailPage> {
  final UserProductService _productService = UserProductService();
  UserProductModel? _product;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadProductDetail();
  }

  /// 加载产品详情
  Future<void> _loadProductDetail() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final product = await _productService.getProductDetail(widget.productId);

      if (mounted) {
        setState(() {
          _product = product;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).productDetail,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: ThemeHelper.getTextPrimary(context),
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _buildBody(),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: ThemeHelper.getTextSecondary(context),
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(
                color: ThemeHelper.getTextSecondary(context),
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadProductDetail,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_product == null) {
      return const Center(child: Text('产品不存在'));
    }

    return Column(
      children: [
        // 可滚动内容
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 商品信息
                _buildProductInfoCard(),
                const SizedBox(height: 20),
                // 价格信息
                _buildPriceInfoSection(),
                const SizedBox(height: 20),
                // 产品详情图片（如果有）
                if (_product!.fullImageUrls.isNotEmpty)
                  _buildProductImagesSection(),
                if (_product!.fullImageUrls.isNotEmpty)
                  const SizedBox(height: 20),
                // 规格信息（如果有）
                if (_product!.specifications != null &&
                    _product!.specifications!.isNotEmpty)
                  _buildSpecificationsSection(),
                if (_product!.specifications != null &&
                    _product!.specifications!.isNotEmpty)
                  const SizedBox(height: 20),
                // 详细说明
                _buildDetailedDescription(),
                // 底部安全区域，避免被固定按钮遮挡
                const SizedBox(height: 100),
              ],
            ),
          ),
        ),
        // 固定在底部的购买按钮
        _buildFixedBottomButtons(),
      ],
    );
  }

  /// 构建商品信息卡片
  Widget _buildProductInfoCard() {
    return Column(
      children: [
        // 上方虚线
        _buildDottedLine(),
        const SizedBox(height: 8),
        // 商品信息卡片
        Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF1A3A2E) // 暗色模式下的深绿色
                : const Color(0xFFF0FFF4), // 亮色模式下的薄荷绿背景
            borderRadius: BorderRadius.circular(30), // 胶囊形状
          ),
          child: Row(
            children: [
              // 商品信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 商品名称
                    Text(
                      _product!.name,
                      style: TextStyle(
                        color: ThemeHelper.getTextPrimary(context),
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: 12),
                    // 商品描述
                    if (_product!.description != null &&
                        _product!.description!.isNotEmpty)
                      Text(
                        _product!.description!,
                        style: TextStyle(
                          color: ThemeHelper.getTextSecondary(context),
                          fontSize: 14,
                          height: 1.4,
                        ),
                      ),
                    if (_product!.description != null &&
                        _product!.description!.isNotEmpty)
                      const SizedBox(height: 8),
                    // 制造商
                    if (_product!.manufacturer != null &&
                        _product!.manufacturer!.isNotEmpty)
                      Text(
                        '${AppLocalizations.of(context).manufacturer}：${_product!.manufacturer}',
                        style: TextStyle(
                          color: ThemeHelper.getTextSecondary(context),
                          fontSize: 12,
                        ),
                      ),
                    if (_product!.manufacturer != null &&
                        _product!.manufacturer!.isNotEmpty)
                      const SizedBox(height: 8),
                    // 医生信息
                    Text(
                      '${AppLocalizations.of(context).doctor}：${_product!.doctorName}',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              // 商品图片
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: _product!.fullMainImageUrl.isNotEmpty
                      ? Image.network(
                          _product!.fullMainImageUrl,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultProductImage();
                          },
                        )
                      : _buildDefaultProductImage(),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // 下方虚线
        _buildDottedLine(),
      ],
    );
  }

  /// 构建虚线分隔线
  Widget _buildDottedLine() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: CustomPaint(
        size: const Size(double.infinity, 1),
        painter: DottedLinePainter(),
      ),
    );
  }

  /// 构建价格信息区域
  Widget _buildPriceInfoSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '${AppLocalizations.of(context).price}：',
                style: TextStyle(
                  color: ThemeHelper.getTextSecondary(context),
                  fontSize: 16,
                ),
              ),
              Row(
                children: [
                  Text(
                    _product!.formattedPrice,
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (_product!.hasDiscount) ...[
                    const SizedBox(width: 8),
                    Text(
                      _product!.formattedOriginalPrice!,
                      style: TextStyle(
                        color: ThemeHelper.getTextHint(context),
                        fontSize: 16,
                        decoration: TextDecoration.lineThrough,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: ThemeHelper.getTextSecondary(context),
              ),
              const SizedBox(width: 4),
              Text(
                '${AppLocalizations.of(context).appointmentTime}：${_formatAppointmentTime(context)}',
                style: TextStyle(
                  color: ThemeHelper.getTextSecondary(context),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建固定在底部的购买按钮
  Widget _buildFixedBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 购物车按钮
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.primary, width: 1.5),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => _handleAddToCart(),
                  borderRadius: BorderRadius.circular(16),
                  child: Icon(
                    Icons.shopping_cart_outlined,
                    color: AppColors.primary,
                    size: 24,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            // 立即购买按钮
            Expanded(
              child: ElevatedButton(
                onPressed: () => _handleBuyNow(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 18),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  AppLocalizations.of(context).buyNow,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建详细说明
  Widget _buildDetailedDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).detailedDescription,
          style: TextStyle(
            color: ThemeHelper.getTextPrimary(context),
            fontSize: 18,
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ThemeHelper.getCardBackground(context),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            _product!.detailedDescription ??
                (_product!.description ?? '暂无详细说明'),
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建默认产品图片
  Widget _buildDefaultProductImage() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(Icons.medical_services, color: AppColors.primary, size: 30),
    );
  }

  /// 构建产品详情图片区域
  Widget _buildProductImagesSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '产品详情图片',
            style: TextStyle(
              color: ThemeHelper.getTextPrimary(context),
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),
          // 图片网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.0,
            ),
            itemCount: _product!.fullImageUrls.length,
            itemBuilder: (context, index) {
              return _buildDetailImage(_product!.fullImageUrls[index], index);
            },
          ),
        ],
      ),
    );
  }

  /// 构建详情图片项
  Widget _buildDetailImage(String imageUrl, int index) {
    return GestureDetector(
      onTap: () => _showImageViewer(index),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.grey[200],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Image.network(
            imageUrl,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: AppColors.primary.withValues(alpha: 0.1),
                ),
                child: Icon(
                  Icons.image_not_supported,
                  color: AppColors.primary,
                  size: 40,
                ),
              );
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.grey[100],
                ),
                child: Center(
                  child: CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                        : null,
                    strokeWidth: 2,
                    color: AppColors.primary,
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 显示图片查看器
  void _showImageViewer(int initialIndex) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.zero,
        child: Stack(
          children: [
            // 图片查看器
            PageView.builder(
              controller: PageController(initialPage: initialIndex),
              itemCount: _product!.fullImageUrls.length,
              itemBuilder: (context, index) {
                return InteractiveViewer(
                  child: Center(
                    child: Image.network(
                      _product!.fullImageUrls[index],
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.grey[600],
                            size: 64,
                          ),
                        );
                      },
                    ),
                  ),
                );
              },
            ),
            // 关闭按钮
            Positioned(
              top: 40,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(Icons.close, color: Colors.white, size: 24),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建规格信息区域
  Widget _buildSpecificationsSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '产品规格',
            style: TextStyle(
              color: ThemeHelper.getTextPrimary(context),
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),
          ...(_product!.specifications!.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 80,
                    child: Text(
                      '${entry.key}：',
                      style: TextStyle(
                        color: ThemeHelper.getTextSecondary(context),
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      entry.value.toString(),
                      style: TextStyle(
                        color: ThemeHelper.getTextPrimary(context),
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList()),
        ],
      ),
    );
  }
}

/// 虚线绘制器
class DottedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary
          .withValues(alpha: 0.8) // 深绿色
      ..strokeWidth = 1.5
      ..style = PaintingStyle.fill;

    const dotRadius = 1.0;
    const dotSpacing = 4.0;

    double currentX = 0;
    while (currentX < size.width) {
      canvas.drawCircle(Offset(currentX, size.height / 2), dotRadius, paint);
      currentX += dotSpacing;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

extension on _ProductDetailPageState {
  /// 处理立即购买
  Future<void> _handleBuyNow() async {
    if (_product == null) return;

    // 调试：检查当前登录状态
    final authService = AuthService();
    print('ProductDetailPage: 检查登录状态');
    print('ProductDetailPage: isLoggedIn = ${authService.isLoggedIn}');
    print('ProductDetailPage: currentUser = ${authService.currentUser}');
    print('ProductDetailPage: token = ${authService.currentUser?.token}');

    // 检查用户是否已登录
    final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
      context,
      featureName: AppLocalizations.of(context).buyNow,
    );
    if (!isLoggedIn) {
      print('ProductDetailPage: 用户未登录，显示登录对话框');
      return;
    }

    print('ProductDetailPage: 用户已登录，显示购买对话框');
    // 显示购买确认对话框
    _showPurchaseDialog();
  }

  /// 显示购买确认对话框
  void _showPurchaseDialog() {
    int selectedQuantity = 1;
    final quantityController = TextEditingController(text: '1');

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            AppLocalizations.of(context).purchaseConfirmation,
            style: FontUtil.createHeadingTextStyle(
              text: AppLocalizations.of(context).purchaseConfirmation,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${AppLocalizations.of(context).productName}：${_product!.name}',
                style: FontUtil.createBodyTextStyle(
                  text:
                      '${AppLocalizations.of(context).productName}：${_product!.name}',
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${AppLocalizations.of(context).unitPrice}：${_product!.formattedPrice}',
                style: FontUtil.createBodyTextStyle(
                  text:
                      '${AppLocalizations.of(context).unitPrice}：${_product!.formattedPrice}',
                ),
              ),
              const SizedBox(height: 16),

              // 数量选择
              Row(
                children: [
                  Text(
                    '${AppLocalizations.of(context).quantity}：',
                    style: FontUtil.createBodyTextStyle(
                      text: '${AppLocalizations.of(context).quantity}：',
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 减少按钮
                        InkWell(
                          onTap: selectedQuantity > 1
                              ? () {
                                  setState(() {
                                    selectedQuantity--;
                                  });
                                }
                              : null,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            child: Icon(
                              Icons.remove,
                              size: 18,
                              color: selectedQuantity > 1
                                  ? Colors.black
                                  : Colors.grey,
                            ),
                          ),
                        ),
                        // 数量输入
                        GestureDetector(
                          onTap: () => _showQuantityInputDialog(
                            context,
                            selectedQuantity,
                            quantityController,
                            (newQuantity) {
                              setState(() {
                                selectedQuantity = newQuantity;
                                quantityController.text = newQuantity
                                    .toString();
                              });
                            },
                          ),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            child: Text(
                              selectedQuantity.toString(),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        // 增加按钮
                        InkWell(
                          onTap: selectedQuantity < 99
                              ? () {
                                  setState(() {
                                    selectedQuantity++;
                                  });
                                }
                              : null,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            child: Icon(
                              Icons.add,
                              size: 18,
                              color: selectedQuantity < 99
                                  ? Colors.black
                                  : Colors.grey,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // 总价显示
              Row(
                children: [
                  Text(
                    '${AppLocalizations.of(context).totalAmount}：',
                    style: FontUtil.createBodyTextStyle(
                      text: '${AppLocalizations.of(context).totalAmount}：',
                    ),
                  ),
                  Text(
                    '¥${(_product!.price * selectedQuantity).toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF109D58),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).purchaseConfirmationMessage,
                style: FontUtil.createCaptionTextStyle(
                  text: AppLocalizations.of(
                    context,
                  ).purchaseConfirmationMessage,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                AppLocalizations.of(context).cancel,
                style: FontUtil.createBodyTextStyle(
                  text: AppLocalizations.of(context).cancel,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToOrderConfirm(selectedQuantity);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: Text(
                AppLocalizations.of(context).confirmPurchase,
                style: FontUtil.createBodyTextStyle(
                  text: AppLocalizations.of(context).confirmPurchase,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示数量输入对话框
  void _showQuantityInputDialog(
    BuildContext context,
    int currentQuantity,
    TextEditingController controller,
    Function(int) onQuantityChanged,
  ) {
    controller.text = currentQuantity.toString();
    controller.selection = TextSelection.fromPosition(
      TextPosition(offset: controller.text.length),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(context).selectQuantity,
          style: FontUtil.createHeadingTextStyle(
            text: AppLocalizations.of(context).selectQuantity,
          ),
        ),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          autofocus: true,
          decoration: InputDecoration(
            labelText: AppLocalizations.of(context).quantity,
            hintText: AppLocalizations.of(context).quantityRange,
            border: const OutlineInputBorder(),
          ),
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: FontUtil.createBodyTextStyle(
                text: AppLocalizations.of(context).cancel,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final inputText = controller.text.trim();
              if (inputText.isEmpty) {
                return;
              }

              final quantity = int.tryParse(inputText);
              if (quantity == null || quantity < 1 || quantity > 99) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(AppLocalizations.of(context).quantityRange),
                  ),
                );
                return;
              }

              onQuantityChanged(quantity);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.primary),
            child: Text(
              AppLocalizations.of(context).confirm,
              style: FontUtil.createBodyTextStyle(
                text: AppLocalizations.of(context).confirm,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 导航到订单确认页面
  void _navigateToOrderConfirm(int quantity) {
    if (_product != null && mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) =>
              OrderConfirmPage(product: _product!, quantity: quantity),
        ),
      );
    }
  }

  /// 处理添加到购物车
  Future<void> _handleAddToCart() async {
    if (_product == null) return;

    // 检查用户是否已登录
    final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
      context,
      featureName: AppLocalizations.of(context).addToCart,
    );
    if (!isLoggedIn) {
      return;
    }

    try {
      // 显示数量选择对话框
      final quantity = await _showQuantityDialog();
      if (quantity == null || quantity <= 0) return;

      // 调用购物车服务添加商品
      final cartService = CartService();
      await cartService.addToCart(productId: _product!.id, quantity: quantity);

      if (mounted) {
        ToastUtil.show(context, '已添加到购物车');
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '添加失败: ${e.toString()}');
      }
    }
  }

  /// 显示数量选择对话框
  Future<int?> _showQuantityDialog() async {
    int selectedQuantity = 1;

    return showDialog<int>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(AppLocalizations.of(context).selectQuantity),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _product!.name,
                style: const TextStyle(fontSize: 16),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 减少按钮
                  IconButton(
                    onPressed: selectedQuantity > 1
                        ? () => setState(() => selectedQuantity--)
                        : null,
                    icon: const Icon(Icons.remove),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                    ),
                  ),

                  // 数量显示
                  Container(
                    width: 60,
                    alignment: Alignment.center,
                    child: Text(
                      selectedQuantity.toString(),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  // 增加按钮
                  IconButton(
                    onPressed: selectedQuantity < _product!.inventoryCount
                        ? () => setState(() => selectedQuantity++)
                        : null,
                    icon: const Icon(Icons.add),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '库存: ${_product!.inventoryCount}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context).cancel),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(selectedQuantity),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: Text(AppLocalizations.of(context).confirm),
            ),
          ],
        ),
      ),
    );
  }

  /// 格式化预约时间显示
  String _formatAppointmentTime(BuildContext context) {
    // 这里可以根据实际的预约时间数据进行格式化
    // 目前使用示例数据
    final dayOfWeek = AppLocalizations.of(context).wednesday;
    final timeOfDay = AppLocalizations.of(context).morning;
    return '$dayOfWeek, $timeOfDay 10:00';
  }
}
