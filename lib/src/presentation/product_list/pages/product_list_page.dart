import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/widgets/dynamic_direction_text_field.dart';
import '../../../common/utils/font_util.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../widgets/doctor_list_widget.dart';
import '../widgets/category_product_list_widget.dart';

/// 产品列表页面 - 包含搜索框和医生/产品标签页
class ProductListPage extends StatefulWidget {
  const ProductListPage({super.key});

  @override
  State<ProductListPage> createState() => _ProductListPageState();
}

class _ProductListPageState extends State<ProductListPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).searchPageTitle,
          style: TextStyle(
            color: ThemeHelper.getTextPrimary(context),
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        centerTitle: true,
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          // 搜索框
          _buildSearchBar(),
          // 标签页
          _buildTabBar(),
          // 标签页内容
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                DoctorListWidget(searchQuery: _searchQuery),
                CategoryProductListWidget(searchQuery: _searchQuery),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建搜索框
  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DynamicDirectionTextFieldPlain(
        controller: _searchController,
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: AppLocalizations.of(context).searchHint,
          hintStyle: TextStyle(
            color: ThemeHelper.getTextSecondary(context),
            fontSize: 16,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: ThemeHelper.getTextSecondary(context),
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        style: TextStyle(
          color: ThemeHelper.getTextPrimary(context),
          fontSize: 16,
        ),
      ),
    );
  }

  /// 构建标签栏
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(8),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.all(4),
        labelColor: Colors.white,
        unselectedLabelColor: ThemeHelper.getTextSecondary(context),
        labelStyle: FontUtil.createTabLabelStyle(
          text: AppLocalizations.of(context).doctorTab,
          fontSize: 16,
        ),
        unselectedLabelStyle: FontUtil.createTabUnselectedLabelStyle(
          text: AppLocalizations.of(context).doctorTab,
          fontSize: 16,
        ),
        dividerColor: Colors.transparent, // 移除底部分割线
        overlayColor: WidgetStateProperty.all(Colors.transparent), // 移除点击效果
        tabs: [
          Tab(text: AppLocalizations.of(context).doctorTab),
          Tab(text: AppLocalizations.of(context).productTab),
        ],
      ),
    );
  }
}
