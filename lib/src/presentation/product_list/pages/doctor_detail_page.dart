import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../models/doctor_model.dart';
import '../../../services/doctor_interaction_service.dart';
import '../../../services/doctor_service.dart';
import '../../../../generated/l10n/app_localizations.dart';

import '../widgets/recommended_products_widget.dart';

/// 医生详情页面
class DoctorDetailPage extends StatefulWidget {
  final DoctorModel doctor;

  const DoctorDetailPage({super.key, required this.doctor});

  @override
  State<DoctorDetailPage> createState() => _DoctorDetailPageState();
}

class _DoctorDetailPageState extends State<DoctorDetailPage> {
  final DoctorInteractionService _interactionService =
      DoctorInteractionService();
  final DoctorService _doctorService = DoctorService();

  late DoctorModel _currentDoctor; // 当前显示的医生信息
  bool _isLiked = false;
  bool _isFavorited = false;
  int _likesCount = 0;
  int _favoritesCount = 0;
  bool _isLoading = false;
  bool _isLoadingDetail = true; // 初始状态为加载中

  @override
  void initState() {
    super.initState();
    _currentDoctor = widget.doctor; // 初始化当前医生信息
    _initializeDoctorStatus();
    _loadDoctorDetail(); // 重新获取完整的医生详情
  }

  /// 初始化医生状态
  void _initializeDoctorStatus() {
    // 从医生模型中获取初始状态
    _isLiked = _currentDoctor.isLiked ?? false;
    _isFavorited = _currentDoctor.isFavorited ?? false;
    _likesCount = _currentDoctor.likesCount;
    _favoritesCount = _currentDoctor.favoritesCount;

    // 如果没有互动状态信息，从API获取
    if (_currentDoctor.isLiked == null || _currentDoctor.isFavorited == null) {
      _loadDoctorStatus();
    }
  }

  /// 从API重新获取医生详细信息
  Future<void> _loadDoctorDetail() async {
    try {
      setState(() {
        _isLoadingDetail = true;
      });

      // 先尝试从DoctorService的缓存中获取完整信息
      final cachedDoctor = _doctorService.getDoctorById(_currentDoctor.id);
      if (cachedDoctor != null) {
        if (mounted) {
          setState(() {
            _currentDoctor = cachedDoctor;
            _isLoadingDetail = false;
            // 更新互动状态
            _isLiked = cachedDoctor.isLiked ?? _isLiked;
            _isFavorited = cachedDoctor.isFavorited ?? _isFavorited;
            _likesCount = cachedDoctor.likesCount;
            _favoritesCount = cachedDoctor.favoritesCount;
          });
        }
        return;
      }

      // 如果缓存中没有，尝试获取包含互动状态的医生信息
      final detailedDoctor = await _doctorService.getDoctorWithInteractionById(
        _currentDoctor.id,
      );
      if (detailedDoctor == null) {
        // 如果获取失败，使用当前医生信息
        if (mounted) {
          setState(() {
            _isLoadingDetail = false;
          });
        }
        return;
      }

      if (mounted) {
        setState(() {
          _currentDoctor = detailedDoctor;
          _isLoadingDetail = false;
          // 更新互动状态
          _isLiked = detailedDoctor.isLiked ?? _isLiked;
          _isFavorited = detailedDoctor.isFavorited ?? _isFavorited;
          _likesCount = detailedDoctor.likesCount;
          _favoritesCount = detailedDoctor.favoritesCount;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingDetail = false;
        });
        // 静默处理错误，继续使用传入的医生信息
        print('获取医生详情失败: $e');
      }
    }
  }

  /// 从API加载医生状态
  Future<void> _loadDoctorStatus() async {
    try {
      final status = await _interactionService.getDoctorStatus(
        _currentDoctor.id,
      );
      if (mounted) {
        setState(() {
          _isLiked = status['is_liked'] ?? false;
          _isFavorited = status['is_favorited'] ?? false;
          _likesCount = status['likes_count'] ?? 0;
          _favoritesCount = status['favorites_count'] ?? 0;
        });
      }
    } catch (e) {
      // 静默处理错误，使用默认值
    }
  }

  /// 切换点赞状态
  Future<void> _toggleLike() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isLiked) {
        await _interactionService.unlikeDoctor(_currentDoctor.id);
        setState(() {
          _isLiked = false;
          _likesCount = (_likesCount - 1).clamp(0, double.infinity).toInt();
        });
        // 取消点赞时不显示提示
      } else {
        await _interactionService.likeDoctor(_currentDoctor.id);
        setState(() {
          _isLiked = true;
          _likesCount++;
        });
        if (mounted) {
          _showSnackBar(AppLocalizations.of(context).likeSuccess);
        }
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('${AppLocalizations.of(context).operationFailed}: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 切换收藏状态
  Future<void> _toggleFavorite() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isFavorited) {
        await _interactionService.unfavoriteDoctor(_currentDoctor.id);
        setState(() {
          _isFavorited = false;
          _favoritesCount = (_favoritesCount - 1)
              .clamp(0, double.infinity)
              .toInt();
        });
        // 取消收藏时不显示提示
      } else {
        await _interactionService.favoriteDoctor(_currentDoctor.id);
        setState(() {
          _isFavorited = true;
          _favoritesCount++;
        });
        if (mounted) {
          _showSnackBar(AppLocalizations.of(context).favoriteSuccess);
        }
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('${AppLocalizations.of(context).operationFailed}: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), duration: const Duration(seconds: 2)),
      );
    }
  }

  /// 处理咨询医生
  void _handleConsultDoctor() {
    // 直接导航到健康助手页面
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/home',
      (route) => false,
      arguments: {
        'selectedDoctorId': _currentDoctor.id,
        'switchToHealthAssistant': true,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      body: CustomScrollView(
        slivers: [
          // 自定义AppBar
          _buildSliverAppBar(),
          // 医生信息卡片
          SliverToBoxAdapter(child: _buildDoctorInfoCard()),
          // 医生详情
          SliverToBoxAdapter(child: _buildDoctorDetails()),
          // 联系方式
          SliverToBoxAdapter(child: _buildContactInfo()),
          // 擅长领域
          SliverToBoxAdapter(child: _buildSpecialties()),
          // 医生推荐
          SliverToBoxAdapter(child: _buildRecommendedProducts()),
          // 底部间距
          const SliverToBoxAdapter(child: SizedBox(height: 20)),
        ],
      ),
    );
  }

  /// 构建SliverAppBar
  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: AppColors.primary,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        AppLocalizations.of(context).doctorDetails,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        // 点赞按钮
        IconButton(
          icon: Icon(
            _isLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
            color: _isLiked ? Colors.amber : Colors.white,
          ),
          onPressed: _isLoading ? null : _toggleLike,
        ),
        // 收藏按钮
        IconButton(
          icon: Icon(
            _isFavorited ? Icons.favorite : Icons.favorite_border,
            color: _isFavorited ? Colors.red : Colors.white,
          ),
          onPressed: _isLoading ? null : _toggleFavorite,
        ),
        IconButton(
          icon: const Icon(Icons.location_on, color: Colors.white),
          onPressed: () {
            // TODO: 定位功能
          },
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.primary,
                AppColors.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 60), // AppBar高度补偿
                // 医生头像
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 3),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: ClipOval(
                    child: _currentDoctor.fullAvatarUrl.isNotEmpty
                        ? Image.network(
                            _currentDoctor.fullAvatarUrl,
                            width: 100,
                            height: 100,
                            fit: BoxFit.cover,
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Container(
                                width: 100,
                                height: 100,
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: CircularProgressIndicator(
                                    value:
                                        loadingProgress.expectedTotalBytes !=
                                            null
                                        ? loadingProgress
                                                  .cumulativeBytesLoaded /
                                              loadingProgress
                                                  .expectedTotalBytes!
                                        : null,
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                ),
                              );
                            },
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 100,
                                height: 100,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      AppColors.primary.withValues(alpha: 0.8),
                                      AppColors.primary.withValues(alpha: 0.6),
                                    ],
                                  ),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.person,
                                  color: Colors.white,
                                  size: 50,
                                ),
                              );
                            },
                          )
                        : Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.primary.withValues(alpha: 0.8),
                                  AppColors.primary.withValues(alpha: 0.6),
                                ],
                              ),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 50,
                            ),
                          ),
                  ),
                ),
                const SizedBox(height: 16),
                // 经验和职称标签
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${_currentDoctor.experience} ${AppLocalizations.of(context).years} • ${AppLocalizations.of(context).physician}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建医生信息卡片
  Widget _buildDoctorInfoCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 医生姓名
          Text(
            _currentDoctor.name,
            style: TextStyle(
              color: ThemeHelper.getTextPrimary(context),
              fontSize: 24,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 8),
          // 专业领域
          Text(
            _currentDoctor.specialization,
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),
          // 评分、时间、咨询 - 使用Wrap避免溢出
          Wrap(
            spacing: 12,
            runSpacing: 8,
            alignment: WrapAlignment.spaceBetween,
            children: [
              // 评分
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ...List.generate(5, (index) {
                    return Icon(
                      Icons.star,
                      size: 16,
                      color: index < _currentDoctor.rating.floor()
                          ? Colors.amber
                          : Colors.grey.shade300,
                    );
                  }),
                  const SizedBox(width: 4),
                  Text(
                    _currentDoctor.rating.toString(),
                    style: TextStyle(
                      color: ThemeHelper.getTextSecondary(context),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              // 工作时间
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    AppLocalizations.of(context).workingHours,
                    style: TextStyle(
                      color: ThemeHelper.getTextSecondary(context),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              // 点赞数量
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.thumb_up_outlined,
                    size: 16,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _likesCount.toString(),
                    style: TextStyle(
                      color: ThemeHelper.getTextSecondary(context),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              // 收藏数量
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.favorite_border,
                    size: 16,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _favoritesCount.toString(),
                    style: TextStyle(
                      color: ThemeHelper.getTextSecondary(context),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              // 咨询按钮
              InkWell(
                onTap: () {
                  _handleConsultDoctor();
                },
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.chat_bubble_outline,
                        size: 14,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        AppLocalizations.of(context).consultDoctor,
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建医生详情
  Widget _buildDoctorDetails() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _isLoadingDetail
          ? const Center(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: CircularProgressIndicator(),
              ),
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).doctorDetails,
                  style: TextStyle(
                    color: ThemeHelper.getTextPrimary(context),
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  _currentDoctor.safeDetailedInfo,
                  style: TextStyle(
                    color: ThemeHelper.getTextSecondary(context),
                    fontSize: 15,
                    height: 1.5,
                  ),
                ),
              ],
            ),
    );
  }

  /// 构建联系方式信息
  Widget _buildContactInfo() {
    // 如果没有联系方式信息，不显示此卡片
    if ((_currentDoctor.phone == null || _currentDoctor.phone!.isEmpty) &&
        (_currentDoctor.address == null || _currentDoctor.address!.isEmpty)) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // 联系电话
          if (_currentDoctor.phone != null && _currentDoctor.phone!.isNotEmpty)
            _buildContactItem(
              icon: Icons.phone,
              title: AppLocalizations.of(context).contactPhone,
              content: _currentDoctor.phone!,
              onTap: () => _makePhoneCall(_currentDoctor.phone!),
              showActionButton: true,
              actionButtonText: AppLocalizations.of(context).call,
            ),

          // 工作地址
          if (_currentDoctor.address != null &&
              _currentDoctor.address!.isNotEmpty) ...[
            if (_currentDoctor.phone != null &&
                _currentDoctor.phone!.isNotEmpty)
              const SizedBox(height: 12),
            _buildContactItem(
              icon: Icons.location_on,
              title: AppLocalizations.of(context).workAddress,
              content: _currentDoctor.address!,
              onTap: () => _copyToClipboard(
                _currentDoctor.address!,
                AppLocalizations.of(context).workAddress,
              ),
              showActionButton: true,
              actionButtonText: AppLocalizations.of(context).copy,
            ),
          ],
        ],
      ),
    );
  }

  /// 构建联系方式项目
  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String content,
    required VoidCallback? onTap,
    required bool showActionButton,
    required String actionButtonText,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 左侧图标
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: AppColors.primary, size: 24),
          ),
          const SizedBox(width: 16),
          // 中间内容
          Expanded(
            child: InkWell(
              onTap: () => _copyToClipboard(content, title),
              borderRadius: BorderRadius.circular(8),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      content,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: ThemeHelper.getTextPrimary(context),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // 右侧操作按钮
          if (showActionButton && onTap != null) ...[
            const SizedBox(width: 12),
            InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(8),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Text(
                  actionButtonText,
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 复制内容到剪贴板
  Future<void> _copyToClipboard(String content, String title) async {
    if (!mounted) return;

    try {
      await Clipboard.setData(ClipboardData(text: content));

      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).copiedToClipboard(title),
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, AppLocalizations.of(context).copyFailed);
      }
    }
  }

  /// 拨打电话
  Future<void> _makePhoneCall(String phoneNumber) async {
    if (!mounted) return;

    try {
      // 清理电话号码，移除空格和特殊字符
      final cleanPhoneNumber = phoneNumber.replaceAll(
        RegExp(r'[^\d+\-()]'),
        '',
      );
      final Uri phoneUri = Uri(scheme: 'tel', path: cleanPhoneNumber);

      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri, mode: LaunchMode.externalApplication);
      } else {
        // 如果无法启动电话应用，则复制号码到剪贴板
        await Clipboard.setData(ClipboardData(text: cleanPhoneNumber));

        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).cannotOpenPhoneApp,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        // 显示错误提示
        ToastUtil.show(
          context,
          '${AppLocalizations.of(context).operationFailedManualDial}：$phoneNumber',
        );
      }
    }
  }

  /// 构建擅长领域
  Widget _buildSpecialties() {
    // 使用医生模型中的擅长领域列表，如果为空则不显示
    final specialtiesList = _currentDoctor.specialtiesList;

    // 如果没有擅长领域数据，则不显示此部分
    if (specialtiesList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).specialties,
            style: TextStyle(
              color: ThemeHelper.getTextPrimary(context),
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: specialtiesList.map((specialty) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  specialty,
                  style: TextStyle(
                    color: AppColors.primary,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建推荐产品
  Widget _buildRecommendedProducts() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Text(
              AppLocalizations.of(context).doctorRecommendations,
              style: TextStyle(
                color: ThemeHelper.getTextPrimary(context),
                fontSize: 18,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          const SizedBox(height: 12),
          RecommendedProductsWidget(doctorId: _currentDoctor.id),
        ],
      ),
    );
  }
}
