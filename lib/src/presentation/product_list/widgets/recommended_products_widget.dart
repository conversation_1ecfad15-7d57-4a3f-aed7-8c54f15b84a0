import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../services/user_product_service.dart';
import '../../../models/user_product_model.dart';
import '../pages/product_detail_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 推荐产品组件
class RecommendedProductsWidget extends StatefulWidget {
  final int? doctorId;

  const RecommendedProductsWidget({super.key, this.doctorId});

  @override
  State<RecommendedProductsWidget> createState() =>
      _RecommendedProductsWidgetState();
}

class _RecommendedProductsWidgetState extends State<RecommendedProductsWidget> {
  final UserProductService _userProductService = UserProductService();
  List<UserProductModel> _products = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  /// 加载产品数据
  Future<void> _loadProducts() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      print('RecommendedProductsWidget: 开始加载产品数据');
      print('RecommendedProductsWidget: doctorId = ${widget.doctorId}');

      ProductListResponse response;
      if (widget.doctorId != null) {
        // 获取特定医生的产品
        print('RecommendedProductsWidget: 获取医生 ${widget.doctorId} 的产品');
        response = await _userProductService.getDoctorProducts(
          widget.doctorId!,
        );
      } else {
        // 获取所有产品（限制数量）
        print('RecommendedProductsWidget: 获取所有产品');
        response = await _userProductService.getProducts(pageSize: 10);
      }

      print('RecommendedProductsWidget: 成功获取 ${response.products.length} 个产品');

      if (mounted) {
        setState(() {
          _products = response.products;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('RecommendedProductsWidget: 加载产品失败: $e');
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        height: 220,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (_errorMessage != null) {
      return SizedBox(
        height: 220,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: ThemeHelper.getTextSecondary(context),
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                '加载失败',
                style: TextStyle(
                  color: ThemeHelper.getTextSecondary(context),
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _errorMessage ?? '',
                style: TextStyle(
                  color: ThemeHelper.getTextHint(context),
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              TextButton(onPressed: _loadProducts, child: const Text('重试')),
            ],
          ),
        ),
      );
    }

    if (_products.isEmpty) {
      return SizedBox(
        height: 220,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inventory_2_outlined,
                color: ThemeHelper.getTextSecondary(context),
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                '暂无推荐产品',
                style: TextStyle(
                  color: ThemeHelper.getTextSecondary(context),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: 220,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 4),
        itemCount: _products.length,
        itemBuilder: (context, index) {
          return _buildProductCard(context, _products[index]);
        },
      ),
    );
  }

  /// 构建产品卡片
  Widget _buildProductCard(BuildContext context, UserProductModel product) {
    return Container(
      width: 140,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 产品图片
          Container(
            height: 80,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: product.fullMainImageUrl.isNotEmpty
                  ? Image.network(
                      product.fullMainImageUrl,
                      width: double.infinity,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildPlaceholderImage();
                      },
                    )
                  : _buildPlaceholderImage(),
            ),
          ),
          // 产品信息
          Expanded(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(10, 10, 10, 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 产品名称
                  Text(
                    product.name,
                    style: TextStyle(
                      color: ThemeHelper.getTextPrimary(context),
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 3),
                  // 产品描述 - 减少高度
                  SizedBox(
                    height: 28, // 减少高度
                    child: Text(
                      product.description ??
                          AppLocalizations.of(context).noDescription,
                      style: TextStyle(
                        color: ThemeHelper.getTextSecondary(context),
                        fontSize: 11,
                        height: 1.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(height: 3),
                  // 价格信息
                  Row(
                    children: [
                      Text(
                        product.formattedPrice,
                        style: TextStyle(
                          color: AppColors.primary,
                          fontSize: 13,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      if (product.hasDiscount) ...[
                        const SizedBox(width: 3),
                        Text(
                          product.formattedOriginalPrice!,
                          style: TextStyle(
                            color: ThemeHelper.getTextHint(context),
                            fontSize: 9,
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                      ],
                    ],
                  ),
                  const Spacer(), // 使用Spacer推送按钮到底部
                  // 查看详情按钮
                  SizedBox(
                    width: double.infinity,
                    height: 26, // 减少按钮高度
                    child: ElevatedButton(
                      onPressed: () =>
                          _navigateToProductDetail(context, product),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        AppLocalizations.of(context).viewDetails,
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建占位图片
  Widget _buildPlaceholderImage() {
    return Center(
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(Icons.medical_services, color: AppColors.primary, size: 30),
      ),
    );
  }

  /// 导航到产品详情页
  void _navigateToProductDetail(
    BuildContext context,
    UserProductModel product,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductDetailPage(productId: product.id),
      ),
    );
  }
}
