import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../models/user_product_model.dart';
import '../../../services/user_product_cache_service.dart';
import '../pages/product_detail_page.dart';

/// 按分类展开显示的产品列表组件
class CategoryProductListWidget extends StatefulWidget {
  final String searchQuery;

  const CategoryProductListWidget({super.key, required this.searchQuery});

  @override
  State<CategoryProductListWidget> createState() =>
      _CategoryProductListWidgetState();
}

class _CategoryProductListWidgetState extends State<CategoryProductListWidget> {
  final UserProductCacheService _productCacheService =
      UserProductCacheService();

  List<UserProductModel> _allProducts = [];
  List<ProductCategoryModel> _categories = [];
  final Set<String> _expandedCategories = <String>{};
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadDataInstantly();
  }

  @override
  void didUpdateWidget(CategoryProductListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchQuery != widget.searchQuery) {
      _loadDataInstantly();
    }
  }

  /// 立即加载产品数据（优先使用缓存，避免刷新）
  void _loadDataInstantly() {
    // 先尝试同步获取缓存数据
    final cachedProducts = _productCacheService.getCachedProductsSync();
    final cachedCategories = _productCacheService.getCachedCategoriesSync();

    if (cachedProducts.isNotEmpty && cachedCategories.isNotEmpty) {
      // 如果有缓存数据，立即显示
      setState(() {
        _allProducts = cachedProducts;
        _categories = cachedCategories;
        _isLoading = false;
        _errorMessage = null;
      });

      // 后台异步检查更新（不阻塞UI）
      _backgroundRefreshData();
    } else {
      // 如果没有缓存数据，显示加载状态并异步加载
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
      _loadDataAsync();
    }
  }

  /// 后台刷新产品数据（不阻塞UI）
  void _backgroundRefreshData() {
    // 异步后台刷新，不影响当前显示
    Future.microtask(() async {
      try {
        final products = await _productCacheService.getProducts(pageSize: 1000);
        final categories = await _productCacheService.getCategories();

        if (mounted) {
          // 只有数据真正变化时才更新UI
          if (_hasDataChanged(products, categories)) {
            setState(() {
              _allProducts = products;
              _categories = categories;
            });
          }
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
      }
    });
  }

  /// 检查数据是否发生变化
  bool _hasDataChanged(
    List<UserProductModel> newProducts,
    List<ProductCategoryModel> newCategories,
  ) {
    if (_allProducts.length != newProducts.length ||
        _categories.length != newCategories.length) {
      return true;
    }

    // 比较产品ID
    for (int i = 0; i < _allProducts.length; i++) {
      if (_allProducts[i].id != newProducts[i].id) return true;
    }

    // 比较分类名称
    for (int i = 0; i < _categories.length; i++) {
      if (_categories[i].name != newCategories[i].name) return true;
    }

    return false;
  }

  /// 异步加载产品数据（用于首次加载）
  Future<void> _loadDataAsync() async {
    try {
      final products = await _productCacheService.getProducts(pageSize: 1000);
      final categories = await _productCacheService.getCategories();

      if (mounted) {
        setState(() {
          _allProducts = products;
          _categories = categories;
          _isLoading = false;
          _errorMessage = null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 尝试从缓存服务加载数据
      try {
        // 并行加载分类和所有产品
        final results = await Future.wait([
          _productCacheService.getCategories(),
          _productCacheService.getProducts(pageSize: 1000), // 加载所有产品
        ]);

        if (mounted) {
          setState(() {
            _categories = results[0] as List<ProductCategoryModel>;
            _allProducts = results[1] as List<UserProductModel>;
            _isLoading = false;
          });
        }
      } catch (apiError) {
        if (mounted) {
          setState(() {
            _errorMessage = 'API加载失败: $apiError';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  /// 刷新数据
  Future<void> _onRefresh() async {
    await _loadDataAsync();
  }

  /// 获取过滤后的分类列表
  List<ProductCategoryModel> _getFilteredCategories() {
    if (widget.searchQuery.isEmpty) {
      return _categories;
    }

    return _categories.where((category) {
      // 检查分类名称是否匹配
      final categoryMatch = category.name.toLowerCase().contains(
        widget.searchQuery.toLowerCase(),
      );

      // 检查该分类下是否有匹配的产品
      final hasMatchingProducts = _getProductsForCategory(
        category.name,
      ).isNotEmpty;

      return categoryMatch || hasMatchingProducts;
    }).toList();
  }

  /// 获取指定分类下的产品列表
  List<UserProductModel> _getProductsForCategory(String categoryName) {
    var products = _allProducts
        .where((product) => product.category == categoryName)
        .toList();

    // 如果有搜索查询，进一步过滤产品
    if (widget.searchQuery.isNotEmpty) {
      products = products.where((product) {
        final query = widget.searchQuery.toLowerCase();
        return product.name.toLowerCase().contains(query) ||
            (product.description?.toLowerCase().contains(query) ?? false) ||
            (product.manufacturer?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    return products;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return _buildErrorView();
    }

    final filteredCategories = _getFilteredCategories();

    if (filteredCategories.isEmpty) {
      return _buildEmptyView();
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredCategories.length,
        itemBuilder: (context, index) {
          return _buildCategoryCard(filteredCategories[index]);
        },
      ),
    );
  }

  /// 构建分类卡片
  Widget _buildCategoryCard(ProductCategoryModel category) {
    final isExpanded = _expandedCategories.contains(category.name);
    final products = _getProductsForCategory(category.name);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 分类标题
          InkWell(
            onTap: () {
              setState(() {
                if (isExpanded) {
                  _expandedCategories.remove(category.name);
                } else {
                  _expandedCategories.add(category.name);
                }
              });
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // 分类图标
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getCategoryIcon(category.name),
                      color: AppColors.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // 分类信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category.name,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: ThemeHelper.getTextPrimary(context),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          products.isEmpty
                              ? AppLocalizations.of(context).noProducts
                              : AppLocalizations.of(
                                  context,
                                ).productsCount(products.length),
                          style: TextStyle(
                            fontSize: 12,
                            color: ThemeHelper.getTextSecondary(context),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 展开/收起图标
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
                ],
              ),
            ),
          ),
          // 产品列表
          if (isExpanded) ...[
            _buildDottedLine(),
            if (products.isNotEmpty)
              ...products.map((product) => _buildProductItem(product))
            else
              _buildEmptyProductsView(),
          ],
        ],
      ),
    );
  }

  /// 构建产品项
  Widget _buildProductItem(UserProductModel product) {
    return Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _navigateToProductDetail(product),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // 产品图片
                  _buildProductImage(product),
                  const SizedBox(width: 12),

                  // 产品信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 产品名称
                        Text(
                          product.name,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: ThemeHelper.getTextPrimary(context),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),

                        // 产品描述
                        if (product.description != null &&
                            product.description!.isNotEmpty)
                          Text(
                            product.description!,
                            style: TextStyle(
                              fontSize: 12,
                              color: ThemeHelper.getTextSecondary(context),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        const SizedBox(height: 8),

                        // 价格和医生信息
                        Row(
                          children: [
                            Text(
                              product.formattedPrice,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w700,
                                color: Colors.red,
                              ),
                            ),
                            if (product.hasDiscount) ...[
                              const SizedBox(width: 8),
                              Text(
                                product.formattedOriginalPrice!,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: ThemeHelper.getTextHint(context),
                                  decoration: TextDecoration.lineThrough,
                                ),
                              ),
                            ],
                            const Spacer(),
                            Text(
                              product.doctorName,
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        _buildDottedLine(),
      ],
    );
  }

  /// 构建产品图片
  Widget _buildProductImage(UserProductModel product) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: product.fullMainImageUrl.isNotEmpty
            ? Image.network(
                product.fullMainImageUrl,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultImage();
                },
              )
            : _buildDefaultImage(),
      ),
    );
  }

  /// 构建默认图片
  Widget _buildDefaultImage() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: AppColors.primary.withValues(alpha: 0.1),
      ),
      child: Icon(Icons.medical_services, color: AppColors.primary, size: 20),
    );
  }

  /// 构建虚线分隔线
  Widget _buildDottedLine() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: CustomPaint(
        size: const Size(double.infinity, 1),
        painter: DottedLinePainter(),
      ),
    );
  }

  /// 构建空产品视图
  Widget _buildEmptyProductsView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 48,
              color: ThemeHelper.getTextHint(context),
            ),
            const SizedBox(height: 8),
            Text(
              '该分类暂无产品',
              style: TextStyle(
                color: ThemeHelper.getTextHint(context),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误视图
  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: ThemeHelper.getTextSecondary(context),
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage!,
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(onPressed: _loadInitialData, child: const Text('重试')),
        ],
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: ThemeHelper.getTextSecondary(context),
          ),
          const SizedBox(height: 16),
          Text(
            widget.searchQuery.isEmpty
                ? AppLocalizations.of(context).noProductsAvailable
                : AppLocalizations.of(context).noSearchResults,
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取分类图标
  IconData _getCategoryIcon(String categoryName) {
    switch (categoryName) {
      case '感冒药':
      case '感冒':
        return Icons.sick;
      case '消化药':
      case '消化':
        return Icons.restaurant;
      case '止痛药':
      case '止痛':
        return Icons.healing;
      case '保健品':
      case '保健':
        return Icons.health_and_safety;
      case '药品':
        return Icons.medical_services;
      case '营养品':
      case '营养':
        return Icons.fitness_center;
      default:
        return Icons.medical_services;
    }
  }

  /// 导航到产品详情页面
  void _navigateToProductDetail(UserProductModel product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductDetailPage(productId: product.id),
      ),
    );
  }
}

/// 虚线绘制器
class DottedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary.withValues(alpha: 0.8)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.fill;

    const dotRadius = 1.0;
    const dotSpacing = 4.0;

    double currentX = 0;
    while (currentX < size.width) {
      canvas.drawCircle(Offset(currentX, size.height / 2), dotRadius, paint);
      currentX += dotSpacing;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
