import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../utils/toast_util.dart';
import '../../../models/doctor_model.dart';
import '../../../services/doctor_service.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../pages/doctor_detail_page.dart';
import 'doctor_list_skeleton.dart';

/// 医生列表组件
class DoctorListWidget extends StatefulWidget {
  final String searchQuery;

  const DoctorListWidget({super.key, required this.searchQuery});

  @override
  State<DoctorListWidget> createState() => _DoctorListWidgetState();
}

class _DoctorListWidgetState extends State<DoctorListWidget> {
  final DoctorService _doctorService = DoctorService();
  List<DoctorModel> _allDoctors = [];
  List<DoctorModel> _filteredDoctors = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDoctorsInstantly();
  }

  @override
  void didUpdateWidget(DoctorListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchQuery != widget.searchQuery) {
      _filterDoctors();
    }
  }

  /// 立即加载医生数据（优先使用缓存，避免刷新）
  void _loadDoctorsInstantly() {
    // 先尝试同步获取缓存数据
    final cachedDoctors = _doctorService.getCachedDoctorsSync();

    if (cachedDoctors.isNotEmpty) {
      // 如果有缓存数据，立即显示
      setState(() {
        _allDoctors = cachedDoctors;
        _isLoading = false;
      });
      _filterDoctors();

      // 后台异步检查更新（不阻塞UI）
      _backgroundRefreshDoctors();
    } else {
      // 如果没有缓存数据，显示加载状态并异步加载
      setState(() {
        _isLoading = true;
      });
      _loadDoctorsAsync();
    }
  }

  /// 后台刷新医生数据（不阻塞UI）
  void _backgroundRefreshDoctors() {
    // 异步后台刷新，不影响当前显示
    Future.microtask(() async {
      try {
        final doctors = await _doctorService.getDoctors();
        if (mounted && doctors.isNotEmpty) {
          // 只有数据真正变化时才更新UI
          if (_hasDataChanged(doctors)) {
            setState(() {
              _allDoctors = doctors;
            });
            await _filterDoctors();
          }
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
      }
    });
  }

  /// 检查医生数据是否发生变化
  bool _hasDataChanged(List<DoctorModel> newDoctors) {
    if (_allDoctors.length != newDoctors.length) return true;

    for (int i = 0; i < _allDoctors.length; i++) {
      if (_allDoctors[i].id != newDoctors[i].id) return true;
    }

    return false;
  }

  /// 异步加载医生数据（用于首次加载）
  Future<void> _loadDoctorsAsync() async {
    try {
      final doctors = await _doctorService.getDoctors();
      if (mounted) {
        setState(() {
          _allDoctors = doctors;
          _isLoading = false;
        });
        await _filterDoctors();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 过滤医生列表
  Future<void> _filterDoctors() async {
    if (widget.searchQuery.isEmpty) {
      setState(() {
        _filteredDoctors = _allDoctors;
      });
    } else {
      final filtered = await DoctorService().searchDoctors(widget.searchQuery);
      if (mounted) {
        setState(() {
          _filteredDoctors = filtered;
        });
      }
    }
  }

  /// 处理咨询医生
  void _handleConsultDoctor(BuildContext context, DoctorModel doctor) {
    print('🩺 点击咨询医生: ${doctor.name} (ID: ${doctor.id})');

    // 直接导航到健康助手页面
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/home',
      (route) => false,
      arguments: {
        'selectedDoctorId': doctor.id,
        'switchToHealthAssistant': true,
      },
    );
    print('🔙 已导航到主页面并传递医生ID: ${doctor.id}');
  }

  @override
  Widget build(BuildContext context) {
    // 如果正在加载，显示骨架动画
    if (_isLoading) {
      return const DoctorListSkeleton();
    }

    // 如果没有数据，显示空状态
    if (_filteredDoctors.isEmpty) {
      return _buildEmptyState(context);
    }

    // 显示医生列表
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredDoctors.length,
      itemBuilder: (context, index) {
        return _buildDoctorCard(context, _filteredDoctors[index]);
      },
    );
  }

  /// 构建医生卡片
  Widget _buildDoctorCard(BuildContext context, DoctorModel doctor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DoctorDetailPage(doctor: doctor),
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // 医生头像
                _buildDoctorAvatar(doctor),
                const SizedBox(width: 16),
                // 医生信息
                Expanded(child: _buildDoctorInfo(context, doctor)),
                // 操作按钮
                _buildActionButtons(context, doctor),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建医生头像
  Widget _buildDoctorAvatar(DoctorModel doctor) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: doctor.fullAvatarUrl.isNotEmpty
            ? Image.network(
                doctor.fullAvatarUrl,
                width: 56,
                height: 56,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primary.withValues(alpha: 0.8),
                          AppColors.primary.withValues(alpha: 0.6),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.person,
                      color: Colors.white,
                      size: 32,
                    ),
                  );
                },
              )
            : Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary.withValues(alpha: 0.8),
                      AppColors.primary.withValues(alpha: 0.6),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(Icons.person, color: Colors.white, size: 32),
              ),
      ),
    );
  }

  /// 构建医生信息
  Widget _buildDoctorInfo(BuildContext context, DoctorModel doctor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 医生姓名和职称
        Row(
          children: [
            Flexible(
              child: Text(
                doctor.name,
                style: TextStyle(
                  color: ThemeHelper.getTextPrimary(context),
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                AppLocalizations.of(context).doctorTitle,
                style: TextStyle(
                  color: AppColors.primary,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // 专科
        Text(
          doctor.specialization,
          style: TextStyle(
            color: ThemeHelper.getTextSecondary(context),
            fontSize: 15,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        // 经验和评分
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.work_outline, size: 14, color: Colors.blue),
                  const SizedBox(width: 4),
                  Text(
                    '${doctor.experience} ${AppLocalizations.of(context).years}',
                    style: TextStyle(
                      color: Colors.blue,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.amber.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.star, size: 14, color: Colors.amber),
                  const SizedBox(width: 4),
                  Text(
                    doctor.rating.toString(),
                    style: TextStyle(
                      color: Colors.amber.shade700,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context, DoctorModel doctor) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 咨询按钮
        SizedBox(
          width: 60,
          height: 32,
          child: ElevatedButton(
            onPressed: () {
              // 阻止事件冒泡
              _handleConsultDoctor(context, doctor);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 0,
            ),
            child: Text(
              AppLocalizations.of(context).consultation,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
          ),
        ),
        const SizedBox(height: 6),
        // 预约按钮
        SizedBox(
          width: 60,
          height: 32,
          child: OutlinedButton(
            onPressed: () {
              // 阻止事件冒泡
              _showDoctorContactDialog(context, doctor);
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: BorderSide(color: AppColors.primary, width: 1),
              padding: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              AppLocalizations.of(context).appointment,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: ThemeHelper.getTextSecondary(context),
          ),
          const SizedBox(height: 16),
          Text(
            widget.searchQuery.isEmpty
                ? AppLocalizations.of(context).noDoctorsAvailable
                : AppLocalizations.of(context).noSearchResults,
            style: TextStyle(
              color: ThemeHelper.getTextSecondary(context),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示医生联系信息弹窗
  void _showDoctorContactDialog(BuildContext context, DoctorModel doctor) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: ThemeHelper.getCardBackground(context),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Row(
                  children: [
                    Icon(Icons.person, color: AppColors.primary, size: 24),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context).doctorInfo,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: ThemeHelper.getTextPrimary(context),
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(
                        Icons.close,
                        color: ThemeHelper.getTextSecondary(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // 医生姓名
                _buildContactInfoItem(
                  context: context,
                  icon: Icons.person_outline,
                  title: AppLocalizations.of(context).doctorName,
                  content: doctor.name,
                  onTap: () => _copyToClipboard(
                    context,
                    doctor.name,
                    AppLocalizations.of(context).doctorName,
                  ),
                ),

                // 联系电话
                if (doctor.phone != null && doctor.phone!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _buildContactInfoItem(
                    context: context,
                    icon: Icons.phone,
                    title: AppLocalizations.of(context).contactPhone,
                    content: doctor.phone!,
                    onTap: () => _makePhoneCall(context, doctor.phone!),
                    showActionButton: true,
                    actionButtonText: AppLocalizations.of(context).call,
                    actionButtonColor: AppColors.primary,
                  ),
                ],

                // 工作地址
                if (doctor.address != null && doctor.address!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _buildContactInfoItem(
                    context: context,
                    icon: Icons.location_on,
                    title: AppLocalizations.of(context).workAddress,
                    content: doctor.address!,
                    onTap: () => _copyToClipboard(
                      context,
                      doctor.address!,
                      AppLocalizations.of(context).workAddress,
                    ),
                  ),
                ],

                const SizedBox(height: 24),

                // 关闭按钮
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '关闭',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建联系信息项目
  Widget _buildContactInfoItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String content,
    required VoidCallback onTap,
    bool showActionButton = false,
    String? actionButtonText,
    Color? actionButtonColor,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ThemeHelper.getCardBackground(context),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: ThemeHelper.getBorder(context).withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // 左侧图标
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(icon, color: AppColors.primary, size: 20),
            ),
            const SizedBox(width: 12),
            // 中间内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: ThemeHelper.getTextSecondary(context),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    content,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: ThemeHelper.getTextPrimary(context),
                    ),
                  ),
                ],
              ),
            ),
            // 右侧操作按钮
            if (showActionButton && actionButtonText != null) ...[
              const SizedBox(width: 8),
              Text(
                actionButtonText,
                style: TextStyle(
                  color: actionButtonColor ?? AppColors.primary,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 复制内容到剪贴板
  Future<void> _copyToClipboard(
    BuildContext context,
    String content,
    String title,
  ) async {
    try {
      await Clipboard.setData(ClipboardData(text: content));
      if (context.mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).copiedToClipboardWithTitle(title),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ToastUtil.show(context, AppLocalizations.of(context).copyFailed);
      }
    }
  }

  /// 拨打电话
  Future<void> _makePhoneCall(BuildContext context, String phoneNumber) async {
    try {
      // 清理电话号码，移除空格和特殊字符
      final cleanPhoneNumber = phoneNumber.replaceAll(
        RegExp(r'[^\d+\-()]'),
        '',
      );
      final Uri phoneUri = Uri(scheme: 'tel', path: cleanPhoneNumber);

      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri, mode: LaunchMode.externalApplication);
      } else {
        // 如果无法启动电话应用，则复制号码到剪贴板
        await Clipboard.setData(ClipboardData(text: cleanPhoneNumber));

        if (context.mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).cannotOpenPhoneAppCopied,
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(
            context,
          ).operationFailedManualDialWithNumber(phoneNumber),
        );
      }
    }
  }
}
