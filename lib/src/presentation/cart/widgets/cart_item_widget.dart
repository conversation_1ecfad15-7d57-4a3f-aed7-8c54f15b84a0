import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../config/themes/app_colors.dart';
import '../../../config/api/api_config.dart';
import '../../../models/cart_model.dart';
import '../../../utils/theme_helper.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 购物车商品项组件
class CartItemWidget extends StatefulWidget {
  final CartItemModel item;
  final bool isEditing;
  final Function(int) onQuantityChanged;
  final Function(bool) onSelectedChanged;
  final VoidCallback onDelete;

  const CartItemWidget({
    super.key,
    required this.item,
    required this.isEditing,
    required this.onQuantityChanged,
    required this.onSelectedChanged,
    required this.onDelete,
  });

  @override
  State<CartItemWidget> createState() => _CartItemWidgetState();
}

class _CartItemWidgetState extends State<CartItemWidget> {
  late TextEditingController _quantityController;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _quantityController = TextEditingController(
      text: widget.item.quantity.toString(),
    );
  }

  @override
  void didUpdateWidget(CartItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.item.quantity != widget.item.quantity) {
      _quantityController.text = widget.item.quantity.toString();
    }
  }

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }

  /// 增加数量
  void _increaseQuantity() {
    if (_isUpdating) return;
    final newQuantity = widget.item.quantity + 1;
    if (newQuantity <= widget.item.productInventory && newQuantity <= 999) {
      _updateQuantity(newQuantity);
    }
  }

  /// 减少数量
  void _decreaseQuantity() {
    if (_isUpdating) return;
    final newQuantity = widget.item.quantity - 1;
    if (newQuantity >= 1) {
      _updateQuantity(newQuantity);
    }
  }

  /// 更新数量
  void _updateQuantity(int quantity) {
    setState(() {
      _isUpdating = true;
    });

    widget.onQuantityChanged(quantity);

    // 延迟重置更新状态，避免快速点击
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    });
  }

  /// 处理数量输入框变化
  void _handleQuantityInputChanged(String value) {
    final quantity = int.tryParse(value);
    if (quantity != null &&
        quantity >= 1 &&
        quantity <= 999 &&
        quantity <= widget.item.productInventory) {
      _updateQuantity(quantity);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ThemeHelper.getBorder(context), width: 0.5),
      ),
      child: Column(
        children: [
          // 主要内容行
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 选择框
              if (!widget.isEditing) ...[
                GestureDetector(
                  onTap: () => widget.onSelectedChanged(!widget.item.selected),
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    child: Icon(
                      widget.item.selected
                          ? Icons.check_circle
                          : Icons.radio_button_unchecked,
                      color: widget.item.selected
                          ? AppColors.primary
                          : ThemeHelper.getTextHint(context),
                      size: 20,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
              ],

              // 商品图片
              _buildProductImage(),
              const SizedBox(width: 12),

              // 商品信息
              Expanded(child: _buildProductInfo()),

              // 删除按钮（编辑模式）
              if (widget.isEditing)
                GestureDetector(
                  onTap: widget.onDelete,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    child: Icon(
                      Icons.delete_outline,
                      color: AppColors.error,
                      size: 20,
                    ),
                  ),
                ),
            ],
          ),

          // 底部操作栏
          if (!widget.isEditing) ...[
            const SizedBox(height: 12),
            _buildBottomActions(),
          ],
        ],
      ),
    );
  }

  /// 构建商品图片
  Widget _buildProductImage() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: ThemeHelper.getIconBackground(context),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child:
            widget.item.productImage != null &&
                widget.item.productImage!.isNotEmpty
            ? Image.network(
                ApiConfig.buildImageUrl(widget.item.productImage!),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildPlaceholderImage(),
              )
            : _buildPlaceholderImage(),
      ),
    );
  }

  /// 构建占位图片
  Widget _buildPlaceholderImage() {
    return Container(
      decoration: BoxDecoration(
        color: ThemeHelper.getIconBackground(context),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        Icons.image_outlined,
        color: ThemeHelper.getTextHint(context),
        size: 32,
      ),
    );
  }

  /// 构建商品信息
  Widget _buildProductInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 商品名称
        Text(
          widget.item.productName,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: ThemeHelper.getTextPrimary(context),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),

        // 医生信息
        Text(
          '${AppLocalizations.of(context).doctor}: ${widget.item.doctorName}',
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextHint(context),
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
        const SizedBox(height: 8),

        // 价格信息
        Row(
          children: [
            Text(
              '¥${widget.item.productPrice.toStringAsFixed(2)}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: 8),
            if (widget.item.productOriginalPrice > widget.item.productPrice)
              Text(
                '¥${widget.item.productOriginalPrice.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextHint(context),
                  decoration: TextDecoration.lineThrough,
                ),
              ),
          ],
        ),

        // 库存状态
        if (!widget.item.isAvailable) ...[
          const SizedBox(height: 4),
          Text('商品已下架', style: TextStyle(fontSize: 12, color: AppColors.error)),
        ] else if (widget.item.productInventory <= 10) ...[
          const SizedBox(height: 4),
          Text(
            '仅剩 ${widget.item.productInventory} 件',
            style: TextStyle(fontSize: 12, color: AppColors.warning),
          ),
        ],
      ],
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 小计
        Text(
          '${AppLocalizations.of(context).subtotal}: ¥${widget.item.subtotal.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: ThemeHelper.getTextSecondary(context),
          ),
        ),

        // 数量控制器
        _buildQuantityController(),
      ],
    );
  }

  /// 构建数量控制器
  Widget _buildQuantityController() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: ThemeHelper.getBorder(context)),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 减少按钮
          GestureDetector(
            onTap: widget.item.quantity > 1 && !_isUpdating
                ? _decreaseQuantity
                : null,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: widget.item.quantity > 1 && !_isUpdating
                    ? AppColors.primary.withValues(alpha: 0.1)
                    : ThemeHelper.getIconBackground(context),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                ),
              ),
              child: Icon(
                Icons.remove,
                size: 16,
                color: widget.item.quantity > 1 && !_isUpdating
                    ? AppColors.primary
                    : ThemeHelper.getTextHint(context),
              ),
            ),
          ),

          // 数量输入框
          Container(
            width: 50,
            height: 32,
            alignment: Alignment.center,
            child: TextField(
              controller: _quantityController,
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(3),
              ],
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextPrimary(context),
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
                isDense: true,
              ),
              onSubmitted: _handleQuantityInputChanged,
              onEditingComplete: () {
                _handleQuantityInputChanged(_quantityController.text);
              },
            ),
          ),

          // 增加按钮
          GestureDetector(
            onTap:
                widget.item.quantity < widget.item.productInventory &&
                    widget.item.quantity < 999 &&
                    !_isUpdating
                ? _increaseQuantity
                : null,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color:
                    widget.item.quantity < widget.item.productInventory &&
                        widget.item.quantity < 999 &&
                        !_isUpdating
                    ? AppColors.primary.withValues(alpha: 0.1)
                    : ThemeHelper.getIconBackground(context),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Icon(
                Icons.add,
                size: 16,
                color:
                    widget.item.quantity < widget.item.productInventory &&
                        widget.item.quantity < 999 &&
                        !_isUpdating
                    ? AppColors.primary
                    : ThemeHelper.getTextHint(context),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
