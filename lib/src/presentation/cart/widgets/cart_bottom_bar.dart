import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../models/cart_model.dart';
import '../../../utils/theme_helper.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 购物车底部操作栏
class CartBottomBar extends StatelessWidget {
  final CartStatisticsModel statistics;
  final bool isEditing;
  final VoidCallback onCheckout;
  final VoidCallback onDeleteSelected;

  const CartBottomBar({
    super.key,
    required this.statistics,
    required this.isEditing,
    required this.onCheckout,
    required this.onDeleteSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        border: Border(
          top: BorderSide(color: ThemeHelper.getBorder(context), width: 0.5),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 左侧统计信息
            Expanded(child: _buildStatistics(context)),

            const SizedBox(width: 16),

            // 右侧操作按钮
            _buildActionButton(context),
          ],
        ),
      ),
    );
  }

  /// 构建统计信息
  Widget _buildStatistics(BuildContext context) {
    if (isEditing) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '已选择 ${statistics.selectedCount} 件商品',
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextSecondary(context),
            ),
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 选中商品数量
        Text(
          AppLocalizations.of(
            context,
          ).selectedItemsCount(statistics.selectedCount),
          style: TextStyle(
            fontSize: 12,
            color: ThemeHelper.getTextHint(context),
          ),
        ),
        const SizedBox(height: 2),

        // 总金额
        Row(
          children: [
            Text(
              AppLocalizations.of(context).totalAmount,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
            Text(
              '¥${statistics.totalAmount.toStringAsFixed(2)}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(BuildContext context) {
    if (isEditing) {
      return _buildDeleteButton(context);
    } else {
      return _buildCheckoutButton(context);
    }
  }

  /// 构建删除按钮
  Widget _buildDeleteButton(BuildContext context) {
    final hasSelected = statistics.selectedCount > 0;

    return SizedBox(
      height: 44,
      child: ElevatedButton(
        onPressed: hasSelected ? onDeleteSelected : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: hasSelected
              ? AppColors.error
              : ThemeHelper.getIconBackground(context),
          foregroundColor: hasSelected
              ? Colors.white
              : ThemeHelper.getTextHint(context),
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(22),
          ),
        ),
        child: Text(
          AppLocalizations.of(
            context,
          ).deleteWithCount(statistics.selectedCount),
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
      ),
    );
  }

  /// 构建结算按钮
  Widget _buildCheckoutButton(BuildContext context) {
    final hasSelected =
        statistics.selectedCount > 0 && statistics.totalAmount > 0;

    return SizedBox(
      height: 44,
      child: ElevatedButton(
        onPressed: hasSelected ? onCheckout : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: hasSelected
              ? AppColors.primary
              : ThemeHelper.getIconBackground(context),
          foregroundColor: hasSelected
              ? Colors.white
              : ThemeHelper.getTextHint(context),
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(22),
          ),
        ),
        child: Text(
          AppLocalizations.of(
            context,
          ).checkoutWithCount(statistics.selectedCount),
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
      ),
    );
  }
}
