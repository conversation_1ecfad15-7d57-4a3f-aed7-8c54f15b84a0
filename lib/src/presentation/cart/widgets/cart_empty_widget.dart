import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../utils/theme_helper.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 购物车空状态组件
class CartEmptyWidget extends StatelessWidget {
  const CartEmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 空状态图标
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.shopping_cart_outlined,
              size: 60,
              color: AppColors.primary.withValues(alpha: 0.6),
            ),
          ),

          const SizedBox(height: 24),

          // 主要提示文字
          Text(
            AppLocalizations.of(context).cartEmpty,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: ThemeHelper.getTextPrimary(context),
            ),
          ),

          const SizedBox(height: 8),

          // 副标题文字
          Text(
            AppLocalizations.of(context).cartEmptyDescription,
            style: TextStyle(
              fontSize: 14,
              color: ThemeHelper.getTextHint(context),
            ),
          ),

          const SizedBox(height: 32),

          // 去购物按钮
          SizedBox(
            width: 160,
            height: 44,
            child: ElevatedButton(
              onPressed: () {
                // 返回到商品列表页面
                Navigator.of(context).pop();

                // 可以考虑导航到商品列表页面
                // Navigator.of(context).pushReplacementNamed('/products');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(22),
                ),
              ),
              child: Text(
                AppLocalizations.of(context).goShopping,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
