import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../config/api/api_config.dart';
import '../../../models/cart_model.dart';
import '../../../models/address_model.dart';
import '../../../services/cart_service.dart';
import '../../../services/address_service.dart';

import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../address/pages/address_list_page.dart';

import '../../product_list/pages/product_detail_page.dart';
import 'cart_batch_payment_page.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// 购物车结算页面
class CartCheckoutPage extends StatefulWidget {
  final List<CartItemModel> selectedItems;
  final CartStatisticsModel statistics;

  const CartCheckoutPage({
    super.key,
    required this.selectedItems,
    required this.statistics,
  });

  @override
  State<CartCheckoutPage> createState() => _CartCheckoutPageState();
}

class _CartCheckoutPageState extends State<CartCheckoutPage> {
  final CartService _cartService = CartService();
  final AddressService _addressService = AddressService();

  AddressModel? _selectedAddress;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDefaultAddress();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// 加载默认地址
  Future<void> _loadDefaultAddress() async {
    try {
      final defaultAddress = await _addressService.getDefaultAddress();
      if (mounted) {
        setState(() {
          _selectedAddress = defaultAddress;
        });
      }
    } catch (e) {
      // 没有默认地址，保持为空
    }
  }

  /// 选择地址
  Future<void> _selectAddress() async {
    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => const AddressListPage(isSelectMode: true),
      ),
    );

    if (result != null && result['address'] != null) {
      setState(() {
        _selectedAddress = AddressModel.fromJson(result['address']);
      });
    }
  }

  /// 提交订单
  Future<void> _submitOrder() async {
    if (_isLoading) return;

    // 验证地址信息
    if (_selectedAddress == null) {
      ToastUtil.show(context, '请选择收货地址');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final cartIds = widget.selectedItems.map((item) => item.id).toList();

      // 使用选择的地址
      final result = await _cartService.checkoutWithAddress(
        addressId: _selectedAddress!.id,
        cartIds: cartIds,
      );

      if (mounted) {
        ToastUtil.show(context, result.message);

        // 导航到批量支付页面
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => CartBatchPaymentPage(
              orderIds: result.orderIds,
              totalAmount: result.totalAmount,
              batchId: result.batchId,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).checkoutFailed(e.toString()),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: const Text('确认订单'),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 地址选择区域
                  _buildAddressSection(),

                  const SizedBox(height: 16),

                  // 商品列表
                  _buildProductList(),

                  const SizedBox(height: 16),

                  // 订单统计
                  _buildOrderSummary(),
                ],
              ),
            ),
          ),

          // 底部提交按钮
          _buildBottomBar(),
        ],
      ),
    );
  }

  /// 构建地址选择区域
  Widget _buildAddressSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ThemeHelper.getBorder(context), width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '收货地址',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 选择已保存的地址
          _buildSavedAddressSelection(),
        ],
      ),
    );
  }

  /// 构建已保存地址选择
  Widget _buildSavedAddressSelection() {
    if (_selectedAddress == null) {
      return GestureDetector(
        onTap: _selectAddress,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: ThemeHelper.getBorder(context)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.add_location_outlined, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                '选择收货地址',
                style: TextStyle(color: AppColors.primary, fontSize: 14),
              ),
              const Spacer(),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: ThemeHelper.getTextHint(context),
              ),
            ],
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: _selectAddress,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
          color: AppColors.primary.withValues(alpha: 0.05),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  _selectedAddress!.receiverName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _selectedAddress!.receiverPhone,
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeHelper.getTextSecondary(context),
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: ThemeHelper.getTextHint(context),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              _selectedAddress!.fullAddress,
              style: TextStyle(
                fontSize: 14,
                color: ThemeHelper.getTextSecondary(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建商品列表
  Widget _buildProductList() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ThemeHelper.getBorder(context), width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.shopping_bag_outlined,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '商品清单',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 商品列表
          ...widget.selectedItems.map((item) => _buildProductItem(item)),
        ],
      ),
    );
  }

  /// 构建商品项
  Widget _buildProductItem(CartItemModel item) {
    return GestureDetector(
      onTap: () {
        // 跳转到商品详情页
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ProductDetailPage(productId: item.productId),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: ThemeHelper.getIconBackground(context),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // 商品图片
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: ThemeHelper.getBackground(context),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child:
                    item.productImage != null && item.productImage!.isNotEmpty
                    ? Image.network(
                        ApiConfig.buildImageUrl(item.productImage!),
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Icon(
                          Icons.image_outlined,
                          color: ThemeHelper.getTextHint(context),
                        ),
                      )
                    : Icon(
                        Icons.image_outlined,
                        color: ThemeHelper.getTextHint(context),
                      ),
              ),
            ),

            const SizedBox(width: 12),

            // 商品信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.productName,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: ThemeHelper.getTextPrimary(context),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    '医生: ${item.doctorName}',
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeHelper.getTextHint(context),
                    ),
                  ),
                ],
              ),
            ),

            // 数量和价格
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '¥${item.productPrice.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'x${item.quantity}',
                  style: TextStyle(
                    fontSize: 12,
                    color: ThemeHelper.getTextHint(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建订单统计
  Widget _buildOrderSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ThemeHelper.getBorder(context), width: 0.5),
      ),
      child: Column(
        children: [
          _buildSummaryRow(
            AppLocalizations.of(context).productQuantity,
            '${widget.statistics.selectedQuantity} ${AppLocalizations.of(context).items}',
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            AppLocalizations.of(context).productTotalPrice,
            '¥${widget.statistics.totalAmount.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            AppLocalizations.of(context).shippingFee,
            AppLocalizations.of(context).free,
          ),
          const Divider(height: 16),
          _buildSummaryRow(
            AppLocalizations.of(context).actualPayment,
            '¥${widget.statistics.totalAmount.toStringAsFixed(2)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  /// 构建统计行
  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: isTotal
                ? ThemeHelper.getTextPrimary(context)
                : ThemeHelper.getTextSecondary(context),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 18 : 14,
            fontWeight: FontWeight.bold,
            color: isTotal
                ? AppColors.primary
                : ThemeHelper.getTextPrimary(context),
          ),
        ),
      ],
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        border: Border(
          top: BorderSide(color: ThemeHelper.getBorder(context), width: 0.5),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 总价显示
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '合计: ¥${widget.statistics.totalAmount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  Text(
                    '共 ${widget.statistics.selectedCount} 件商品',
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeHelper.getTextHint(context),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // 提交订单按钮
            SizedBox(
              height: 44,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _submitOrder,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(22),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Text(
                        '提交订单',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
