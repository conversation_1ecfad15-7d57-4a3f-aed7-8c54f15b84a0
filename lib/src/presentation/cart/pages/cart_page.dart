import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../models/cart_model.dart';
import '../../../services/cart_cache_service.dart';
import '../../../services/login_check_service.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../../common/utils/font_util.dart';
import '../widgets/cart_item_widget.dart';
import '../widgets/cart_bottom_bar.dart';
import '../widgets/cart_empty_widget.dart';
import '../../../../generated/l10n/app_localizations.dart';

import 'cart_checkout_page.dart';

/// 购物车页面
class CartPage extends StatefulWidget {
  const CartPage({super.key});

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  final CartCacheService _cartCacheService = CartCacheService();

  CartListResponseModel? _cartData;
  bool _isLoading = true;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    // 延迟到下一帧执行，确保context完全初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCartDataInstantly();
    });
  }

  /// 加载购物车数据
  Future<void> _loadCartData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      print('CartPage: 开始加载购物车数据');

      // 检查登录状态
      print('CartPage: 检查登录状态...');
      final isLoggedIn = await LoginCheckService.ensureUserIsLoggedIn(
        context,
        featureName: AppLocalizations.of(context).viewShoppingCartFeature,
      );

      print('CartPage: 登录状态检查结果: $isLoggedIn');

      if (!isLoggedIn) {
        print('CartPage: 用户未登录，停止加载购物车');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      print('CartPage: 用户已登录，开始获取购物车列表');
      final cartData = await _cartCacheService.getCartData();
      print('CartPage: 购物车数据获取成功，商品数量: ${cartData?.items.length ?? 0}');

      if (mounted) {
        setState(() {
          _cartData = cartData;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('CartPage: 加载购物车失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ToastUtil.show(context, '加载购物车失败: ${e.toString()}');
      }
    }
  }

  /// 立即加载购物车数据（优先使用缓存，避免刷新）
  void _loadCartDataInstantly() {
    // 先尝试同步获取缓存数据
    final cachedCart = _cartCacheService.getCachedCartSync();

    if (cachedCart != null) {
      // 如果有缓存数据，立即显示
      setState(() {
        _cartData = cachedCart;
        _isLoading = false;
      });

      // 后台异步检查更新（不阻塞UI）
      _backgroundRefreshCart();
    } else {
      // 如果没有缓存数据，显示加载状态并异步加载
      setState(() {
        _isLoading = true;
      });
      _loadCartData();
    }
  }

  /// 后台刷新购物车数据（不阻塞UI）
  void _backgroundRefreshCart() {
    // 异步后台刷新，不影响当前显示
    Future.microtask(() async {
      try {
        final cartData = await _cartCacheService.getCartData();

        if (mounted && cartData != null) {
          // 只有数据真正变化时才更新UI
          if (_hasCartDataChanged(cartData)) {
            setState(() {
              _cartData = cartData;
            });
          }
        }
      } catch (e) {
        // 后台刷新失败时静默处理，不影响用户体验
      }
    });
  }

  /// 检查购物车数据是否发生变化
  bool _hasCartDataChanged(CartListResponseModel newCart) {
    if (_cartData == null) return true;
    if (_cartData!.items.length != newCart.items.length) return true;
    if (_cartData!.statistics.totalAmount != newCart.statistics.totalAmount) {
      return true;
    }
    if (_cartData!.statistics.totalQuantity !=
        newCart.statistics.totalQuantity) {
      return true;
    }

    // 比较商品ID和数量
    for (int i = 0; i < _cartData!.items.length; i++) {
      if (_cartData!.items[i].id != newCart.items[i].id ||
          _cartData!.items[i].quantity != newCart.items[i].quantity) {
        return true;
      }
    }

    return false;
  }

  /// 切换编辑模式
  void _toggleEditMode() {
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  /// 全选/取消全选
  Future<void> _toggleSelectAll() async {
    if (_cartData == null || _cartData!.items.isEmpty) return;

    final allSelected = _cartData!.items.every((item) => item.selected);
    final cartIds = _cartData!.items.map((item) => item.id).toList();
    final newSelectedState = !allSelected;

    // 保存原始状态以便回滚
    final originalCartData = _cartData!;

    try {
      // 先更新本地UI状态
      final updatedItems = _cartData!.items
          .map((item) => item.copyWith(selected: newSelectedState))
          .toList();

      setState(() {
        _cartData = _cartData!.copyWith(
          items: updatedItems,
          statistics: _calculateStatistics(updatedItems),
        );
      });

      // 后台发送请求并刷新缓存
      for (final cartId in cartIds) {
        await _cartCacheService.updateCartItem(
          cartId: cartId,
          selected: newSelectedState,
        );
      }
    } catch (e) {
      // 如果请求失败，恢复原来的状态
      setState(() {
        _cartData = originalCartData;
      });

      if (mounted) {
        ToastUtil.show(context, '操作失败: ${e.toString()}');
      }
    }
  }

  /// 删除选中商品
  Future<void> _deleteSelectedItems() async {
    if (_cartData == null) return;

    final selectedItems = _cartData!.items
        .where((item) => item.selected)
        .toList();
    if (selectedItems.isEmpty) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseSelectItemsToDelete,
      );
      return;
    }

    // 显示确认对话框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppLocalizations.of(context).confirmDelete,
          style: FontUtil.createHeadingTextStyle(
            text: AppLocalizations.of(context).confirmDelete,
          ),
        ),
        content: Text(
          AppLocalizations.of(context).confirmDeleteItems(selectedItems.length),
          style: FontUtil.createBodyTextStyle(
            text: AppLocalizations.of(
              context,
            ).confirmDeleteItems(selectedItems.length),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              AppLocalizations.of(context).cancel,
              style: FontUtil.createButtonTextStyle(
                text: AppLocalizations.of(context).cancel,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: Text(
              AppLocalizations.of(context).delete,
              style: FontUtil.createButtonTextStyle(
                text: AppLocalizations.of(context).delete,
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final cartIds = selectedItems.map((item) => item.id).toList();
        await _cartCacheService.deleteSelectedItems(cartIds);

        if (mounted) {
          ToastUtil.show(context, AppLocalizations.of(context).deleteSuccess);
          _toggleEditMode(); // 退出编辑模式
        }
      } catch (e) {
        if (mounted) {
          ToastUtil.show(
            context,
            AppLocalizations.of(context).deleteFailed(e.toString()),
          );
        }
      }
    }
  }

  /// 结算选中商品
  Future<void> _checkoutSelectedItems() async {
    if (_cartData == null) return;

    final selectedItems = _cartData!.items
        .where((item) => item.selected && item.isAvailable)
        .toList();
    if (selectedItems.isEmpty) {
      ToastUtil.show(
        context,
        AppLocalizations.of(context).pleaseSelectItemsToCheckout,
      );
      return;
    }

    // 导航到确认订单页面
    if (mounted) {
      Navigator.of(context)
          .push(
            MaterialPageRoute(
              builder: (context) => CartCheckoutPage(
                selectedItems: selectedItems,
                statistics: _cartData!.statistics,
              ),
            ),
          )
          .then((_) {
            // 从确认订单页面返回后刷新购物车
            _loadCartData();
          });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _cartData != null && _cartData!.items.isNotEmpty
          ? CartBottomBar(
              statistics: _cartData!.statistics,
              isEditing: _isEditing,
              onCheckout: _checkoutSelectedItems,
              onDeleteSelected: _deleteSelectedItems,
            )
          : null,
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        AppLocalizations.of(context).cartTitle,
        style: FontUtil.createAppBarTitleStyle(
          context: context,
          text: AppLocalizations.of(context).cartTitle,
        ),
      ),
      backgroundColor: ThemeHelper.getCardBackground(context),
      elevation: 0,
      actions: [
        if (_cartData != null && _cartData!.items.isNotEmpty)
          TextButton(
            onPressed: _toggleEditMode,
            child: Text(
              _isEditing
                  ? AppLocalizations.of(context).done
                  : AppLocalizations.of(context).edit,
              style: FontUtil.createButtonTextStyle(
                text: _isEditing
                    ? AppLocalizations.of(context).done
                    : AppLocalizations.of(context).edit,
                fontSize: 16,
                color: AppColors.primary,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      );
    }

    if (_cartData == null || _cartData!.items.isEmpty) {
      return const CartEmptyWidget();
    }

    return Column(
      children: [
        // 全选栏
        _buildSelectAllBar(),

        // 商品列表
        Expanded(
          child: RefreshIndicator(
            onRefresh: _loadCartData,
            color: AppColors.primary,
            child: ListView.builder(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 12, // 添加顶部间距
              ),
              itemCount: _cartData!.items.length,
              itemBuilder: (context, index) {
                final item = _cartData!.items[index];
                return CartItemWidget(
                  item: item,
                  isEditing: _isEditing,
                  onQuantityChanged: (quantity) =>
                      _updateItemQuantity(item.id, quantity),
                  onSelectedChanged: (selected) =>
                      _updateItemSelected(item.id, selected),
                  onDelete: () => _deleteItem(item.id),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  /// 构建全选栏
  Widget _buildSelectAllBar() {
    if (_cartData == null || _cartData!.items.isEmpty) {
      return const SizedBox.shrink();
    }

    final allSelected = _cartData!.items.every((item) => item.selected);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        border: Border(
          bottom: BorderSide(color: ThemeHelper.getBorder(context), width: 0.5),
        ),
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: _toggleSelectAll,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  allSelected
                      ? Icons.check_circle
                      : Icons.radio_button_unchecked,
                  color: allSelected
                      ? AppColors.primary
                      : ThemeHelper.getTextHint(context),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).selectAll,
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeHelper.getTextPrimary(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          Text(
            AppLocalizations.of(
              context,
            ).totalItems(_cartData!.statistics.totalCount),
            style: TextStyle(
              fontSize: 12,
              color: ThemeHelper.getTextHint(context),
            ),
          ),
        ],
      ),
    );
  }

  /// 更新商品数量
  Future<void> _updateItemQuantity(int cartId, int quantity) async {
    if (_cartData == null) return;

    // 保存原始状态以便回滚
    final originalCartData = _cartData!;

    try {
      // 先更新本地UI状态
      final updatedItems = _cartData!.items
          .map(
            (item) =>
                item.id == cartId ? item.copyWith(quantity: quantity) : item,
          )
          .toList();

      setState(() {
        _cartData = _cartData!.copyWith(
          items: updatedItems,
          statistics: _calculateStatistics(updatedItems),
        );
      });

      // 后台发送请求并刷新缓存
      await _cartCacheService.updateCartItem(
        cartId: cartId,
        quantity: quantity,
      );
    } catch (e) {
      // 如果请求失败，恢复原来的状态
      setState(() {
        _cartData = originalCartData;
      });

      if (mounted) {
        ToastUtil.show(context, '更新失败: ${e.toString()}');
      }
    }
  }

  /// 更新商品选中状态
  Future<void> _updateItemSelected(int cartId, bool selected) async {
    if (_cartData == null) return;

    // 保存原始状态以便回滚
    final originalCartData = _cartData!;

    try {
      // 先更新本地UI状态
      final updatedItems = _cartData!.items
          .map(
            (item) =>
                item.id == cartId ? item.copyWith(selected: selected) : item,
          )
          .toList();

      setState(() {
        _cartData = _cartData!.copyWith(
          items: updatedItems,
          statistics: _calculateStatistics(updatedItems),
        );
      });

      // 后台发送请求并刷新缓存
      await _cartCacheService.updateCartItem(
        cartId: cartId,
        selected: selected,
      );
    } catch (e) {
      // 如果请求失败，恢复原来的状态
      setState(() {
        _cartData = originalCartData;
      });

      if (mounted) {
        ToastUtil.show(context, '操作失败: ${e.toString()}');
      }
    }
  }

  /// 删除单个商品
  Future<void> _deleteItem(int cartId) async {
    if (_cartData == null) return;

    // 保存原始状态以便回滚
    final originalCartData = _cartData!;

    try {
      // 先更新本地UI状态 - 移除指定商品
      final updatedItems = _cartData!.items
          .where((item) => item.id != cartId)
          .toList();

      setState(() {
        _cartData = _cartData!.copyWith(
          items: updatedItems,
          statistics: _calculateStatistics(updatedItems),
        );
      });

      // 后台发送请求并刷新缓存
      await _cartCacheService.deleteCartItem(cartId);

      if (mounted) {
        ToastUtil.show(context, AppLocalizations.of(context).deleteSuccess);
      }
    } catch (e) {
      // 如果请求失败，恢复原来的状态
      setState(() {
        _cartData = originalCartData;
      });

      if (mounted) {
        ToastUtil.show(
          context,
          AppLocalizations.of(context).deleteFailed(e.toString()),
        );
      }
    }
  }

  /// 计算购物车统计信息
  CartStatisticsModel _calculateStatistics(List<CartItemModel> items) {
    final selectedItems = items.where((item) => item.selected).toList();
    final availableItems = items.where((item) => item.isAvailable).toList();

    final totalQuantity = items.fold<int>(
      0,
      (sum, item) => sum + item.quantity,
    );

    final selectedQuantity = selectedItems.fold<int>(
      0,
      (sum, item) => sum + item.quantity,
    );

    final totalAmount = selectedItems.fold<double>(
      0.0,
      (sum, item) => sum + (item.productPrice * item.quantity),
    );

    return CartStatisticsModel(
      totalCount: items.length,
      totalQuantity: totalQuantity,
      selectedCount: selectedItems.length,
      selectedQuantity: selectedQuantity,
      totalAmount: totalAmount,
      availableCount: availableItems.length,
    );
  }
}
