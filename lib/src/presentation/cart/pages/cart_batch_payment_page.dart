import 'package:flutter/material.dart';
import '../../../config/themes/app_colors.dart';
import '../../../services/payment_service.dart';
import '../../../services/wechat_payment_service.dart';
import '../../../models/payment_model.dart';
import '../../../utils/toast_util.dart';
import '../../../utils/theme_helper.dart';
import '../../orders/pages/my_orders_page.dart';

/// 购物车批量支付页面
class CartBatchPaymentPage extends StatefulWidget {
  final List<int> orderIds;
  final double totalAmount;
  final String batchId;

  const CartBatchPaymentPage({
    super.key,
    required this.orderIds,
    required this.totalAmount,
    required this.batchId,
  });

  @override
  State<CartBatchPaymentPage> createState() => _CartBatchPaymentPageState();
}

class _CartBatchPaymentPageState extends State<CartBatchPaymentPage> {
  final PaymentService _paymentService = PaymentService();
  final WechatPaymentService _wechatPaymentService = WechatPaymentService();

  bool _isProcessing = false;
  final List<PaymentResult> _paymentResults = [];

  @override
  void initState() {
    super.initState();
    // 页面加载后自动开始批量支付流程
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startBatchPayment();
    });
  }

  /// 开始批量支付流程
  Future<void> _startBatchPayment() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
      _paymentResults.clear();
    });

    try {
      // 初始化微信支付
      final isInitialized = await _wechatPaymentService.initialize();
      if (!isInitialized) {
        throw Exception('微信支付初始化失败，请检查是否安装微信');
      }

      // 逐个处理订单支付
      for (int i = 0; i < widget.orderIds.length; i++) {
        final orderId = widget.orderIds[i];

        try {
          // 创建支付请求
          final paymentResponse = await _paymentService.createAppPayment(
            orderId: orderId,
          );

          // 获取APP支付参数
          final appPayParams = paymentResponse.appPayParams;
          if (appPayParams == null) {
            throw Exception('获取支付参数失败');
          }

          // 调用微信支付
          final result = await _wechatPaymentService.pay(appPayParams);

          setState(() {
            _paymentResults.add(result);
          });

          // 如果支付失败，询问用户是否继续
          if (result != PaymentResult.success) {
            final shouldContinue = await _showPaymentFailedDialog(
              orderId,
              i + 1,
            );
            if (!shouldContinue) {
              break;
            }
          }
        } catch (e) {
          setState(() {
            _paymentResults.add(PaymentResult.failed);
          });

          final shouldContinue = await _showPaymentErrorDialog(
            orderId,
            i + 1,
            e.toString(),
          );
          if (!shouldContinue) {
            break;
          }
        }
      }

      // 所有支付完成后的处理
      _handleBatchPaymentComplete();
    } catch (e) {
      if (mounted) {
        ToastUtil.show(context, '批量支付初始化失败: ${e.toString()}');
        _navigateToOrders();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// 显示支付失败对话框
  Future<bool> _showPaymentFailedDialog(int orderId, int orderIndex) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('支付失败'),
            content: Text('订单 $orderId (第 $orderIndex 个) 支付失败，是否继续支付剩余订单？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('停止支付'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: AppColors.primary),
                child: const Text('继续支付'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// 显示支付错误对话框
  Future<bool> _showPaymentErrorDialog(
    int orderId,
    int orderIndex,
    String error,
  ) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('支付错误'),
            content: Text(
              '订单 $orderId (第 $orderIndex 个) 支付出错：$error\n\n是否继续支付剩余订单？',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('停止支付'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: AppColors.primary),
                child: const Text('继续支付'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// 处理批量支付完成
  void _handleBatchPaymentComplete() {
    final successCount = _paymentResults
        .where((result) => result == PaymentResult.success)
        .length;
    final totalCount = widget.orderIds.length;

    String message;
    if (successCount == totalCount) {
      message = '恭喜！所有 $totalCount 个订单支付成功';
    } else if (successCount > 0) {
      message = '部分支付成功：$successCount/$totalCount 个订单支付成功';
    } else {
      message = '支付失败，请稍后重试';
    }

    if (mounted) {
      ToastUtil.show(context, message);
      _navigateToOrders();
    }
  }

  /// 导航到订单页面
  void _navigateToOrders() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const MyOrdersPage()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeHelper.getBackground(context),
      appBar: AppBar(
        title: const Text('批量支付'),
        backgroundColor: ThemeHelper.getCardBackground(context),
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 支付信息卡片
            _buildPaymentInfoCard(),

            const SizedBox(height: 24),

            // 支付进度
            _buildPaymentProgress(),

            const SizedBox(height: 24),

            // 支付状态列表
            Expanded(child: _buildPaymentStatusList()),

            // 底部按钮
            if (!_isProcessing) _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  /// 构建支付信息卡片
  Widget _buildPaymentInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ThemeHelper.getBorder(context), width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.payment, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                '批量支付信息',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '订单数量',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
              Text(
                '${widget.orderIds.length} 个',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '支付总额',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
              Text(
                '¥${widget.totalAmount.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '批次号',
                style: TextStyle(
                  fontSize: 12,
                  color: ThemeHelper.getTextHint(context),
                ),
              ),
              Text(
                widget.batchId,
                style: TextStyle(
                  fontSize: 12,
                  color: ThemeHelper.getTextHint(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建支付进度
  Widget _buildPaymentProgress() {
    final completedCount = _paymentResults.length;
    final totalCount = widget.orderIds.length;
    final progress = totalCount > 0 ? completedCount / totalCount : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ThemeHelper.getBorder(context), width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '支付进度',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: ThemeHelper.getTextPrimary(context),
                ),
              ),
              Text(
                '$completedCount/$totalCount',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeHelper.getTextSecondary(context),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          LinearProgressIndicator(
            value: progress,
            backgroundColor: ThemeHelper.getIconBackground(context),
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            minHeight: 6,
          ),

          if (_isProcessing) ...[
            const SizedBox(height: 8),
            Text(
              '正在处理第 ${completedCount + 1} 个订单...',
              style: TextStyle(
                fontSize: 12,
                color: ThemeHelper.getTextHint(context),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建支付状态列表
  Widget _buildPaymentStatusList() {
    return Container(
      decoration: BoxDecoration(
        color: ThemeHelper.getCardBackground(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ThemeHelper.getBorder(context), width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              '支付状态',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: ThemeHelper.getTextPrimary(context),
              ),
            ),
          ),

          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: widget.orderIds.length,
              itemBuilder: (context, index) {
                final orderId = widget.orderIds[index];
                final isCompleted = index < _paymentResults.length;
                final isProcessing =
                    index == _paymentResults.length && _isProcessing;

                PaymentResult? result;
                if (isCompleted) {
                  result = _paymentResults[index];
                }

                return _buildPaymentStatusItem(
                  orderId: orderId,
                  index: index + 1,
                  result: result,
                  isProcessing: isProcessing,
                );
              },
            ),
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  /// 构建支付状态项
  Widget _buildPaymentStatusItem({
    required int orderId,
    required int index,
    PaymentResult? result,
    bool isProcessing = false,
  }) {
    IconData icon;
    Color iconColor;
    String statusText;

    if (isProcessing) {
      icon = Icons.hourglass_empty;
      iconColor = AppColors.warning;
      statusText = '支付中...';
    } else if (result == null) {
      icon = Icons.radio_button_unchecked;
      iconColor = ThemeHelper.getTextHint(context);
      statusText = '等待支付';
    } else {
      switch (result) {
        case PaymentResult.success:
          icon = Icons.check_circle;
          iconColor = AppColors.success;
          statusText = '支付成功';
          break;
        case PaymentResult.failed:
          icon = Icons.error;
          iconColor = AppColors.error;
          statusText = '支付失败';
          break;
        case PaymentResult.cancelled:
          icon = Icons.cancel;
          iconColor = AppColors.warning;
          statusText = '支付取消';
          break;
        case PaymentResult.unknown:
          icon = Icons.help;
          iconColor = AppColors.warning;
          statusText = '状态未知';
          break;
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ThemeHelper.getIconBackground(context),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: iconColor, size: 20),

          const SizedBox(width: 12),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '订单 $index',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: ThemeHelper.getTextPrimary(context),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '订单号: $orderId',
                  style: TextStyle(
                    fontSize: 12,
                    color: ThemeHelper.getTextHint(context),
                  ),
                ),
              ],
            ),
          ),

          Text(
            statusText,
            style: TextStyle(
              fontSize: 12,
              color: iconColor,
              fontWeight: FontWeight.w500,
            ),
          ),

          if (isProcessing) ...[
            const SizedBox(width: 8),
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions() {
    return SafeArea(
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: _navigateToOrders,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                '查看订单',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
