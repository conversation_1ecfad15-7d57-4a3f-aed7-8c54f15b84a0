import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../../models/conversation_model.dart';
import '../../models/chat_message_model.dart';

/// 聊天缓存服务 - 管理聊天记录的本地数据库操作
class ChatCacheService {
  static final ChatCacheService _instance = ChatCacheService._internal();
  static Database? _database;

  // 单例模式
  factory ChatCacheService() => _instance;

  ChatCacheService._internal();

  // 获取数据库实例
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // 初始化数据库
  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'chat_cache.db');

    return await openDatabase(
      path,
      version: 2, // 增加版本号以触发数据库升级
      onCreate: (db, version) async {
        // 创建对话表
        await db.execute('''
          CREATE TABLE conversations (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL,
            message_count INTEGER DEFAULT 0,
            last_message TEXT,
            last_message_role TEXT,
            doctor_id INTEGER,
            doctor TEXT
          )
        ''');

        // 创建消息表
        await db.execute('''
          CREATE TABLE chat_messages (
            id TEXT PRIMARY KEY,
            conversation_id TEXT NOT NULL,
            role TEXT NOT NULL,
            content TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            is_loading INTEGER DEFAULT 0,
            error TEXT,
            metadata TEXT,
            FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
          )
        ''');

        // 创建索引
        await db.execute('''
          CREATE INDEX idx_messages_conversation_id ON chat_messages (conversation_id)
        ''');

        await db.execute('''
          CREATE INDEX idx_messages_timestamp ON chat_messages (timestamp DESC)
        ''');

        await db.execute('''
          CREATE INDEX idx_conversations_updated_at ON conversations (updated_at DESC)
        ''');
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (oldVersion < 2) {
          // 添加doctor相关字段
          await db.execute('''
            ALTER TABLE conversations ADD COLUMN doctor_id INTEGER
          ''');
          await db.execute('''
            ALTER TABLE conversations ADD COLUMN doctor TEXT
          ''');
        }
      },
    );
  }

  /// 保存对话列表
  Future<void> saveConversations(List<ConversationModel> conversations) async {
    final db = await database;

    await db.transaction((txn) async {
      // 清空现有对话
      await txn.delete('conversations');

      // 批量插入新对话
      for (var conversation in conversations) {
        await txn.insert(
          'conversations',
          conversation.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  /// 获取对话列表
  Future<List<ConversationModel>> getConversations() async {
    final db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'conversations',
      orderBy: 'updated_at DESC',
    );

    return maps.map((map) => ConversationModel.fromMap(map)).toList();
  }

  /// 保存单个对话
  Future<void> saveConversation(ConversationModel conversation) async {
    final db = await database;

    await db.insert(
      'conversations',
      conversation.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// 删除对话
  Future<void> deleteConversation(String conversationId) async {
    final db = await database;

    await db.transaction((txn) async {
      // 删除对话相关的所有消息
      await txn.delete(
        'chat_messages',
        where: 'conversation_id = ?',
        whereArgs: [conversationId],
      );

      // 删除对话
      await txn.delete(
        'conversations',
        where: 'id = ?',
        whereArgs: [conversationId],
      );
    });
  }

  /// 保存消息列表
  Future<void> saveMessages(
    String conversationId,
    List<ChatMessageModel> messages,
  ) async {
    final db = await database;

    await db.transaction((txn) async {
      // 删除该对话的现有消息
      await txn.delete(
        'chat_messages',
        where: 'conversation_id = ?',
        whereArgs: [conversationId],
      );

      // 批量插入新消息
      for (var message in messages) {
        await txn.insert(
          'chat_messages',
          message.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  /// 获取消息列表
  Future<List<ChatMessageModel>> getMessages(
    String conversationId, {
    int? limit,
    int? offset,
  }) async {
    final db = await database;

    final List<Map<String, dynamic>> maps = await db.query(
      'chat_messages',
      where: 'conversation_id = ?',
      whereArgs: [conversationId],
      orderBy: 'timestamp ASC',
      limit: limit,
      offset: offset,
    );

    return maps.map((map) => ChatMessageModel.fromMap(map)).toList();
  }

  /// 添加单条消息
  Future<void> addMessage(ChatMessageModel message) async {
    final db = await database;

    await db.insert(
      'chat_messages',
      message.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // 更新对话的最后消息信息
    await _updateConversationLastMessage(message.conversationId);
  }

  /// 更新消息
  Future<void> updateMessage(ChatMessageModel message) async {
    final db = await database;

    await db.update(
      'chat_messages',
      message.toMap(),
      where: 'id = ?',
      whereArgs: [message.id],
    );

    // 更新对话的最后消息信息
    await _updateConversationLastMessage(message.conversationId);
  }

  /// 删除消息
  Future<void> deleteMessage(String messageId) async {
    final db = await database;

    // 获取消息信息
    final messageMap = await db.query(
      'chat_messages',
      where: 'id = ?',
      whereArgs: [messageId],
      limit: 1,
    );

    if (messageMap.isNotEmpty) {
      final conversationId = messageMap.first['conversation_id'] as String;

      // 删除消息
      await db.delete('chat_messages', where: 'id = ?', whereArgs: [messageId]);

      // 更新对话的最后消息信息
      await _updateConversationLastMessage(conversationId);
    }
  }

  /// 更新对话的最后消息信息
  Future<void> _updateConversationLastMessage(String conversationId) async {
    final db = await database;

    // 获取该对话的最新消息
    final latestMessages = await db.query(
      'chat_messages',
      where: 'conversation_id = ?',
      whereArgs: [conversationId],
      orderBy: 'timestamp DESC',
      limit: 1,
    );

    // 获取消息总数
    final messageCount =
        Sqflite.firstIntValue(
          await db.rawQuery(
            'SELECT COUNT(*) FROM chat_messages WHERE conversation_id = ?',
            [conversationId],
          ),
        ) ??
        0;

    if (latestMessages.isNotEmpty) {
      final latestMessage = ChatMessageModel.fromMap(latestMessages.first);

      await db.update(
        'conversations',
        {
          'updated_at': latestMessage.timestamp.millisecondsSinceEpoch,
          'message_count': messageCount,
          'last_message': latestMessage.textContent,
          'last_message_role': latestMessage.role.name,
        },
        where: 'id = ?',
        whereArgs: [conversationId],
      );
    } else {
      // 如果没有消息，只更新消息数量
      await db.update(
        'conversations',
        {
          'message_count': messageCount,
          'last_message': null,
          'last_message_role': null,
        },
        where: 'id = ?',
        whereArgs: [conversationId],
      );
    }
  }

  /// 清空所有缓存
  Future<void> clearCache() async {
    final db = await database;

    await db.transaction((txn) async {
      await txn.delete('chat_messages');
      await txn.delete('conversations');
    });
  }

  /// 获取对话消息数量
  Future<int> getMessageCount(String conversationId) async {
    final db = await database;

    final count = Sqflite.firstIntValue(
      await db.rawQuery(
        'SELECT COUNT(*) FROM chat_messages WHERE conversation_id = ?',
        [conversationId],
      ),
    );

    return count ?? 0;
  }

  /// 检查对话是否存在
  Future<bool> conversationExists(String conversationId) async {
    final db = await database;

    final count = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM conversations WHERE id = ?', [
        conversationId,
      ]),
    );

    return (count ?? 0) > 0;
  }
}
