import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import '../../config/api/api_config.dart';
import '../../models/conversation_model.dart';
import '../../models/chat_message_model.dart';
import '../../services/auth_service.dart';

/// 聊天API服务 - 处理与后端AI导游聊天相关的网络请求
class ChatApiService {
  static final ChatApiService _instance = ChatApiService._internal();
  factory ChatApiService() => _instance;
  ChatApiService._internal();

  final AuthService _authService = AuthService();

  /// 获取认证请求头
  Map<String, String> _getAuthHeaders() {
    final token = _authService.currentUser?.token;
    if (token == null || token.isEmpty) {
      throw Exception('用户未登录，请先登录');
    }

    // 根据API文档，需要Bearer前缀
    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  /// 获取认证请求头（不包含Content-Type，用于multipart请求）
  Map<String, String> _getAuthHeadersWithoutContentType() {
    final token = _authService.currentUser?.token;
    if (token == null || token.isEmpty) {
      throw Exception('用户未登录，请先登录');
    }

    return {'Authorization': 'Bearer $token'};
  }

  /// 获取对话列表
  Future<List<ConversationModel>> fetchConversations({
    int page = 1,
    int pageSize = 20,
    String? lang,
  }) async {
    try {
      // 构建查询参数
      final queryParams = <String, String>{};
      if (lang != null && lang.isNotEmpty) {
        queryParams['lang'] = lang;
      }

      // 新API不需要分页参数，直接获取所有对话
      final uri = Uri.parse(
        ApiConfig.getConversationsUrl,
      ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await http.get(uri, headers: _getAuthHeaders());

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (jsonData['code'] == 200) {
          // 根据API文档，data字段应该直接是对话数组
          final dynamic dataField = jsonData['data'];
          if (dataField == null) {
            return [];
          }

          if (dataField is List) {
            // 标准API响应格式：{"code": 200, "data": [{"id": 1, "title": "...", "updated_at": "..."}]}
            return dataField
                .map(
                  (json) =>
                      ConversationModel.fromJson(json as Map<String, dynamic>),
                )
                .toList();
          } else {
            // 如果不是数组格式，返回空列表
            return [];
          }
        } else {
          throw Exception('获取对话列表失败: ${jsonData['message']}');
        }
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('获取对话列表异常: $e');
    }
  }

  /// 获取对话消息列表
  Future<List<ChatMessageModel>> fetchMessages(
    String conversationId, {
    int limit = 50,
    String? beforeMessageId,
  }) async {
    try {
      final queryParams = <String, String>{'limit': limit.toString()};

      if (beforeMessageId != null) {
        queryParams['before_message_id'] = beforeMessageId;
      }

      final uri = Uri.parse(
        ApiConfig.getMessagesUrl(conversationId),
      ).replace(queryParameters: queryParams);

      final response = await http.get(uri, headers: _getAuthHeaders());

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (jsonData['code'] == 200) {
          // 新API直接返回消息数组
          final List<dynamic> messagesData = jsonData['data'] as List<dynamic>;
          return messagesData
              .map(
                (json) =>
                    ChatMessageModel.fromJson(json as Map<String, dynamic>),
              )
              .toList();
        } else {
          throw Exception('获取消息列表失败: ${jsonData['message']}');
        }
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('获取消息列表异常: $e');
    }
  }

  /// 解析API响应中的消息数据 - 根据新的API文档格式
  ChatMessageModel _parseMessageFromResponse(Map<String, dynamic> jsonData) {
    final data = jsonData['data'];
    print('data字段: $data');
    print('data字段类型: ${data.runtimeType}');

    if (data is Map<String, dynamic>) {
      // 标准API响应格式：{"data": {"message": {...}, "conversation_id": 1, "success": true}}
      final messageData = data['message'] as Map<String, dynamic>;
      return ChatMessageModel.fromJson(messageData);
    } else if (data is List) {
      // 兼容旧的二维数组格式（如果后端还在使用）
      final dataMap = <String, dynamic>{};
      for (var item in data) {
        print('处理数组项: $item, 类型: ${item.runtimeType}');
        if (item is List && item.length == 2) {
          final key = item[0] as String;
          final value = item[1];
          print('添加到dataMap: $key = $value');
          dataMap[key] = value;
        }
      }
      print('转换后的dataMap: $dataMap');

      final messageData = dataMap['message'] as Map<String, dynamic>;
      return ChatMessageModel.fromJson(messageData);
    } else {
      throw Exception('未知的数据格式: ${data.runtimeType}');
    }
  }

  /// 发送文本消息 (流式响应)
  Stream<ChatMessageModel> sendTextMessageStream({
    required String conversationId,
    required String content,
    required int doctorId,
    List<ChatMessageModel>? history,
    double? latitude,
    double? longitude,
    String? address,
  }) async* {
    try {
      print('🔄 API: 发送流式消息到对话: $conversationId');
      print('📝 API: 消息内容: $content');

      // 构建新消息内容 - 根据API文档格式
      final messageContent = <Map<String, dynamic>>[
        {'text': content, 'image': null},
      ];

      // 构建新API格式的请求数据
      final requestData = <String, dynamic>{
        'newMessage': {'role': 'user', 'content': messageContent},
        'history': history?.map((msg) => msg.toJson()).toList() ?? [],
        'doctor_id': doctorId,
      };

      print('📦 API: 请求数据: ${jsonEncode(requestData)}');

      print('=== 发送流式消息请求详情 ===');
      print('URL: ${ApiConfig.sendMessageUrl(conversationId)}/stream');
      print('Conversation ID: $conversationId');
      print('Headers: ${_getAuthHeaders()}');
      print('Request Body: ${jsonEncode(requestData)}');

      final request = http.Request(
        'POST',
        Uri.parse('${ApiConfig.sendMessageUrl(conversationId)}/stream'),
      );
      request.headers.addAll(_getAuthHeaders());
      request.body = jsonEncode(requestData);

      final streamedResponse = await request.send();

      if (streamedResponse.statusCode == 200) {
        String buffer = '';
        String accumulatedText = '';

        await for (final chunk in streamedResponse.stream.transform(
          utf8.decoder,
        )) {
          buffer += chunk;

          // 处理可能的多行数据
          final lines = buffer.split('\n');
          buffer = lines.removeLast(); // 保留最后一个可能不完整的行

          for (final line in lines) {
            if (line.trim().isEmpty) continue;

            try {
              // 处理SSE格式：data: {...}
              String jsonStr = line.trim();

              if (jsonStr.startsWith('data: ')) {
                jsonStr = jsonStr.substring(6).trim(); // 移除 "data: " 前缀并去除空格
              }

              // 跳过空行和SSE注释
              if (jsonStr.isEmpty || jsonStr.startsWith(':')) continue;

              print('🔍 准备解析JSON: $jsonStr');
              final jsonData = jsonDecode(jsonStr);

              if (jsonData['type'] == 'ai_chunk') {
                // 流式文本块
                final content = jsonData['content'] as String;
                accumulatedText += content;

                // 创建临时消息对象用于显示
                final tempMessage = ChatMessageModel(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  conversationId: '',
                  role: MessageRole.assistant,
                  content: [
                    MessageContent(
                      type: MessageContentType.text,
                      content: accumulatedText,
                    ),
                  ],
                  timestamp: DateTime.now(),
                );

                yield tempMessage;
              } else if (jsonData['type'] == 'ai_complete') {
                // AI回复完成
                final messageData = jsonData['message'] as Map<String, dynamic>;
                final finalMessage = ChatMessageModel.fromJson(messageData);
                yield finalMessage;
                return; // 流式响应完成，结束流
              } else if (jsonData['type'] == 'error') {
                // 处理错误消息
                final errorMessage = jsonData['message'] ?? '未知错误';
                print('❌ 服务器返回错误: $errorMessage');
                throw Exception('服务器错误: $errorMessage');
              }
            } catch (e) {
              print('解析流式响应失败: $e, line: $line');
            }
          }
        }
      } else if (streamedResponse.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${streamedResponse.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('发送流式消息异常: $e');
    }
  }

  /// 发送图片消息 (流式响应)
  Stream<ChatMessageModel> sendImageMessageStream({
    required String conversationId,
    required File imageFile,
    required int doctorId,
    String? description,
    List<ChatMessageModel>? history,
    double? latitude,
    double? longitude,
    String? address,
  }) async* {
    try {
      print('🖼️ API: 开始发送流式图片消息');

      // 先上传图片获取URL
      final imageUrl = await uploadChatImage(imageFile);
      print('📸 API: 图片上传完成，URL: $imageUrl');

      // 构建新消息内容 - 根据API文档格式
      final messageContent = <Map<String, dynamic>>[];

      if (description != null && description.isNotEmpty) {
        // 如果有描述文本，先添加文本内容
        messageContent.add({'text': description, 'image': null});
        // 然后添加图片内容
        messageContent.add({'text': null, 'image': imageUrl});
      } else {
        // 如果没有描述文本，只添加图片内容
        messageContent.add({'text': null, 'image': imageUrl});
      }

      // 构建新API格式的请求数据
      final requestData = <String, dynamic>{
        'newMessage': {'role': 'user', 'content': messageContent},
        'history': history?.map((msg) => msg.toJson()).toList() ?? [],
        'doctor_id': doctorId,
      };

      print('📤 API: 发送流式图片消息请求');
      final request = http.Request(
        'POST',
        Uri.parse('${ApiConfig.sendMessageUrl(conversationId)}/stream'),
      );
      request.headers.addAll(_getAuthHeaders());
      request.body = jsonEncode(requestData);

      final streamedResponse = await request.send();

      if (streamedResponse.statusCode == 200) {
        String accumulatedText = '';

        await for (final chunk in streamedResponse.stream.transform(
          utf8.decoder,
        )) {
          final lines = chunk.split('\n');
          for (final line in lines) {
            if (line.trim().isEmpty) continue;

            try {
              // 处理SSE格式：data: {...}
              String jsonStr = line.trim();

              if (jsonStr.startsWith('data: ')) {
                jsonStr = jsonStr.substring(6).trim(); // 移除 "data: " 前缀并去除空格
              }

              // 跳过空行和SSE注释
              if (jsonStr.isEmpty || jsonStr.startsWith(':')) continue;

              final jsonData = jsonDecode(jsonStr);
              print('📥 流式图片消息响应: $jsonData');

              // 处理不同类型的流式响应
              if (jsonData['type'] == 'ai_chunk') {
                // 流式文本块
                final content = jsonData['content'] as String;
                accumulatedText += content;

                // 创建临时消息对象用于显示
                final tempMessage = ChatMessageModel(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  conversationId: conversationId,
                  role: MessageRole.assistant,
                  content: [
                    MessageContent(
                      type: MessageContentType.text,
                      content: accumulatedText,
                    ),
                  ],
                  timestamp: DateTime.now(),
                );

                yield tempMessage;
              } else if (jsonData['type'] == 'ai_complete') {
                // AI回复完成
                final messageData = jsonData['message'] as Map<String, dynamic>;
                final finalMessage = ChatMessageModel.fromJson(messageData);
                yield finalMessage;
              }
            } catch (e) {
              print('❌ 解析流式图片消息响应失败: $e, 原始数据: $line');
              continue;
            }
          }
        }
      } else if (streamedResponse.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${streamedResponse.statusCode}');
      }
    } catch (e) {
      print('❌ API: 发送流式图片消息异常: $e');
      if (e is Exception) {
        rethrow;
      }
      throw Exception('发送流式图片消息异常: $e');
    }
  }

  /// 发送语音消息
  Future<ChatMessageModel> sendAudioMessage({
    required String conversationId,
    required File audioFile,
    int? duration,
    List<ChatMessageModel>? history,
    double? latitude,
    double? longitude,
    String? address,
  }) async {
    try {
      // 读取音频文件并转换为base64
      final audioBytes = await audioFile.readAsBytes();
      final base64Audio = 'data:audio/wav;base64,${base64Encode(audioBytes)}';

      // 构建新消息内容
      final messageContent = <Map<String, dynamic>>[
        {'audio': base64Audio},
      ];

      // 构建新API格式的请求数据
      final requestData = <String, dynamic>{
        'newMessage': {'role': 'user', 'content': messageContent},
        'history': history?.map((msg) => msg.toJson()).toList() ?? [],
      };

      final response = await http.post(
        Uri.parse(ApiConfig.sendMessageUrl(conversationId)),
        headers: _getAuthHeaders(),
        body: jsonEncode(requestData),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (jsonData['code'] == 200) {
          return _parseMessageFromResponse(jsonData);
        } else {
          throw Exception('发送语音消息失败: ${jsonData['message']}');
        }
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('发送语音消息异常: $e');
    }
  }

  /// 创建新对话并发送第一条消息 (流式响应)
  Stream<Map<String, dynamic>> createConversationWithMessageStream({
    required String content,
    required int doctorId,
    String? title,
    String messageType = 'text',
    double? latitude,
    double? longitude,
    String? address,
  }) async* {
    try {
      // 构建新消息内容 - 根据API文档格式
      final messageContent = <Map<String, dynamic>>[
        {'text': content, 'image': null},
      ];

      // 构建新API格式的请求数据
      final requestData = <String, dynamic>{
        'newMessage': {'role': 'user', 'content': messageContent},
        'history': [],
        'doctor_id': doctorId,
      };

      print('=== 创建流式对话请求详情 ===');
      print('URL: ${ApiConfig.createConversationUrl}/stream');
      print('Headers: ${_getAuthHeaders()}');
      print('Request Body: ${jsonEncode(requestData)}');

      final request = http.Request(
        'POST',
        Uri.parse('${ApiConfig.createConversationUrl}/stream'),
      );

      request.headers.addAll(_getAuthHeaders());
      request.body = jsonEncode(requestData);

      final streamedResponse = await request.send();

      if (streamedResponse.statusCode == 200) {
        String? conversationId;
        bool conversationComplete = false;
        String accumulatedText = ''; // 添加累积文本变量

        await for (final chunk in streamedResponse.stream.transform(
          utf8.decoder,
        )) {
          final lines = chunk.split('\n');
          for (final line in lines) {
            if (line.trim().isEmpty) continue;

            try {
              // 处理SSE格式：data: {...}
              String jsonStr = line.trim();

              if (jsonStr.startsWith('data: ')) {
                jsonStr = jsonStr.substring(6).trim(); // 移除 "data: " 前缀并去除空格
              }

              // 跳过空行和SSE注释
              if (jsonStr.isEmpty || jsonStr.startsWith(':')) continue;

              final jsonData = jsonDecode(jsonStr);
              print('📥 创建对话流式响应: $jsonData');

              // 处理新的流式响应格式
              if (jsonData['type'] == 'conversation_id') {
                conversationId = jsonData['conversation_id'].toString();
                print('🆔 获取到对话ID: $conversationId');
              } else if (jsonData['type'] == 'user_message') {
                // 用户消息，可以忽略或处理
                print('📝 用户消息已确认');
              } else if (jsonData['type'] == 'ai_start') {
                // AI开始回复
                print('🤖 AI开始回复');
              } else if (jsonData['type'] == 'ai_chunk') {
                // AI回复片段，处理流式数据
                final content = jsonData['content'] as String;
                accumulatedText += content; // 累积文本
                print('📝 AI回复片段: $content');

                // 创建临时消息对象用于流式显示，使用累积的文本
                final tempMessage = ChatMessageModel(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  conversationId: conversationId ?? '',
                  role: MessageRole.assistant,
                  content: [
                    MessageContent(
                      type: MessageContentType.text,
                      content: accumulatedText, // 使用累积的文本
                    ),
                  ],
                  timestamp: DateTime.now(),
                );

                yield {
                  'type': 'message',
                  'message': tempMessage,
                  'conversation_id': conversationId,
                  'success': false,
                };
              } else if (jsonData['type'] == 'ai_complete') {
                // AI回复完成
                final messageData = jsonData['message'] as Map<String, dynamic>;
                final message = ChatMessageModel.fromJson(messageData);

                yield {
                  'type': 'message',
                  'message': message,
                  'conversation_id': conversationId,
                  'success': false,
                };
              } else if (jsonData['type'] == 'complete') {
                // 整个对话完成
                conversationComplete = true;
                yield {
                  'type': 'conversation_complete',
                  'conversation_id': conversationId,
                  'success': true,
                };
                break;
              } else if (jsonData['type'] == 'error') {
                // 处理错误消息
                final errorMessage = jsonData['message'] ?? '未知错误';
                print('❌ 服务器返回错误: $errorMessage');
                throw Exception('服务器错误: $errorMessage');
              }
            } catch (e) {
              print('❌ 解析流式响应失败: $e, 原始数据: $line');
              continue;
            }
          }

          if (conversationComplete) break;
        }
      } else if (streamedResponse.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${streamedResponse.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('创建流式对话异常: $e');
    }
  }

  /// 创建新对话并发送第一条图片消息 (流式响应)
  Stream<Map<String, dynamic>> createConversationWithImageStream({
    required File imageFile,
    required int doctorId,
    String? description,
    String? title,
    double? latitude,
    double? longitude,
    String? address,
  }) async* {
    try {
      print('🖼️ API: 开始创建图片对话');

      // 先上传图片获取URL
      final imageUrl = await uploadChatImage(imageFile);
      print('📸 API: 图片上传完成，URL: $imageUrl');

      // 构建新消息内容 - 根据API文档格式
      final messageContent = <Map<String, dynamic>>[];

      if (description != null && description.isNotEmpty) {
        // 如果有描述文本，先添加文本内容
        messageContent.add({'text': description, 'image': null});
        // 然后添加图片内容
        messageContent.add({'text': null, 'image': imageUrl});
      } else {
        // 如果没有描述文本，只添加图片内容
        messageContent.add({'text': null, 'image': imageUrl});
      }

      // 构建新API格式的请求数据
      final requestData = <String, dynamic>{
        'newMessage': {'role': 'user', 'content': messageContent},
        'history': [],
        'doctor_id': doctorId,
      };

      print('=== 创建图片对话流式请求详情 ===');
      print('URL: ${ApiConfig.createConversationUrl}/stream');
      print('Headers: ${_getAuthHeaders()}');
      print('Request Body: ${jsonEncode(requestData)}');

      final request = http.Request(
        'POST',
        Uri.parse('${ApiConfig.createConversationUrl}/stream'),
      );
      request.headers.addAll(_getAuthHeaders());
      request.body = jsonEncode(requestData);

      final streamedResponse = await request.send();

      if (streamedResponse.statusCode == 200) {
        String? conversationId;
        bool conversationComplete = false;

        await for (final chunk in streamedResponse.stream.transform(
          utf8.decoder,
        )) {
          final lines = chunk.split('\n');
          for (final line in lines) {
            if (line.trim().isEmpty) continue;

            try {
              // 处理SSE格式：data: {...}
              String jsonStr = line.trim();

              if (jsonStr.startsWith('data: ')) {
                jsonStr = jsonStr.substring(6).trim(); // 移除 "data: " 前缀并去除空格
              }

              // 跳过空行和SSE注释
              if (jsonStr.isEmpty || jsonStr.startsWith(':')) continue;

              final jsonData = jsonDecode(jsonStr);
              print('📥 创建图片对话流式响应: $jsonData');

              // 处理不同类型的流式响应
              if (jsonData['type'] == 'ai_chunk') {
                // 流式文本块
                final content = jsonData['content'] as String;

                // 创建临时消息对象用于显示
                final tempMessage = ChatMessageModel(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  conversationId: conversationId ?? '',
                  role: MessageRole.assistant,
                  content: [
                    MessageContent(
                      type: MessageContentType.text,
                      content: content,
                    ),
                  ],
                  timestamp: DateTime.now(),
                );

                yield {
                  'type': 'message',
                  'message': tempMessage,
                  'conversation_id': conversationId,
                  'success': false,
                };
              } else if (jsonData['type'] == 'ai_complete') {
                // AI回复完成
                final messageData = jsonData['message'] as Map<String, dynamic>;
                final finalMessage = ChatMessageModel.fromJson(messageData);

                yield {
                  'type': 'message',
                  'message': finalMessage,
                  'conversation_id':
                      conversationId ?? finalMessage.conversationId,
                  'success': false,
                };
              } else if (jsonData['type'] == 'complete') {
                // 整个对话完成，获取conversation_id
                conversationId ??= jsonData['conversation_id']?.toString();
                print('🆔 从complete消息获取到图片对话ID: $conversationId');

                conversationComplete = true;
                yield {
                  'type': 'conversation_complete',
                  'conversation_id': conversationId,
                  'success': true,
                };
                break;
              }
            } catch (e) {
              print('❌ 解析图片对话流式响应失败: $e, 原始数据: $line');
              continue;
            }
          }

          if (conversationComplete) break;
        }
      } else if (streamedResponse.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${streamedResponse.statusCode}');
      }
    } catch (e) {
      print('❌ API: 创建图片对话异常: $e');
      if (e is Exception) {
        rethrow;
      }
      throw Exception('创建图片对话异常: $e');
    }
  }

  /// 删除对话
  Future<void> deleteConversation(String conversationId) async {
    try {
      final response = await http.delete(
        Uri.parse(ApiConfig.deleteConversationUrl(conversationId)),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (jsonData['code'] != 200) {
          throw Exception('删除对话失败: ${jsonData['message']}');
        }
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('删除对话异常: $e');
    }
  }

  /// 删除所有对话
  Future<void> deleteAllConversations() async {
    try {
      final response = await http.delete(
        Uri.parse(ApiConfig.deleteAllConversationsUrl),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (jsonData['code'] != 200) {
          throw Exception('删除所有对话失败: ${jsonData['message']}');
        }
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('删除所有对话异常: $e');
    }
  }

  /// 更新对话标题
  Future<void> updateConversationTitle(
    String conversationId,
    String title,
  ) async {
    try {
      // 新API使用查询参数传递标题
      final uri = Uri.parse(
        ApiConfig.updateConversationTitleUrl(conversationId),
      ).replace(queryParameters: {'title': title});

      final response = await http.put(uri, headers: _getAuthHeaders());

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (jsonData['code'] != 200) {
          throw Exception('更新对话标题失败: ${jsonData['message']}');
        }
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('更新对话标题异常: $e');
    }
  }

  /// 上传聊天图片
  Future<String> uploadChatImage(File imageFile) async {
    try {
      print('🖼️ API: 开始上传聊天图片');
      print('📁 API: 图片路径: ${imageFile.path}');

      // 创建multipart请求
      final request = http.MultipartRequest(
        'POST',
        Uri.parse(ApiConfig.uploadChatImageUrl),
      );

      // 添加认证头
      request.headers.addAll(_getAuthHeadersWithoutContentType());

      // 添加图片文件
      final imageStream = http.ByteStream(imageFile.openRead());
      final imageLength = await imageFile.length();
      final fileName = imageFile.path.split('/').last;

      // 确定MIME类型
      String? mimeType;
      if (fileName.toLowerCase().endsWith('.jpg') ||
          fileName.toLowerCase().endsWith('.jpeg')) {
        mimeType = 'image/jpeg';
      } else if (fileName.toLowerCase().endsWith('.png')) {
        mimeType = 'image/png';
      } else if (fileName.toLowerCase().endsWith('.webp')) {
        mimeType = 'image/webp';
      } else if (fileName.toLowerCase().endsWith('.gif')) {
        mimeType = 'image/gif';
      } else {
        mimeType = 'image/jpeg'; // 默认
      }

      final multipartFile = http.MultipartFile(
        'image', // 字段名必须是'image'
        imageStream,
        imageLength,
        filename: fileName,
        contentType: MediaType.parse(mimeType),
      );

      request.files.add(multipartFile);

      print('🚀 API: 发送图片上传请求');
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('📥 API: 图片上传响应状态: ${response.statusCode}');
      print('📄 API: 图片上传响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        if (jsonData['code'] == 200) {
          final data = jsonData['data'] as Map<String, dynamic>;
          final imageUrl = data['image_url'] as String;

          print('✅ API: 图片上传成功，URL: $imageUrl');
          return imageUrl;
        } else {
          throw Exception('图片上传失败: ${jsonData['message']}');
        }
      } else if (response.statusCode == 401) {
        throw Exception('登录已过期，请重新登录');
      } else {
        throw Exception('网络请求失败: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ API: 图片上传异常: $e');
      if (e is Exception) {
        rethrow;
      }
      throw Exception('图片上传异常: $e');
    }
  }
}
