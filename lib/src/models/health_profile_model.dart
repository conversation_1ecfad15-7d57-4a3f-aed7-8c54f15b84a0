/// 健康档案模型类
class HealthProfileModel {
  final int? id;
  final int? userId;
  final double? height; // 身高(cm)
  final double? weight; // 体重(kg)
  final String? bloodType; // 血型 (A/B/AB/O/unknown)

  // 地址信息
  final String? addressProvince; // 省份
  final String? addressCity; // 城市
  final String? addressDistrict; // 区县
  final String? addressDetail; // 详细地址

  // 过敏史
  final bool hasAllergies;
  final List<String> allergyDrugs; // 药物过敏
  final List<String> allergyFoods; // 食物过敏
  final String? allergyOthers; // 其他过敏

  // 当前用药
  final bool hasCurrentMedication;
  final String? currentMedications; // 用药详情

  // 慢性病史
  final bool hasChronicDiseases;
  final List<String> chronicDiseases; // 慢性病列表
  final String? bloodPressureRange; // 血压范围
  final String? bloodSugarRange; // 血糖范围
  final String? otherChronicDiseases; // 其他慢性病

  // 手术史
  final bool hasSurgeryHistory;
  final String? surgeryHistory; // 手术史详情

  // 家族病史
  final List<String> familyHistory; // 家族病史
  final String? otherFamilyHistory; // 其他家族病史

  // 生活方式
  final String? exerciseFrequency; // 运动频率 (sedentary/light/moderate/active)
  final List<String> dietaryPreferences; // 饮食偏好
  final String? smokingStatus; // 吸烟状况 (never/quit/occasional/daily)
  final String? drinkingStatus; // 饮酒状况 (never/quit/social/weekly/daily)
  final String? sleepDuration; // 睡眠时长 (<6/6-7/7-8/>8)
  final String? sleepQuality; // 睡眠质量 (good/fair/poor)
  final String? stressLevel; // 压力水平 (low/mild/moderate/high/extreme)

  // 女性健康
  final bool? isMenopause; // 是否绝经
  final String? menstrualCycle; // 月经周期 (regular/irregular/uncertain)
  final bool? hasPregnancy; // 是否怀孕过
  final int? birthCount; // 生育次数

  // 时间戳
  final String? createdAt;
  final String? updatedAt;

  HealthProfileModel({
    this.id,
    this.userId,
    this.height,
    this.weight,
    this.bloodType,
    this.addressProvince,
    this.addressCity,
    this.addressDistrict,
    this.addressDetail,
    this.hasAllergies = false,
    this.allergyDrugs = const [],
    this.allergyFoods = const [],
    this.allergyOthers,
    this.hasCurrentMedication = false,
    this.currentMedications,
    this.hasChronicDiseases = false,
    this.chronicDiseases = const [],
    this.bloodPressureRange,
    this.bloodSugarRange,
    this.otherChronicDiseases,
    this.hasSurgeryHistory = false,
    this.surgeryHistory,
    this.familyHistory = const [],
    this.otherFamilyHistory,
    this.exerciseFrequency,
    this.dietaryPreferences = const [],
    this.smokingStatus,
    this.drinkingStatus,
    this.sleepDuration,
    this.sleepQuality,
    this.stressLevel,
    this.isMenopause,
    this.menstrualCycle,
    this.hasPregnancy,
    this.birthCount,
    this.createdAt,
    this.updatedAt,
  });

  /// 从JSON创建健康档案模型
  factory HealthProfileModel.fromJson(Map<String, dynamic> json) {
    return HealthProfileModel(
      id: json['id'],
      userId: json['user_id'],
      height: _parseDouble(json['height']),
      weight: _parseDouble(json['weight']),
      bloodType: json['blood_type'],
      addressProvince: json['address_province'],
      addressCity: json['address_city'],
      addressDistrict: json['address_district'],
      addressDetail: json['address_detail'],
      hasAllergies: json['has_allergies'] ?? false,
      allergyDrugs: _parseStringList(json['allergy_drugs']),
      allergyFoods: _parseStringList(json['allergy_foods']),
      allergyOthers: json['allergy_others'],
      hasCurrentMedication: json['has_current_medication'] ?? false,
      currentMedications: json['current_medications'],
      hasChronicDiseases: json['has_chronic_diseases'] ?? false,
      chronicDiseases: _parseStringList(json['chronic_diseases']),
      bloodPressureRange: json['blood_pressure_range'],
      bloodSugarRange: json['blood_sugar_range'],
      otherChronicDiseases: json['other_chronic_diseases'],
      hasSurgeryHistory: json['has_surgery_history'] ?? false,
      surgeryHistory: json['surgery_history'],
      familyHistory: _parseStringList(json['family_history']),
      otherFamilyHistory: json['other_family_history'],
      exerciseFrequency: json['exercise_frequency'],
      dietaryPreferences: _parseStringList(json['dietary_preferences']),
      smokingStatus: json['smoking_status'],
      drinkingStatus: json['drinking_status'],
      sleepDuration: json['sleep_duration'],
      sleepQuality: json['sleep_quality'],
      stressLevel: json['stress_level'],
      isMenopause: json['is_menopause'],
      menstrualCycle: json['menstrual_cycle'],
      hasPregnancy: json['has_pregnancy'],
      birthCount: json['birth_count'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (id != null) data['id'] = id;
    if (userId != null) data['user_id'] = userId;
    if (height != null) data['height'] = height;
    if (weight != null) data['weight'] = weight;
    if (bloodType != null) data['blood_type'] = bloodType;

    // 地址信息
    if (addressProvince != null) data['address_province'] = addressProvince;
    if (addressCity != null) data['address_city'] = addressCity;
    if (addressDistrict != null) data['address_district'] = addressDistrict;
    if (addressDetail != null) data['address_detail'] = addressDetail;

    data['has_allergies'] = hasAllergies;
    if (allergyDrugs.isNotEmpty) data['allergy_drugs'] = allergyDrugs;
    if (allergyFoods.isNotEmpty) data['allergy_foods'] = allergyFoods;
    if (allergyOthers != null) data['allergy_others'] = allergyOthers;

    data['has_current_medication'] = hasCurrentMedication;
    if (currentMedications != null) {
      data['current_medications'] = currentMedications;
    }

    data['has_chronic_diseases'] = hasChronicDiseases;
    if (chronicDiseases.isNotEmpty) data['chronic_diseases'] = chronicDiseases;
    if (bloodPressureRange != null) {
      data['blood_pressure_range'] = bloodPressureRange;
    }
    if (bloodSugarRange != null) data['blood_sugar_range'] = bloodSugarRange;
    if (otherChronicDiseases != null) {
      data['other_chronic_diseases'] = otherChronicDiseases;
    }

    data['has_surgery_history'] = hasSurgeryHistory;
    if (surgeryHistory != null) data['surgery_history'] = surgeryHistory;

    if (familyHistory.isNotEmpty) data['family_history'] = familyHistory;
    if (otherFamilyHistory != null) {
      data['other_family_history'] = otherFamilyHistory;
    }

    if (exerciseFrequency != null) {
      data['exercise_frequency'] = exerciseFrequency;
    }
    if (dietaryPreferences.isNotEmpty) {
      data['dietary_preferences'] = dietaryPreferences;
    }
    if (smokingStatus != null) data['smoking_status'] = smokingStatus;
    if (drinkingStatus != null) data['drinking_status'] = drinkingStatus;
    if (sleepDuration != null) data['sleep_duration'] = sleepDuration;
    if (sleepQuality != null) data['sleep_quality'] = sleepQuality;
    if (stressLevel != null) data['stress_level'] = stressLevel;

    if (isMenopause != null) data['is_menopause'] = isMenopause;
    if (menstrualCycle != null) data['menstrual_cycle'] = menstrualCycle;
    if (hasPregnancy != null) data['has_pregnancy'] = hasPregnancy;
    if (birthCount != null) data['birth_count'] = birthCount;

    if (createdAt != null) data['created_at'] = createdAt;
    if (updatedAt != null) data['updated_at'] = updatedAt;

    return data;
  }

  /// 安全解析double值
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// 安全解析字符串列表
  static List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return [];
  }

  /// 复制并修改属性
  HealthProfileModel copyWith({
    int? id,
    int? userId,
    double? height,
    double? weight,
    String? bloodType,
    String? addressProvince,
    String? addressCity,
    String? addressDistrict,
    String? addressDetail,
    bool? hasAllergies,
    List<String>? allergyDrugs,
    List<String>? allergyFoods,
    String? allergyOthers,
    bool? hasCurrentMedication,
    String? currentMedications,
    bool? hasChronicDiseases,
    List<String>? chronicDiseases,
    String? bloodPressureRange,
    String? bloodSugarRange,
    String? otherChronicDiseases,
    bool? hasSurgeryHistory,
    String? surgeryHistory,
    List<String>? familyHistory,
    String? otherFamilyHistory,
    String? exerciseFrequency,
    List<String>? dietaryPreferences,
    String? smokingStatus,
    String? drinkingStatus,
    String? sleepDuration,
    String? sleepQuality,
    String? stressLevel,
    bool? isMenopause,
    String? menstrualCycle,
    bool? hasPregnancy,
    int? birthCount,
    String? createdAt,
    String? updatedAt,
  }) {
    return HealthProfileModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      bloodType: bloodType ?? this.bloodType,
      addressProvince: addressProvince ?? this.addressProvince,
      addressCity: addressCity ?? this.addressCity,
      addressDistrict: addressDistrict ?? this.addressDistrict,
      addressDetail: addressDetail ?? this.addressDetail,
      hasAllergies: hasAllergies ?? this.hasAllergies,
      allergyDrugs: allergyDrugs ?? this.allergyDrugs,
      allergyFoods: allergyFoods ?? this.allergyFoods,
      allergyOthers: allergyOthers ?? this.allergyOthers,
      hasCurrentMedication: hasCurrentMedication ?? this.hasCurrentMedication,
      currentMedications: currentMedications ?? this.currentMedications,
      hasChronicDiseases: hasChronicDiseases ?? this.hasChronicDiseases,
      chronicDiseases: chronicDiseases ?? this.chronicDiseases,
      bloodPressureRange: bloodPressureRange ?? this.bloodPressureRange,
      bloodSugarRange: bloodSugarRange ?? this.bloodSugarRange,
      otherChronicDiseases: otherChronicDiseases ?? this.otherChronicDiseases,
      hasSurgeryHistory: hasSurgeryHistory ?? this.hasSurgeryHistory,
      surgeryHistory: surgeryHistory ?? this.surgeryHistory,
      familyHistory: familyHistory ?? this.familyHistory,
      otherFamilyHistory: otherFamilyHistory ?? this.otherFamilyHistory,
      exerciseFrequency: exerciseFrequency ?? this.exerciseFrequency,
      dietaryPreferences: dietaryPreferences ?? this.dietaryPreferences,
      smokingStatus: smokingStatus ?? this.smokingStatus,
      drinkingStatus: drinkingStatus ?? this.drinkingStatus,
      sleepDuration: sleepDuration ?? this.sleepDuration,
      sleepQuality: sleepQuality ?? this.sleepQuality,
      stressLevel: stressLevel ?? this.stressLevel,
      isMenopause: isMenopause ?? this.isMenopause,
      menstrualCycle: menstrualCycle ?? this.menstrualCycle,
      hasPregnancy: hasPregnancy ?? this.hasPregnancy,
      birthCount: birthCount ?? this.birthCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
