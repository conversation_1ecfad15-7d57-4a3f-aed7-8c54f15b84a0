/// 地址模型
class AddressModel {
  final int id;
  final String receiverName;
  final String receiverPhone;
  final String province;
  final String city;
  final String district;
  final String detailedAddress;
  final String? postalCode;
  final String? addressLabel;
  final bool isDefault;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  AddressModel({
    required this.id,
    required this.receiverName,
    required this.receiverPhone,
    required this.province,
    required this.city,
    required this.district,
    required this.detailedAddress,
    this.postalCode,
    this.addressLabel,
    required this.isDefault,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 获取完整地址
  String get fullAddress {
    return '$province$city$district$detailedAddress';
  }

  /// 获取地区信息（省市区）
  String get regionInfo {
    return '$province $city $district';
  }

  /// 获取显示用的地址标签
  String get displayLabel {
    return addressLabel?.isNotEmpty == true ? addressLabel! : '地址';
  }

  factory AddressModel.fromJson(Map<String, dynamic> json) {
    return AddressModel(
      id: json['id'] as int,
      receiverName: json['receiver_name'] as String,
      receiverPhone: json['receiver_phone'] as String,
      province: json['province'] as String,
      city: json['city'] as String,
      district: json['district'] as String,
      detailedAddress: json['detailed_address'] as String,
      postalCode: json['postal_code'] as String?,
      addressLabel: json['address_label'] as String?,
      isDefault: json['is_default'] as bool,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'receiver_name': receiverName,
      'receiver_phone': receiverPhone,
      'province': province,
      'city': city,
      'district': district,
      'detailed_address': detailedAddress,
      'postal_code': postalCode,
      'address_label': addressLabel,
      'is_default': isDefault,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  AddressModel copyWith({
    int? id,
    String? receiverName,
    String? receiverPhone,
    String? province,
    String? city,
    String? district,
    String? detailedAddress,
    String? postalCode,
    String? addressLabel,
    bool? isDefault,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AddressModel(
      id: id ?? this.id,
      receiverName: receiverName ?? this.receiverName,
      receiverPhone: receiverPhone ?? this.receiverPhone,
      province: province ?? this.province,
      city: city ?? this.city,
      district: district ?? this.district,
      detailedAddress: detailedAddress ?? this.detailedAddress,
      postalCode: postalCode ?? this.postalCode,
      addressLabel: addressLabel ?? this.addressLabel,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// 简化地址模型（用于订单选择）
class SimpleAddressModel {
  final int id;
  final String receiverName;
  final String receiverPhone;
  final String fullAddress;
  final String? addressLabel;
  final bool isDefault;

  SimpleAddressModel({
    required this.id,
    required this.receiverName,
    required this.receiverPhone,
    required this.fullAddress,
    this.addressLabel,
    required this.isDefault,
  });

  /// 获取显示用的地址标签
  String get displayLabel {
    return addressLabel?.isNotEmpty == true ? addressLabel! : '地址';
  }

  factory SimpleAddressModel.fromJson(Map<String, dynamic> json) {
    return SimpleAddressModel(
      id: json['id'] as int,
      receiverName: json['receiver_name'] as String,
      receiverPhone: json['receiver_phone'] as String,
      fullAddress: json['full_address'] as String,
      addressLabel: json['address_label'] as String?,
      isDefault: json['is_default'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'receiver_name': receiverName,
      'receiver_phone': receiverPhone,
      'full_address': fullAddress,
      'address_label': addressLabel,
      'is_default': isDefault,
    };
  }
}

/// 创建/更新地址请求模型
class AddressRequestModel {
  final String receiverName;
  final String receiverPhone;
  final String province;
  final String city;
  final String district;
  final String detailedAddress;
  final String? postalCode;
  final String? addressLabel;
  final bool isDefault;

  AddressRequestModel({
    required this.receiverName,
    required this.receiverPhone,
    required this.province,
    required this.city,
    required this.district,
    required this.detailedAddress,
    this.postalCode,
    this.addressLabel,
    this.isDefault = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'receiver_name': receiverName,
      'receiver_phone': receiverPhone,
      'province': province,
      'city': city,
      'district': district,
      'detailed_address': detailedAddress,
      'postal_code': postalCode,
      'address_label': addressLabel,
      'is_default': isDefault,
    };
  }
}
