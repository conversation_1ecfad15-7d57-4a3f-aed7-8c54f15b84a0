import 'dart:convert';

/// 聊天消息内容类型枚举
enum MessageContentType { text, image, audio, location }

/// 消息内容模型 - 支持多模态内容
class MessageContent {
  final MessageContentType type;
  final String content;
  final Map<String, dynamic>? metadata;

  const MessageContent({
    required this.type,
    required this.content,
    this.metadata,
  });

  /// 从JSON创建对象 - 支持新API格式
  factory MessageContent.fromJson(Map<String, dynamic> json) {
    // 新API格式：{"text": "内容", "image": null} 或 {"text": null, "image": "base64数据"}
    // 优先处理非null的字段
    if (json.containsKey('text') &&
        json['text'] != null &&
        json['text'].toString().isNotEmpty) {
      return MessageContent(
        type: MessageContentType.text,
        content: json['text'] as String,
      );
    } else if (json.containsKey('image') &&
        json['image'] != null &&
        json['image'].toString().isNotEmpty) {
      return MessageContent(
        type: MessageContentType.image,
        content: json['image'] as String,
      );
    } else if (json.containsKey('audio') &&
        json['audio'] != null &&
        json['audio'].toString().isNotEmpty) {
      return MessageContent(
        type: MessageContentType.audio,
        content: json['audio'] as String,
      );
    } else if (json.containsKey('type') && json.containsKey('content')) {
      // 兼容旧格式
      return MessageContent(
        type: MessageContentType.values.firstWhere(
          (e) => e.name == json['type'],
          orElse: () => MessageContentType.text,
        ),
        content: json['content'] as String,
        metadata: json['metadata'] as Map<String, dynamic>?,
      );
    } else {
      // 如果所有字段都是null或空，返回空文本内容
      return MessageContent(type: MessageContentType.text, content: '');
    }
  }

  /// 转换为JSON - 支持新API格式
  Map<String, dynamic> toJson() {
    // 新API格式：{"text": "内容"} 或 {"image": "base64数据"} 或 {"audio": "base64数据"}
    switch (type) {
      case MessageContentType.text:
        return {'text': content};
      case MessageContentType.image:
        return {'image': content};
      case MessageContentType.audio:
        return {'audio': content};
      default:
        // 兼容旧格式
        return {'type': type.name, 'content': content, 'metadata': metadata};
    }
  }

  /// 创建文本内容
  factory MessageContent.text(String text) {
    return MessageContent(type: MessageContentType.text, content: text);
  }

  /// 创建图片内容
  factory MessageContent.image(String imageUrl, {String? description}) {
    return MessageContent(
      type: MessageContentType.image,
      content: imageUrl,
      metadata: description != null ? {'description': description} : null,
    );
  }

  /// 创建音频内容
  factory MessageContent.audio(String audioUrl, {int? duration}) {
    return MessageContent(
      type: MessageContentType.audio,
      content: audioUrl,
      metadata: duration != null ? {'duration': duration} : null,
    );
  }

  /// 创建位置内容
  factory MessageContent.location(
    double latitude,
    double longitude, {
    String? address,
  }) {
    return MessageContent(
      type: MessageContentType.location,
      content: '$latitude,$longitude',
      metadata: address != null ? {'address': address} : null,
    );
  }
}

/// 消息角色枚举
enum MessageRole { user, assistant, system }

/// 聊天消息模型
class ChatMessageModel {
  final String id;
  final String conversationId;
  final MessageRole role;
  final List<MessageContent> content;
  final DateTime timestamp;
  final bool isLoading;
  final String? error;
  final Map<String, dynamic>? metadata;

  const ChatMessageModel({
    required this.id,
    required this.conversationId,
    required this.role,
    required this.content,
    required this.timestamp,
    this.isLoading = false,
    this.error,
    this.metadata,
  });

  /// 从JSON创建对象
  factory ChatMessageModel.fromJson(Map<String, dynamic> json) {
    return ChatMessageModel(
      id: json['id'].toString(), // 支持整数和字符串ID
      conversationId: json['conversation_id'].toString(), // 支持整数和字符串ID
      role: MessageRole.values.firstWhere(
        (e) => e.name == json['role'],
        orElse: () => MessageRole.user,
      ),
      content: (json['content'] as List<dynamic>)
          .map((e) => MessageContent.fromJson(e as Map<String, dynamic>))
          .toList(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      isLoading: json['is_loading'] as bool? ?? false,
      error: json['error'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'conversation_id': conversationId,
      'role': role.name,
      'content': content.map((e) => e.toJson()).toList(),
      'timestamp': timestamp.toIso8601String(),
      'is_loading': isLoading,
      'error': error,
      'metadata': metadata,
    };
  }

  /// 从数据库Map创建对象
  factory ChatMessageModel.fromMap(Map<String, dynamic> map) {
    return ChatMessageModel(
      id: map['id'] as String,
      conversationId: map['conversation_id'] as String,
      role: MessageRole.values.firstWhere(
        (e) => e.name == map['role'],
        orElse: () => MessageRole.user,
      ),
      content: (jsonDecode(map['content'] as String) as List<dynamic>)
          .map((e) => MessageContent.fromJson(e as Map<String, dynamic>))
          .toList(),
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] as int),
      isLoading: (map['is_loading'] as int? ?? 0) == 1,
      error: map['error'] as String?,
      metadata: map['metadata'] != null
          ? jsonDecode(map['metadata'] as String) as Map<String, dynamic>
          : null,
    );
  }

  /// 转换为数据库Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'conversation_id': conversationId,
      'role': role.name,
      'content': jsonEncode(content.map((e) => e.toJson()).toList()),
      'timestamp': timestamp.millisecondsSinceEpoch,
      'is_loading': isLoading ? 1 : 0,
      'error': error,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
    };
  }

  /// 复制并更新部分字段
  ChatMessageModel copyWith({
    String? id,
    String? conversationId,
    MessageRole? role,
    List<MessageContent>? content,
    DateTime? timestamp,
    bool? isLoading,
    String? error,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessageModel(
      id: id ?? this.id,
      conversationId: conversationId ?? this.conversationId,
      role: role ?? this.role,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 获取纯文本内容
  String get textContent {
    return content
        .where((c) => c.type == MessageContentType.text)
        .map((c) => c.content)
        .join('\n');
  }

  /// 是否包含图片
  bool get hasImage {
    return content.any((c) => c.type == MessageContentType.image);
  }

  /// 是否包含音频
  bool get hasAudio {
    return content.any((c) => c.type == MessageContentType.audio);
  }

  /// 是否包含位置信息
  bool get hasLocation {
    return content.any((c) => c.type == MessageContentType.location);
  }

  /// 创建文本消息
  factory ChatMessageModel.text({
    required String id,
    required String conversationId,
    required MessageRole role,
    required String text,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessageModel(
      id: id,
      conversationId: conversationId,
      role: role,
      content: [MessageContent.text(text)],
      timestamp: timestamp ?? DateTime.now(),
      metadata: metadata,
    );
  }

  /// 创建加载中的消息
  factory ChatMessageModel.loading({
    required String id,
    required String conversationId,
  }) {
    return ChatMessageModel(
      id: id,
      conversationId: conversationId,
      role: MessageRole.assistant,
      content: [MessageContent.text('正在思考中...')],
      timestamp: DateTime.now(),
      isLoading: true,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessageModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChatMessageModel(id: $id, role: $role, content: ${content.length} items, timestamp: $timestamp)';
  }
}
