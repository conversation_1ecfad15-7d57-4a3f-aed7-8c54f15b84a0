import 'dart:convert';
import 'doctor_model.dart';

/// 对话模型 - 表示一个完整的对话会话
class ConversationModel {
  final String id;
  final String title;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int messageCount;
  final String? lastMessage;
  final String? lastMessageRole;
  final int? doctorId;
  final DoctorModel? doctor;

  const ConversationModel({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    this.messageCount = 0,
    this.lastMessage,
    this.lastMessageRole,
    this.doctorId,
    this.doctor,
  });

  /// 从JSON创建对象
  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'].toString(), // 支持整数和字符串ID
      title: json['title'] as String,
      // 新API只返回updated_at，created_at使用相同值
      createdAt: DateTime.parse(json['updated_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      messageCount: json['message_count'] as int? ?? 0,
      lastMessage: json['last_message'] as String?,
      lastMessageRole: json['last_message_role'] as String?,
      doctorId: json['doctor_id'] as int?,
      doctor: json['doctor'] != null
          ? DoctorModel.fromJson(json['doctor'] as Map<String, dynamic>)
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'message_count': messageCount,
      'last_message': lastMessage,
      'last_message_role': lastMessageRole,
      'doctor_id': doctorId,
      'doctor': doctor?.toJson(),
    };
  }

  /// 从数据库Map创建对象
  factory ConversationModel.fromMap(Map<String, dynamic> map) {
    DoctorModel? doctor;
    if (map['doctor'] != null) {
      try {
        // 如果doctor字段是字符串，需要先解析JSON
        if (map['doctor'] is String) {
          final doctorJson = jsonDecode(map['doctor'] as String);
          doctor = DoctorModel.fromJson(doctorJson as Map<String, dynamic>);
        } else if (map['doctor'] is Map<String, dynamic>) {
          // 如果已经是Map，直接使用
          doctor = DoctorModel.fromJson(map['doctor'] as Map<String, dynamic>);
        }
      } catch (e) {
        // 解析doctor字段失败，使用null值
        doctor = null;
      }
    }

    return ConversationModel(
      id: map['id'] as String,
      title: map['title'] as String,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      messageCount: map['message_count'] as int? ?? 0,
      lastMessage: map['last_message'] as String?,
      lastMessageRole: map['last_message_role'] as String?,
      doctorId: map['doctor_id'] as int?,
      doctor: doctor,
    );
  }

  /// 转换为数据库Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'message_count': messageCount,
      'last_message': lastMessage,
      'last_message_role': lastMessageRole,
      'doctor_id': doctorId,
      'doctor': doctor != null ? jsonEncode(doctor!.toJson()) : null,
    };
  }

  /// 复制并更新部分字段
  ConversationModel copyWith({
    String? id,
    String? title,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? messageCount,
    String? lastMessage,
    String? lastMessageRole,
    int? doctorId,
    DoctorModel? doctor,
  }) {
    return ConversationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      messageCount: messageCount ?? this.messageCount,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageRole: lastMessageRole ?? this.lastMessageRole,
      doctorId: doctorId ?? this.doctorId,
      doctor: doctor ?? this.doctor,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConversationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ConversationModel(id: $id, title: $title, updatedAt: $updatedAt, messageCount: $messageCount)';
  }
}
