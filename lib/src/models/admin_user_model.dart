import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';

/// 管理员用户管理模型
class AdminUserModel {
  final int id;
  final String? uuid;
  final String nickname;
  final String phone;
  final int sex; // 0:未知 1:男 2:女
  final String? avatar;
  final String? birthday;
  final String money;
  final int integral;
  final String? disCode;
  final bool status;
  final bool isAdmin;
  final bool isDoctor;
  final int? doctorId;
  final bool isReferrer;
  final int referrerLevel;
  final int registerSource; // 1:APP 2:小程序
  final String? ip;
  final String createdAt;
  final String? updatedAt;
  final String? loginAt;
  final DoctorInfo? doctorInfo;
  final int? tokenCount;
  final int? addressCount;

  AdminUserModel({
    required this.id,
    this.uuid,
    required this.nickname,
    required this.phone,
    required this.sex,
    this.avatar,
    this.birthday,
    required this.money,
    required this.integral,
    this.disCode,
    required this.status,
    required this.isAdmin,
    required this.isDoctor,
    this.doctorId,
    required this.isReferrer,
    required this.referrerLevel,
    required this.registerSource,
    this.ip,
    required this.createdAt,
    this.updatedAt,
    this.loginAt,
    this.doctorInfo,
    this.tokenCount,
    this.addressCount,
  });

  factory AdminUserModel.fromJson(Map<String, dynamic> json) {
    return AdminUserModel(
      id: json['id'] ?? 0,
      uuid: json['uuid'],
      nickname: json['nickname'] ?? '',
      phone: json['phone'] ?? '',
      sex: json['sex'] ?? 0,
      avatar: json['avatar'],
      birthday: json['birthday'],
      money: json['money']?.toString() ?? '0.00',
      integral: json['integral'] ?? 0,
      disCode: json['dis_code'],
      status: json['status'] ?? false,
      isAdmin: json['is_admin'] ?? false,
      isDoctor: json['is_doctor'] ?? false,
      doctorId: json['doctor_id'],
      isReferrer: json['is_referrer'] ?? false,
      referrerLevel: json['referrer_level'] ?? 0,
      registerSource: json['register_source'] ?? 1,
      ip: json['ip'],
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'],
      loginAt: json['login_at'],
      doctorInfo: json['doctor_info'] != null
          ? DoctorInfo.fromJson(json['doctor_info'])
          : null,
      tokenCount: json['token_count'],
      addressCount: json['address_count'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'uuid': uuid,
      'nickname': nickname,
      'phone': phone,
      'sex': sex,
      'avatar': avatar,
      'birthday': birthday,
      'money': money,
      'integral': integral,
      'dis_code': disCode,
      'status': status,
      'is_admin': isAdmin,
      'is_doctor': isDoctor,
      'doctor_id': doctorId,
      'is_referrer': isReferrer,
      'referrer_level': referrerLevel,
      'register_source': registerSource,
      'ip': ip,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'login_at': loginAt,
      'doctor_info': doctorInfo?.toJson(),
      'token_count': tokenCount,
      'address_count': addressCount,
    };
  }

  /// 获取性别显示文本
  String getSexText([BuildContext? context]) {
    if (context != null) {
      // 使用国际化文本
      switch (sex) {
        case 1:
          return AppLocalizations.of(context).genderMale;
        case 2:
          return AppLocalizations.of(context).genderFemale;
        default:
          return AppLocalizations.of(context).genderUnknown;
      }
    } else {
      // 回退到硬编码文本（兼容性）
      switch (sex) {
        case 1:
          return '男';
        case 2:
          return '女';
        default:
          return '未知';
      }
    }
  }

  /// 获取注册来源显示文本
  String getRegisterSourceText([BuildContext? context]) {
    if (context != null) {
      // 使用国际化文本
      switch (registerSource) {
        case 1:
          return AppLocalizations.of(context).registerSourceApp;
        case 2:
          return AppLocalizations.of(context).registerSourceMiniProgram;
        default:
          return AppLocalizations.of(context).genderUnknown;
      }
    } else {
      // 回退到硬编码文本（兼容性）
      switch (registerSource) {
        case 1:
          return 'APP';
        case 2:
          return '小程序';
        default:
          return '未知';
      }
    }
  }

  /// 获取状态显示文本
  String getStatusText([BuildContext? context]) {
    if (context != null) {
      // 使用国际化文本
      return status
          ? AppLocalizations.of(context).statusEnabled
          : AppLocalizations.of(context).statusDisabled;
    } else {
      // 回退到硬编码文本（兼容性）
      return status ? '启用' : '禁用';
    }
  }

  /// 获取角色显示文本
  List<String> getRoles() {
    List<String> roles = [];
    if (isAdmin) roles.add('管理员');
    if (isDoctor) roles.add('医生');
    if (isReferrer) roles.add('分销员');
    if (roles.isEmpty) roles.add('普通用户');
    return roles;
  }
}

/// 医生信息
class DoctorInfo {
  final int id;
  final Map<String, String> name;
  final Map<String, String> specialty;
  final Map<String, String> description;

  DoctorInfo({
    required this.id,
    required this.name,
    required this.specialty,
    required this.description,
  });

  factory DoctorInfo.fromJson(Map<String, dynamic> json) {
    return DoctorInfo(
      id: json['id'] ?? 0,
      name: Map<String, String>.from(json['name'] ?? {}),
      specialty: Map<String, String>.from(json['specialty'] ?? {}),
      description: Map<String, String>.from(json['description'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'specialty': specialty,
      'description': description,
    };
  }
}

/// 用户统计数据模型
class UserStatisticsModel {
  final int totalUsers;
  final int activeUsers;
  final int inactiveUsers;
  final int adminUsers;
  final int doctorUsers;
  final int referrerUsers;
  final int todayNewUsers;
  final int thisWeekNewUsers;
  final int thisMonthNewUsers;
  final String totalBalance;
  final int totalIntegral;

  UserStatisticsModel({
    required this.totalUsers,
    required this.activeUsers,
    required this.inactiveUsers,
    required this.adminUsers,
    required this.doctorUsers,
    required this.referrerUsers,
    required this.todayNewUsers,
    required this.thisWeekNewUsers,
    required this.thisMonthNewUsers,
    required this.totalBalance,
    required this.totalIntegral,
  });

  factory UserStatisticsModel.fromJson(Map<String, dynamic> json) {
    return UserStatisticsModel(
      totalUsers: json['total_users'] ?? 0,
      activeUsers: json['active_users'] ?? 0,
      inactiveUsers: json['inactive_users'] ?? 0,
      adminUsers: json['admin_users'] ?? 0,
      doctorUsers: json['doctor_users'] ?? 0,
      referrerUsers: json['referrer_users'] ?? 0,
      todayNewUsers: json['today_new_users'] ?? 0,
      thisWeekNewUsers: json['this_week_new_users'] ?? 0,
      thisMonthNewUsers: json['this_month_new_users'] ?? 0,
      totalBalance: json['total_balance']?.toString() ?? '0.00',
      totalIntegral: json['total_integral'] ?? 0,
    );
  }
}

/// 用户令牌模型
class UserTokenModel {
  final int id;
  final String token;
  final String deviceType;
  final String expiresAt;
  final String createdAt;
  final bool isExpired;

  UserTokenModel({
    required this.id,
    required this.token,
    required this.deviceType,
    required this.expiresAt,
    required this.createdAt,
    required this.isExpired,
  });

  factory UserTokenModel.fromJson(Map<String, dynamic> json) {
    return UserTokenModel(
      id: json['id'] ?? 0,
      token: json['token'] ?? '',
      deviceType: json['device_type'] ?? '',
      expiresAt: json['expires_at'] ?? '',
      createdAt: json['created_at'] ?? '',
      isExpired: json['is_expired'] ?? false,
    );
  }
}

/// 用户列表响应模型
class UserListResponse {
  final List<AdminUserModel> users;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  UserListResponse({
    required this.users,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  factory UserListResponse.fromJson(Map<String, dynamic> json) {
    return UserListResponse(
      users:
          (json['users'] as List<dynamic>?)
              ?.map((item) => AdminUserModel.fromJson(item))
              .toList() ??
          [],
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      pageSize: json['page_size'] ?? 10,
      totalPages: json['total_pages'] ?? 1,
    );
  }
}
