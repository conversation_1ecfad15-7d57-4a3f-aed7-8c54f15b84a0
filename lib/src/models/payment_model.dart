// ignore_for_file: dangling_library_doc_comments

import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';

/// 支付相关数据模型

/// 支付创建请求模型
class ProductOrderPaymentIn {
  final String payType; // "app" 或 "jsapi"
  final String? openid; // 微信用户openid，jsapi支付时必填

  const ProductOrderPaymentIn({required this.payType, this.openid});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {'pay_type': payType};

    if (openid != null) {
      data['openid'] = openid;
    }

    return data;
  }

  /// 验证数据有效性
  bool isValid() {
    // jsapi支付时openid必填
    if (payType == 'jsapi' && (openid == null || openid!.isEmpty)) {
      return false;
    }

    // 支付类型必须是app或jsapi
    if (payType != 'app' && payType != 'jsapi') {
      return false;
    }

    return true;
  }
}

/// 支付创建响应模型
class ProductOrderPaymentOut {
  final String orderSn; // 支付订单号
  final Map<String, dynamic> payParams; // 微信支付参数
  final int paySource; // 支付来源 1=APP, 2=小程序

  const ProductOrderPaymentOut({
    required this.orderSn,
    required this.payParams,
    required this.paySource,
  });

  factory ProductOrderPaymentOut.fromJson(Map<String, dynamic> json) {
    return ProductOrderPaymentOut(
      orderSn: json['order_sn'] ?? '',
      payParams: json['pay_params'] ?? {},
      paySource: json['pay_source'] ?? 1,
    );
  }

  /// 是否为APP支付
  bool get isAppPayment => paySource == 1;

  /// 是否为小程序支付
  bool get isMiniProgramPayment => paySource == 2;

  /// 获取APP支付参数
  AppPayParams? get appPayParams {
    if (!isAppPayment) return null;
    return AppPayParams.fromJson(payParams);
  }

  /// 获取小程序支付参数
  MiniProgramPayParams? get miniProgramPayParams {
    if (!isMiniProgramPayment) return null;
    return MiniProgramPayParams.fromJson(payParams);
  }
}

/// APP支付参数
class AppPayParams {
  final String appid;
  final String partnerid;
  final String prepayid;
  final String package;
  final String nonceStr;
  final String timestamp;
  final String signType;
  final String paySign;

  const AppPayParams({
    required this.appid,
    required this.partnerid,
    required this.prepayid,
    required this.package,
    required this.nonceStr,
    required this.timestamp,
    required this.signType,
    required this.paySign,
  });

  factory AppPayParams.fromJson(Map<String, dynamic> json) {
    return AppPayParams(
      appid: json['appid'] ?? '',
      partnerid: json['partnerid'] ?? '',
      prepayid: json['prepayid'] ?? '',
      package: json['package'] ?? '',
      nonceStr: json['nonce_str'] ?? '',
      timestamp: json['timestamp'] ?? '',
      signType: json['sign_type'] ?? '',
      paySign: json['pay_sign'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'appid': appid,
      'partnerid': partnerid,
      'prepayid': prepayid,
      'package': package,
      'nonce_str': nonceStr,
      'timestamp': timestamp,
      'sign_type': signType,
      'pay_sign': paySign,
    };
  }
}

/// 小程序支付参数
class MiniProgramPayParams {
  final String timeStamp;
  final String nonceStr;
  final String package;
  final String signType;
  final String paySign;

  const MiniProgramPayParams({
    required this.timeStamp,
    required this.nonceStr,
    required this.package,
    required this.signType,
    required this.paySign,
  });

  factory MiniProgramPayParams.fromJson(Map<String, dynamic> json) {
    return MiniProgramPayParams(
      timeStamp: json['timeStamp'] ?? '',
      nonceStr: json['nonceStr'] ?? '',
      package: json['package'] ?? '',
      signType: json['signType'] ?? '',
      paySign: json['paySign'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'timeStamp': timeStamp,
      'nonceStr': nonceStr,
      'package': package,
      'signType': signType,
      'paySign': paySign,
    };
  }
}

/// 支付状态查询响应模型
class ProductOrderPaymentStatusOut {
  final String orderSn; // 产品订单号
  final int payStatus; // 支付状态 0=未支付, 1=已支付, 2=已退款
  final int orderStatus; // 订单状态 0=待支付, 1=待发货, 2=已发货, 3=已完成, 4=已取消
  final String? payTime; // 支付时间
  final double totalAmount; // 订单金额
  final String message; // 状态描述

  const ProductOrderPaymentStatusOut({
    required this.orderSn,
    required this.payStatus,
    required this.orderStatus,
    this.payTime,
    required this.totalAmount,
    required this.message,
  });

  factory ProductOrderPaymentStatusOut.fromJson(Map<String, dynamic> json) {
    return ProductOrderPaymentStatusOut(
      orderSn: json['order_sn'] ?? '',
      payStatus: json['pay_status'] ?? 0,
      orderStatus: json['order_status'] ?? 0,
      payTime: json['pay_time'],
      totalAmount: _parseDouble(json['total_amount']) ?? 0.0,
      message: json['message'] ?? '',
    );
  }

  /// 解析double值
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  /// 获取支付状态文本
  String get payStatusText {
    switch (payStatus) {
      case 0:
        return '未支付';
      case 1:
        return '已支付';
      case 2:
        return '已退款';
      default:
        return '未知';
    }
  }

  /// 获取订单状态文本
  String getOrderStatusText(BuildContext context) {
    switch (orderStatus) {
      case 0:
        return AppLocalizations.of(context).orderStatusPending;
      case 1:
        return AppLocalizations.of(context).orderStatusPendingShipment;
      case 2:
        return AppLocalizations.of(context).orderStatusShipped;
      case 3:
        return AppLocalizations.of(context).orderStatusCompleted;
      case 4:
        return AppLocalizations.of(context).orderStatusCancelled;
      default:
        return AppLocalizations.of(context).orderStatusUnknown;
    }
  }

  /// 是否已支付
  bool get isPaid => payStatus == 1;

  /// 是否可以支付
  bool get canPay => payStatus == 0 && orderStatus == 0;

  /// 格式化总金额
  String get formattedTotalAmount {
    return '¥${totalAmount.toStringAsFixed(2)}';
  }

  /// 格式化支付时间
  String? get formattedPayTime {
    if (payTime == null) return null;
    try {
      final dateTime = DateTime.parse(payTime!);
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return payTime;
    }
  }
}

/// 支付同步响应模型
class PaymentSyncResponse {
  final String message;

  const PaymentSyncResponse({required this.message});

  factory PaymentSyncResponse.fromJson(Map<String, dynamic> json) {
    return PaymentSyncResponse(message: json['message'] ?? '');
  }

  /// 是否同步成功
  bool get isSuccess {
    return message.contains('成功') || message.contains('已支付');
  }
}

/// 支付结果枚举
enum PaymentResult {
  success, // 支付成功
  failed, // 支付失败
  cancelled, // 用户取消
  unknown, // 未知状态
}

/// 支付错误类型
enum PaymentErrorType {
  networkError, // 网络错误
  invalidOrder, // 订单状态错误
  paymentCreateFailed, // 支付创建失败
  unauthorized, // 未授权
  orderNotFound, // 订单不存在
  syncFailed, // 同步失败
  unknown, // 未知错误
}

/// 支付异常类
class PaymentException implements Exception {
  final PaymentErrorType type;
  final String message;
  final int? statusCode;

  const PaymentException({
    required this.type,
    required this.message,
    this.statusCode,
  });

  @override
  String toString() {
    return 'PaymentException: $message (type: $type, statusCode: $statusCode)';
  }

  /// 从HTTP状态码创建异常
  factory PaymentException.fromStatusCode(int statusCode, String message) {
    PaymentErrorType type;
    switch (statusCode) {
      case 400:
        type = PaymentErrorType.invalidOrder;
        break;
      case 401:
        type = PaymentErrorType.unauthorized;
        break;
      case 404:
        type = PaymentErrorType.orderNotFound;
        break;
      case 500:
        type = PaymentErrorType.paymentCreateFailed;
        break;
      default:
        type = PaymentErrorType.unknown;
    }

    return PaymentException(
      type: type,
      message: message,
      statusCode: statusCode,
    );
  }
}
