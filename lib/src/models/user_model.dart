/// VIP 数据模型
class VipData {
  final int userVipId;
  final int vipId;
  final int type;
  final String typeName;
  final String vipName;
  final String startTime;
  final String endTime;

  VipData({
    required this.userVipId,
    required this.vipId,
    required this.type,
    required this.typeName,
    required this.vipName,
    required this.startTime,
    required this.endTime,
  });

  factory VipData.fromJson(Map<String, dynamic> json) {
    return VipData(
      userVipId: json['user_vip_id'] ?? 0,
      vipId: json['vip_id'] ?? 0,
      type: json['type'] ?? 0,
      typeName: json['type_name'] ?? '',
      vipName: json['vip_name'] ?? '',
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_vip_id': userVipId,
      'vip_id': vipId,
      'type': type,
      'type_name': typeName,
      'vip_name': vipName,
      'start_time': startTime,
      'end_time': endTime,
    };
  }
}

/// 用户模型类
class UserModel {
  final int id;
  final String username;
  final String phone;
  final String token;
  final bool? vip;
  final VipData? vipData;
  final bool isAdmin;
  final bool isDoctor;
  final int? doctorId;

  UserModel({
    required this.id,
    required this.username,
    required this.phone,
    required this.token,
    this.vip,
    this.vipData,
    this.isAdmin = false,
    this.isDoctor = false,
    this.doctorId,
  });

  /// 从JSON映射创建用户模型
  factory UserModel.fromJson(Map<String, dynamic> json) {
    // App登录接口现在返回input_token字段，优先使用它
    String tokenValue = '';
    if (json['input_token'] != null) {
      tokenValue = json['input_token'].toString();
    } else if (json['session_key'] != null) {
      tokenValue = json['session_key'].toString();
    } else if (json['token'] != null) {
      tokenValue = json['token'].toString();
    }

    // 解析VIP数据
    VipData? vipData;
    if (json['vip_data'] != null && json['vip_data'] is Map<String, dynamic>) {
      vipData = VipData.fromJson(json['vip_data']);
    }

    return UserModel(
      id: json['id'] is int ? json['id'] : int.parse(json['id'].toString()),
      username: json['username']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
      token: tokenValue,
      vip: json['vip'] as bool?,
      vipData: vipData,
      isAdmin: json['is_admin'] == true || json['is_admin'] == 1,
      isDoctor: json['is_doctor'] == true || json['is_doctor'] == 1,
      doctorId: json['doctor_id'] != null
          ? int.tryParse(json['doctor_id'].toString())
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'phone': phone,
      'token': token,
      'vip': vip,
      'vip_data': vipData?.toJson(),
      'is_admin': isAdmin,
      'is_doctor': isDoctor,
      'doctor_id': doctorId,
    };
  }

  /// 复制并修改属性
  UserModel copyWith({
    int? id,
    String? username,
    String? phone,
    String? token,
    bool? vip,
    VipData? vipData,
    bool? isAdmin,
    bool? isDoctor,
    int? doctorId,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      phone: phone ?? this.phone,
      token: token ?? this.token,
      vip: vip ?? this.vip,
      vipData: vipData ?? this.vipData,
      isAdmin: isAdmin ?? this.isAdmin,
      isDoctor: isDoctor ?? this.isDoctor,
      doctorId: doctorId ?? this.doctorId,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, username: $username, phone: $phone, isAdmin: $isAdmin, isDoctor: $isDoctor, doctorId: $doctorId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
