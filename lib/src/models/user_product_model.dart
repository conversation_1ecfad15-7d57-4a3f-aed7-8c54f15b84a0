import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';
import '../config/api/api_config.dart';

/// 用户端产品模型
class UserProductModel {
  final int id;
  final String name;
  final String? description;
  final String? detailedDescription;
  final double price;
  final double? originalPrice;
  final String? manufacturer;
  final List<String>? imageUrls;
  final String? mainImageUrl;
  final String? category;
  final Map<String, dynamic>? specifications;
  final int inventoryCount;
  final int salesCount;
  final int status; // 1=审核通过（用户端只显示审核通过的产品）
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int doctorId;
  final dynamic doctorName; // 支持字符串或多语言对象

  const UserProductModel({
    required this.id,
    required this.name,
    this.description,
    this.detailedDescription,
    required this.price,
    this.originalPrice,
    this.manufacturer,
    this.imageUrls,
    this.mainImageUrl,
    this.category,
    this.specifications,
    this.inventoryCount = 0,
    this.salesCount = 0,
    this.status = 1,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    required this.doctorId,
    required this.doctorName,
  });

  /// 安全解析double值，支持字符串和数字格式
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  /// 从JSON创建产品模型
  factory UserProductModel.fromJson(Map<String, dynamic> json) {
    return UserProductModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'],
      detailedDescription: json['detailed_description'],
      price: _parseDouble(json['price']) ?? 0.0,
      originalPrice: json['original_price'] != null
          ? _parseDouble(json['original_price'])
          : null,
      manufacturer: json['manufacturer'],
      imageUrls: json['image_urls'] != null
          ? List<String>.from(json['image_urls'])
          : null,
      mainImageUrl: json['main_image_url'],
      category: json['category'],
      specifications: json['specifications'] != null
          ? Map<String, dynamic>.from(json['specifications'])
          : null,
      inventoryCount: json['inventory_count'] ?? 0,
      salesCount: json['sales_count'] ?? 0,
      status: json['status'] ?? 1,
      isActive: json['is_active'] == true || json['is_active'] == 1,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : DateTime.now(),
      doctorId: json['doctor_id'] ?? 0,
      doctorName: json['doctor_name'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'detailed_description': detailedDescription,
      'price': price,
      'original_price': originalPrice,
      'manufacturer': manufacturer,
      'image_urls': imageUrls,
      'main_image_url': mainImageUrl,
      'category': category,
      'specifications': specifications,
      'inventory_count': inventoryCount,
      'sales_count': salesCount,
      'status': status,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'doctor_id': doctorId,
      'doctor_name': doctorName,
    };
  }

  /// 获取完整的主图URL
  String get fullMainImageUrl {
    if (mainImageUrl == null || mainImageUrl!.isEmpty) {
      return '';
    }

    // 使用ApiConfig中的buildImageUrl方法来处理URL
    return ApiConfig.buildImageUrl(mainImageUrl!);
  }

  /// 获取完整的详情图片URL列表
  List<String> get fullImageUrls {
    if (imageUrls == null || imageUrls!.isEmpty) {
      return [];
    }

    return imageUrls!.map((url) {
      if (url.isEmpty) return '';
      // 使用ApiConfig中的buildImageUrl方法来处理URL
      return ApiConfig.buildImageUrl(url);
    }).toList();
  }

  /// 是否有折扣
  bool get hasDiscount {
    return originalPrice != null && originalPrice! > price;
  }

  /// 折扣百分比
  double get discountPercentage {
    if (!hasDiscount) return 0.0;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }

  /// 格式化价格显示
  String get formattedPrice {
    return '¥${price.toStringAsFixed(2)}';
  }

  /// 格式化原价显示
  String? get formattedOriginalPrice {
    if (originalPrice == null) return null;
    return '¥${originalPrice!.toStringAsFixed(2)}';
  }

  @override
  String toString() {
    return 'UserProductModel(id: $id, name: $name, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProductModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 产品分类模型
class ProductCategoryModel {
  final String name;
  final int count;

  const ProductCategoryModel({required this.name, required this.count});

  factory ProductCategoryModel.fromJson(Map<String, dynamic> json) {
    return ProductCategoryModel(
      name: json['name'] ?? '',
      count: json['count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {'name': name, 'count': count};
  }
}

/// 产品列表响应模型
class ProductListResponse {
  final List<UserProductModel> products;
  final PaginationModel pagination;

  const ProductListResponse({required this.products, required this.pagination});

  factory ProductListResponse.fromJson(Map<String, dynamic> json) {
    return ProductListResponse(
      products:
          (json['products'] as List<dynamic>?)
              ?.map((item) => UserProductModel.fromJson(item))
              .toList() ??
          [],
      pagination: PaginationModel.fromJson(json['pagination'] ?? {}),
    );
  }
}

/// 分页模型
class PaginationModel {
  final int currentPage;
  final int pageSize;
  final int total;
  final int totalPages;
  final bool hasNext;
  final bool hasPrev;

  const PaginationModel({
    required this.currentPage,
    required this.pageSize,
    required this.total,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  factory PaginationModel.fromJson(Map<String, dynamic> json) {
    return PaginationModel(
      currentPage: json['current_page'] ?? 1,
      pageSize: json['page_size'] ?? 20,
      total: json['total'] ?? 0,
      totalPages: json['total_pages'] ?? 0,
      hasNext: json['has_next'] ?? false,
      hasPrev: json['has_prev'] ?? false,
    );
  }
}

/// 产品订单模型
class ProductOrderModel {
  final int id;
  final String orderSn;
  final int quantity;
  final double unitPrice;
  final double totalAmount;
  final int payStatus; // 0=未支付, 1=已支付, 2=已退款
  final int orderStatus; // 0=待支付, 1=待发货, 2=已发货, 3=已完成, 4=已取消
  final String shippingAddress;
  final String shippingPhone;
  final String shippingName;
  final String? trackingNumber;
  final String? shippingCompany;
  final String? shippingNote;
  final DateTime? payTime;
  final DateTime? shipTime;
  final DateTime? completeTime;
  final DateTime createdAt;
  final int productId;
  final String productName;
  final String? productImage;
  final int doctorId;
  final String doctorName;
  final int userId;
  final String userNickname;

  const ProductOrderModel({
    required this.id,
    required this.orderSn,
    required this.quantity,
    required this.unitPrice,
    required this.totalAmount,
    required this.payStatus,
    required this.orderStatus,
    required this.shippingAddress,
    required this.shippingPhone,
    required this.shippingName,
    this.trackingNumber,
    this.shippingCompany,
    this.shippingNote,
    this.payTime,
    this.shipTime,
    this.completeTime,
    required this.createdAt,
    required this.productId,
    required this.productName,
    this.productImage,
    required this.doctorId,
    required this.doctorName,
    required this.userId,
    required this.userNickname,
  });

  factory ProductOrderModel.fromJson(Map<String, dynamic> json) {
    return ProductOrderModel(
      id: json['id'] ?? 0,
      orderSn: json['order_sn'] ?? '',
      quantity: json['quantity'] ?? 1,
      unitPrice: UserProductModel._parseDouble(json['unit_price']) ?? 0.0,
      totalAmount: UserProductModel._parseDouble(json['total_amount']) ?? 0.0,
      payStatus: json['pay_status'] ?? 0,
      orderStatus: json['order_status'] ?? 0,
      shippingAddress: json['shipping_address'] ?? '',
      shippingPhone: json['shipping_phone'] ?? '',
      shippingName: json['shipping_name'] ?? '',
      trackingNumber: json['tracking_number'],
      shippingCompany: json['shipping_company'],
      shippingNote: json['shipping_note'],
      payTime: json['pay_time'] != null
          ? DateTime.parse(json['pay_time'])
          : null,
      shipTime: json['ship_time'] != null
          ? DateTime.parse(json['ship_time'])
          : null,
      completeTime: json['complete_time'] != null
          ? DateTime.parse(json['complete_time'])
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      productId: json['product_id'] ?? 0,
      productName: json['product_name'] ?? '',
      productImage: json['product_image'],
      doctorId: json['doctor_id'] ?? 0,
      doctorName: json['doctor_name'] ?? '',
      userId: json['user_id'] ?? 0,
      userNickname: json['user_nickname'] ?? '',
    );
  }

  /// 获取订单状态文本
  String getOrderStatusText(BuildContext context) {
    switch (orderStatus) {
      case 0:
        return AppLocalizations.of(context).orderStatusPending;
      case 1:
        return AppLocalizations.of(context).orderStatusPendingShipment;
      case 2:
        return AppLocalizations.of(context).orderStatusShipped;
      case 3:
        return AppLocalizations.of(context).orderStatusCompleted;
      case 4:
        return AppLocalizations.of(context).orderStatusCancelled;
      default:
        return AppLocalizations.of(context).orderStatusUnknown;
    }
  }

  /// 获取支付状态文本
  String getPayStatusText(BuildContext context) {
    switch (payStatus) {
      case 0:
        return AppLocalizations.of(context).payStatusUnpaid;
      case 1:
        return AppLocalizations.of(context).payStatusPaid;
      case 2:
        return AppLocalizations.of(context).payStatusRefunded;
      default:
        return AppLocalizations.of(context).orderStatusUnknown;
    }
  }

  /// 是否可以取消
  bool get canCancel {
    return orderStatus == 0; // 只有待支付状态可以取消
  }

  /// 格式化总金额
  String get formattedTotalAmount {
    return '¥${totalAmount.toStringAsFixed(2)}';
  }

  /// 是否可以发货
  bool get canShip {
    return payStatus == 1 && orderStatus == 1; // 已支付且待发货
  }

  /// 是否已发货
  bool get isShipped {
    return orderStatus >= 2; // 已发货、已完成
  }

  /// 获取物流状态文本
  String getShippingStatusText(BuildContext context) {
    if (trackingNumber?.isNotEmpty == true) {
      switch (orderStatus) {
        case 2:
          return AppLocalizations.of(context).shippingStatusWaitingReceive;
        case 3:
          return AppLocalizations.of(context).shippingStatusCompleted;
        default:
          return AppLocalizations.of(context).shippingStatusShipped;
      }
    } else {
      switch (orderStatus) {
        case 0:
          return AppLocalizations.of(context).shippingStatusPending;
        case 1:
          return AppLocalizations.of(context).shippingStatusWaitingShip;
        case 4:
          return AppLocalizations.of(context).shippingStatusCancelled;
        default:
          return AppLocalizations.of(context).shippingStatusUnknown;
      }
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_sn': orderSn,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_amount': totalAmount,
      'pay_status': payStatus,
      'order_status': orderStatus,
      'shipping_address': shippingAddress,
      'shipping_phone': shippingPhone,
      'shipping_name': shippingName,
      'tracking_number': trackingNumber,
      'shipping_company': shippingCompany,
      'shipping_note': shippingNote,
      'pay_time': payTime?.toIso8601String(),
      'ship_time': shipTime?.toIso8601String(),
      'complete_time': completeTime?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'product_id': productId,
      'product_name': productName,
      'product_image': productImage,
      'doctor_id': doctorId,
      'doctor_name': doctorName,
      'user_id': userId,
      'user_nickname': userNickname,
    };
  }
}
