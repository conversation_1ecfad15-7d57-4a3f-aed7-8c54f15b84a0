/// 用户个人资料模型类
class UserProfileModel {
  final int id;
  final String nickname;
  final String phone;
  final String? avatar;
  final int sex; // 0:未知, 1:男, 2:女
  final String? birthday; // YYYY-MM-DD格式
  final double money; // 余额
  final int integral; // 积分
  final String? disCode; // 分销码
  final bool isReferrer; // 是否为分销员
  final int referrerLevel; // 分销等级
  final String? levelName; // 分销等级名称
  final int freeTCount; // 免费翻译次数
  final bool auth; // 授权状态（是否完善了头像和昵称）
  final int registerSource; // 注册来源（1:APP, 2:小程序）
  final String? loginAt; // 最近登录时间
  final String? createdAt; // 创建时间
  final bool isAdmin; // 是否为管理员

  // 医生相关信息
  final bool isDoctor; // 是否为医生
  final int? doctorId; // 医生ID

  // 会员相关信息
  final bool? vip; // 是否为VIP
  final VipProfileData? vipData; // VIP详细信息

  UserProfileModel({
    required this.id,
    required this.nickname,
    required this.phone,
    this.avatar,
    required this.sex,
    this.birthday,
    this.money = 0.0,
    this.integral = 0,
    this.disCode,
    this.isReferrer = false,
    this.referrerLevel = 0,
    this.levelName,
    this.freeTCount = 0,
    this.auth = false,
    this.registerSource = 1,
    this.loginAt,
    this.createdAt,
    this.isAdmin = false,
    this.isDoctor = false,
    this.doctorId,
    this.vip,
    this.vipData,
  });

  /// 从JSON映射创建用户资料模型
  factory UserProfileModel.fromJson(Map<String, dynamic> json) {
    // 解析VIP数据
    VipProfileData? vipData;
    if (json['vip_data'] != null && json['vip_data'] is Map<String, dynamic>) {
      vipData = VipProfileData.fromJson(json['vip_data']);
    }

    return UserProfileModel(
      id: json['id'] is int ? json['id'] : int.parse(json['id'].toString()),
      nickname: json['nickname']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
      avatar: json['avatar']?.toString(),
      sex: json['sex'] is int ? json['sex'] : int.parse(json['sex'].toString()),
      birthday: json['birthday']?.toString(),
      money: _parseDouble(json['money']),
      integral: json['integral'] is int
          ? json['integral']
          : int.parse(json['integral']?.toString() ?? '0'),
      disCode: json['dis_code']?.toString(),
      isReferrer: json['is_referrer'] == true || json['is_referrer'] == 1,
      referrerLevel: json['referrer_level'] is int
          ? json['referrer_level']
          : int.parse(json['referrer_level']?.toString() ?? '0'),
      levelName: json['level_name']?.toString(),
      freeTCount: json['free_t_count'] is int
          ? json['free_t_count']
          : int.parse(json['free_t_count']?.toString() ?? '0'),
      auth: json['auth'] == true || json['auth'] == 1,
      registerSource: json['register_source'] is int
          ? json['register_source']
          : int.parse(json['register_source']?.toString() ?? '1'),
      loginAt: json['login_at']?.toString(),
      createdAt: json['created_at']?.toString(),
      isAdmin: json['is_admin'] == true || json['is_admin'] == 1,
      isDoctor: json['is_doctor'] == true || json['is_doctor'] == 1,
      doctorId: json['doctor_id'] != null
          ? int.tryParse(json['doctor_id'].toString())
          : null,
      vip: json['vip'] as bool?,
      vipData: vipData,
    );
  }

  /// 安全解析double值
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    try {
      return double.parse(value.toString());
    } catch (e) {
      return 0.0;
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nickname': nickname,
      'phone': phone,
      'avatar': avatar,
      'sex': sex,
      'birthday': birthday,
      'money': money,
      'integral': integral,
      'dis_code': disCode,
      'is_referrer': isReferrer,
      'referrer_level': referrerLevel,
      'level_name': levelName,
      'free_t_count': freeTCount,
      'auth': auth,
      'register_source': registerSource,
      'login_at': loginAt,
      'created_at': createdAt,
      'is_admin': isAdmin,
      'is_doctor': isDoctor,
      'doctor_id': doctorId,
      'vip': vip,
      'vip_data': vipData?.toJson(),
    };
  }

  /// 获取性别的中文显示
  String get genderDisplay {
    switch (sex) {
      case 1:
        return '男';
      case 2:
        return '女';
      default:
        return '未设置';
    }
  }

  /// 获取生日的DateTime对象
  DateTime? get birthdayDate {
    if (birthday == null || birthday!.isEmpty) return null;
    try {
      return DateTime.parse(birthday!);
    } catch (e) {
      return null;
    }
  }

  /// 获取登录时间的DateTime对象
  DateTime? get loginDateTime {
    if (loginAt == null || loginAt!.isEmpty) return null;
    try {
      return DateTime.parse(loginAt!);
    } catch (e) {
      return null;
    }
  }

  /// 获取创建时间的DateTime对象
  DateTime? get createdDateTime {
    if (createdAt == null || createdAt!.isEmpty) return null;
    try {
      return DateTime.parse(createdAt!);
    } catch (e) {
      return null;
    }
  }

  /// 检查VIP是否已过期
  bool get isVipExpired {
    if (vip != true) return true;
    if (vipData?.endDateTime == null) return false;
    return DateTime.now().isAfter(vipData!.endDateTime!);
  }

  /// 获取VIP到期时间
  DateTime? get vipExpiryDate {
    return vipData?.endDateTime;
  }

  /// 获取VIP显示文本
  String get vipDisplayText {
    if (vip == true && vipData != null) {
      return vipData!.vipName;
    } else if (vip == true) {
      return 'VIP会员';
    } else {
      return '普通用户';
    }
  }

  /// 复制并修改属性
  UserProfileModel copyWith({
    int? id,
    String? nickname,
    String? phone,
    String? avatar,
    int? sex,
    String? birthday,
    double? money,
    int? integral,
    String? disCode,
    bool? isReferrer,
    int? referrerLevel,
    String? levelName,
    int? freeTCount,
    bool? auth,
    int? registerSource,
    String? loginAt,
    String? createdAt,
    bool? isAdmin,
    bool? isDoctor,
    int? doctorId,
    bool? vip,
    VipProfileData? vipData,
  }) {
    return UserProfileModel(
      id: id ?? this.id,
      nickname: nickname ?? this.nickname,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      sex: sex ?? this.sex,
      birthday: birthday ?? this.birthday,
      money: money ?? this.money,
      integral: integral ?? this.integral,
      disCode: disCode ?? this.disCode,
      isReferrer: isReferrer ?? this.isReferrer,
      referrerLevel: referrerLevel ?? this.referrerLevel,
      levelName: levelName ?? this.levelName,
      freeTCount: freeTCount ?? this.freeTCount,
      auth: auth ?? this.auth,
      registerSource: registerSource ?? this.registerSource,
      loginAt: loginAt ?? this.loginAt,
      createdAt: createdAt ?? this.createdAt,
      isAdmin: isAdmin ?? this.isAdmin,
      isDoctor: isDoctor ?? this.isDoctor,
      doctorId: doctorId ?? this.doctorId,
      vip: vip ?? this.vip,
      vipData: vipData ?? this.vipData,
    );
  }
}

/// VIP Profile数据模型（用于用户资料）
class VipProfileData {
  final int userVipId;
  final int vipId;
  final int type;
  final String typeName;
  final String vipName;
  final String startTime;
  final String endTime;

  VipProfileData({
    required this.userVipId,
    required this.vipId,
    required this.type,
    required this.typeName,
    required this.vipName,
    required this.startTime,
    required this.endTime,
  });

  factory VipProfileData.fromJson(Map<String, dynamic> json) {
    return VipProfileData(
      userVipId: json['user_vip_id'] ?? 0,
      vipId: json['vip_id'] ?? 0,
      type: json['type'] ?? 0,
      typeName: json['type_name'] ?? '',
      vipName: json['vip_name'] ?? '',
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_vip_id': userVipId,
      'vip_id': vipId,
      'type': type,
      'type_name': typeName,
      'vip_name': vipName,
      'start_time': startTime,
      'end_time': endTime,
    };
  }

  /// 获取开始时间的DateTime对象
  DateTime? get startDateTime {
    if (startTime.isEmpty) return null;
    try {
      return DateTime.parse(startTime);
    } catch (e) {
      return null;
    }
  }

  /// 获取结束时间的DateTime对象
  DateTime? get endDateTime {
    if (endTime.isEmpty) return null;
    try {
      return DateTime.parse(endTime);
    } catch (e) {
      return null;
    }
  }

  /// 检查VIP是否已过期
  bool get isExpired {
    final end = endDateTime;
    if (end == null) return false;
    return DateTime.now().isAfter(end);
  }

  /// 获取剩余天数
  int get daysRemaining {
    final end = endDateTime;
    if (end == null) return 0;
    final now = DateTime.now();
    if (now.isAfter(end)) return 0;
    return end.difference(now).inDays;
  }
}
