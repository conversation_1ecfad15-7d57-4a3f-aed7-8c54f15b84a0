/// 购物车相关数据模型
library;

/// 购物车商品模型
class CartItemModel {
  final int id;
  final int quantity;
  final bool selected;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int productId;
  final String productName;
  final double productPrice;
  final double productOriginalPrice;
  final String? productImage;
  final int productInventory;
  final int productStatus;
  final bool productIsActive;
  final int doctorId;
  final String doctorName;
  final double subtotal;
  final bool isAvailable;

  const CartItemModel({
    required this.id,
    required this.quantity,
    required this.selected,
    required this.createdAt,
    required this.updatedAt,
    required this.productId,
    required this.productName,
    required this.productPrice,
    required this.productOriginalPrice,
    this.productImage,
    required this.productInventory,
    required this.productStatus,
    required this.productIsActive,
    required this.doctorId,
    required this.doctorName,
    required this.subtotal,
    required this.isAvailable,
  });

  /// 从JSON创建模型
  factory CartItemModel.fromJson(Map<String, dynamic> json) {
    return CartItemModel(
      id: _parseInt(json['id']),
      quantity: _parseInt(json['quantity']),
      selected: json['selected'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      productId: _parseInt(json['product_id']),
      productName: json['product_name'] as String,
      productPrice: _parseDouble(json['product_price']),
      productOriginalPrice: _parseDouble(json['product_original_price']),
      productImage: json['product_image'] as String?,
      productInventory: _parseInt(json['product_inventory']),
      productStatus: _parseInt(json['product_status']),
      productIsActive: json['product_is_active'] as bool,
      doctorId: _parseInt(json['doctor_id']),
      doctorName: json['doctor_name'] as String,
      subtotal: _parseDouble(json['subtotal']),
      isAvailable: json['is_available'] as bool,
    );
  }

  /// 安全解析整数
  static int _parseInt(dynamic value) {
    if (value is int) return value;
    if (value is String) return int.parse(value);
    if (value is double) return value.toInt();
    throw FormatException('Cannot parse int from $value');
  }

  /// 安全解析双精度浮点数
  static double _parseDouble(dynamic value) {
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.parse(value);
    throw FormatException('Cannot parse double from $value');
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quantity': quantity,
      'selected': selected,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'product_id': productId,
      'product_name': productName,
      'product_price': productPrice,
      'product_original_price': productOriginalPrice,
      'product_image': productImage,
      'product_inventory': productInventory,
      'product_status': productStatus,
      'product_is_active': productIsActive,
      'doctor_id': doctorId,
      'doctor_name': doctorName,
      'subtotal': subtotal,
      'is_available': isAvailable,
    };
  }

  /// 复制并修改部分属性
  CartItemModel copyWith({
    int? id,
    int? quantity,
    bool? selected,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? productId,
    String? productName,
    double? productPrice,
    double? productOriginalPrice,
    String? productImage,
    int? productInventory,
    int? productStatus,
    bool? productIsActive,
    int? doctorId,
    String? doctorName,
    double? subtotal,
    bool? isAvailable,
  }) {
    return CartItemModel(
      id: id ?? this.id,
      quantity: quantity ?? this.quantity,
      selected: selected ?? this.selected,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productPrice: productPrice ?? this.productPrice,
      productOriginalPrice: productOriginalPrice ?? this.productOriginalPrice,
      productImage: productImage ?? this.productImage,
      productInventory: productInventory ?? this.productInventory,
      productStatus: productStatus ?? this.productStatus,
      productIsActive: productIsActive ?? this.productIsActive,
      doctorId: doctorId ?? this.doctorId,
      doctorName: doctorName ?? this.doctorName,
      subtotal: subtotal ?? this.subtotal,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItemModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CartItemModel(id: $id, productName: $productName, quantity: $quantity, selected: $selected)';
  }
}

/// 购物车统计模型
class CartStatisticsModel {
  final int totalCount;
  final int totalQuantity;
  final int selectedCount;
  final int selectedQuantity;
  final double totalAmount;
  final int availableCount;

  const CartStatisticsModel({
    required this.totalCount,
    required this.totalQuantity,
    required this.selectedCount,
    required this.selectedQuantity,
    required this.totalAmount,
    required this.availableCount,
  });

  /// 从JSON创建模型
  factory CartStatisticsModel.fromJson(Map<String, dynamic> json) {
    return CartStatisticsModel(
      totalCount: CartItemModel._parseInt(json['total_count']),
      totalQuantity: CartItemModel._parseInt(json['total_quantity']),
      selectedCount: CartItemModel._parseInt(json['selected_count']),
      selectedQuantity: CartItemModel._parseInt(json['selected_quantity']),
      totalAmount: CartItemModel._parseDouble(json['total_amount']),
      availableCount: CartItemModel._parseInt(json['available_count']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'total_count': totalCount,
      'total_quantity': totalQuantity,
      'selected_count': selectedCount,
      'selected_quantity': selectedQuantity,
      'total_amount': totalAmount,
      'available_count': availableCount,
    };
  }

  @override
  String toString() {
    return 'CartStatisticsModel(totalCount: $totalCount, selectedCount: $selectedCount, totalAmount: $totalAmount)';
  }
}

/// 购物车列表响应模型
class CartListResponseModel {
  final List<CartItemModel> items;
  final CartStatisticsModel statistics;

  const CartListResponseModel({required this.items, required this.statistics});

  /// 从JSON创建模型
  factory CartListResponseModel.fromJson(Map<String, dynamic> json) {
    final itemsJson = json['items'] as List<dynamic>;
    final items = itemsJson
        .map((item) => CartItemModel.fromJson(item as Map<String, dynamic>))
        .toList();

    // 统计信息直接从根级别获取
    final statistics = CartStatisticsModel(
      totalCount: CartItemModel._parseInt(json['total_count']),
      totalQuantity: CartItemModel._parseInt(json['total_quantity']),
      selectedCount: CartItemModel._parseInt(json['selected_count']),
      selectedQuantity: CartItemModel._parseInt(json['selected_quantity']),
      totalAmount: CartItemModel._parseDouble(json['total_amount']),
      availableCount: CartItemModel._parseInt(json['available_count']),
    );

    return CartListResponseModel(items: items, statistics: statistics);
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'total_count': statistics.totalCount,
      'total_quantity': statistics.totalQuantity,
      'selected_count': statistics.selectedCount,
      'selected_quantity': statistics.selectedQuantity,
      'total_amount': statistics.totalAmount,
      'available_count': statistics.availableCount,
    };
  }

  /// 复制并修改部分属性
  CartListResponseModel copyWith({
    List<CartItemModel>? items,
    CartStatisticsModel? statistics,
  }) {
    return CartListResponseModel(
      items: items ?? this.items,
      statistics: statistics ?? this.statistics,
    );
  }

  @override
  String toString() {
    return 'CartListResponseModel(items: ${items.length}, statistics: $statistics)';
  }
}

/// 购物车数量响应模型
class CartCountResponseModel {
  final int totalCount;
  final int totalQuantity;

  const CartCountResponseModel({
    required this.totalCount,
    required this.totalQuantity,
  });

  /// 从JSON创建模型
  factory CartCountResponseModel.fromJson(Map<String, dynamic> json) {
    return CartCountResponseModel(
      totalCount: CartItemModel._parseInt(json['total_count']),
      totalQuantity: CartItemModel._parseInt(json['total_quantity']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {'total_count': totalCount, 'total_quantity': totalQuantity};
  }

  @override
  String toString() {
    return 'CartCountResponseModel(totalCount: $totalCount, totalQuantity: $totalQuantity)';
  }
}

/// 批量结算响应模型
class CartCheckoutResponseModel {
  final String batchId;
  final List<int> orderIds;
  final double totalAmount;
  final int orderCount;
  final String message;

  const CartCheckoutResponseModel({
    required this.batchId,
    required this.orderIds,
    required this.totalAmount,
    required this.orderCount,
    required this.message,
  });

  /// 从JSON创建模型
  factory CartCheckoutResponseModel.fromJson(Map<String, dynamic> json) {
    final orderIdsJson = json['order_ids'] as List<dynamic>;
    final orderIds = orderIdsJson
        .map((id) => CartItemModel._parseInt(id))
        .toList();

    return CartCheckoutResponseModel(
      batchId: json['batch_id'] as String,
      orderIds: orderIds,
      totalAmount: CartItemModel._parseDouble(json['total_amount']),
      orderCount: CartItemModel._parseInt(json['order_count']),
      message: json['message'] as String,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'batch_id': batchId,
      'order_ids': orderIds,
      'total_amount': totalAmount,
      'order_count': orderCount,
      'message': message,
    };
  }

  @override
  String toString() {
    return 'CartCheckoutResponseModel(batchId: $batchId, orderCount: $orderCount, totalAmount: $totalAmount)';
  }
}
