/// VIP价格套餐模型
class VipPriceModel {
  final int id;
  final String name;
  final double price; // 原价
  final double currentPrice; // 现价
  final int automaticRenewal; // 是否支持自动续费 (0=否, 1=是)
  final double? aRPrice; // 自动续费价格
  final String describe;

  const VipPriceModel({
    required this.id,
    required this.name,
    required this.price,
    required this.currentPrice,
    required this.automaticRenewal,
    this.aRPrice,
    required this.describe,
  });

  /// 从JSON创建模型实例
  factory VipPriceModel.fromJson(Map<String, dynamic> json) {
    return VipPriceModel(
      id: json['id'] as int,
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      currentPrice: (json['current_price'] as num).toDouble(),
      automaticRenewal: json['automatic_renewal'] as int,
      aRPrice: json['a_r_price'] != null
          ? (json['a_r_price'] as num).toDouble()
          : null,
      describe: json['describe'] as String,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'current_price': currentPrice,
      'automatic_renewal': automaticRenewal,
      'a_r_price': aRPrice,
      'describe': describe,
    };
  }

  /// 是否支持自动续费
  bool get isAutoRenewalSupported => automaticRenewal == 1;

  /// 获取显示的续费价格
  double get renewalPrice => aRPrice ?? currentPrice;

  /// 计算折扣百分比
  double get discountPercentage {
    if (price <= 0) return 0;
    return ((price - currentPrice) / price * 100);
  }

  /// 是否是月度会员
  bool get isMonthly =>
      name.contains('月') || name.toLowerCase().contains('month');

  /// 是否是年度会员
  bool get isYearly =>
      name.contains('年') || name.toLowerCase().contains('year');

  /// 是否是永久会员
  bool get isPermanent =>
      name.contains('永久') || name.toLowerCase().contains('permanent');

  @override
  String toString() {
    return 'VipPriceModel(id: $id, name: $name, currentPrice: $currentPrice, price: $price)';
  }
}
