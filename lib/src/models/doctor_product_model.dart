import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';
import '../config/api/api_config.dart';

/// 医生产品模型
class DoctorProductModel {
  final int id;
  final String name;
  final String? description;
  final String? detailedDescription;
  final double price;
  final double? originalPrice;
  final String? manufacturer;
  final List<String>? imageUrls;
  final String? mainImageUrl;
  final String? category;
  final Map<String, dynamic>? specifications;
  final int inventoryCount;
  final int salesCount;
  final int status; // 0=待审核, 1=审核通过, 2=审核拒绝, 3=下架
  final String? adminReviewNote; // 管理员审核备注（拒绝原因）
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int doctorId;
  final String doctorName;

  DoctorProductModel({
    required this.id,
    required this.name,
    this.description,
    this.detailedDescription,
    required this.price,
    this.originalPrice,
    this.manufacturer,
    this.imageUrls,
    this.mainImageUrl,
    this.category,
    this.specifications,
    this.inventoryCount = 0,
    this.salesCount = 0,
    this.status = 0,
    this.adminReviewNote,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    required this.doctorId,
    required this.doctorName,
  });

  /// 根据语言代码获取本地化的产品名称
  String getLocalizedName(String languageCode) {
    return _extractMultiLangTextWithLang(_rawName, name, languageCode);
  }

  /// 根据语言代码获取本地化的产品描述
  String getLocalizedDescription(String languageCode) {
    return _extractMultiLangTextWithLang(
      _rawDescription,
      description ?? '',
      languageCode,
    );
  }

  /// 根据语言代码获取本地化的详细描述
  String getLocalizedDetailedDescription(String languageCode) {
    return _extractMultiLangTextWithLang(
      _rawDetailedDescription,
      detailedDescription ?? '',
      languageCode,
    );
  }

  /// 根据语言代码获取本地化的制造商
  String getLocalizedManufacturer(String languageCode) {
    return _extractMultiLangTextWithLang(
      _rawManufacturer,
      manufacturer ?? '',
      languageCode,
    );
  }

  /// 根据语言代码获取本地化的分类
  String getLocalizedCategory(String languageCode) {
    return _extractMultiLangTextWithLang(
      _rawCategory,
      category ?? '',
      languageCode,
    );
  }

  /// 根据语言代码获取本地化的医生姓名
  String getLocalizedDoctorName(String languageCode) {
    return _extractMultiLangTextWithLang(
      _rawDoctorName,
      doctorName,
      languageCode,
    );
  }

  // 存储原始多语言数据的私有字段
  dynamic _rawName;
  dynamic _rawDescription;
  dynamic _rawDetailedDescription;
  dynamic _rawManufacturer;
  dynamic _rawCategory;
  dynamic _rawDoctorName;

  /// 安全解析double值，支持字符串和数字格式
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  /// 从多语言对象中提取文本（支持语言参数）
  static String _extractMultiLangTextWithLang(
    dynamic value,
    String defaultValue,
    String? languageCode,
  ) {
    if (value == null) return defaultValue;

    // 如果是字符串，直接返回（向后兼容）
    if (value is String) return value;

    // 如果是多语言对象，根据当前语言提取
    if (value is Map<String, dynamic>) {
      // 如果指定了语言代码，优先使用指定语言
      if (languageCode != null) {
        final text = value[languageCode]?.toString() ?? '';
        if (text.isNotEmpty) {
          return text;
        }
      }

      // 回退策略：中文 > 英文 > 维吾尔语
      final zhText = value['zh']?.toString() ?? '';
      final enText = value['en']?.toString() ?? '';
      final ugText = value['ug']?.toString() ?? '';

      if (zhText.isNotEmpty) return zhText;
      if (enText.isNotEmpty) return enText;
      if (ugText.isNotEmpty) return ugText;
    }

    return defaultValue;
  }

  /// 从多语言对象中提取文本（向后兼容）
  static String _extractMultiLangText(
    dynamic value, [
    String defaultValue = '',
  ]) {
    return _extractMultiLangTextWithLang(value, defaultValue, null);
  }

  /// 从JSON创建产品模型
  factory DoctorProductModel.fromJson(Map<String, dynamic> json) {
    final product = DoctorProductModel(
      id: json['id'] ?? 0,
      name: _extractMultiLangText(json['name'], ''),
      description: _extractMultiLangText(json['description']),
      detailedDescription: _extractMultiLangText(json['detailed_description']),
      price: _parseDouble(json['price']) ?? 0.0,
      originalPrice: json['original_price'] != null
          ? _parseDouble(json['original_price'])
          : null,
      manufacturer: _extractMultiLangText(json['manufacturer']),
      imageUrls: json['image_urls'] != null
          ? List<String>.from(json['image_urls'])
          : null,
      mainImageUrl: json['main_image_url'],
      category: _extractMultiLangText(json['category']),
      specifications: json['specifications'] != null
          ? Map<String, dynamic>.from(json['specifications'])
          : null,
      inventoryCount: json['inventory_count'] ?? 0,
      salesCount: json['sales_count'] ?? 0,
      status: json['status'] ?? 0,
      adminReviewNote: json['admin_review_note'],
      isActive: json['is_active'] == true || json['is_active'] == 1,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : DateTime.now(),
      doctorId: json['doctor_id'] ?? 0,
      doctorName: _extractMultiLangText(json['doctor_name'], ''),
    );

    // 保存原始多语言数据
    product._rawName = json['name'];
    product._rawDescription = json['description'];
    product._rawDetailedDescription = json['detailed_description'];
    product._rawManufacturer = json['manufacturer'];
    product._rawCategory = json['category'];
    product._rawDoctorName = json['doctor_name'];

    return product;
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'detailed_description': detailedDescription,
      'price': price,
      'original_price': originalPrice,
      'manufacturer': manufacturer,
      'image_urls': imageUrls,
      'main_image_url': mainImageUrl,
      'category': category,
      'specifications': specifications,
      'inventory_count': inventoryCount,
      'sales_count': salesCount,
      'status': status,
      'admin_review_note': adminReviewNote,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'doctor_id': doctorId,
      'doctor_name': doctorName,
    };
  }

  /// 获取状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '待审核';
      case 1:
        return '审核通过';
      case 2:
        return '审核拒绝';
      case 3:
        return '下架';
      default:
        return '未知';
    }
  }

  /// 获取状态颜色
  int get statusColor {
    switch (status) {
      case 0:
        return 0xFFF39C12; // 橙色
      case 1:
        return 0xFF27AE60; // 绿色
      case 2:
        return 0xFFE74C3C; // 红色
      case 3:
        return 0xFF95A5A6; // 灰色
      default:
        return 0xFFBDC3C7; // 浅灰色
    }
  }

  /// 获取完整的主图URL
  String get fullMainImageUrl {
    if (mainImageUrl == null || mainImageUrl!.isEmpty) {
      return '';
    }

    // 使用ApiConfig中的buildImageUrl方法来处理URL
    return ApiConfig.buildImageUrl(mainImageUrl!);
  }

  /// 获取完整的详情图片URL列表
  List<String> get fullImageUrls {
    if (imageUrls == null || imageUrls!.isEmpty) {
      return [];
    }

    return imageUrls!.map((url) {
      if (url.isEmpty) return '';
      // 使用ApiConfig中的buildImageUrl方法来处理URL
      return ApiConfig.buildImageUrl(url);
    }).toList();
  }

  /// 是否有折扣
  bool get hasDiscount {
    return originalPrice != null && originalPrice! > price;
  }

  /// 折扣百分比
  double get discountPercentage {
    if (!hasDiscount) return 0.0;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }

  @override
  String toString() {
    return 'DoctorProductModel(id: $id, name: $name, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DoctorProductModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 产品统计模型
class ProductStatisticsModel {
  final int totalProducts;
  final int pendingReview;
  final int approvedProducts;
  final int rejectedProducts;
  final int offlineProducts;
  final double totalSales;
  final int totalOrders;

  const ProductStatisticsModel({
    this.totalProducts = 0,
    this.pendingReview = 0,
    this.approvedProducts = 0,
    this.rejectedProducts = 0,
    this.offlineProducts = 0,
    this.totalSales = 0.0,
    this.totalOrders = 0,
  });

  /// 安全解析double值，支持字符串和数字格式
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  /// 从JSON创建统计模型
  factory ProductStatisticsModel.fromJson(Map<String, dynamic> json) {
    return ProductStatisticsModel(
      totalProducts: json['total_products'] ?? 0,
      pendingReview: json['pending_review'] ?? 0,
      approvedProducts: json['approved_products'] ?? 0,
      rejectedProducts: json['rejected_products'] ?? 0,
      offlineProducts: json['offline_products'] ?? 0,
      totalSales: _parseDouble(json['total_sales']),
      totalOrders: json['total_orders'] ?? 0,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'total_products': totalProducts,
      'pending_review': pendingReview,
      'approved_products': approvedProducts,
      'rejected_products': rejectedProducts,
      'offline_products': offlineProducts,
      'total_sales': totalSales,
      'total_orders': totalOrders,
    };
  }
}

/// 产品订单模型
class ProductOrderModel {
  final int id;
  final String orderSn;
  final int quantity;
  final double unitPrice;
  final double totalAmount;
  final int payStatus; // 0=未支付, 1=已支付, 2=已退款
  final int orderStatus; // 0=待支付, 1=待发货, 2=已发货, 3=已完成, 4=已取消
  final String? shippingAddress;
  final String? shippingPhone;
  final String? shippingName;
  final String? trackingNumber;
  final String? shippingCompany;
  final String? shippingNote;
  final DateTime? payTime;
  final DateTime? shipTime;
  final DateTime? completeTime;
  final DateTime createdAt;
  final int productId;
  final dynamic productName; // 支持多语言数据
  final String? productImage;
  final int doctorId;
  final dynamic doctorName; // 支持多语言数据
  final int userId;
  final String userNickname;

  const ProductOrderModel({
    required this.id,
    required this.orderSn,
    required this.quantity,
    required this.unitPrice,
    required this.totalAmount,
    this.payStatus = 0,
    this.orderStatus = 0,
    this.shippingAddress,
    this.shippingPhone,
    this.shippingName,
    this.trackingNumber,
    this.shippingCompany,
    this.shippingNote,
    this.payTime,
    this.shipTime,
    this.completeTime,
    required this.createdAt,
    required this.productId,
    required this.productName,
    this.productImage,
    required this.doctorId,
    required this.doctorName,
    required this.userId,
    required this.userNickname,
  });

  /// 根据语言代码获取产品名称
  String getProductName(String languageCode) {
    if (productName is Map<String, dynamic>) {
      final nameMap = productName as Map<String, dynamic>;
      return nameMap[languageCode]?.toString() ??
          nameMap['zh']?.toString() ??
          nameMap.values.first?.toString() ??
          '';
    }
    return productName?.toString() ?? '';
  }

  /// 根据语言代码获取医生名称
  String getDoctorName(String languageCode) {
    if (doctorName is Map<String, dynamic>) {
      final nameMap = doctorName as Map<String, dynamic>;
      return nameMap[languageCode]?.toString() ??
          nameMap['zh']?.toString() ??
          nameMap.values.first?.toString() ??
          '';
    }
    return doctorName?.toString() ?? '';
  }

  /// 获取完整的产品图片URL
  String get fullProductImageUrl {
    if (productImage == null || productImage!.isEmpty) {
      return '';
    }

    // 使用ApiConfig中的buildImageUrl方法来处理URL
    return ApiConfig.buildImageUrl(productImage!);
  }

  /// 安全解析double值，支持字符串和数字格式
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  /// 从JSON创建订单模型
  factory ProductOrderModel.fromJson(Map<String, dynamic> json) {
    return ProductOrderModel(
      id: json['id'] ?? 0,
      orderSn: json['order_sn'] ?? '',
      quantity: json['quantity'] ?? 0,
      unitPrice: _parseDouble(json['unit_price']),
      totalAmount: _parseDouble(json['total_amount']),
      payStatus: json['pay_status'] ?? 0,
      orderStatus: json['order_status'] ?? 0,
      shippingAddress: json['shipping_address'],
      shippingPhone: json['shipping_phone'],
      shippingName: json['shipping_name'],
      trackingNumber: json['tracking_number'],
      shippingCompany: json['shipping_company'],
      shippingNote: json['shipping_note'],
      payTime: json['pay_time'] != null
          ? DateTime.parse(json['pay_time'])
          : null,
      shipTime: json['ship_time'] != null
          ? DateTime.parse(json['ship_time'])
          : null,
      completeTime: json['complete_time'] != null
          ? DateTime.parse(json['complete_time'])
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      productId: json['product_id'] ?? 0,
      productName: json['product_name'], // 保留原始多语言对象
      productImage: json['product_image'],
      doctorId: json['doctor_id'] ?? 0,
      doctorName: json['doctor_name'], // 保留原始多语言对象
      userId: json['user_id'] ?? 0,
      userNickname: json['user_nickname'] ?? '',
    );
  }

  /// 获取支付状态文本
  String get payStatusText {
    switch (payStatus) {
      case 0:
        return '未支付';
      case 1:
        return '已支付';
      case 2:
        return '已退款';
      default:
        return '未知';
    }
  }

  /// 获取订单状态文本
  String getOrderStatusText(BuildContext context) {
    switch (orderStatus) {
      case 0:
        return AppLocalizations.of(context).orderStatusPending;
      case 1:
        return AppLocalizations.of(context).orderStatusPendingShipment;
      case 2:
        return AppLocalizations.of(context).orderStatusShipped;
      case 3:
        return AppLocalizations.of(context).orderStatusCompleted;
      case 4:
        return AppLocalizations.of(context).orderStatusCancelled;
      default:
        return AppLocalizations.of(context).orderStatusUnknown;
    }
  }

  /// 格式化总金额
  String get formattedTotalAmount {
    return '¥${totalAmount.toStringAsFixed(2)}';
  }

  /// 是否可以发货
  bool get canShip {
    return payStatus == 1 && orderStatus == 1; // 已支付且待发货
  }

  /// 是否已发货
  bool get isShipped {
    return orderStatus >= 2; // 已发货、已完成
  }

  /// 获取物流状态文本
  String getShippingStatusText(BuildContext context) {
    if (trackingNumber?.isNotEmpty == true) {
      switch (orderStatus) {
        case 2:
          return AppLocalizations.of(context).shippingStatusWaitingReceive;
        case 3:
          return AppLocalizations.of(context).shippingStatusCompleted;
        default:
          return AppLocalizations.of(context).shippingStatusShipped;
      }
    } else {
      switch (orderStatus) {
        case 0:
          return AppLocalizations.of(context).shippingStatusPending;
        case 1:
          return AppLocalizations.of(context).shippingStatusWaitingShip;
        case 4:
          return AppLocalizations.of(context).shippingStatusCancelled;
        default:
          return AppLocalizations.of(context).shippingStatusUnknown;
      }
    }
  }
}
