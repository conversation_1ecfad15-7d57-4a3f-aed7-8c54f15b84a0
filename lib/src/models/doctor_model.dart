import '../config/api/api_config.dart';

/// 医生信息模型
class DoctorModel {
  final int id;
  final String name;
  final String? specialty;
  final String? specialties; // 擅长领域，逗号分隔
  final String? avatarUrl;
  final String? description;
  final String? detailedInfo;
  final String? systemPrompt;
  final String? llmModelName;
  final bool isActive;
  final int likesCount;
  final int favoritesCount;
  final bool? isLiked;
  final bool? isFavorited;
  final int yearsOfExperience;
  final double rating;
  final String? digitalHumanUrl;
  final String? phone;
  final String? address;

  // 为了兼容现有UI，保留这些字段，但从specialty派生
  // 注意：title现在需要在UI层使用AppLocalizations.of(context).physician
  String get title => 'Physician'; // 默认职称，建议在UI层使用国际化
  String get specialization => specialty ?? ''; // 专业领域，不提供默认值
  String get avatarPath => avatarUrl ?? ''; // 头像路径
  String get safeDescription => description ?? ''; // 描述文本，不提供默认值
  String get safeDetailedInfo => detailedInfo ?? ''; // 详细信息，不提供默认值
  int get experience => yearsOfExperience; // 使用API返回的工作年限
  bool get isOnline => true; // 默认在线状态

  /// 获取擅长领域列表
  List<String> get specialtiesList {
    if (specialties == null || specialties!.isEmpty) {
      return [];
    }
    return specialties!
        .split(',')
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
  }

  /// 获取完整的头像URL
  String get fullAvatarUrl {
    if (avatarUrl == null || avatarUrl!.isEmpty) {
      return '';
    }

    // 使用ApiConfig中的buildImageUrl方法来处理URL
    return ApiConfig.buildImageUrl(avatarUrl!);
  }

  DoctorModel({
    required this.id,
    required this.name,
    this.specialty,
    this.specialties,
    this.avatarUrl,
    this.description,
    this.detailedInfo,
    this.systemPrompt,
    this.llmModelName,
    this.isActive = true,
    this.likesCount = 0,
    this.favoritesCount = 0,
    this.isLiked,
    this.isFavorited,
    this.yearsOfExperience = 0,
    this.rating = 0.0,
    this.digitalHumanUrl,
    this.phone,
    this.address,
  });

  /// 根据语言代码获取本地化的姓名
  String getLocalizedName(String languageCode) {
    return _extractMultiLangText(_rawName, name, languageCode);
  }

  /// 根据语言代码获取本地化的专科
  String getLocalizedSpecialty(String languageCode) {
    return _extractMultiLangText(_rawSpecialty, specialty ?? '', languageCode);
  }

  /// 根据语言代码获取本地化的描述
  String getLocalizedDescription(String languageCode) {
    return _extractMultiLangText(
      _rawDescription,
      description ?? '',
      languageCode,
    );
  }

  /// 根据语言代码获取本地化的详细信息
  String getLocalizedDetailedInfo(String languageCode) {
    return _extractMultiLangText(
      _rawDetailedInfo,
      detailedInfo ?? '',
      languageCode,
    );
  }

  /// 根据语言代码获取本地化的擅长领域
  String getLocalizedSpecialties(String languageCode) {
    return _extractMultiLangText(
      _rawSpecialties,
      specialties ?? '',
      languageCode,
    );
  }

  // 存储原始多语言数据的私有字段
  dynamic _rawName;
  dynamic _rawSpecialty;
  dynamic _rawDescription;
  dynamic _rawDetailedInfo;
  dynamic _rawSpecialties;

  /// 从多语言对象中提取文本
  static String _extractMultiLangText(
    dynamic value, [
    String defaultValue = '',
    String? languageCode,
  ]) {
    if (value == null) return defaultValue;

    // 如果是字符串，直接返回（向后兼容）
    if (value is String) return value;

    // 如果是多语言对象，根据当前语言提取
    if (value is Map<String, dynamic>) {
      print('🌐 DoctorModel: 解析多语言字段，原始数据: $value, 目标语言: $languageCode');

      // 如果指定了语言代码，优先使用指定语言
      if (languageCode != null) {
        final text = value[languageCode]?.toString() ?? '';
        if (text.isNotEmpty) {
          print('🌐 DoctorModel: 找到目标语言($languageCode)文本: $text');
          return text;
        }
      }

      // 回退策略：中文 > 英文 > 维吾尔语
      final zhText = value['zh']?.toString() ?? '';
      final enText = value['en']?.toString() ?? '';
      final ugText = value['ug']?.toString() ?? '';

      if (zhText.isNotEmpty) {
        print('🌐 DoctorModel: 使用中文回退: $zhText');
        return zhText;
      }
      if (enText.isNotEmpty) {
        print('🌐 DoctorModel: 使用英文回退: $enText');
        return enText;
      }
      if (ugText.isNotEmpty) {
        print('🌐 DoctorModel: 使用维吾尔语回退: $ugText');
        return ugText;
      }
    }

    return defaultValue;
  }

  /// 从JSON创建医生模型
  factory DoctorModel.fromJson(
    Map<String, dynamic> json, [
    String? languageCode,
  ]) {
    final doctor = DoctorModel(
      id: json['id'] ?? 0,
      name: _extractMultiLangText(json['name'], '', languageCode),
      specialty: _extractMultiLangText(json['specialty'], '', languageCode),
      specialties: _extractMultiLangText(json['specialties'], '', languageCode),
      avatarUrl: json['avatar_url'],
      description: _extractMultiLangText(json['description'], '', languageCode),
      detailedInfo: _extractMultiLangText(
        json['detailed_info'],
        '',
        languageCode,
      ),
      systemPrompt: _extractMultiLangText(
        json['system_prompt'],
        '',
        languageCode,
      ),
      llmModelName: json['llm_model_name'],
      isActive: json['is_active'] == true || json['is_active'] == 1,
      likesCount: json['likes_count'] ?? 0,
      favoritesCount: json['favorites_count'] ?? 0,
      isLiked: json['is_liked'],
      isFavorited: json['is_favorited'],
      yearsOfExperience: json['years_of_experience'] ?? 0,
      rating: (json['rating'] ?? 0.0).toDouble(),
      digitalHumanUrl: json['digital_human_url'],
      phone: json['phone'],
      address: json['address'],
    );

    // 保存原始多语言数据
    doctor._rawName = json['name'];
    doctor._rawSpecialty = json['specialty'];
    doctor._rawDescription = json['description'];
    doctor._rawDetailedInfo = json['detailed_info'];
    doctor._rawSpecialties = json['specialties'];

    return doctor;
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'specialty': specialty,
      'specialties': specialties,
      'avatar_url': avatarUrl,
      'description': description,
      'detailed_info': detailedInfo,
      'system_prompt': systemPrompt,
      'llm_model_name': llmModelName,
      'is_active': isActive,
      'likes_count': likesCount,
      'favorites_count': favoritesCount,
      'is_liked': isLiked,
      'is_favorited': isFavorited,
      'years_of_experience': yearsOfExperience,
      'rating': rating,
      'digital_human_url': digitalHumanUrl,
      'phone': phone,
      'address': address,
    };
  }

  @override
  String toString() {
    return 'DoctorModel(id: $id, name: $name, specialty: $specialty)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DoctorModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
