// ignore_for_file: dangling_library_doc_comments

/// 物流相关数据模型

/// 发货请求模型
class ShipOrderRequest {
  final String trackingNumber;
  final String? shippingCompany;
  final String? shippingNote;

  const ShipOrderRequest({
    required this.trackingNumber,
    this.shippingCompany,
    this.shippingNote,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'tracking_number': trackingNumber,
    };

    if (shippingCompany != null && shippingCompany!.isNotEmpty) {
      data['shipping_company'] = shippingCompany;
    }

    if (shippingNote != null && shippingNote!.isNotEmpty) {
      data['shipping_note'] = shippingNote;
    }

    return data;
  }

  /// 验证数据有效性
  bool isValid() {
    return trackingNumber.isNotEmpty;
  }
}

/// 发货响应模型
class ShipOrderResponse {
  final int orderId;
  final String orderSn;
  final String trackingNumber;
  final String? shippingCompany;
  final String? shippingNote;
  final DateTime shipTime;
  final int orderStatus;
  final int payStatus;
  final String? shippingAddress;
  final String? shippingPhone;
  final String? shippingName;
  final int productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final double totalAmount;
  final int userId;
  final String userNickname;

  const ShipOrderResponse({
    required this.orderId,
    required this.orderSn,
    required this.trackingNumber,
    this.shippingCompany,
    this.shippingNote,
    required this.shipTime,
    required this.orderStatus,
    required this.payStatus,
    this.shippingAddress,
    this.shippingPhone,
    this.shippingName,
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.totalAmount,
    required this.userId,
    required this.userNickname,
  });

  factory ShipOrderResponse.fromJson(Map<String, dynamic> json) {
    return ShipOrderResponse(
      orderId: json['order_id'] ?? 0,
      orderSn: json['order_sn'] ?? '',
      trackingNumber: json['tracking_number'] ?? '',
      shippingCompany: json['shipping_company'],
      shippingNote: json['shipping_note'],
      shipTime: DateTime.parse(json['ship_time']),
      orderStatus: json['order_status'] ?? 0,
      payStatus: json['pay_status'] ?? 0,
      shippingAddress: json['shipping_address'],
      shippingPhone: json['shipping_phone'],
      shippingName: json['shipping_name'],
      productId: json['product_id'] ?? 0,
      productName: json['product_name'] ?? '',
      quantity: json['quantity'] ?? 0,
      unitPrice: _parseDouble(json['unit_price']),
      totalAmount: _parseDouble(json['total_amount']),
      userId: json['user_id'] ?? 0,
      userNickname: json['user_nickname'] ?? '',
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }
}

/// 物流状态响应模型
class ShippingStatusResponse {
  final int orderId;
  final String orderSn;
  final int orderStatus;
  final int payStatus;
  final String? trackingNumber;
  final String? shippingCompany;
  final String? shippingNote;
  final DateTime? shipTime;
  final DateTime? payTime;
  final DateTime? completeTime;
  final DateTime createdAt;
  final String statusText;
  final String shippingStatusText;

  const ShippingStatusResponse({
    required this.orderId,
    required this.orderSn,
    required this.orderStatus,
    required this.payStatus,
    this.trackingNumber,
    this.shippingCompany,
    this.shippingNote,
    this.shipTime,
    this.payTime,
    this.completeTime,
    required this.createdAt,
    required this.statusText,
    required this.shippingStatusText,
  });

  factory ShippingStatusResponse.fromJson(Map<String, dynamic> json) {
    return ShippingStatusResponse(
      orderId: json['order_id'] ?? 0,
      orderSn: json['order_sn'] ?? '',
      orderStatus: json['order_status'] ?? 0,
      payStatus: json['pay_status'] ?? 0,
      trackingNumber: json['tracking_number'],
      shippingCompany: json['shipping_company'],
      shippingNote: json['shipping_note'],
      shipTime: json['ship_time'] != null
          ? DateTime.parse(json['ship_time'])
          : null,
      payTime: json['pay_time'] != null
          ? DateTime.parse(json['pay_time'])
          : null,
      completeTime: json['complete_time'] != null
          ? DateTime.parse(json['complete_time'])
          : null,
      createdAt: DateTime.parse(json['created_at']),
      statusText: json['status_text'] ?? '',
      shippingStatusText: json['shipping_status_text'] ?? '',
    );
  }

  /// 是否已发货
  bool get isShipped {
    return trackingNumber?.isNotEmpty == true;
  }

  /// 是否可以查看物流
  bool get canTrack {
    return isShipped;
  }
}

/// 常用快递公司列表
class ShippingCompanies {
  static const List<String> companies = [
    '顺丰速运',
    '中通快递',
    '圆通速递',
    '申通快递',
    '韵达速递',
    '百世快递',
    '德邦快递',
    '京东物流',
    '邮政EMS',
    '天天快递',
    '宅急送',
    '其他',
  ];

  static String getCompanyName(String? company) {
    if (company == null || company.isEmpty) {
      return '未知快递';
    }
    return company;
  }
}
