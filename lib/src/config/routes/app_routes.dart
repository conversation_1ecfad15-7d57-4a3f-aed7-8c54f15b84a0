import 'package:flutter/material.dart';
import '../../presentation/splash/screens/splash_screen.dart';
import '../../presentation/auth/auth.dart';
import '../../presentation/auth/screens/password_reset_screen.dart';
import '../../presentation/core/screens/home_screen.dart';
import '../../presentation/settings/screens/profile_edit_screen.dart';

/// 应用路由
class AppRoutes {
  // 路由名称
  static const String splash = '/';
  static const String login = '/login';
  static const String passwordLogin = '/password_login';
  static const String passwordReset = '/password_reset';
  static const String register = '/register';
  static const String home = '/home';
  static const String profileEdit = '/profile-edit';

  /// 获取应用程序的路由配置
  static Map<String, PageRouteBuilder Function(RouteSettings)> getRoutes() {
    return {
      splash: (settings) => _buildPageRoute(
        settings: settings,
        builder: (context) => const SplashScreen(),
        transitionType: PageTransitionType.fade,
      ),
      login: (settings) => _buildPageRoute(
        settings: settings,
        builder: (context) => const LoginScreen(),
      ),
      passwordLogin: (settings) => _buildPageRoute(
        settings: settings,
        builder: (context) => const PasswordLoginScreen(),
      ),
      passwordReset: (settings) => _buildPageRoute(
        settings: settings,
        builder: (context) => const PasswordResetScreen(),
      ),
      register: (settings) => _buildPageRoute(
        settings: settings,
        builder: (context) => const RegisterScreen(),
      ),
      home: (settings) => _buildPageRoute(
        settings: settings,
        builder: (context) {
          final args = settings.arguments as Map<String, dynamic>?;
          return HomeScreen(
            selectedDoctorId: args?['selectedDoctorId'] as int?,
            switchToHealthAssistant:
                args?['switchToHealthAssistant'] as bool? ?? false,
          );
        },
        transitionType: PageTransitionType.rightToLeft,
      ),
      profileEdit: (settings) => _buildPageRoute(
        settings: settings,
        builder: (context) => const ProfileEditScreen(),
        transitionType: PageTransitionType.rightToLeft,
      ),
    };
  }

  /// 创建带有过渡动画的页面路由
  static PageRouteBuilder _buildPageRoute({
    required RouteSettings settings,
    required WidgetBuilder builder,
    PageTransitionType transitionType = PageTransitionType.rightToLeft,
  }) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => builder(context),
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        switch (transitionType) {
          case PageTransitionType.fade:
            return FadeTransition(opacity: animation, child: child);
          case PageTransitionType.rightToLeft:
            return SlideTransition(
              position:
                  Tween<Offset>(
                    begin: const Offset(1, 0),
                    end: Offset.zero,
                  ).animate(
                    CurvedAnimation(parent: animation, curve: Curves.easeInOut),
                  ),
              child: child,
            );
          case PageTransitionType.leftToRight:
            return SlideTransition(
              position:
                  Tween<Offset>(
                    begin: const Offset(-1, 0),
                    end: Offset.zero,
                  ).animate(
                    CurvedAnimation(parent: animation, curve: Curves.easeInOut),
                  ),
              child: child,
            );
          case PageTransitionType.bottomToTop:
            return SlideTransition(
              position:
                  Tween<Offset>(
                    begin: const Offset(0, 1),
                    end: Offset.zero,
                  ).animate(
                    CurvedAnimation(parent: animation, curve: Curves.easeInOut),
                  ),
              child: child,
            );
        }
      },
    );
  }

  /// 应用程序初始路由
  static String get initialRoute => splash;

  /// 生成路由
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final routes = getRoutes();
    final builder = routes[settings.name];
    if (builder != null) {
      return builder(settings);
    }
    return _buildPageRoute(
      settings: settings,
      builder: (_) => const Scaffold(body: Center(child: Text('页面不存在'))),
    );
  }

  /// 导航到登录页面
  static void navigateToLogin(BuildContext context) {
    Navigator.pushNamed(context, login);
  }

  /// 从登录页面导航到注册页面
  static void navigateToRegister(BuildContext context) {
    Navigator.pushNamed(context, register);
  }

  /// 从登录页面导航到密码登录页面
  static void navigateToPasswordLogin(BuildContext context) {
    Navigator.pushNamed(context, passwordLogin);
  }

  /// 导航到密码重置页面
  static void navigateToPasswordReset(BuildContext context) {
    Navigator.pushNamed(context, passwordReset);
  }

  /// 处理返回到前一个页面
  static void goBack(BuildContext context) {
    Navigator.of(context).pop();
  }

  /// 从启动页面导航到主页
  static void navigateToHome(BuildContext context) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      home,
      (route) => false, // 清除所有路由历史，包括启动页
    );
  }

  /// 处理导航后操作（防止返回到启动页）
  static Future<bool> handleBackPress(BuildContext context) async {
    // 检查当前路由名称
    final currentRoute = ModalRoute.of(context)?.settings.name;

    // 如果当前是启动页，则导航到主页
    if (currentRoute == splash) {
      navigateToHome(context);
      return false; // 阻止默认的返回行为
    }

    return true; // 允许默认的返回行为
  }
}

/// 页面过渡效果类型
enum PageTransitionType { fade, rightToLeft, leftToRight, bottomToTop }
