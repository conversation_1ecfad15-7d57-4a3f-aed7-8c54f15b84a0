import 'package:flutter/material.dart';

class AppColors {
  // 应用主色调
  static const Color primary = Color(0xFF109D58);
  static const Color secondary = Color(0xFF12B768);

  // 启动页使用的颜色
  static const Color splashGradientStart = Color(0xFFAB78FF); // 淡紫色渐变起始
  static const Color splashGradientMiddle = Color(0xFF9BB8FF); // 中间蓝色
  static const Color splashGradientEnd = Color(0xFF9FDCDC); // 浅绿蓝色渐变结束
  static const Color purpleCircle = Color(0xFFB35FFF);
  static const Color greenCircle = Color(0xFF7BFF30); // 中间绿色圆
  static const Color locationIconBlue = Color(0xFF0D9EFD); // 定位图标蓝色
  static const Color locationIconTeal = Color(0xFF30DDD2); // 定位图标青色
  static const Color textBlue = Color(0xFF5A96FF); // 文本蓝色
  static const Color transparentCircle = Color(0x33FFFFFF); // 半透明圆圈
  static const Color buttonBackground = Color(0xFFC8A6C7); // 按钮背景色
  static const Color circleOutline = Color(0x2B66C09E);
  static const Color transparentWhite = Color(0x19FFFFFF);
  static const Color greenGlow = Color(0x6B0CEFAD);
  static const Color borderColor = Color(0xFF3D2D2C);
  static const Color bottomContainerColor = Color(0x7F7F3A44);
  static const Color secondBorderColor = Color(0xFF2B3833);
  static const Color thirdBorderColor = Color(0xFF433A46);
  static const Color greenCircleSmall = Color(0x1E28FF30);
  static const Color carCircleBackground = Color(0xAAA0E5C8); // 底部汽车圆形背景
}
