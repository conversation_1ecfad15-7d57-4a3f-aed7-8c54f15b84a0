/// API配置文件 - 包含全局API地址及相关配置
class ApiConfig {
  /// API基础URL
  static String baseUrl = "https://appdava.sulmas.com.cn";

  /// 获取域名（不包含协议）
  static String get domainName {
    return baseUrl.replaceAll('https://', '').replaceAll('http://', '');
  }

  /// 注册验证码接口
  static String get sendRegisterSmsCodeUrl =>
      "$baseUrl/applet/v1/app/send_sms_code";

  /// 登录验证码接口
  static String get sendLoginSmsCodeUrl =>
      "$baseUrl/applet/v1/app/send_login_code";

  /// 短信登录接口
  static String get smsLoginUrl => "$baseUrl/applet/v1/app/sms_login";

  /// 密码登录接口
  static String get passwordLoginUrl => "$baseUrl/applet/v1/app/login";

  /// 用户注册接口
  static String get registerUrl => "$baseUrl/applet/v1/app/register";

  /// 获取用户个人资料接口
  static String get getUserProfileUrl => "$baseUrl/applet/v1/app/profile";

  /// 更新用户个人资料接口
  static String get updateUserProfileUrl =>
      "$baseUrl/applet/v1/app/update_profile";

  /// 上传头像接口
  static String get uploadAvatarUrl => "$baseUrl/applet/v1/app/upload_avatar";

  /// 修改密码接口
  static String get changePasswordUrl =>
      "$baseUrl/applet/v1/app/change_password";

  /// 发送忘记密码验证码接口
  static String get sendForgotPasswordCodeUrl =>
      "$baseUrl/api/v1/send_forgot_password_code";

  /// 忘记密码重置接口
  static String get forgotPasswordUrl => "$baseUrl/api/v1/forgot_password";

  /// VIP价格列表接口
  static String get vipPriceListUrl => "$baseUrl/applet/index/vip_list";

  // =========================== 分销管理相关接口 ===========================

  /// 申请成为分销员接口
  static String get applyDistributorUrl => "$baseUrl/applet/user/apply_ref";

  /// 获取分销员收入信息接口
  static String get getDistributorMoneysUrl =>
      "$baseUrl/applet/user/get_moneys";

  /// 获取余额变动记录接口
  static String get getBalanceRecordsUrl =>
      "$baseUrl/applet/user/get_balance_r";

  /// 获取下级用户列表接口
  static String get getLevelUsersUrl => "$baseUrl/applet/user/l_level_users";

  /// 修改下级分销等级接口
  static String get updateDistributorLevelUrl =>
      "$baseUrl/applet/user/up_dist_level";

  /// 获取分销海报列表接口
  static String get getDistributorPostersUrl =>
      "$baseUrl/applet/user/get_poster";

  /// 获取分销等级列表接口 (通用接口)
  static String get getDistributorLevelsUrl =>
      "$baseUrl/applet/index/dist_level";

  // =========================== AI导游聊天接口 ===========================

  /// 获取对话列表接口
  static String get getConversationsUrl =>
      "$baseUrl/applet/v1/chat/conversations";

  /// 获取对话消息接口 (需要对话ID)
  static String getMessagesUrl(String conversationId) =>
      "$baseUrl/applet/v1/chat/conversations/$conversationId/messages";

  /// 发送消息接口 (需要对话ID)
  static String sendMessageUrl(String conversationId) =>
      "$baseUrl/applet/v1/chat/conversations/$conversationId/messages";

  /// 创建新对话接口
  static String get createConversationUrl =>
      "$baseUrl/applet/v1/chat/conversations";

  /// 删除对话接口 (需要对话ID)
  static String deleteConversationUrl(String conversationId) =>
      "$baseUrl/applet/v1/chat/conversations/$conversationId";

  /// 更新对话标题接口 (需要对话ID)
  static String updateConversationTitleUrl(String conversationId) =>
      "$baseUrl/applet/v1/chat/conversations/$conversationId/title";

  /// 删除所有对话接口
  static String get deleteAllConversationsUrl =>
      "$baseUrl/applet/v1/chat/conversations/all";

  /// 上传聊天图片接口
  static String get uploadChatImageUrl =>
      "$baseUrl/applet/v1/chat/upload_image";

  /// 语音转文字接口
  static String get speechToTextUrl => "$baseUrl/applet/v1/chat/speech_to_text";

  /// 文本转语音接口
  static String get textToSpeechUrl => "$baseUrl/applet/v1/trans/tts";

  /// 获取TTS支持的语言列表接口
  static String get ttsLanguagesUrl => "$baseUrl/applet/v1/trans/tts/languages";

  // =========================== 医生管理相关接口 ===========================

  /// 获取医生列表接口
  static String get getDoctorsUrl => "$baseUrl/applet/v1/doctors";

  /// 获取包含互动状态的医生列表接口
  static String get getDoctorsWithInteractionUrl =>
      "$baseUrl/applet/v1/doctors/with-interaction";

  /// 获取单个医生详情接口 (需要医生ID)
  static String getDoctorDetailUrl(int doctorId) =>
      "$baseUrl/applet/v1/doctors/$doctorId";

  /// 点赞医生接口 (需要医生ID)
  static String likeDoctorUrl(int doctorId) =>
      "$baseUrl/applet/v1/doctors/$doctorId/like";

  /// 取消点赞医生接口 (需要医生ID)
  static String unlikeDoctorUrl(int doctorId) =>
      "$baseUrl/applet/v1/doctors/$doctorId/like";

  /// 收藏医生接口 (需要医生ID)
  static String favoriteDoctorUrl(int doctorId) =>
      "$baseUrl/applet/v1/doctors/$doctorId/favorite";

  /// 取消收藏医生接口 (需要医生ID)
  static String unfavoriteDoctorUrl(int doctorId) =>
      "$baseUrl/applet/v1/doctors/$doctorId/favorite";

  /// 获取医生互动状态接口 (需要医生ID)
  static String getDoctorStatusUrl(int doctorId) =>
      "$baseUrl/applet/v1/doctors/$doctorId/status";

  /// 获取用户收藏的医生列表接口
  static String get getFavoriteDoctorsUrl =>
      "$baseUrl/applet/v1/doctors/favorites";

  /// 获取用户点赞的医生列表接口
  static String get getLikedDoctorsUrl => "$baseUrl/applet/v1/doctors/likes";

  // =========================== 医生产品管理相关接口 ===========================

  /// 创建产品接口
  static String get createDoctorProductUrl =>
      "$baseUrl/applet/v1/doctor/products";

  /// 获取产品列表接口
  static String get getDoctorProductsUrl =>
      "$baseUrl/applet/v1/doctor/products";

  /// 获取产品详情接口 (需要产品ID)
  static String getDoctorProductDetailUrl(int productId) =>
      "$baseUrl/applet/v1/doctor/products/$productId";

  /// 更新产品接口 (需要产品ID)
  static String updateDoctorProductUrl(int productId) =>
      "$baseUrl/applet/v1/doctor/products/$productId";

  /// 删除产品接口 (需要产品ID)
  static String deleteDoctorProductUrl(int productId) =>
      "$baseUrl/applet/v1/doctor/products/$productId";

  /// 获取产品统计接口
  static String get getDoctorProductStatisticsUrl =>
      "$baseUrl/applet/v1/doctor/products/statistics/overview";

  /// 获取产品订单列表接口
  static String get getDoctorProductOrdersUrl =>
      "$baseUrl/applet/v1/doctor/products/orders/list";

  /// 单张产品图片上传接口
  static String get uploadProductImageUrl =>
      "$baseUrl/applet/v1/doctor/products/upload-image";

  /// 批量产品图片上传接口
  static String get uploadProductImagesUrl =>
      "$baseUrl/applet/v1/doctor/products/upload-images";

  // =========================== 管理员医生管理相关接口 ===========================

  /// 获取所有医生列表接口（管理员）
  static String get getAdminDoctorsUrl => "$baseUrl/applet/v1/admin/doctors";

  /// 获取指定医生信息接口（管理员，需要医生ID）
  static String getAdminDoctorDetailUrl(int doctorId) =>
      "$baseUrl/applet/v1/admin/doctors/$doctorId";

  /// 创建新医生接口（管理员）
  static String get createAdminDoctorUrl => "$baseUrl/applet/v1/admin/doctors";

  /// 更新医生信息接口（管理员，需要医生ID）
  static String updateAdminDoctorUrl(int doctorId) =>
      "$baseUrl/applet/v1/admin/doctors/$doctorId";

  /// 上传医生头像接口（管理员）
  static String get uploadAdminDoctorAvatarUrl =>
      "$baseUrl/applet/v1/admin/doctors/upload_avatar";

  // =========================== 管理员产品审核相关接口 ===========================

  /// 获取所有产品列表接口（管理员）
  static String get getAdminProductsUrl => "$baseUrl/applet/v1/admin/products";

  /// 获取待审核产品列表接口（管理员）
  static String get getPendingAdminProductsUrl =>
      "$baseUrl/applet/v1/admin/products/pending";

  /// 获取产品详情接口（管理员，需要产品ID）
  static String getAdminProductDetailUrl(int productId) =>
      "$baseUrl/applet/v1/admin/products/$productId";

  /// 审核产品接口（管理员，需要产品ID）
  static String reviewAdminProductUrl(int productId) =>
      "$baseUrl/applet/v1/admin/products/$productId/review";

  /// 批量审核产品接口（管理员）
  static String get batchReviewAdminProductsUrl =>
      "$baseUrl/applet/v1/admin/products/batch-review";

  /// 获取产品统计接口（管理员）
  static String get getAdminProductStatisticsUrl =>
      "$baseUrl/applet/v1/admin/products/statistics/overview";

  /// 获取有产品的医生列表接口（管理员）
  static String get getAdminProductDoctorsUrl =>
      "$baseUrl/applet/v1/admin/products/doctors/list";

  /// 获取指定医生的产品接口（管理员，需要医生ID）
  static String getAdminProductsByDoctorUrl(int doctorId) =>
      "$baseUrl/applet/v1/admin/products/doctor/$doctorId";

  // =========================== 用户端产品浏览相关接口 ===========================

  /// 获取产品列表接口（用户端，无需登录）
  static String get getUserProductsUrl => "$baseUrl/applet/v1/products";

  /// 获取产品分类列表接口（用户端，无需登录）
  static String get getUserProductCategoriesUrl =>
      "$baseUrl/applet/v1/products/categories";

  /// 获取产品详情接口（用户端，无需登录，需要产品ID）
  static String getUserProductDetailUrl(int productId) =>
      "$baseUrl/applet/v1/products/$productId";

  /// 获取医生的产品接口（用户端，无需登录，需要医生ID）
  static String getUserProductsByDoctorUrl(int doctorId) =>
      "$baseUrl/applet/v1/products/doctor/$doctorId";

  /// 创建订单接口（用户端，需要登录）
  static String get createUserProductOrderUrl =>
      "$baseUrl/applet/v1/products/orders";

  /// 获取我的订单接口（用户端，需要登录）
  static String get getUserProductOrdersUrl =>
      "$baseUrl/applet/v1/products/orders/my";

  /// 获取订单详情接口（用户端，需要登录，需要订单ID）
  static String getUserProductOrderDetailUrl(int orderId) =>
      "$baseUrl/applet/v1/products/orders/$orderId";

  /// 取消订单接口（用户端，需要登录，需要订单ID）
  static String cancelUserProductOrderUrl(int orderId) =>
      "$baseUrl/applet/v1/products/orders/$orderId/cancel";

  // =========================== 产品订单支付相关接口 ===========================

  /// 创建订单支付接口（需要登录，需要订单ID）
  static String createOrderPaymentUrl(int orderId) =>
      "$baseUrl/applet/v1/products/orders/$orderId/payment";

  /// 查询订单支付状态接口（需要登录，需要订单ID）
  static String getOrderPaymentStatusUrl(int orderId) =>
      "$baseUrl/applet/v1/products/orders/$orderId/payment/status";

  // =========================== 物流系统相关接口 ===========================

  /// 获取待发货订单列表接口（医生端，需要登录）
  static String get getDoctorPendingShipmentOrdersUrl =>
      "$baseUrl/applet/v1/doctor/products/orders/pending-shipment";

  /// 订单发货接口（医生端，需要登录，需要订单ID）
  static String shipDoctorOrderUrl(int orderId) =>
      "$baseUrl/applet/v1/doctor/products/orders/$orderId/ship";

  /// 获取订单物流状态接口（医生端，需要登录，需要订单ID）
  static String getDoctorOrderShippingStatusUrl(int orderId) =>
      "$baseUrl/applet/v1/doctor/products/orders/$orderId/shipping-status";

  /// 获取订单物流状态接口（用户端，需要登录，需要订单ID）
  static String getUserOrderShippingStatusUrl(int orderId) =>
      "$baseUrl/applet/v1/products/orders/$orderId/shipping-status";

  /// 获取我的已发货订单接口（用户端，需要登录）
  static String get getMyShippedOrdersUrl =>
      "$baseUrl/applet/v1/products/orders/my/shipped";

  // =========================== 管理员订单管理相关接口 ===========================

  /// 获取所有订单列表接口（管理员，需要登录）
  static String get getAdminAllOrdersUrl => "$baseUrl/applet/v1/admin/orders";

  /// 获取指定订单详情接口（管理员，需要登录，需要订单ID）
  static String getAdminOrderDetailUrl(int orderId) =>
      "$baseUrl/applet/v1/admin/orders/$orderId";

  /// 更新订单状态接口（管理员，需要登录，需要订单ID）
  static String updateAdminOrderStatusUrl(int orderId) =>
      "$baseUrl/applet/v1/admin/orders/$orderId/status";

  /// 更新支付状态接口（管理员，需要登录，需要订单ID）
  static String updateAdminPayStatusUrl(int orderId) =>
      "$baseUrl/applet/v1/admin/orders/$orderId/pay-status";

  /// 管理员代发货接口（管理员，需要登录，需要订单ID）
  static String adminShipOrderUrl(int orderId) =>
      "$baseUrl/applet/v1/admin/orders/$orderId/ship";

  /// 获取订单物流状态接口（管理员，需要登录，需要订单ID）
  static String getAdminOrderShippingStatusUrl(int orderId) =>
      "$baseUrl/applet/v1/admin/orders/$orderId/shipping-status";

  /// 删除订单接口（管理员，需要登录，需要订单ID）
  static String deleteAdminOrderUrl(int orderId) =>
      "$baseUrl/applet/v1/admin/orders/$orderId";

  /// 获取订单统计信息接口（管理员，需要登录）
  static String get getAdminOrderStatisticsUrl =>
      "$baseUrl/applet/v1/admin/orders/statistics/overview";

  /// 同步订单支付状态接口（需要登录，需要订单ID）
  static String syncOrderPaymentStatusUrl(int orderId) =>
      "$baseUrl/applet/v1/products/orders/$orderId/payment/sync";

  // =========================== 购物车管理相关接口 ===========================

  /// 加入购物车接口（需要登录）
  static String get addToCartUrl => "$baseUrl/applet/v1/cart/add";

  /// 获取购物车列表接口（需要登录）
  static String get getCartListUrl => "$baseUrl/applet/v1/cart";

  /// 更新购物车商品接口（需要登录，需要购物车商品ID）
  static String updateCartItemUrl(int cartId) =>
      "$baseUrl/applet/v1/cart/$cartId";

  /// 删除购物车商品接口（需要登录，需要购物车商品ID）
  static String deleteCartItemUrl(int cartId) =>
      "$baseUrl/applet/v1/cart/$cartId";

  /// 批量删除购物车商品接口（需要登录）
  static String get batchDeleteCartItemsUrl =>
      "$baseUrl/applet/v1/cart/batch-delete";

  /// 选择/取消选择购物车商品接口（需要登录）
  static String get selectCartItemsUrl => "$baseUrl/applet/v1/cart/select";

  /// 购物车结算（批量下单）接口（需要登录）
  static String get cartCheckoutUrl => "$baseUrl/applet/v1/cart/checkout";

  /// 获取购物车商品数量接口（需要登录）
  static String get getCartCountUrl => "$baseUrl/applet/v1/cart/count";

  // =========================== 地址管理相关接口 ===========================

  /// 创建地址接口（需要登录）
  static String get createAddressUrl => "$baseUrl/applet/v1/addresses";

  /// 获取地址列表接口（需要登录）
  static String get getAddressListUrl => "$baseUrl/applet/v1/addresses";

  /// 获取简化地址列表接口（需要登录）
  static String get getSimpleAddressListUrl =>
      "$baseUrl/applet/v1/addresses/simple";

  /// 获取地址详情接口（需要登录，需要地址ID）
  static String getAddressDetailUrl(int addressId) =>
      "$baseUrl/applet/v1/addresses/$addressId";

  /// 更新地址接口（需要登录，需要地址ID）
  static String updateAddressUrl(int addressId) =>
      "$baseUrl/applet/v1/addresses/$addressId";

  /// 删除地址接口（需要登录，需要地址ID）
  static String deleteAddressUrl(int addressId) =>
      "$baseUrl/applet/v1/addresses/$addressId";

  /// 设置默认地址接口（需要登录）
  static String get setDefaultAddressUrl =>
      "$baseUrl/applet/v1/addresses/set-default";

  /// 获取默认地址接口（需要登录）
  static String get getDefaultAddressUrl =>
      "$baseUrl/applet/v1/addresses/default/info";

  // =========================== 管理员用户管理相关接口 ===========================

  /// 获取用户列表接口（管理员，需要登录）
  static String get getAdminUsersUrl =>
      "$baseUrl/applet/v1/admin/user-management/users";

  /// 获取用户详情接口（管理员，需要登录，需要用户ID）
  static String getAdminUserDetailUrl(int userId) =>
      "$baseUrl/applet/v1/admin/user-management/users/$userId";

  /// 更新用户基础信息接口（管理员，需要登录，需要用户ID）
  static String updateAdminUserUrl(int userId) =>
      "$baseUrl/applet/v1/admin/user-management/users/$userId";

  /// 更新用户状态接口（管理员，需要登录，需要用户ID）
  static String updateAdminUserStatusUrl(int userId) =>
      "$baseUrl/applet/v1/admin/user-management/users/$userId/status";

  /// 调整用户余额接口（管理员，需要登录，需要用户ID）
  static String adjustAdminUserBalanceUrl(int userId) =>
      "$baseUrl/applet/v1/admin/user-management/users/$userId/balance";

  /// 调整用户积分接口（管理员，需要登录，需要用户ID）
  static String adjustAdminUserIntegralUrl(int userId) =>
      "$baseUrl/applet/v1/admin/user-management/users/$userId/integral";

  /// 更新用户角色权限接口（管理员，需要登录，需要用户ID）
  static String updateAdminUserRoleUrl(int userId) =>
      "$baseUrl/applet/v1/admin/user-management/users/$userId/role";

  /// 重置用户密码接口（管理员，需要登录，需要用户ID）
  static String resetAdminUserPasswordUrl(int userId) =>
      "$baseUrl/applet/v1/admin/user-management/users/$userId/reset-password";

  /// 获取用户登录令牌列表接口（管理员，需要登录，需要用户ID）
  static String getAdminUserTokensUrl(int userId) =>
      "$baseUrl/applet/v1/admin/user-management/users/$userId/tokens";

  /// 清除用户所有登录令牌接口（管理员，需要登录，需要用户ID）
  static String clearAdminUserAllTokensUrl(int userId) =>
      "$baseUrl/applet/v1/admin/user-management/users/$userId/tokens";

  /// 清除用户指定登录令牌接口（管理员，需要登录，需要用户ID和令牌ID）
  static String clearAdminUserTokenUrl(int userId, int tokenId) =>
      "$baseUrl/applet/v1/admin/user-management/users/$userId/tokens/$tokenId";

  /// 获取用户统计数据接口（管理员，需要登录）
  static String get getAdminUserStatisticsUrl =>
      "$baseUrl/applet/v1/admin/user-management/statistics";

  // =========================== 原有接口继续 ===========================

  /// 用户头像文件基础路径
  static String get userAvatarBasePath => "$baseUrl/static/uploads/user_avatar";

  /// 构建完整的用户头像URL
  static String buildUserAvatarUrl(String avatarFileName) {
    if (avatarFileName.isEmpty) {
      return ''; // 返回空字符串，前端可以显示默认头像
    }

    // 如果后端返回的文件名使用127.0.0.1，替换为前端配置的域名
    if (avatarFileName.contains('127.0.0.1:8002')) {
      return avatarFileName.replaceAll('127.0.0.1:8002', domainName);
    }

    // 如果已经是完整URL，直接返回
    if (avatarFileName.startsWith('http://') ||
        avatarFileName.startsWith('https://')) {
      return avatarFileName;
    }

    // 如果是完整的相对路径（包含static/uploads/user_avatar），直接拼接baseUrl
    if (avatarFileName.startsWith('static/uploads/user_avatar/')) {
      return '$baseUrl/$avatarFileName';
    }

    // 否则认为是纯文件名，拼接完整路径
    return '$userAvatarBasePath/$avatarFileName';
  }

  /// 构建完整的头像URL（保持向后兼容）
  static String buildAvatarUrl(String avatarPath) {
    return buildUserAvatarUrl(avatarPath);
  }

  /// 构建医生头像完整URL
  static String buildDoctorAvatarUrl(String? avatarUrl) {
    if (avatarUrl == null || avatarUrl.isEmpty) {
      return ''; // 返回空字符串，前端可以显示默认头像
    }

    // 如果后端返回的URL使用127.0.0.1，替换为前端配置的域名
    if (avatarUrl.contains('127.0.0.1:8002')) {
      return avatarUrl.replaceAll('127.0.0.1:8002', domainName);
    }

    if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
      // 如果已经是完整URL，直接返回
      return avatarUrl;
    } else {
      // 否则拼接baseUrl，处理路径开头的斜杠避免双斜杠
      String cleanPath = avatarUrl.startsWith('/')
          ? avatarUrl.substring(1)
          : avatarUrl;
      return '$baseUrl/$cleanPath';
    }
  }

  /// 构建完整的图片URL（通用方法）
  static String buildImageUrl(String imageUrl) {
    if (imageUrl.isEmpty) return '';

    // 如果后端返回的URL使用127.0.0.1，替换为前端配置的域名
    if (imageUrl.contains('127.0.0.1:8002')) {
      return imageUrl.replaceAll('127.0.0.1:8002', domainName);
    }

    // 如果已经是完整URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // 否则拼接baseUrl，处理路径开头的斜杠避免双斜杠
    String cleanPath = imageUrl.startsWith('/')
        ? imageUrl.substring(1)
        : imageUrl;
    return '$baseUrl/$cleanPath';
  }
}
