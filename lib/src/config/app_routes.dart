import 'package:flutter/material.dart';
import '../presentation/splash/screens/splash_screen.dart';
import '../presentation/settings/screens/profile_edit_screen.dart';

class AppRoutes {
  static const String splash = '/splash';
  static const String home = '/home';
  static const String profileEdit = '/profile-edit';
  static const String profileTest = '/profile-test';

  static Map<String, WidgetBuilder> get routes => {
    splash: (context) => const SplashScreen(),
    profileEdit: (context) => const ProfileEditScreen(),
    // 其他页面路由将在后续添加
  };
}
