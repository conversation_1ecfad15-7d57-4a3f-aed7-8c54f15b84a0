import 'package:flutter/material.dart';

/// 颜色扩展方法 - 提供更安全的透明度处理方式
extension ColorExtensions on Color {
  /// 使用安全的方式设置透明度 - 替代已弃用的 withOpacity
  Color withValues({int? red, int? green, int? blue, int? alpha}) {
    return Color.fromARGB(
      alpha ?? ((a * 255.0).round() & 0xFF),
      red ?? ((r * 255.0).round() & 0xFF),
      green ?? ((g * 255.0).round() & 0xFF),
      blue ?? ((b * 255.0).round() & 0xFF),
    );
  }
}
