import 'package:flutter/material.dart';

/// 应用颜色主题配置
class AppColors {
  // 主题色 - 在浅色和暗色模式下保持一致
  static const Color primary = Color(0xFF109D58);
  static const Color secondary = Color(0xFF12B768);
  static const Color accent = Color(0xFF109D58);

  // 状态色 - 在浅色和暗色模式下保持一致
  static const Color success = Color(0xFF58CC02);
  static const Color error = Color(0xFFFF4B4B);
  static const Color warning = Color(0xFFFFB800);
  static const Color info = Color(0xFF84D8FF);

  // 浅色模式颜色
  static const Color lightBackground = Color(0xFFF9F9F9);
  static const Color lightCardBackground = Colors.white;
  static const Color lightIconBackground = Color(0xFFEEF3FF);
  static const Color lightTextPrimary = Color(0xFF333333);
  static const Color lightTextSecondary = Color(0xFF666666);
  static const Color lightTextHint = Color(0xFF999999);
  static const Color lightBorder = Color(0xFFE0E0E0);
  static const Color lightDivider = Color(0xFFEEEEEE);

  // 暗色模式颜色
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkCardBackground = Color(0xFF1E1E1E);
  static const Color darkIconBackground = Color(0xFF2A2A2A);
  static const Color darkTextPrimary = Color(0xFFE0E0E0);
  static const Color darkTextSecondary = Color(0xFFB0B0B0);
  static const Color darkTextHint = Color(0xFF707070);
  static const Color darkBorder = Color(0xFF404040);
  static const Color darkDivider = Color(0xFF2A2A2A);

  // 基础色
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color grey = Color(0xFFF5F5F5);
  static const Color darkGrey = Color(0xFF9E9E9E);

  // 翻译页面特定颜色
  static const Color translationBlue = Color(0xFF2E89FF);
  static const Color translationBackgroundLight = Color(0xFFEFF6FF);
  static const Color translationBackgroundDark = Color(0xFF1A2332);

  // 动态颜色获取器 - 根据当前主题模式返回对应颜色

  /// 获取背景色
  static Color background(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkBackground
        : lightBackground;
  }

  /// 获取卡片背景色
  static Color cardBackground(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkCardBackground
        : lightCardBackground;
  }

  /// 获取图标背景色
  static Color iconBackground(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkIconBackground
        : lightIconBackground;
  }

  /// 获取主要文字色
  static Color textPrimary(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkTextPrimary
        : lightTextPrimary;
  }

  /// 获取次要文字色
  static Color textSecondary(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkTextSecondary
        : lightTextSecondary;
  }

  /// 获取提示文字色
  static Color textHint(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkTextHint
        : lightTextHint;
  }

  /// 获取边框色
  static Color border(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkBorder
        : lightBorder;
  }

  /// 获取分割线色
  static Color divider(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkDivider
        : lightDivider;
  }

  /// 获取翻译页面背景色
  static Color translationBackground(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? translationBackgroundDark
        : translationBackgroundLight;
  }

  // 兼容性 - 保持原有的静态颜色定义，用于不需要主题感知的场景
  static const Color backgroundStatic = lightBackground;
  static const Color cardBackgroundStatic = lightCardBackground;
  static const Color iconBackgroundStatic = lightIconBackground;
  static const Color textPrimaryStatic = lightTextPrimary;
  static const Color textSecondaryStatic = lightTextSecondary;
  static const Color textHintStatic = lightTextHint;
  static const Color borderStatic = lightBorder;
  static const Color dividerStatic = lightDivider;

  /// 扩展方法替代withAlpha - 解决const构造器中无法使用withAlpha的问题
  static Color withAlpha(Color color, int alpha) {
    return Color.fromARGB(
      alpha,
      (color.r * 255).round(),
      (color.g * 255).round(),
      (color.b * 255).round(),
    );
  }
}
