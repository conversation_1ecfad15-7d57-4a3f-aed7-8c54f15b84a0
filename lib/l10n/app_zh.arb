{"@@locale": "zh", "settingsTitle": "设置", "@settingsTitle": {"description": "设置页面标题"}, "languageSettings": "语言设置", "@languageSettings": {"description": "语言设置选项"}, "selectLanguage": "选择语言", "@selectLanguage": {"description": "选择语言"}, "selectSourceLanguage": "选择源语言", "@selectSourceLanguage": {"description": "选择源语言标题"}, "selectTargetLanguage": "选择目标语言", "@selectTargetLanguage": {"description": "选择目标语言标题"}, "languageChinese": "中文", "@languageChinese": {"description": "中文语言名称"}, "languageEnglish": "English", "@languageEnglish": {"description": "英文语言名称"}, "languageUyghur": "ئۇيغۇرچە", "@languageUyghur": {"description": "维吾尔语语言名称"}, "cancel": "取消", "@cancel": {"description": "取消按钮"}, "confirm": "确认", "@confirm": {"description": "确认按钮"}, "displaySettings": "显示设置", "@displaySettings": {"description": "显示设置分组标题"}, "darkMode": "暗色模式", "@darkMode": {"description": "暗色模式设置"}, "darkModeDescription": "在浅色和暗色主题之间切换", "@darkModeDescription": {"description": "暗色模式设置描述"}, "followSystemTheme": "跟随系统主题", "@followSystemTheme": {"description": "跟随系统主题设置"}, "followSystemThemeDescription": "自动适应系统的浅色/暗色设置", "@followSystemThemeDescription": {"description": "跟随系统主题设置描述"}, "fontSize": "字体大小", "@fontSize": {"description": "字体大小设置"}, "fontSizeDescription": "调整应用内字体大小", "@fontSizeDescription": {"description": "字体大小设置描述"}, "languageSettingsDescription": "选择应用显示语言", "@languageSettingsDescription": {"description": "语言设置描述"}, "other": "其他", "@other": {"description": "其他设置分组"}, "helpAndFeedback": "帮助与反馈", "@helpAndFeedback": {"description": "帮助与反馈"}, "helpAndFeedbackDescription": "常见问题和意见反馈", "@helpAndFeedbackDescription": {"description": "帮助与反馈描述"}, "aboutUs": "关于我们", "@aboutUs": {"description": "关于我们"}, "aboutUsDescription": "版本信息和公司介绍", "@aboutUsDescription": {"description": "关于我们描述"}, "logout": "退出登录", "@logout": {"description": "退出登录"}, "logoutDescription": "清除登录状态并返回登录页", "@logoutDescription": {"description": "退出登录描述"}, "aboutDialogTitle": "关于我们", "@aboutDialogTitle": {"description": "关于对话框标题"}, "aboutDialogContent": "健康助手 v1.0.0\n\n一款专业的AI健康导游应用", "@aboutDialogContent": {"description": "关于对话框内容"}, "homeTitle": "健康助手", "@homeTitle": {"description": "健康助手页面标题"}, "historyTitle": "历史", "@historyTitle": {"description": "历史页面标题"}, "aiTourGuideTitle": "健康助手", "@aiTourGuideTitle": {"description": "健康助手页面标题"}, "smartGuide": "智能导游", "@smartGuide": {"description": "智能导游标签"}, "welcomeTo": "欢迎您来到", "@welcomeTo": {"description": "欢迎来到前缀"}, "shareApp": "分享应用", "@shareApp": {"description": "分享应用菜单项"}, "distributionManagement": "分销管理", "@distributionManagement": {"description": "分销管理菜单项"}, "myLanguage": "我方语言", "@myLanguage": {"description": "我方语言"}, "theirLanguage": "对方语言", "@theirLanguage": {"description": "对方语言"}, "sourceLanguage": "源", "@sourceLanguage": {"description": "源语言标签"}, "targetLanguage": "目标", "@targetLanguage": {"description": "目标语言标签"}, "bottomNavHome": "健康助手", "@bottomNavHome": {"description": "底部导航健康助手"}, "bottomNavHistory": "历史", "@bottomNavHistory": {"description": "底部导航历史"}, "bottomNavAiGuide": "健康助手", "@bottomNavAiGuide": {"description": "底部导航健康助手"}, "bottomNavSearch": "列表", "@bottomNavSearch": {"description": "底部导航列表"}, "bottomNavProfile": "我的", "@bottomNavProfile": {"description": "底部导航我的"}, "bottomNavSettings": "设置", "@bottomNavSettings": {"description": "底部导航设置"}, "searchPageTitle": "列表", "@searchPageTitle": {"description": "列表页面标题"}, "searchHint": "搜索医生或产品...", "@searchHint": {"description": "搜索框提示文本"}, "doctorTab": "医生", "@doctorTab": {"description": "医生标签页"}, "productTab": "产品", "@productTab": {"description": "产品标签页"}, "noDoctorsAvailable": "暂无医生信息", "@noDoctorsAvailable": {"description": "无医生信息提示"}, "noProductsAvailable": "暂无产品信息", "@noProductsAvailable": {"description": "无产品信息提示"}, "noSearchResults": "未找到相关结果", "@noSearchResults": {"description": "无搜索结果提示"}, "inputHint": "请输入您的问题...", "@inputHint": {"description": "输入框提示文本"}, "tapToSpeak": "点击说话", "@tapToSpeak": {"description": "语音输入提示"}, "listening": "正在听...", "@listening": {"description": "语音识别中"}, "processing": "处理中...", "@processing": {"description": "处理中状态文字"}, "clearHistory": "清空", "@clearHistory": {"description": "清空按钮"}, "copy": "复制", "@copy": {"description": "复制按钮"}, "share": "分享", "@share": {"description": "分享按钮"}, "play": "播放", "@play": {"description": "播放按钮"}, "pause": "暂停", "@pause": {"description": "暂停按钮"}, "retry": "重试", "@retry": {"description": "重试按钮"}, "error": "错误", "@error": {"description": "错误对话框标题"}, "networkError": "网络连接失败，请检查网络设置", "@networkError": {"description": "网络错误提示"}, "permissionDenied": "权限被拒绝", "@permissionDenied": {"description": "权限被拒绝提示"}, "cameraPermissionRequired": "需要相机权限", "@cameraPermissionRequired": {"description": "相机权限提示"}, "microphonePermissionRequired": "需要麦克风权限才能录音", "@microphonePermissionRequired": {"description": "需要麦克风权限才能录音"}, "appTitle": "健康助手", "@appTitle": {"description": "应用标题"}, "welcomeMessage": "欢迎使用健康助手", "@welcomeMessage": {"description": "欢迎信息"}, "welcomeDescription": "您的专属AI健康导游，随时为您提供健康咨询和导游服务", "@welcomeDescription": {"description": "欢迎页面描述"}, "exitAppConfirm": "再次返回即可退出软件", "@exitAppConfirm": {"description": "退出应用确认提示"}, "themeSwitch": "主题切换", "@themeSwitch": {"description": "主题切换对话框标题"}, "themeSwitchMessage": "切换主题模式需要重启应用才能完全生效，是否立即重启？", "@themeSwitchMessage": {"description": "主题切换确认消息"}, "languageSwitchMessage": "切换语言需要重启应用才能完全生效，是否立即重启？", "@languageSwitchMessage": {"description": "语言切换确认消息"}, "restart": "重启", "@restart": {"description": "重启按钮"}, "userManagementTab": "用户管理", "@userManagementTab": {"description": "用户管理标签页"}, "userManagement": "用户管理", "@userManagement": {"description": "用户管理"}, "userList": "用户列表", "@userList": {"description": "用户列表"}, "userStatistics": "用户统计", "@userStatistics": {"description": "用户统计"}, "totalUsers": "总用户数", "@totalUsers": {"description": "总用户数"}, "activeUsers": "活跃用户", "@activeUsers": {"description": "活跃用户"}, "adminUsers": "管理员", "@adminUsers": {"description": "管理员用户"}, "doctorUsers": "医生", "@doctorUsers": {"description": "医生用户"}, "searchUsers": "搜索用户", "@searchUsers": {"description": "搜索用户"}, "searchByNicknamePhoneId": "按昵称、电话或ID搜索", "@searchByNicknamePhoneId": {"description": "搜索用户提示文本"}, "userStatus": "用户状态", "@userStatus": {"description": "用户状态"}, "allStatus": "全部", "@allStatus": {"description": "全部状态筛选"}, "enabledStatus": "已启用", "@enabledStatus": {"description": "已启用状态"}, "disabledStatus": "已禁用", "@disabledStatus": {"description": "已禁用状态"}, "userRole": "用户角色", "@userRole": {"description": "用户角色"}, "allRoles": "全部角色", "@allRoles": {"description": "全部角色"}, "normalUser": "普通用户", "@normalUser": {"description": "普通用户状态"}, "adminUser": "管理员", "@adminUser": {"description": "管理员用户"}, "doctorUser": "医生", "@doctorUser": {"description": "医生用户"}, "userGender": "性别", "@userGender": {"description": "用户性别"}, "male": "男", "@male": {"description": "男性别选项"}, "female": "女", "@female": {"description": "女性别选项"}, "unknown": "未知", "@unknown": {"description": "未知标签"}, "registerSource": "注册来源", "@registerSource": {"description": "注册来源"}, "appSource": "APP", "@appSource": {"description": "APP注册来源"}, "miniProgramSource": "小程序", "@miniProgramSource": {"description": "小程序注册来源"}, "userBalance": "用户余额", "@userBalance": {"description": "用户余额"}, "userIntegral": "用户积分", "@userIntegral": {"description": "用户积分"}, "adjustBalance": "调整余额", "@adjustBalance": {"description": "调整余额"}, "adjustIntegral": "调整积分", "@adjustIntegral": {"description": "调整积分"}, "adjustAmount": "调整金额", "@adjustAmount": {"description": "调整金额"}, "adjustReason": "调整原因", "@adjustReason": {"description": "调整原因"}, "pleaseEnterAmount": "请输入调整金额", "@pleaseEnterAmount": {"description": "请输入调整金额"}, "pleaseEnterReason": "请输入调整原因", "@pleaseEnterReason": {"description": "请输入调整原因"}, "positiveForIncrease": "正数为增加，负数为减少", "@positiveForIncrease": {"description": "正数为增加，负数为减少"}, "userDetail": "用户详情", "@userDetail": {"description": "用户详情"}, "editUser": "编辑用户", "@editUser": {"description": "编辑用户"}, "enableUser": "启用用户", "@enableUser": {"description": "启用用户"}, "disableUser": "禁用用户", "@disableUser": {"description": "禁用用户"}, "resetPassword": "重置密码", "@resetPassword": {"description": "重置密码"}, "newPassword": "新密码", "@newPassword": {"description": "新密码字段标签"}, "pleaseEnterNewPassword": "请输入新密码", "@pleaseEnterNewPassword": {"description": "请输入新密码"}, "passwordLength": "密码长度6-20位", "@passwordLength": {"description": "密码长度6-20位"}, "userNickname": "用户昵称", "@userNickname": {"description": "用户昵称"}, "userPhone": "用户电话", "@userPhone": {"description": "用户电话"}, "userBirthday": "用户生日", "@userBirthday": {"description": "用户生日"}, "registrationTime": "注册时间", "@registrationTime": {"description": "注册时间"}, "lastLoginTime": "最后登录", "@lastLoginTime": {"description": "最后登录时间"}, "userTokens": "登录令牌", "@userTokens": {"description": "用户登录令牌"}, "clearAllTokens": "清除所有令牌", "@clearAllTokens": {"description": "清除所有令牌"}, "confirmClearTokens": "确认清除所有登录令牌？", "@confirmClearTokens": {"description": "确认清除所有登录令牌？"}, "clearTokensWarning": "清除后用户将在所有设备上被强制下线", "@clearTokensWarning": {"description": "清除令牌警告"}, "deviceType": "设备类型", "@deviceType": {"description": "设备类型"}, "expiryTime": "过期时间", "@expiryTime": {"description": "过期时间"}, "createTime": "创建时间", "@createTime": {"description": "创建时间"}, "expired": "已过期", "@expired": {"description": "已过期"}, "valid": "有效", "@valid": {"description": "有效"}, "loadUserListFailed": "加载用户列表失败", "@loadUserListFailed": {"description": "加载用户列表失败"}, "loadUserDetailFailed": "加载用户详情失败", "@loadUserDetailFailed": {"description": "加载用户详情失败"}, "updateUserSuccess": "更新用户信息成功", "@updateUserSuccess": {"description": "更新用户信息成功"}, "updateUserFailed": "更新用户信息失败", "@updateUserFailed": {"description": "更新用户信息失败"}, "adjustBalanceSuccess": "调整余额成功", "@adjustBalanceSuccess": {"description": "调整余额成功"}, "adjustBalanceFailed": "调整余额失败", "@adjustBalanceFailed": {"description": "调整余额失败"}, "adjustIntegralSuccess": "调整积分成功", "@adjustIntegralSuccess": {"description": "调整积分成功"}, "adjustIntegralFailed": "调整积分失败", "@adjustIntegralFailed": {"description": "调整积分失败"}, "resetPasswordSuccess": "重置密码成功", "@resetPasswordSuccess": {"description": "重置密码成功"}, "resetPasswordFailed": "重置密码失败", "@resetPasswordFailed": {"description": "重置密码失败"}, "clearTokensSuccess": "清除令牌成功", "@clearTokensSuccess": {"description": "清除令牌成功"}, "clearTokensFailed": "清除令牌失败", "@clearTokensFailed": {"description": "清除令牌失败"}, "noUsersFound": "暂无用户", "@noUsersFound": {"description": "暂无用户"}, "enableUserSuccess": "启用用户成功", "@enableUserSuccess": {"description": "启用用户成功"}, "disableUserSuccess": "禁用用户成功", "@disableUserSuccess": {"description": "禁用用户成功"}, "reset": "重置", "@reset": {"description": "重置"}, "apply": "应用", "@apply": {"description": "应用"}, "todayNewUsers": "今日新增", "@todayNewUsers": {"description": "今日新增用户"}, "thisWeekNewUsers": "本周新增", "@thisWeekNewUsers": {"description": "本周新增用户"}, "totalBalance": "总余额", "@totalBalance": {"description": "总余额"}, "totalIntegral": "总积分", "@totalIntegral": {"description": "总积分"}, "expandFilters": "展开筛选", "@expandFilters": {"description": "展开筛选"}, "collapseFilters": "收起筛选", "@collapseFilters": {"description": "收起筛选"}, "userDevelopmentInProgress": "用户详情页面开发中...", "@userDevelopmentInProgress": {"description": "用户详情页面开发中"}, "roleAdmin": "管理员", "@roleAdmin": {"description": "管理员角色"}, "roleDoctor": "医生", "@roleDoctor": {"description": "医生角色"}, "roleReferrer": "分销员", "@roleReferrer": {"description": "分销员角色"}, "roleNormalUser": "普通用户", "@roleNormalUser": {"description": "普通用户角色"}, "editUserInfo": "编辑用户信息", "@editUserInfo": {"description": "编辑用户信息"}, "editUserRole": "编辑用户角色", "@editUserRole": {"description": "编辑用户角色"}, "userNicknameLabel": "用户昵称", "@userNicknameLabel": {"description": "用户昵称标签"}, "userPhoneLabel": "用户电话", "@userPhoneLabel": {"description": "用户电话标签"}, "userBirthdayLabel": "用户生日", "@userBirthdayLabel": {"description": "用户生日标签"}, "userGenderLabel": "用户性别", "@userGenderLabel": {"description": "用户性别标签"}, "pleaseEnterNickname": "请输入用户昵称", "@pleaseEnterNickname": {"description": "请输入用户昵称"}, "pleaseEnterPhone": "请输入用户电话", "@pleaseEnterPhone": {"description": "请输入用户电话"}, "selectBirthday": "选择生日", "@selectBirthday": {"description": "选择生日"}, "selectGender": "选择性别", "@selectGender": {"description": "选择性别"}, "associatedDoctor": "关联医生", "@associatedDoctor": {"description": "关联医生"}, "selectDoctor": "选择医生", "@selectDoctor": {"description": "选择医生"}, "referrerLevel": "分销员等级", "@referrerLevel": {"description": "分销员等级"}, "level": "等级", "@level": {"description": "等级"}, "ipAddress": "IP地址", "@ipAddress": {"description": "IP地址"}, "roleManagement": "角色权限管理", "@roleManagement": {"description": "角色权限管理"}, "currentRoles": "当前角色", "@currentRoles": {"description": "当前角色"}, "roleDetails": "角色详情", "@roleDetails": {"description": "角色详情"}, "editRole": "编辑角色", "@editRole": {"description": "编辑角色"}, "balanceIntegralManagement": "余额积分管理", "@balanceIntegralManagement": {"description": "余额积分管理"}, "tokenManagement": "令牌管理", "@tokenManagement": {"description": "令牌管理"}, "totalTokens": "总令牌数", "@totalTokens": {"description": "总令牌数"}, "noTokensFound": "暂无登录令牌", "@noTokensFound": {"description": "暂无登录令牌"}, "tokenClearSuccess": "令牌清除成功", "@tokenClearSuccess": {"description": "令牌清除成功"}, "tokenClearFailed": "令牌清除失败", "@tokenClearFailed": {"description": "令牌清除失败"}, "loadTokensFailed": "加载令牌列表失败", "@loadTokensFailed": {"description": "加载令牌列表失败"}, "pleaseCompleteInfo": "请填写完整信息", "@pleaseCompleteInfo": {"description": "请填写完整信息"}, "pleaseEnterValidAmount": "请输入有效的金额", "@pleaseEnterValidAmount": {"description": "请输入有效的金额"}, "pleaseEnterValidPoints": "请输入有效的积分", "@pleaseEnterValidPoints": {"description": "请输入有效的积分"}, "userEditInProgress": "用户编辑功能开发中...", "@userEditInProgress": {"description": "用户编辑功能开发中"}, "roleEditInProgress": "角色编辑功能开发中...", "@roleEditInProgress": {"description": "角色编辑功能开发中"}, "genderMale": "男", "@genderMale": {"description": "男性"}, "genderFemale": "女", "@genderFemale": {"description": "女性"}, "genderUnknown": "未知", "@genderUnknown": {"description": "性别未知"}, "referrerText": "分销员", "@referrerText": {"description": "分销员文本"}, "updateRoleSuccess": "角色更新成功", "@updateRoleSuccess": {"description": "角色更新成功"}, "updateRoleFailed": "角色更新失败", "@updateRoleFailed": {"description": "角色更新失败"}, "adminRoleDescription": "拥有系统管理权限", "@adminRoleDescription": {"description": "管理员角色描述"}, "doctorRoleDescription": "可以管理产品和订单", "@doctorRoleDescription": {"description": "医生角色描述"}, "referrerRoleDescription": "可以推广获得佣金", "@referrerRoleDescription": {"description": "分销员角色描述"}, "pleaseEnterReferrerLevel": "请输入分销员等级", "@pleaseEnterReferrerLevel": {"description": "请输入分销员等级"}, "registerSourceApp": "APP", "@registerSourceApp": {"description": "APP注册来源"}, "registerSourceMiniProgram": "小程序", "@registerSourceMiniProgram": {"description": "小程序注册来源"}, "statusEnabled": "启用", "@statusEnabled": {"description": "启用状态"}, "statusDisabled": "禁用", "@statusDisabled": {"description": "禁用状态"}, "healthProfile": "健康档案", "@healthProfile": {"description": "健康档案"}, "viewFullHealthProfile": "查看完整档案", "@viewFullHealthProfile": {"description": "查看完整健康档案"}, "heartRate": "心率", "@heartRate": {"description": "心率"}, "bodyTemperature": "体温", "@bodyTemperature": {"description": "体温"}, "weight": "体重", "@weight": {"description": "体重标签"}, "height": "身高", "@height": {"description": "身高标签"}, "healthProfileFeatureComingSoon": "健康档案功能即将上线", "@healthProfileFeatureComingSoon": {"description": "健康档案功能即将上线"}, "bloodType": "血型", "@bloodType": {"description": "血型标签"}, "exerciseFrequency": "运动频率", "@exerciseFrequency": {"description": "运动频率标签"}, "sedentary": "久坐", "@sedentary": {"description": "久坐不动"}, "lightExercise": "轻度运动", "@lightExercise": {"description": "轻度运动"}, "moderateExercise": "中度运动", "@moderateExercise": {"description": "中度运动"}, "activeExercise": "高强度运动", "@activeExercise": {"description": "高强度运动"}, "noHealthProfileYet": "暂无健康档案", "@noHealthProfileYet": {"description": "暂无健康档案"}, "createHealthProfile": "创建健康档案", "@createHealthProfile": {"description": "创建健康档案"}, "healthProfileEditFeatureComingSoon": "健康档案编辑功能即将上线", "@healthProfileEditFeatureComingSoon": {"description": "健康档案编辑功能即将上线"}, "basicInfo": "基本信息", "@basicInfo": {"description": "基本信息部分"}, "allergyHistory": "过敏史", "@allergyHistory": {"description": "过敏史标题"}, "chronicDiseaseHistory": "慢性病史", "@chronicDiseaseHistory": {"description": "慢性病史标题"}, "currentMedication": "当前用药", "@currentMedication": {"description": "当前用药标题"}, "lifestyle": "生活方式", "@lifestyle": {"description": "生活方式标题"}, "hasAllergies": "是否有药物、食物或其它物质过敏", "@hasAllergies": {"description": "是否有过敏询问"}, "drugAllergies": "药物过敏", "@drugAllergies": {"description": "药物过敏"}, "foodAllergies": "食物过敏", "@foodAllergies": {"description": "食物过敏"}, "otherAllergies": "其他过敏", "@otherAllergies": {"description": "其他过敏"}, "hasChronicDiseases": "是否有慢性病", "@hasChronicDiseases": {"description": "是否有慢性病"}, "chronicDiseasesList": "慢性病列表", "@chronicDiseasesList": {"description": "慢性病列表"}, "bloodPressureRange": "平常血压范围", "@bloodPressureRange": {"description": "血压范围标签"}, "bloodSugarRange": "平常空腹血糖范围", "@bloodSugarRange": {"description": "血糖范围标签"}, "hasCurrentMedication": "是否用药", "@hasCurrentMedication": {"description": "是否用药"}, "medicationDetails": "用药详情", "@medicationDetails": {"description": "用药详情"}, "smokingStatus": "吸烟情况", "@smokingStatus": {"description": "吸烟情况标签"}, "drinkingStatus": "饮酒情况", "@drinkingStatus": {"description": "饮酒情况标签"}, "sleepDuration": "平均每晚睡眠时长", "@sleepDuration": {"description": "睡眠时长标签"}, "sleepQuality": "睡眠质量", "@sleepQuality": {"description": "睡眠质量标签"}, "stressLevel": "近期压力水平", "@stressLevel": {"description": "压力水平标签"}, "yes": "是", "@yes": {"description": "是选项"}, "no": "否", "@no": {"description": "否选项"}, "never": "从不", "@never": {"description": "从不"}, "quit": "已戒", "@quit": {"description": "已戒"}, "occasional": "偶尔", "@occasional": {"description": "偶尔"}, "daily": "每天", "@daily": {"description": "每天"}, "social": "社交", "@social": {"description": "社交"}, "weekly": "每周", "@weekly": {"description": "每周"}, "lessThan6Hours": "少于6小时", "@lessThan6Hours": {"description": "少于6小时"}, "sixToSevenHours": "6-7小时", "@sixToSevenHours": {"description": "6-7小时"}, "sevenToEightHours": "7-8小时", "@sevenToEightHours": {"description": "7-8小时"}, "moreThan8Hours": "超过8小时", "@moreThan8Hours": {"description": "超过8小时"}, "good": "良好", "@good": {"description": "良好"}, "fair": "一般", "@fair": {"description": "一般"}, "poor": "较差", "@poor": {"description": "较差"}, "veryLow": "很低", "@veryLow": {"description": "很低"}, "low": "低", "@low": {"description": "低"}, "moderate": "中等", "@moderate": {"description": "中等"}, "high": "高", "@high": {"description": "高"}, "veryHigh": "很高", "@veryHigh": {"description": "很高"}, "pleaseEnterPhoneNumber": "请输入手机号", "@pleaseEnterPhoneNumber": {"description": "请输入手机号验证提示"}, "pleaseEnterCorrectPhoneNumber": "请输入正确的手机号", "@pleaseEnterCorrectPhoneNumber": {"description": "请输入正确的手机号提示"}, "pleaseEnterPassword": "请输入密码", "@pleaseEnterPassword": {"description": "请输入密码提示"}, "passwordMinLength": "密码长度至少6位", "@passwordMinLength": {"description": "密码最小长度提示"}, "loggingIn": "正在登录...", "@loggingIn": {"description": "正在登录提示"}, "loginSuccessful": "登录成功", "@loginSuccessful": {"description": "登录成功提示"}, "loginSuccessButNoData": "登录成功但用户数据为空", "@loginSuccessButNoData": {"description": "登录成功但数据为空"}, "dataProcessingError": "处理用户数据时出错: {error}", "@dataProcessingError": {"description": "数据处理错误", "placeholders": {"error": {"type": "String"}}}, "loginProcessError": "登录过程中出错: {error}", "@loginProcessError": {"description": "登录过程错误", "placeholders": {"error": {"type": "String"}}}, "passwordIncorrect": "密码错误", "@passwordIncorrect": {"description": "密码错误提示"}, "phoneNotRegistered": "该手机号未注册，请先注册", "@phoneNotRegistered": {"description": "手机号未注册提示"}, "passwordLogin": "密码登录", "@passwordLogin": {"description": "密码登录标题"}, "passwordLoginSubtitle": "请使用您的手机号和密码登录", "@passwordLoginSubtitle": {"description": "密码登录副标题"}, "phoneNumberHint": "请输入手机号", "@phoneNumberHint": {"description": "手机号输入提示"}, "passwordHint": "请输入密码", "@passwordHint": {"description": "密码输入提示"}, "forgotPassword": "忘记密码?", "@forgotPassword": {"description": "忘记密码按钮"}, "smsLogin": "验证码登录", "@smsLogin": {"description": "验证码登录按钮"}, "registerAccount": "注册账号", "@registerAccount": {"description": "注册账号按钮"}, "orOtherLoginMethods": "或选其他登录方式", "@orOtherLoginMethods": {"description": "其他登录方式提示"}, "loginAgreement": "登录即表示您同意《用户协议》和《隐私政策》", "@loginAgreement": {"description": "登录协议文本"}, "verificationCodeSent": "验证码已发送", "@verificationCodeSent": {"description": "验证码已发送"}, "sendFailed": "发送失败", "@sendFailed": {"description": "发送失败"}, "pleaseEnterVerificationCode": "请输入验证码", "@pleaseEnterVerificationCode": {"description": "输入验证码提示"}, "verificationCodeShouldBe6Digits": "验证码应为6位数字", "@verificationCodeShouldBe6Digits": {"description": "验证码位数提示"}, "login": "登录", "@login": {"description": "登录按钮"}, "welcomeBack": "欢迎回来", "@welcomeBack": {"description": "欢迎回来"}, "pleaseLoginWithPhoneNumber": "请使用您的手机号登录账户", "@pleaseLoginWithPhoneNumber": {"description": "请使用手机号登录"}, "passwordLoginDesc": "请使用您的手机号和密码登录", "@passwordLoginDesc": {"description": "密码登录描述"}, "agreeToTerms": "登录即表示您同意《用户协议》和《隐私政策》", "@agreeToTerms": {"description": "同意条款"}, "verificationCodeHint": "请输入验证码", "@verificationCodeHint": {"description": "验证码输入提示"}, "newPasswordHint": "请输入新密码", "@newPasswordHint": {"description": "新密码输入提示"}, "confirmPasswordHint": "请再次输入新密码", "@confirmPasswordHint": {"description": "确认密码输入提示"}, "getVerificationCode": "获取验证码", "@getVerificationCode": {"description": "获取验证码"}, "resendVerificationCode": "重新发送", "@resendVerificationCode": {"description": "重新发送验证码"}, "resetPasswordDescription": "请输入手机号获取验证码，然后设置新密码", "@resetPasswordDescription": {"description": "重置密码描述"}, "confirmReset": "确认重置", "@confirmReset": {"description": "确认重置"}, "passwordsDoNotMatch": "两次输入的密码不一致", "@passwordsDoNotMatch": {"description": "密码不一致验证消息"}, "passwordResetSuccess": "密码重置成功，请使用新密码登录", "@passwordResetSuccess": {"description": "密码重置成功"}, "contactCustomerService": "如有问题，请联系客服处理", "@contactCustomerService": {"description": "联系客服"}, "resetPasswordTitle": "重置密码", "@resetPasswordTitle": {"description": "重置密码页面标题"}, "enterCorrectPhoneNumber": "请输入正确的手机号", "@enterCorrectPhoneNumber": {"description": "输入正确手机号"}, "enterVerificationCode": "请输入验证码", "@enterVerificationCode": {"description": "输入验证码"}, "verificationCodeSixDigits": "验证码应为6位数字", "@verificationCodeSixDigits": {"description": "验证码6位数字提示"}, "enterNewPassword": "请输入新密码", "@enterNewPassword": {"description": "输入新密码"}, "passwordMinSixCharacters": "密码长度至少6位", "@passwordMinSixCharacters": {"description": "密码最少6位提示"}, "enterNewPasswordAgain": "请再次输入新密码", "@enterNewPasswordAgain": {"description": "再次输入新密码提示"}, "getVerificationCodeButton": "获取验证码", "@getVerificationCodeButton": {"description": "获取验证码按钮"}, "resendCountdown": "重新发送({seconds}s)", "@resendCountdown": {"description": "重新发送倒计时", "placeholders": {"seconds": {"type": "int"}}}, "sendVerificationCodeFailed": "发送验证码失败", "@sendVerificationCodeFailed": {"description": "发送验证码失败"}, "resettingPasswordLoading": "正在重置密码...", "@resettingPasswordLoading": {"description": "正在重置密码加载提示"}, "passwordResetFailed": "密码重置失败", "@passwordResetFailed": {"description": "密码重置失败"}, "networkConnectionFailedRetry": "网络连接失败，请稍后重试", "@networkConnectionFailedRetry": {"description": "网络连接失败重试提示"}, "clearHistoryTitle": "清空聊天历史", "@clearHistoryTitle": {"description": "清空聊天历史标题"}, "historyCleared": "历史记录已清空", "@historyCleared": {"description": "历史记录已清空提示"}, "clearHistoryFailed": "清空历史记录失败", "@clearHistoryFailed": {"description": "清空历史记录失败"}, "clearAllHistory": "清空所有历史", "@clearAllHistory": {"description": "清空所有历史"}, "daysAgo": "天前", "@daysAgo": {"description": "天前"}, "hoursAgo": "{hours}小时前", "@hoursAgo": {"description": "小时前时间指示器", "placeholders": {"hours": {"type": "int"}}}, "minutesAgo": "{minutes}分钟前", "@minutesAgo": {"description": "分钟前时间指示器", "placeholders": {"minutes": {"type": "int"}}}, "justNow": "刚刚", "@justNow": {"description": "刚刚时间指示器"}, "retakePhoto": "重新拍照", "@retakePhoto": {"description": "重新拍照"}, "imageProcessingError": "处理图片时出错", "@imageProcessingError": {"description": "图片处理错误"}, "exitAppHint": "再次返回即可退出软件", "@exitAppHint": {"description": "退出应用提示"}, "chinese": "中文", "@chinese": {"description": "中文语言"}, "uyghur": "维吾尔语", "@uyghur": {"description": "维吾尔语"}, "kazakh": "哈萨克语", "@kazakh": {"description": "哈萨克语"}, "russian": "俄语", "@russian": {"description": "俄语"}, "french": "法语", "@french": {"description": "法语"}, "spanish": "西班牙语", "@spanish": {"description": "西班牙语"}, "cantonese": "粤语", "@cantonese": {"description": "粤语"}, "selectSourceLanguageFirst": "请先选择源语言", "@selectSourceLanguageFirst": {"description": "请先选择源语言"}, "targetLanguageWillUpdate": "目标语言将根据源语言自动更新", "@targetLanguageWillUpdate": {"description": "目标语言将自动更新"}, "faceToFaceConversation": "面对面交流", "@faceToFaceConversation": {"description": "面对面交流"}, "conversation": "对话", "@conversation": {"description": "对话按钮文本"}, "newChat": "新建", "@newChat": {"description": "新建聊天按钮文本"}, "aiChatHistory": "聊天历史", "@aiChatHistory": {"description": "AI聊天历史标题"}, "noChatHistory": "暂无聊天记录", "@noChatHistory": {"description": "无聊天历史提示"}, "startNewChat": "点击右上角新建按钮开始聊天", "@startNewChat": {"description": "开始新聊天提示"}, "startChatting": "开始聊天", "@startChatting": {"description": "开始聊天提示"}, "sendFirstMessage": "发送第一条消息开始对话", "@sendFirstMessage": {"description": "发送第一条消息提示"}, "thinking": "正在思考中...", "@thinking": {"description": "AI思考中提示"}, "sending": "发送中...", "@sending": {"description": "消息发送中提示"}, "audioMessage": "语音消息", "@audioMessage": {"description": "语音消息标签"}, "typeMessage": "输入消息...", "@typeMessage": {"description": "输入框提示文本"}, "editTitle": "编辑标题", "@editTitle": {"description": "编辑标题按钮"}, "enterTitle": "请输入标题", "@enterTitle": {"description": "输入标题提示"}, "deleteConversation": "删除对话", "@deleteConversation": {"description": "删除对话"}, "deleteConversationConfirm": "确定要删除这个对话吗？此操作无法撤销。", "@deleteConversationConfirm": {"description": "删除对话确认"}, "edit": "编辑", "@edit": {"description": "编辑按钮"}, "delete": "删除", "@delete": {"description": "删除按钮"}, "save": "保存", "@save": {"description": "保存按钮"}, "loadFailed": "加载数据失败", "@loadFailed": {"description": "加载失败"}, "microphonePermissionDenied": "麦克风权限被拒绝", "@microphonePermissionDenied": {"description": "麦克风权限被拒绝"}, "recordingTooShort": "录音时间太短", "@recordingTooShort": {"description": "录音时间太短消息"}, "recordingCancelled": "录音已取消", "@recordingCancelled": {"description": "录音已取消"}, "aiChatHistorySubtitle": "查看您与健康助手的对话记录", "@aiChatHistorySubtitle": {"description": "AI聊天历史副标题"}, "clearHistoryButton": "清空历史", "@clearHistoryButton": {"description": "清空历史按钮文本"}, "clearHistoryConfirm": "确定要清空所有聊天历史吗？此操作无法撤销。", "@clearHistoryConfirm": {"description": "清空聊天历史确认"}, "clearConversation": "清空对话", "@clearConversation": {"description": "清空对话"}, "holdToSpeak": "按住说话", "@holdToSpeak": {"description": "按住说话按钮文字"}, "waitingForOther": "等待对方说话", "@waitingForOther": {"description": "等待对方说话"}, "microphonePermissionNeeded": "需要麦克风权限才能录音", "@microphonePermissionNeeded": {"description": "需要麦克风权限"}, "recordingFailed": "录音失败: {error}", "@recordingFailed": {"description": "录音失败", "placeholders": {"error": {"type": "String"}}}, "invalidAudioFile": "录音文件无效", "@invalidAudioFile": {"description": "录音文件无效"}, "audioProcessingFailed": "音频处理失败，请重试", "@audioProcessingFailed": {"description": "音频处理失败"}, "cannotRecognizeVoice": "未能识别语音内容", "@cannotRecognizeVoice": {"description": "无法识别语音"}, "confirmClearConversation": "确定要清空所有聊天记录吗？此操作无法撤销。", "@confirmClearConversation": {"description": "确认清空对话"}, "conversationCleared": "对话已清空", "@conversationCleared": {"description": "对话已清空"}, "user": "用户", "@user": {"description": "用户"}, "clickToLogin": "点击登录", "@clickToLogin": {"description": "点击登录"}, "loginToEnjoyMoreFeatures": "登录后享受更多功能", "@loginToEnjoyMoreFeatures": {"description": "登录享受更多功能"}, "editProfile": "编辑资料", "@editProfile": {"description": "编辑资料页面标题"}, "vipMember": "VIP会员", "@vipMember": {"description": "VIP会员状态"}, "distributorLevel": "分销员", "@distributorLevel": {"description": "分销员"}, "appName": "健康助手", "@appName": {"description": "应用名称"}, "shareSuccess": "分享成功！好友通过您的链接下载可获得奖励", "@shareSuccess": {"description": "分享成功"}, "shareNotAvailable": "分享功能暂时不可用，请稍后重试", "@shareNotAvailable": {"description": "分享功能不可用"}, "shareSubject": "推荐App", "@shareSubject": {"description": "分享主题"}, "shareContentWithReferral": "我发现了一款超棒的健康助手App：{appName}！专业的AI健康导游，随时为您提供健康咨询和导游服务。使用我的专属推荐链接下载，还有额外福利哦！🎁\n\n立即下载：{url}", "@shareContentWithReferral": {"description": "带推荐的分享内容", "placeholders": {"appName": {"type": "String"}, "url": {"type": "String"}}}, "shareContentNormal": "我发现了一款超棒的健康助手App：{appName}！专业的AI健康导游，随时为您提供健康咨询和导游服务。你也来试试吧！\n\n立即下载：{url}", "@shareContentNormal": {"description": "普通分享内容", "placeholders": {"appName": {"type": "String"}, "url": {"type": "String"}}}, "logoutConfirmation": "确定要退出登录吗？退出后需要重新登录才能使用完整功能。", "@logoutConfirmation": {"description": "退出登录确认"}, "logoutSuccess": "已退出登录", "@logoutSuccess": {"description": "退出登录成功"}, "logoutFailed": "退出登录失败", "@logoutFailed": {"description": "退出登录失败"}, "comingSoon": "(即将推出)", "@comingSoon": {"description": "即将推出功能标识"}, "yearsExperience": "{years}年经验", "@yearsExperience": {"description": "医生工作经验", "placeholders": {"years": {"type": "int"}}}, "ratingScore": "{rating}分", "@ratingScore": {"description": "医生评分", "placeholders": {"rating": {"type": "String"}}}, "aiAssistant": "AI", "@aiAssistant": {"description": "AI助手标识"}, "professionalIntroduction": "专业介绍", "@professionalIntroduction": {"description": "医生专业介绍标题"}, "doctorAiAssistantSelected": "已选择{<PERSON><PERSON><PERSON>}的AI助手", "@doctorAiAssistantSelected": {"description": "医生AI助手选择成功提示", "placeholders": {"doctorName": {"type": "String"}}}, "loading": "加载中...", "@loading": {"description": "加载中文字"}, "noDoctorInfo": "暂无医生信息", "@noDoctorInfo": {"description": "暂无医生信息消息"}, "doctorTitle": "医师", "@doctorTitle": {"description": "医师职称"}, "specialtyField": "擅长领域", "@specialtyField": {"description": "擅长领域字段"}, "startChatWithAiGuide": "开始与AI导游对话吧", "@startChatWithAiGuide": {"description": "对话历史空状态提示"}, "updateTitleFailed": "更新标题失败", "@updateTitleFailed": {"description": "更新对话标题失败提示"}, "selectAddress": "选择地址", "@selectAddress": {"description": "选择地址按钮"}, "addressManagement": "地址管理", "@addressManagement": {"description": "地址管理按钮"}, "noAddressesYet": "暂无收货地址", "@noAddressesYet": {"description": "无地址提示"}, "clickToAddAddress": "点击右下角添加地址", "@clickToAddAddress": {"description": "添加地址提示"}, "setAsDefault": "设为默认", "@setAsDefault": {"description": "设为默认按钮"}, "selectedItemsCount": "已选择 {count} 件商品", "@selectedItemsCount": {"description": "已选择商品数量", "placeholders": {"count": {"type": "int"}}}, "totalAmount": "合计: ", "@totalAmount": {"description": "总金额标签"}, "myLikesTitle": "我的点赞", "@myLikesTitle": {"description": "我的点赞页面标题"}, "myFavoritesTitle": "我的收藏", "@myFavoritesTitle": {"description": "我的收藏页面标题"}, "noLikedDoctors": "暂无点赞的医生", "@noLikedDoctors": {"description": "无点赞医生空状态提示"}, "noFavoriteDoctors": "暂无收藏的医生", "@noFavoriteDoctors": {"description": "无收藏医生空状态提示"}, "goLikeDoctors": "去医生详情页面为您喜欢的医生点赞吧", "@goLikeDoctors": {"description": "去点赞医生提示"}, "goFavoriteDoctors": "去医生详情页面收藏您喜欢的医生吧", "@goFavoriteDoctors": {"description": "去收藏医生提示"}, "selectAll": "全选", "@selectAll": {"description": "全选按钮"}, "deleteSelected": "删除选中", "@deleteSelected": {"description": "删除选中按钮"}, "checkout": "结算", "@checkout": {"description": "结算按钮"}, "deleteWithCount": "删除 ({count})", "@deleteWithCount": {"description": "删除按钮带数量", "placeholders": {"count": {"type": "int"}}}, "checkoutWithCount": "结算 ({count})", "@checkoutWithCount": {"description": "结算按钮带数量", "placeholders": {"count": {"type": "int"}}}, "doctorInfo": "医生信息", "@doctorInfo": {"description": "医生信息标题"}, "doctorName": "医生姓名", "@doctorName": {"description": "医生姓名标签"}, "contactPhone": "联系电话", "@contactPhone": {"description": "联系电话标签"}, "workAddress": "工作地址", "@workAddress": {"description": "工作地址标签"}, "call": "拨打", "@call": {"description": "拨打电话按钮"}, "noProducts": "暂无产品", "@noProducts": {"description": "暂无产品提示"}, "productsCount": "{count}个产品", "@productsCount": {"description": "产品数量", "placeholders": {"count": {"type": "int"}}}, "buyNow": "立即购买", "@buyNow": {"description": "立即购买按钮"}, "addToCart": "加入购物车", "@addToCart": {"description": "加入购物车按钮"}, "doctor": "医生", "@doctor": {"description": "医生标签"}, "productQuantity": "商品数量", "@productQuantity": {"description": "商品数量标签"}, "productTotalPrice": "商品总价", "@productTotalPrice": {"description": "商品总价标签"}, "shippingFee": "运费", "@shippingFee": {"description": "运费标签"}, "free": "免费", "@free": {"description": "免费标签"}, "actualPayment": "实付款", "@actualPayment": {"description": "实付款标签"}, "confirmOrder": "确认订单", "@confirmOrder": {"description": "确认订单按钮"}, "orderCreatedSuccess": "订单创建成功", "@orderCreatedSuccess": {"description": "订单创建成功提示"}, "createOrderFailed": "创建订单失败：{error}", "@createOrderFailed": {"description": "创建订单失败提示", "placeholders": {"error": {"type": "String"}}}, "checkoutFailed": "结算失败: {error}", "@checkoutFailed": {"description": "结算失败提示", "placeholders": {"error": {"type": "String"}}}, "quantityRange": "请输入1-99之间的有效数量", "@quantityRange": {"description": "数量范围提示"}, "items": "件", "@items": {"description": "件数单位"}, "consultation": "咨询", "@consultation": {"description": "咨询按钮"}, "appointment": "预约", "@appointment": {"description": "预约按钮"}, "years": "年", "@years": {"description": "年份单位"}, "yearsOfExperience": "从业年限", "@yearsOfExperience": {"description": "从业年限标签"}, "productDetail": "产品详情", "@productDetail": {"description": "产品详情页面标题"}, "price": "价格", "@price": {"description": "价格标签"}, "appointmentTime": "预约时间", "@appointmentTime": {"description": "预约时间标签"}, "detailedDescription": "详细说明", "@detailedDescription": {"description": "详细说明标题"}, "selectQuantity": "选择数量", "@selectQuantity": {"description": "选择数量标题"}, "quantity": "数量", "@quantity": {"description": "数量标签"}, "cartEmpty": "购物车空空如也", "@cartEmpty": {"description": "购物车空状态提示"}, "cartEmptyDescription": "快去挑选心仪的商品吧", "@cartEmptyDescription": {"description": "购物车空状态描述"}, "goShopping": "去购物", "@goShopping": {"description": "去购物按钮"}, "purchaseConfirmation": "购买确认", "@purchaseConfirmation": {"description": "购买确认弹窗标题"}, "manufacturer": "制造商", "@manufacturer": {"description": "制造商字段"}, "wednesday": "周三", "@wednesday": {"description": "星期三"}, "morning": "上午", "@morning": {"description": "上午时间"}, "afternoon": "下午", "@afternoon": {"description": "下午时间"}, "evening": "晚上", "@evening": {"description": "晚上时间"}, "confirmPurchase": "确认购买", "@confirmPurchase": {"description": "确认购买按钮"}, "productName": "产品名称", "@productName": {"description": "产品名称字段"}, "unitPrice": "单价", "@unitPrice": {"description": "单价标签"}, "purchaseConfirmationMessage": "请确认您的购买信息，点击确认后将跳转到订单确认页面。", "@purchaseConfirmationMessage": {"description": "购买确认提示信息"}, "orderConfirmation": "确认订单", "@orderConfirmation": {"description": "确认订单页面标题"}, "productInfo": "商品信息", "@productInfo": {"description": "商品信息标题"}, "shippingInfo": "物流信息", "@shippingInfo": {"description": "物流信息标题"}, "orderAmount": "订单金额", "@orderAmount": {"description": "订单金额标题"}, "recipientName": "收货人姓名", "@recipientName": {"description": "收货人姓名标签"}, "recipientPhone": "收货人电话", "@recipientPhone": {"description": "收货人电话标签"}, "shippingAddress": "收货地址", "@shippingAddress": {"description": "收货地址标签"}, "getCurrentLocation": "获取当前位置", "@getCurrentLocation": {"description": "获取当前位置按钮"}, "gettingLocation": "正在获取位置...", "@gettingLocation": {"description": "获取位置中提示"}, "subtotal": "小计", "@subtotal": {"description": "小计标签"}, "submitOrder": "提交订单", "@submitOrder": {"description": "提交订单按钮"}, "submittingOrder": "正在提交订单...", "@submittingOrder": {"description": "提交订单中提示"}, "enterRecipientName": "请输入收货人姓名", "@enterRecipientName": {"description": "输入收货人姓名提示"}, "enterRecipientPhone": "请输入收货人电话", "@enterRecipientPhone": {"description": "输入收货人电话提示"}, "enterShippingAddress": "请输入收货地址", "@enterShippingAddress": {"description": "输入收货地址提示"}, "totalItems": "共{count}件商品", "@totalItems": {"description": "商品总数显示", "placeholders": {"count": {"type": "int"}}}, "done": "完成", "@done": {"description": "完成按钮"}, "region": "所在地区", "@region": {"description": "所在地区标签"}, "selectRegion": "请选择省市区", "@selectRegion": {"description": "选择地区提示"}, "pleaseSelectRegion": "请选择所在地区", "@pleaseSelectRegion": {"description": "地区选择验证提示"}, "productAmount": "商品金额", "@productAmount": {"description": "商品金额标签"}, "freeShipping": "免运费", "@freeShipping": {"description": "免运费标签"}, "defaultAddress": "默认", "@defaultAddress": {"description": "默认地址标签"}, "editAddress": "编辑", "@editAddress": {"description": "编辑地址按钮"}, "deleteAddress": "删除", "@deleteAddress": {"description": "删除地址按钮"}, "loadAddressFailed": "加载地址失败", "@loadAddressFailed": {"description": "加载地址失败提示"}, "setDefaultSuccess": "设置成功", "@setDefaultSuccess": {"description": "设置默认地址成功提示"}, "setDefaultFailed": "设置失败", "@setDefaultFailed": {"description": "设置默认地址失败提示"}, "addAddress": "新增地址", "@addAddress": {"description": "新增地址标题"}, "receiverName": "收货人姓名", "@receiverName": {"description": "收货人姓名标签"}, "enterReceiverName": "请输入收货人姓名", "@enterReceiverName": {"description": "收货人姓名输入提示"}, "enterContactPhone": "请输入联系电话", "@enterContactPhone": {"description": "联系电话输入提示"}, "detailedAddress": "详细地址", "@detailedAddress": {"description": "详细地址标签"}, "enterDetailedAddress": "请输入详细的收货地址（街道、门牌号等）", "@enterDetailedAddress": {"description": "详细地址输入提示"}, "postalCodeOptional": "邮政编码（可选）", "@postalCodeOptional": {"description": "邮政编码标签"}, "enterPostalCode": "请输入邮政编码", "@enterPostalCode": {"description": "邮政编码输入提示"}, "addressLabelOptional": "地址标签（可选）", "@addressLabelOptional": {"description": "地址标签标签"}, "enterAddressLabel": "如：家、公司、学校等", "@enterAddressLabel": {"description": "地址标签输入提示"}, "addressTooShort": "详细地址至少需要5个字符", "@addressTooShort": {"description": "地址长度验证提示"}, "addressTooLong": "详细地址不能超过200个字符", "@addressTooLong": {"description": "地址长度验证提示"}, "saveChanges": "保存修改", "@saveChanges": {"description": "保存修改按钮"}, "saveAddress": "保存地址", "@saveAddress": {"description": "保存地址按钮"}, "setAsDefaultAddress": "设为默认地址", "@setAsDefaultAddress": {"description": "设为默认地址开关"}, "likeSuccess": "点赞成功", "@likeSuccess": {"description": "点赞成功提示"}, "unlikeSuccess": "取消点赞成功", "@unlikeSuccess": {"description": "取消点赞成功提示"}, "favoriteSuccess": "收藏成功", "@favoriteSuccess": {"description": "收藏成功提示"}, "unfavoriteSuccess": "取消收藏成功", "@unfavoriteSuccess": {"description": "取消收藏成功提示"}, "operationFailed": "操作失败", "@operationFailed": {"description": "操作失败提示"}, "consultDoctor": "咨询医生", "@consultDoctor": {"description": "咨询医生按钮"}, "doctorDetails": "医生详情", "@doctorDetails": {"description": "医生详情标题"}, "specialties": "擅长领域", "@specialties": {"description": "擅长领域标签"}, "doctorRecommendations": "医生推荐", "@doctorRecommendations": {"description": "医生推荐标题"}, "workingHours": "周一至周五 9:00-17:00", "@workingHours": {"description": "工作时间"}, "cannotOpenPhoneApp": "无法打开电话应用，号码已复制到剪贴板", "@cannotOpenPhoneApp": {"description": "无法打开电话应用提示"}, "operationFailedManualDial": "操作失败，请手动拨打", "@operationFailedManualDial": {"description": "操作失败手动拨打提示"}, "physician": "医师", "@physician": {"description": "医师职称"}, "noDescription": "暂无描述", "@noDescription": {"description": "暂无描述提示"}, "viewDetails": "查看详情", "@viewDetails": {"description": "查看详情按钮"}, "copiedToClipboard": "{content}已复制到剪贴板", "@copiedToClipboard": {"description": "复制成功提示", "placeholders": {"content": {"type": "String"}}}, "copyFailed": "复制失败", "@copyFailed": {"description": "复制失败提示"}, "mapPreparingPleaseWait": "地图正在准备中，请稍后再试", "@mapPreparingPleaseWait": {"description": "地图准备中请等待"}, "mapTitle": "地图", "@mapTitle": {"description": "地图标题"}, "aiGuideVoiceRecognitionFailure": "健康助手语音识别失败", "@aiGuideVoiceRecognitionFailure": {"description": "健康助手语音识别失败"}, "searchingCategory": "正在搜索{category}...", "@searchingCategory": {"description": "正在搜索特定分类", "placeholders": {"category": {"type": "String"}}}, "tryOtherCategoriesOrCheckNetwork": "请尝试选择其他分类或检查网络连接", "@tryOtherCategoriesOrCheckNetwork": {"description": "尝试其他分类或检查网络"}, "noResultsFoundFor": "未找到{city}{category}", "@noResultsFoundFor": {"description": "在城市中未找到分类结果", "placeholders": {"category": {"type": "String"}, "city": {"type": "String"}}}, "noRelatedCategoryFound": "未找到相关{category}", "@noRelatedCategoryFound": {"description": "未找到相关分类", "placeholders": {"category": {"type": "String"}}}, "address": "地址", "@address": {"description": "地址标签"}, "addressLabel": "地址：{address}", "@addressLabel": {"description": "地址标签", "placeholders": {"address": {"type": "String"}}}, "navigation": "导航", "@navigation": {"description": "导航"}, "openNavigationFailed": "打开导航失败: {error}", "@openNavigationFailed": {"description": "打开导航失败", "placeholders": {"error": {"type": "String"}}}, "parksAndSquares": "公园广场", "@parksAndSquares": {"description": "公园广场分类"}, "parks": "公园", "@parks": {"description": "公园分类"}, "zoos": "动物园", "@zoos": {"description": "动物园分类"}, "botanicalGardens": "植物园", "@botanicalGardens": {"description": "植物园分类"}, "aquariums": "水族馆", "@aquariums": {"description": "水族馆分类"}, "citySquares": "城市广场", "@citySquares": {"description": "城市广场分类"}, "memorialHalls": "纪念馆", "@memorialHalls": {"description": "纪念馆分类"}, "templesAndTaoistTemples": "寺庙道观", "@templesAndTaoistTemples": {"description": "寺庙道观分类"}, "churches": "教堂", "@churches": {"description": "教堂分类"}, "beaches": "海滩", "@beaches": {"description": "海滩分类"}, "loadDetailsFailed": "加载详情失败: {error}", "@loadDetailsFailed": {"description": "加载详情失败", "placeholders": {"error": {"type": "String"}}}, "specialtyFood": "特色美食", "@specialtyFood": {"description": "特色美食"}, "contactInfo": "联系方式", "@contactInfo": {"description": "联系信息"}, "website": "网站", "@website": {"description": "网站"}, "generatingDetailInfo": "正在生成详情信息...", "@generatingDetailInfo": {"description": "正在生成详情信息"}, "viewAiGeneratedDetailedIntroduction": "查看AI生成的详细介绍", "@viewAiGeneratedDetailedIntroduction": {"description": "查看AI生成的详细介绍"}, "clickToGetAiGeneratedDetailedIntroduction": "点击获取AI生成的详细介绍", "@clickToGetAiGeneratedDetailedIntroduction": {"description": "点击获取AI生成的详细介绍"}, "aiGenerating": "AI正在生成中...", "@aiGenerating": {"description": "AI正在生成"}, "generateDetailsFailed": "生成详情失败，请稍后重试", "@generateDetailsFailed": {"description": "生成详情失败"}, "openNavigationFailedError": "打开导航失败: {error}", "@openNavigationFailedError": {"description": "打开导航失败错误", "placeholders": {"error": {"type": "String"}}}, "addressCopiedToClipboard": "地址已复制到剪贴板", "@addressCopiedToClipboard": {"description": "地址已复制到剪贴板"}, "scenicSpotType": "风景名胜", "@scenicSpotType": {"description": "景区类型"}, "openNavigationFailedWithError": "打开导航失败: {error}", "@openNavigationFailedWithError": {"description": "打开导航失败错误", "placeholders": {"error": {"type": "String"}}}, "mapLoadFailed": "地图加载失败", "@mapLoadFailed": {"description": "地图加载失败"}, "unableToLoadMapPleaseRetry": "无法加载地图，请返回并重试", "@unableToLoadMapPleaseRetry": {"description": "无法加载地图，请返回并重试"}, "back": "返回", "@back": {"description": "返回"}, "locateToCurrentPosition": "定位到当前位置", "@locateToCurrentPosition": {"description": "定位到当前位置"}, "searchLocation": "搜索地点...", "@searchLocation": {"description": "搜索地点..."}, "foundLocation": "已找到：{name}", "@foundLocation": {"description": "已找到地点", "placeholders": {"name": {"type": "String"}}}, "mapControllerNotInitialized": "地图控制器未初始化，请返回并重试", "@mapControllerNotInitialized": {"description": "地图控制器未初始化"}, "locationServiceException": "定位服务异常：{error}", "@locationServiceException": {"description": "定位服务异常", "placeholders": {"error": {"type": "String"}}}, "mapNotFullyLoaded": "地图未完全加载，请稍后重试", "@mapNotFullyLoaded": {"description": "地图未完全加载"}, "locationFailed": "定位失败：{error}", "@locationFailed": {"description": "定位失败", "placeholders": {"error": {"type": "String"}}}, "cameraPermissionNeeded": "无法打开相机，请检查权限设置", "@cameraPermissionNeeded": {"description": "相机权限需要"}, "aiTourGuideRecognition": "健康助手识别", "@aiTourGuideRecognition": {"description": "健康助手识别"}, "aiTourGuideVoiceRecognition": "健康助手语音识别", "@aiTourGuideVoiceRecognition": {"description": "健康助手语音识别"}, "userAvatarFeatureInDevelopment": "用户头像功能开发中", "@userAvatarFeatureInDevelopment": {"description": "用户头像功能开发中"}, "inDevelopment": "功能开发中", "@inDevelopment": {"description": "功能开发中"}, "close": "关闭", "@close": {"description": "关闭提示"}, "overview": "概览", "@overview": {"description": "概览标签"}, "records": "记录", "@records": {"description": "记录标签"}, "team": "团队", "@team": {"description": "团队标签"}, "promotion": "推广", "@promotion": {"description": "推广标签"}, "loadMoreFailed": "加载更多失败", "@loadMoreFailed": {"description": "加载更多失败"}, "getUserListFailed": "获取用户列表失败", "@getUserListFailed": {"description": "获取用户列表失败"}, "levelUpdateSuccess": "用户等级修改成功", "@levelUpdateSuccess": {"description": "等级修改成功"}, "levelUpdateFailed": "修改等级失败", "@levelUpdateFailed": {"description": "修改等级失败"}, "certifiedDistributor": "已认证分销员", "@certifiedDistributor": {"description": "已认证分销员"}, "fundsDetail": "资金详情", "@fundsDetail": {"description": "资金详情"}, "withdrawing": "提现中", "@withdrawing": {"description": "提现中"}, "withdrawn": "已提现", "@withdrawn": {"description": "已提现"}, "totalCommissionIncome": "分销佣金总收入", "@totalCommissionIncome": {"description": "分销佣金总收入"}, "noPromotionPosters": "暂无推广海报", "@noPromotionPosters": {"description": "暂无推广海报"}, "testDescription": "测试说明：", "@testDescription": {"description": "测试说明"}, "longPressToSelect": "长按下方的文本进行选择，复制的内容只包含汉字。", "@longPressToSelect": {"description": "长按选择说明"}, "smartCopyVersion": "智能复制版本：", "@smartCopyVersion": {"description": "智能复制版本"}, "plainTextVersion": "纯文本版本：", "@plainTextVersion": {"description": "纯文本版本"}, "smartCopyTest": "智能复制功能测试", "@smartCopyTest": {"description": "智能复制功能测试"}, "loadHistoryFailed": "加载历史记录失败: {error}", "@loadHistoryFailed": {"description": "加载历史记录失败", "placeholders": {"error": {"type": "String"}}}, "clearHistoryFailure": "清空历史记录失败: {error}", "@clearHistoryFailure": {"description": "清空历史记录失败", "placeholders": {"error": {"type": "String"}}}, "cameraAccessFailure": "无法打开相机，请检查权限设置", "@cameraAccessFailure": {"description": "相机访问失败"}, "instructions": "您的专属AI健康导游，随时为您提供健康咨询和导游服务", "@instructions": {"description": "使用说明"}, "enterPhoneNumber": "请输入11位手机号", "@enterPhoneNumber": {"description": "输入手机号提示"}, "enterCorrectVerificationCode": "请输入正确的验证码", "@enterCorrectVerificationCode": {"description": "输入正确验证码"}, "sendingVerificationCode": "正在发送验证码...", "@sendingVerificationCode": {"description": "正在发送验证码"}, "verificationCodeSentToPhone": "验证码已发送至您的手机", "@verificationCodeSentToPhone": {"description": "验证码已发送至手机"}, "networkConnectionFailed": "网络连接失败，请稍后重试", "@networkConnectionFailed": {"description": "网络连接失败"}, "networkRequestFailed": "网络请求失败，状态码: {statusCode}", "@networkRequestFailed": {"description": "网络请求失败", "placeholders": {"statusCode": {"type": "String"}}}, "sendVerificationCodeError": "发送验证码出错: {error}", "@sendVerificationCodeError": {"description": "发送验证码出错", "placeholders": {"error": {"type": "String"}}}, "resettingPassword": "正在重置密码...", "@resettingPassword": {"description": "重置密码中"}, "processingUserDataError": "处理用户数据时出错: {error}", "@processingUserDataError": {"description": "处理用户数据出错", "placeholders": {"error": {"type": "String"}}}, "loginSuccessButNoUserData": "登录成功但用户数据为空", "@loginSuccessButNoUserData": {"description": "登录成功但无用户数据"}, "userNotRegisteredRedirecting": "用户未注册，即将跳转到注册页面", "@userNotRegisteredRedirecting": {"description": "用户未注册跳转注册"}, "historyDeleted": "历史记录已删除", "@historyDeleted": {"description": "历史记录已删除"}, "deleteHistoryFailed": "删除历史记录失败: {error}", "@deleteHistoryFailed": {"description": "删除历史记录失败", "placeholders": {"error": {"type": "String"}}}, "noCameraDetected": "未检测到可用相机", "@noCameraDetected": {"description": "未检测到相机"}, "cameraInitializationFailed": "相机初始化失败: {error}", "@cameraInitializationFailed": {"description": "相机初始化失败", "placeholders": {"error": {"type": "String"}}}, "cameraNotReady": "相机未准备就绪", "@cameraNotReady": {"description": "相机未准备就绪"}, "capturePhotoFailed": "拍照失败: {error}", "@capturePhotoFailed": {"description": "拍照失败", "placeholders": {"error": {"type": "String"}}}, "galleryPermissionRequired": "需要相册权限才能选择图片", "@galleryPermissionRequired": {"description": "需要相册权限"}, "selectImageFailed": "选择图片失败: {error}", "@selectImageFailed": {"description": "选择图片失败消息", "placeholders": {"error": {"type": "String"}}}, "flashlightOperationFailed": "手电筒操作失败", "@flashlightOperationFailed": {"description": "手电筒操作失败"}, "switchCameraFailed": "切换摄像头失败", "@switchCameraFailed": {"description": "切换摄像头失败"}, "languageNotSupportedAsSource": "{language}不支持作为源语言，无法切换", "@languageNotSupportedAsSource": {"description": "语言不支持作为源语言", "placeholders": {"language": {"type": "String"}}}, "enterUsername": "请输入用户名", "@enterUsername": {"description": "输入用户名"}, "passwordMinLength6": "密码长度至少为6位", "@passwordMinLength6": {"description": "密码最小长度6位"}, "passwordsNotMatch": "两次输入的密码不一致", "@passwordsNotMatch": {"description": "密码不一致"}, "agreeToUserAgreement": "请同意用户协议和隐私政策", "@agreeToUserAgreement": {"description": "同意用户协议"}, "registering": "正在注册...", "@registering": {"description": "注册中"}, "registerSuccess": "注册成功", "@registerSuccess": {"description": "注册成功"}, "phoneFormatIncorrect": "手机号格式不正确", "@phoneFormatIncorrect": {"description": "手机号格式不正确"}, "verificationCodeExpired": "验证码错误或已过期", "@verificationCodeExpired": {"description": "验证码过期"}, "usernameAlreadyRegistered": "用户名已被注册，请更换用户名", "@usernameAlreadyRegistered": {"description": "用户名已注册"}, "phoneAlreadyRegistered": "该手机号已注册，可直接登录", "@phoneAlreadyRegistered": {"description": "手机号已注册"}, "registerFailed": "注册失败: {message}", "@registerFailed": {"description": "注册失败", "placeholders": {"message": {"type": "String"}}}, "registerProcessError": "注册过程中出错: {error}", "@registerProcessError": {"description": "注册过程出错", "placeholders": {"error": {"type": "String"}}}, "openUserAgreement": "打开用户协议", "@openUserAgreement": {"description": "打开用户协议"}, "openPrivacyPolicy": "打开隐私政策", "@openPrivacyPolicy": {"description": "打开隐私政策"}, "priceInfoLoadingWait": "价格信息加载中，请稍候...", "@priceInfoLoadingWait": {"description": "价格信息加载中"}, "priceInfoLoadFailed": "获取价格信息失败，显示默认价格", "@priceInfoLoadFailed": {"description": "价格信息加载失败"}, "recordingStartFailed": "录音启动失败，请检查麦克风权限", "@recordingStartFailed": {"description": "录音启动失败"}, "recordingStartError": "录音启动出错，请重试", "@recordingStartError": {"description": "录音启动出错"}, "recordingFailedRetry": "录音失败，请重试", "@recordingFailedRetry": {"description": "录音失败重试"}, "audioProcessingFailedRetry": "音频处理失败，请重试", "@audioProcessingFailedRetry": {"description": "音频处理失败重试"}, "voiceProcessingError": "处理语音时出错：{error}", "@voiceProcessingError": {"description": "语音处理出错", "placeholders": {"error": {"type": "String"}}}, "playbackFailed": "播放失败: {error}", "@playbackFailed": {"description": "播放失败", "placeholders": {"error": {"type": "String"}}}, "microphoneRecordingPermissionRequired": "无法录音：需要麦克风权限", "@microphoneRecordingPermissionRequired": {"description": "录音需要麦克风权限"}, "permissionGrantedRetryRecording": "权限已授予，请重新长按录音按钮开始录音", "@permissionGrantedRetryRecording": {"description": "权限已授予重试录音"}, "logoutFailedError": "退出登录失败: {error}", "@logoutFailedError": {"description": "退出登录失败", "placeholders": {"error": {"type": "String"}}}, "aiTourGuideRecognitionResult": "健康助手识别: {text}", "@aiTourGuideRecognitionResult": {"description": "健康助手识别结果", "placeholders": {"text": {"type": "String"}}}, "aiTourGuideVoiceRecognitionResult": "健康助手语音识别: {text}", "@aiTourGuideVoiceRecognitionResult": {"description": "健康助手语音识别结果", "placeholders": {"text": {"type": "String"}}}, "profileTitle": "我的", "@profileTitle": {"description": "个人资料页面标题"}, "editProfileTitle": "编辑个人资料", "@editProfileTitle": {"description": "编辑个人资料页面标题"}, "healthInfo": "健康信息", "@healthInfo": {"description": "健康信息标题"}, "heightHint": "请输入身高 (cm)", "@heightHint": {"description": "身高输入提示"}, "heightValidation": "请输入有效的身高 (50-250cm)", "@heightValidation": {"description": "身高验证提示"}, "weightHint": "请输入体重 (kg)", "@weightHint": {"description": "体重输入提示"}, "weightValidation": "请输入有效的体重 (20-300kg)", "@weightValidation": {"description": "体重验证提示"}, "selectBloodType": "请选择血型", "@selectBloodType": {"description": "血型选择提示"}, "bloodTypeA": "A型", "@bloodTypeA": {"description": "A型血"}, "bloodTypeB": "B型", "@bloodTypeB": {"description": "B型血"}, "bloodTypeAB": "AB型", "@bloodTypeAB": {"description": "AB型血"}, "bloodTypeO": "O型", "@bloodTypeO": {"description": "O型血"}, "bloodTypeUnknown": "不清楚", "@bloodTypeUnknown": {"description": "血型不清楚"}, "residentialAddress": "常住地址", "@residentialAddress": {"description": "常住地址标签"}, "locate": "定位", "@locate": {"description": "定位按钮"}, "selectResidentialAddress": "请选择常住地址", "@selectResidentialAddress": {"description": "选择常住地址提示"}, "regionSelectionFailed": "地区选择失败，请重试", "@regionSelectionFailed": {"description": "地区选择失败提示"}, "commonAllergens": "常见过敏源", "@commonAllergens": {"description": "常见过敏源标题"}, "penicillinAllergy": "青霉素类药物", "@penicillinAllergy": {"description": "青霉素过敏"}, "cephalosporinAllergy": "头孢类药物", "@cephalosporinAllergy": {"description": "头孢过敏"}, "aspirinAllergy": "阿司匹林", "@aspirinAllergy": {"description": "阿司匹林过敏"}, "peanutAllergy": "花生", "@peanutAllergy": {"description": "花生过敏"}, "seafoodAllergy": "海鲜", "@seafoodAllergy": {"description": "海鲜过敏"}, "milkAllergy": "牛奶", "@milkAllergy": {"description": "牛奶过敏"}, "eggAllergy": "鸡蛋", "@eggAllergy": {"description": "鸡蛋过敏"}, "pollenDustMiteAllergy": "花粉/尘螨", "@pollenDustMiteAllergy": {"description": "花粉尘螨过敏"}, "otherAllergens": "其他过敏物质", "@otherAllergens": {"description": "其他过敏物质标签"}, "otherAllergensHint": "请补充其他过敏源", "@otherAllergensHint": {"description": "其他过敏源输入提示"}, "takingMedication": "目前是否在服用任何药物", "@takingMedication": {"description": "是否在服用药物询问"}, "medicationList": "药物清单", "@medicationList": {"description": "药物清单标签"}, "medicationListHint": "请列出正在服用的药物名称、剂量和频率", "@medicationListHint": {"description": "药物清单输入提示"}, "hasChronicDisease": "是否患有慢性疾病", "@hasChronicDisease": {"description": "是否患有慢性疾病询问"}, "specificSymptoms": "具体病症", "@specificSymptoms": {"description": "具体病症标题"}, "hypertension": "高血压", "@hypertension": {"description": "高血压"}, "bloodPressureHint": "例如 130/85 mmHg", "@bloodPressureHint": {"description": "血压输入提示"}, "diabetes": "糖尿病", "@diabetes": {"description": "糖尿病"}, "bloodSugarHint": "例如 5.8 mmol/L", "@bloodSugarHint": {"description": "血糖输入提示"}, "otherChronicDiseases": "其他慢性疾病", "@otherChronicDiseases": {"description": "其他慢性疾病标签"}, "otherChronicDiseasesHint": "请补充其他慢性疾病", "@otherChronicDiseasesHint": {"description": "其他慢性疾病输入提示"}, "surgeryHistory": "手术与住院史", "@surgeryHistory": {"description": "手术与住院史标题"}, "hasSurgeryHistory": "过去是否有过手术或住院经历", "@hasSurgeryHistory": {"description": "是否有手术住院史询问"}, "surgeryDetails": "详情说明", "@surgeryDetails": {"description": "手术详情标签"}, "surgeryDetailsHint": "请描述手术或住院的详细情况", "@surgeryDetailsHint": {"description": "手术详情输入提示"}, "familyHistory": "家族病史", "@familyHistory": {"description": "家族病史标题"}, "familyDiseaseHistory": "直系亲属（父母、兄弟姐妹、子女）是否有以下疾病", "@familyDiseaseHistory": {"description": "家族疾病史询问"}, "familyHypertension": "高血压", "@familyHypertension": {"description": "家族高血压史"}, "familyDiabetes": "糖尿病", "@familyDiabetes": {"description": "家族糖尿病史"}, "familyHeartDisease": "心脏病", "@familyHeartDisease": {"description": "家族心脏病史"}, "familyStroke": "中风", "@familyStroke": {"description": "家族中风史"}, "familyCancer": "癌症", "@familyCancer": {"description": "家族癌症史"}, "familyMentalHealth": "精神健康疾病", "@familyMentalHealth": {"description": "家族精神健康疾病史"}, "otherFamilyHistory": "其他家族病史补充", "@otherFamilyHistory": {"description": "其他家族病史标签"}, "otherFamilyHistoryHint": "请补充其他家族病史", "@otherFamilyHistoryHint": {"description": "其他家族病史输入提示"}, "exerciseSedentary": "久坐 (基本不运动)", "@exerciseSedentary": {"description": "久坐运动频率"}, "exerciseLight": "轻度活跃 (每周运动1-2次)", "@exerciseLight": {"description": "轻度运动频率"}, "exerciseModerate": "中度活跃 (每周运动3-5次)", "@exerciseModerate": {"description": "中度运动频率"}, "exerciseActive": "非常活跃 (每周运动6次及以上)", "@exerciseActive": {"description": "高度运动频率"}, "dietaryPreferences": "日常饮食偏好", "@dietaryPreferences": {"description": "饮食偏好标签"}, "balancedDiet": "饮食均衡", "@balancedDiet": {"description": "饮食均衡"}, "vegetarianDiet": "偏素食", "@vegetarianDiet": {"description": "偏素食"}, "meatDiet": "偏肉食", "@meatDiet": {"description": "偏肉食"}, "oilyFood": "偏好油腻食物", "@oilyFood": {"description": "偏好油腻食物"}, "saltyFood": "偏好咸味食物", "@saltyFood": {"description": "偏好咸味食物"}, "sweetFood": "偏好甜食", "@sweetFood": {"description": "偏好甜食"}, "neverSmoke": "从不吸烟", "@neverSmoke": {"description": "从不吸烟"}, "quitSmoking": "已戒烟", "@quitSmoking": {"description": "已戒烟"}, "occasionalSmoking": "偶尔吸烟 (非每日)", "@occasionalSmoking": {"description": "偶尔吸烟"}, "dailySmoking": "经常吸烟 (每日)", "@dailySmoking": {"description": "经常吸烟"}, "neverDrink": "从不饮酒", "@neverDrink": {"description": "从不饮酒"}, "quitDrinking": "已戒酒", "@quitDrinking": {"description": "已戒酒"}, "socialDrinking": "偶尔社交性饮酒", "@socialDrinking": {"description": "偶尔社交性饮酒"}, "weeklyDrinking": "每周1-3次", "@weeklyDrinking": {"description": "每周1-3次饮酒"}, "dailyDrinking": "几乎每天", "@dailyDrinking": {"description": "几乎每天饮酒"}, "sleepLessThan6": "少于6小时", "@sleepLessThan6": {"description": "少于6小时睡眠"}, "sleep6To7": "6-7小时", "@sleep6To7": {"description": "6-7小时睡眠"}, "sleep7To8": "7-8小时", "@sleep7To8": {"description": "7-8小时睡眠"}, "sleepMoreThan8": "8小时以上", "@sleepMoreThan8": {"description": "8小时以上睡眠"}, "sleepGood": "良好 (容易入睡，很少惊醒)", "@sleepGood": {"description": "睡眠质量良好"}, "sleepFair": "一般 (偶有入睡困难或早醒)", "@sleepFair": {"description": "睡眠质量一般"}, "sleepPoor": "较差 (长期入睡困难、多梦、早醒)", "@sleepPoor": {"description": "睡眠质量较差"}, "stressLow": "很小", "@stressLow": {"description": "压力很小"}, "stressMild": "稍有压力", "@stressMild": {"description": "稍有压力"}, "stressModerate": "压力适中", "@stressModerate": {"description": "压力适中"}, "stressHigh": "压力较大", "@stressHigh": {"description": "压力较大"}, "stressExtreme": "压力极大", "@stressExtreme": {"description": "压力极大"}, "womenHealth": "女性健康", "@womenHealth": {"description": "女性健康标题"}, "isMenopause": "是否已绝经", "@isMenopause": {"description": "是否已绝经询问"}, "menstrualCycleRegular": "月经周期是否规律", "@menstrualCycleRegular": {"description": "月经周期是否规律询问"}, "menstrualRegular": "规律", "@menstrualRegular": {"description": "月经规律"}, "menstrualIrregular": "不规律", "@menstrualIrregular": {"description": "月经不规律"}, "menstrualUncertain": "不确定", "@menstrualUncertain": {"description": "月经周期不确定"}, "hasPregnancy": "是否曾怀孕", "@hasPregnancy": {"description": "是否曾怀孕询问"}, "birthCount": "生育次数", "@birthCount": {"description": "生育次数标签"}, "birthCount0": "0次", "@birthCount0": {"description": "生育0次"}, "birthCount1": "1次", "@birthCount1": {"description": "生育1次"}, "birthCount2": "2次", "@birthCount2": {"description": "生育2次"}, "birthCount3": "3次", "@birthCount3": {"description": "生育3次"}, "birthCount4": "4次", "@birthCount4": {"description": "生育4次"}, "birthCount5Plus": "5次及以上", "@birthCount5Plus": {"description": "生育5次及以上"}, "cannotParseImage": "无法解析图片文件", "@cannotParseImage": {"description": "无法解析图片文件错误"}, "vipMemberBadge": "VIP会员", "@vipMemberBadge": {"description": "VIP会员徽章"}, "normalUserBadge": "普通用户", "@normalUserBadge": {"description": "普通用户徽章"}, "distributorLevelBadge": "分销员 Lv.{level}", "@distributorLevelBadge": {"description": "分销员等级徽章", "placeholders": {"level": {"type": "String"}}}, "distributorBadge": "分销员", "@distributorBadge": {"description": "分销员徽章"}, "arabic": "阿拉伯语", "@arabic": {"description": "阿拉伯语"}, "english": "英语", "@english": {"description": "英语"}, "languageSwitchNotSupported": "{language}不支持作为源语言，无法切换", "@languageSwitchNotSupported": {"description": "语言切换不支持提示", "placeholders": {"language": {"type": "String"}}}, "sourceLanguageLabel": "源", "@sourceLanguageLabel": {"description": "源语言标签"}, "targetLanguageLabel": "目标", "@targetLanguageLabel": {"description": "目标语言标签"}, "currentBalance": "当前余额", "@currentBalance": {"description": "当前余额"}, "todayIncome": "今日收入", "@todayIncome": {"description": "今日收入"}, "totalIncome": "总收入", "@totalIncome": {"description": "总收入"}, "holdMicrophoneToSpeak": "按住麦克风开始说话", "@holdMicrophoneToSpeak": {"description": "按住麦克风开始说话"}, "waitingForOtherParty": "等待对方说话", "@waitingForOtherParty": {"description": "等待对方说话"}, "confirmClear": "确认清空", "@confirmClear": {"description": "确认清空对话标题"}, "confirmClearAllChatRecords": "确定要清空所有聊天记录吗？此操作无法撤销。", "@confirmClearAllChatRecords": {"description": "确认清空所有聊天记录"}, "confirmClearAction": "确定清空", "@confirmClearAction": {"description": "确定清空"}, "registerTitle": "注册用户", "@registerTitle": {"description": "注册页面标题"}, "usernameHint": "请输入用户名", "@usernameHint": {"description": "用户名输入提示"}, "setPassword": "设置密码", "@setPassword": {"description": "设置密码提示"}, "confirmPassword": "重输密码", "@confirmPassword": {"description": "确认密码提示"}, "getCodeButton": "获取验证码", "@getCodeButton": {"description": "获取验证码按钮"}, "countdownSeconds": "{count}秒", "@countdownSeconds": {"description": "倒计时秒数", "placeholders": {"count": {"type": "int"}}}, "register": "注册", "@register": {"description": "注册按钮"}, "phoneNumberLogin": "手机号登录", "@phoneNumberLogin": {"description": "手机号登录按钮"}, "userAgreementAndPrivacyPolicy": "《用户协议》和《隐私政策》", "@userAgreementAndPrivacyPolicy": {"description": "用户协议和隐私政策"}, "iAgreeToThe": "我已阅读并同意", "@iAgreeToThe": {"description": "我已阅读并同意前缀"}, "helpFeedbackTitle": "帮助与反馈", "@helpFeedbackTitle": {"description": "帮助与反馈页面标题"}, "yourNameOptional": "您的称呼 (可选)", "@yourNameOptional": {"description": "姓名输入框标签"}, "yourPhoneNumber": "您的手机号", "@yourPhoneNumber": {"description": "手机号输入框标签"}, "describeProblemDetail": "请详细描述您的问题或建议", "@describeProblemDetail": {"description": "问题描述输入框标签"}, "submitFeedback": "提交反馈", "@submitFeedback": {"description": "提交反馈按钮"}, "pleaseEnterCorrectPhoneFormat": "请输入正确的手机号格式", "@pleaseEnterCorrectPhoneFormat": {"description": "手机号格式错误提示"}, "pleaseDescribeProblem": "请描述您遇到的问题", "@pleaseDescribeProblem": {"description": "请描述问题验证提示"}, "descriptionMinLength": "问题描述至少需要10个字符", "@descriptionMinLength": {"description": "描述最小长度提示"}, "descriptionMaxLength": "问题描述不能超过1000个字符", "@descriptionMaxLength": {"description": "描述最大长度提示"}, "submittingFeedback": "正在提交反馈...", "@submittingFeedback": {"description": "提交反馈中提示"}, "feedbackSubmittedSuccess": "反馈提交成功，感谢您的建议！", "@feedbackSubmittedSuccess": {"description": "反馈提交成功提示"}, "feedbackSubmissionFailed": "反馈提交失败: {error}", "@feedbackSubmissionFailed": {"description": "反馈提交失败提示", "placeholders": {"error": {"type": "String"}}}, "feedbackInstructions": "反馈说明", "@feedbackInstructions": {"description": "反馈说明标题"}, "feedbackInstructionsText": "我们会收集您的问题描述和相关应用数据（不包含敏感信息）以便更好地解决您的问题。提交后我们将通过您提供的手机号与您联系。", "@feedbackInstructionsText": {"description": "反馈说明文本"}, "enterYourName": "请输入您的称呼", "@enterYourName": {"description": "输入姓名提示"}, "problemDescriptionHint": "请详细描述您遇到的问题或建议\n包括：\n• 具体的操作步骤\n• 期望的结果\n• 实际发生的情况\n• 其他相关信息", "@problemDescriptionHint": {"description": "问题描述提示文本"}, "submitting": "提交中...", "@submitting": {"description": "提交中状态文本"}, "testLogGeneration": "测试日志生成", "@testLogGeneration": {"description": "测试日志生成按钮"}, "viewErrorLogs": "查看错误日志", "@viewErrorLogs": {"description": "查看错误日志按钮"}, "generateTestErrors": "生成测试错误", "@generateTestErrors": {"description": "生成测试错误按钮"}, "privacyNotice": "提示：您的隐私对我们很重要，我们不会收集密码等敏感信息，仅收集必要的应用配置和日志数据以帮助解决问题。", "@privacyNotice": {"description": "隐私说明文本"}, "logGenerationSuccess": "日志生成成功", "@logGenerationSuccess": {"description": "日志生成成功标题"}, "logSize": "日志大小: {size}KB", "@logSize": {"description": "日志大小文本", "placeholders": {"size": {"type": "String"}}}, "testErrorLogsGenerated": "已生成测试错误日志，可以在错误日志查看器中查看", "@testErrorLogsGenerated": {"description": "测试错误日志生成消息"}, "feedbackSubmittedSuccessfully": "反馈提交成功，我们会尽快处理您的问题", "@feedbackSubmittedSuccessfully": {"description": "反馈提交成功消息"}, "submissionFailed": "提交失败", "@submissionFailed": {"description": "提交失败消息"}, "submissionFailedCheckNetwork": "提交失败，请检查网络连接", "@submissionFailedCheckNetwork": {"description": "提交失败检查网络消息"}, "logGenerationFailed": "日志生成失败: {error}", "@logGenerationFailed": {"description": "日志生成失败消息", "placeholders": {"error": {"type": "String"}}}, "phoneNumberForContact": "(用于问题回访)", "@phoneNumberForContact": {"description": "手机号联系提示"}, "nickname": "昵称", "@nickname": {"description": "昵称字段标签"}, "nicknameRequired": "昵称不能为空", "@nicknameRequired": {"description": "昵称必填验证消息"}, "nicknameMinLength": "昵称至少需要2个字符", "@nicknameMinLength": {"description": "昵称最小长度验证消息"}, "changePassword": "修改密码", "@changePassword": {"description": "修改密码分组标题"}, "changePasswordDescription": "请设置您的新密码，确保密码安全性。", "@changePasswordDescription": {"description": "修改密码页面说明文字"}, "passwordRequirements": "密码要求", "@passwordRequirements": {"description": "密码要求标题"}, "passwordLengthRequirement": "密码长度为6-20位字符", "@passwordLengthRequirement": {"description": "密码长度要求说明"}, "accountSettings": "账户设置", "@accountSettings": {"description": "账户设置分组标题"}, "changePasswordSubtitle": "修改您的登录密码", "@changePasswordSubtitle": {"description": "修改密码选项副标题"}, "newPasswordRequired": "请输入新密码", "@newPasswordRequired": {"description": "新密码必填验证消息"}, "confirmNewPassword": "确认新密码", "@confirmNewPassword": {"description": "确认新密码字段标签"}, "confirmNewPasswordRequired": "请确认新密码", "@confirmNewPasswordRequired": {"description": "确认新密码必填验证消息"}, "changeAvatar": "更换头像", "@changeAvatar": {"description": "更换头像按钮文本"}, "uploading": "上传中...", "@uploading": {"description": "上传中状态"}, "selectAvatar": "选择头像", "@selectAvatar": {"description": "选择头像对话框标题"}, "takePhoto": "拍照", "@takePhoto": {"description": "拍照选项"}, "selectFromGallery": "从相册选择", "@selectFromGallery": {"description": "从相册选择选项"}, "avatarUploadSuccess": "头像上传成功", "@avatarUploadSuccess": {"description": "头像上传成功消息"}, "avatarUploadFailed": "头像上传失败", "@avatarUploadFailed": {"description": "头像上传失败消息"}, "gender": "性别", "@gender": {"description": "性别字段标签"}, "notSet": "未设置", "@notSet": {"description": "未设置选项"}, "birthday": "生日", "@birthday": {"description": "生日字段标签"}, "profileSaveSuccess": "资料保存成功", "@profileSaveSuccess": {"description": "资料保存成功消息"}, "saveFailed": "保存失败", "@saveFailed": {"description": "保存失败消息"}, "passwordChangeSuccess": "密码修改成功", "@passwordChangeSuccess": {"description": "密码修改成功消息"}, "cropAvatar": "裁剪头像", "@cropAvatar": {"description": "裁剪头像对话框标题"}, "reselectImage": "重新选择", "@reselectImage": {"description": "重新选择图片按钮"}, "confirmCrop": "确认", "@confirmCrop": {"description": "确认裁剪按钮"}, "cannotParseImageFile": "无法解析图片文件", "@cannotParseImageFile": {"description": "无法解析图片文件错误消息"}, "loadUserProfileFailed": "加载用户资料失败", "@loadUserProfileFailed": {"description": "加载用户资料失败消息"}, "takePhotoFailed": "拍照失败: {error}", "@takePhotoFailed": {"description": "拍照失败消息", "placeholders": {"error": {"type": "String"}}}, "avatarUploadFailedButProfileWillSave": "头像上传失败，但其他资料将继续保存", "@avatarUploadFailedButProfileWillSave": {"description": "头像上传失败但资料将保存消息"}, "loginExpiredPleaseRelogin": "登录已过期，请重新登录", "@loginExpiredPleaseRelogin": {"description": "登录过期消息"}, "processImageFailed": "处理图片失败: {error}", "@processImageFailed": {"description": "处理图片失败消息", "placeholders": {"error": {"type": "String"}}}, "newPasswordMaxLength": "新密码不能超过20位", "@newPasswordMaxLength": {"description": "新密码最大长度验证消息"}, "userCardMemberStatusLabel": "会员状态", "@userCardMemberStatusLabel": {"description": "用户卡片中的会员状态标签"}, "userCardExpiryDateLabel": "到期时间", "@userCardExpiryDateLabel": {"description": "用户卡片中的到期日期标签"}, "userCardUidLabel": "UID:", "@userCardUidLabel": {"description": "用户卡片中的UID标签"}, "languageOptionChineseSimplified": "中文", "@languageOptionChineseSimplified": {"description": "简体中文语言选项"}, "languageOptionUyghur": "维吾尔语", "@languageOptionUyghur": {"description": "维吾尔语语言选项"}, "languageOptionEnglish": "英语", "@languageOptionEnglish": {"description": "英语语言选项"}, "languageOptionKazakh": "哈萨克语", "@languageOptionKazakh": {"description": "哈萨克语语言选项"}, "languageOptionRussian": "俄语", "@languageOptionRussian": {"description": "俄语语言选项"}, "languageOptionFrench": "法语", "@languageOptionFrench": {"description": "法语语言选项"}, "languageOptionSpanish": "西班牙语", "@languageOptionSpanish": {"description": "西班牙语语言选项"}, "languageOptionCantonese": "粤语", "@languageOptionCantonese": {"description": "粤语语言选项"}, "languageOptionArabic": "阿拉伯语", "@languageOptionArabic": {"description": "阿拉伯语语言选项"}, "historyListEmpty": "暂无历史记录", "@historyListEmpty": {"description": "历史记录为空提示"}, "aiGuideVoiceQueryButton": "语音提问", "@aiGuideVoiceQueryButton": {"description": "健康助手语音提问按钮"}, "aiGuidePhotoQueryButton": "拍照提问", "@aiGuidePhotoQueryButton": {"description": "健康助手拍照提问按钮"}, "aiGuideQueryHint": "欢迎使用健康助手，请按住下方按钮拍照提问", "@aiGuideQueryHint": {"description": "健康助手提问提示文本"}, "faceToFaceSelectLanguagesHint": "请选择双方语言", "@faceToFaceSelectLanguagesHint": {"description": "面对面交流语言选择提示"}, "resendCodeTimerLabel": "重新发送({seconds}秒)", "@resendCodeTimerLabel": {"description": "重新发送验证码倒计时标签", "placeholders": {"seconds": {"type": "String"}}}, "guestUser": "游客用户", "@guestUser": {"description": "游客用户标签"}, "pleaseLogin": "请先登录", "@pleaseLogin": {"description": "请先登录提示"}, "membershipStatus": "会员状态", "@membershipStatus": {"description": "会员状态标签"}, "expiresOn": "到期时间", "@expiresOn": {"description": "到期时间标签"}, "editProfileButton": "编辑资料", "@editProfileButton": {"description": "编辑资料按钮文本"}, "notLoggedInUser": "未登录用户", "@notLoggedInUser": {"description": "未登录用户状态"}, "verificationCodeLoginTitle": "验证码登录", "@verificationCodeLoginTitle": {"description": "验证码登录页面标题"}, "phoneInputLabel": "手机号", "@phoneInputLabel": {"description": "手机号输入框标签"}, "phoneInputHint": "请输入手机号", "@phoneInputHint": {"description": "手机号输入框提示"}, "codeInputLabel": "验证码", "@codeInputLabel": {"description": "验证码输入框标签"}, "codeInputHint": "请输入验证码", "@codeInputHint": {"description": "验证码输入框提示"}, "resendCodeTimerButton": "重新发送({seconds}秒)", "@resendCodeTimerButton": {"description": "重新发送验证码倒计时按钮", "placeholders": {"seconds": {"type": "String"}}}, "loginButton": "登录", "@loginButton": {"description": "登录按钮"}, "autoRegisterHint": "未注册手机号将自动注册", "@autoRegisterHint": {"description": "自动注册提示"}, "reminderTitle": "提醒", "@reminderTitle": {"description": "提醒弹窗标题"}, "loginRequiredForDistributionMessage": "请先登录再使用分销管理功能", "@loginRequiredForDistributionMessage": {"description": "分销管理需要登录提示"}, "distributionAccessDeniedMessage": "账户没有分销员权限，你可以申请成为分销员", "@distributionAccessDeniedMessage": {"description": "分销管理权限不足提示"}, "goToLoginButton": "去登录", "@goToLoginButton": {"description": "去登录按钮"}, "applyButton": "申请", "@applyButton": {"description": "申请按钮"}, "typeMessageHint": "请输入消息", "@typeMessageHint": {"description": "健康助手页面输入框提示"}, "verificationCodeSentSeconds": "{seconds}秒", "@verificationCodeSentSeconds": {"description": "验证码倒计时显示", "placeholders": {"seconds": {"type": "String"}}}, "welcomeBackTitle": "欢迎回来", "@welcomeBackTitle": {"description": "欢迎回来标题"}, "loginWithPhoneSubtitle": "请使用您的手机号登录账户", "@loginWithPhoneSubtitle": {"description": "手机号登录副标题"}, "registerAccountButton": "注册账号", "@registerAccountButton": {"description": "注册账号按钮"}, "passwordLoginButton": "密码登录", "@passwordLoginButton": {"description": "密码登录按钮"}, "loginAgreementText": "登录即表示您同意《用户协议》和《隐私政策》", "@loginAgreementText": {"description": "登录协议文本"}, "loginFailed": "登录失败", "@loginFailed": {"description": "登录失败提示"}, "loginRequiredForFeature": "请先登录再使用{featureName}功能", "@loginRequiredForFeature": {"description": "功能需要登录提示", "placeholders": {"featureName": {"type": "String"}}}, "loginRequiredGeneral": "此功能需要登录后才能使用，请先登录", "@loginRequiredGeneral": {"description": "通用登录提示"}, "loginButtonText": "登录", "@loginButtonText": {"description": "登录按钮文本"}, "applicationSubmitted": "申请已提交，请等待审核", "@applicationSubmitted": {"description": "申请已提交提示"}, "applicationFailed": "申请失败", "@applicationFailed": {"description": "申请失败提示"}, "distributorBenefitDescription": "成为分销员后，您可以推广产品并获得佣金收益", "@distributorBenefitDescription": {"description": "分销员收益描述"}, "aiGuidePhotoQuestion": "健康助手拍照提问", "@aiGuidePhotoQuestion": {"description": "健康助手拍照提问功能名称"}, "aiGuideVoiceQuestion": "健康助手语音提问", "@aiGuideVoiceQuestion": {"description": "健康助手语音提问功能名称"}, "recordingStartFailedCheckPermission": "录音启动失败，请检查麦克风权限", "@recordingStartFailedCheckPermission": {"description": "录音启动失败权限提示"}, "adjustFontSize": "调整字体大小", "@adjustFontSize": {"description": "调整字体大小对话框标题"}, "fontPreviewText": "字体预览 Font Preview", "@fontPreviewText": {"description": "字体预览文本"}, "smallSize": "小", "@smallSize": {"description": "小字体标签"}, "largeSize": "大", "@largeSize": {"description": "大字体标签"}, "currentSizeLabel": "当前大小: {size}", "@currentSizeLabel": {"description": "当前字体大小标签", "placeholders": {"size": {"type": "String"}}}, "smallSizeLabel": "小", "@smallSizeLabel": {"description": "小字体预设标签"}, "mediumSizeLabel": "中", "@mediumSizeLabel": {"description": "中字体预设标签"}, "largeSizeLabel": "大", "@largeSizeLabel": {"description": "大字体预设标签"}, "vipMembershipTitle": "VIP会员特权", "@vipMembershipTitle": {"description": "VIP会员特权对话框标题"}, "higherAccuracy": "准确度更高", "@higherAccuracy": {"description": "更高准确度功能"}, "adFree": "无广告干扰", "@adFree": {"description": "无广告功能"}, "unlimitedUsage": "无限次使用", "@unlimitedUsage": {"description": "无限使用功能"}, "monthlyMembership": "月度会员", "@monthlyMembership": {"description": "月度会员选项"}, "annualMembership": "年度会员", "@annualMembership": {"description": "年度会员选项"}, "savePercentage": "省{percentage}%", "@savePercentage": {"description": "节省百分比", "placeholders": {"percentage": {"type": "String"}}}, "gettingPriceInfo": "正在获取价格信息...", "@gettingPriceInfo": {"description": "获取价格信息中"}, "originalPrice": "原价", "@originalPrice": {"description": "原价字段"}, "approximately": "约", "@approximately": {"description": "大约、约"}, "monthlyUnit": "月", "@monthlyUnit": {"description": "月单位"}, "yearlyUnit": "年", "@yearlyUnit": {"description": "年单位"}, "perMonth": "/月", "@perMonth": {"description": "每月"}, "perYear": "/年", "@perYear": {"description": "每年"}, "activateVipNow": "立即开通VIP会员", "@activateVipNow": {"description": "立即开通VIP按钮"}, "serviceTermsAgreement": "开通即表示同意《用户服务协议》和《隐私政策》", "@serviceTermsAgreement": {"description": "服务条款同意文本"}, "monthlyMemberPackage": "包月会员", "@monthlyMemberPackage": {"description": "包月会员套餐名称"}, "annualMemberPackage": "包年会员", "@annualMemberPackage": {"description": "包年会员套餐名称"}, "oneMonthVipPrivileges": "一个月VIP特权", "@oneMonthVipPrivileges": {"description": "一个月VIP特权描述"}, "oneYearVipPrivileges": "一年VIP特权，支持自动续费", "@oneYearVipPrivileges": {"description": "一年VIP特权描述"}, "priceLoadFailed": "获取价格信息失败，显示默认价格", "@priceLoadFailed": {"description": "价格加载失败提示"}, "priceInfoLoading": "价格信息加载中，请稍候...", "@priceInfoLoading": {"description": "价格信息加载中提示"}, "aboutToActivate": "即将开通{packageName}，请稍候...", "@aboutToActivate": {"description": "即将开通套餐提示", "placeholders": {"packageName": {"type": "String"}}}, "annualVipMember": "年度VIP会员", "@annualVipMember": {"description": "年度VIP会员徽章"}, "monthlyVipMember": "月度VIP会员", "@monthlyVipMember": {"description": "月度VIP会员徽章"}, "lifetimeVipMember": "终身VIP会员", "@lifetimeVipMember": {"description": "终身VIP会员徽章"}, "fontSizeSmall": "小", "@fontSizeSmall": {"description": "小字体档位"}, "fontSizeMedium": "中", "@fontSizeMedium": {"description": "中字体档位"}, "fontSizeLarge": "大", "@fontSizeLarge": {"description": "大字体档位"}, "myOrders": "我的订单", "@myOrders": {"description": "我的订单菜单项"}, "chatHistory": "聊天历史", "@chatHistory": {"description": "聊天历史提示"}, "doctorManagement": "医生管理", "@doctorManagement": {"description": "医生管理菜单项"}, "adminManagement": "管理员", "@adminManagement": {"description": "管理员菜单项"}, "myLikes": "我的点赞", "@myLikes": {"description": "我的点赞按钮"}, "myFavorites": "我的收藏", "@myFavorites": {"description": "我的收藏按钮"}, "shoppingCart": "购物车", "@shoppingCart": {"description": "购物车按钮"}, "distributionManagementFeature": "分销管理", "@distributionManagementFeature": {"description": "分销管理功能名称"}, "adminManagementFeature": "管理员管理", "@adminManagementFeature": {"description": "管理员管理功能名称"}, "doctorManagementFeature": "医生管理", "@doctorManagementFeature": {"description": "医生管理功能名称"}, "onlyDoctorUsersCanAccess": "只有医生用户才能访问医生管理功能", "@onlyDoctorUsersCanAccess": {"description": "医生访问限制消息"}, "viewShoppingCartFeature": "查看购物车", "@viewShoppingCartFeature": {"description": "查看购物车功能名称"}, "pleaseSelectItemsToDelete": "请选择要删除的商品", "@pleaseSelectItemsToDelete": {"description": "选择要删除的商品提示"}, "confirmDelete": "确认删除", "@confirmDelete": {"description": "确认删除对话框标题"}, "confirmDeleteItems": "确定要删除选中的 {count} 个商品吗？", "@confirmDeleteItems": {"description": "确认删除商品消息", "placeholders": {"count": {"type": "int"}}}, "deleteSuccess": "删除成功", "@deleteSuccess": {"description": "删除成功消息"}, "deleteFailed": "删除失败: {error}", "@deleteFailed": {"description": "删除失败消息", "placeholders": {"error": {"type": "String"}}}, "pleaseSelectItemsToCheckout": "请选择要结算的商品", "@pleaseSelectItemsToCheckout": {"description": "选择要结算的商品提示"}, "cartTitle": "购物车", "@cartTitle": {"description": "购物车页面标题"}, "myOrdersTitle": "我的订单", "@myOrdersTitle": {"description": "我的订单页面标题"}, "myOrdersFeature": "我的订单", "@myOrdersFeature": {"description": "我的订单功能名称"}, "orderStatusAll": "全部", "@orderStatusAll": {"description": "全部订单状态标签"}, "orderStatusPending": "待支付", "@orderStatusPending": {"description": "待支付状态"}, "orderStatusPendingShipment": "待发货", "@orderStatusPendingShipment": {"description": "待发货订单状态"}, "orderStatusShipped": "已发货", "@orderStatusShipped": {"description": "已发货订单状态"}, "orderStatusCompleted": "已完成", "@orderStatusCompleted": {"description": "已完成订单状态"}, "orderStatusCancelled": "已取消", "@orderStatusCancelled": {"description": "已取消订单状态"}, "orderStatusUnknown": "未知状态", "@orderStatusUnknown": {"description": "未知订单状态"}, "payStatusUnpaid": "未支付", "@payStatusUnpaid": {"description": "未支付状态"}, "payStatusPaid": "已支付", "@payStatusPaid": {"description": "已支付状态"}, "payStatusRefunded": "已退款", "@payStatusRefunded": {"description": "已退款状态"}, "product": "商品", "@product": {"description": "商品标签"}, "noOrders": "暂无订单", "@noOrders": {"description": "暂无订单消息"}, "adminManagementTitle": "管理员管理", "@adminManagementTitle": {"description": "管理员管理页面标题"}, "doctorManagementTab": "医生管理", "@doctorManagementTab": {"description": "医生管理标签页"}, "productReviewTab": "产品审核", "@productReviewTab": {"description": "产品审核标签页"}, "orderManagementTab": "订单管理", "@orderManagementTab": {"description": "订单管理标签页"}, "noDoctorData": "暂无医生数据", "@noDoctorData": {"description": "暂无医生数据消息"}, "loadDoctorListFailed": "加载医生列表失败", "@loadDoctorListFailed": {"description": "加载医生列表失败消息"}, "addDoctor": "添加医生", "@addDoctor": {"description": "添加医生按钮"}, "editDoctor": "编辑医生", "@editDoctor": {"description": "编辑医生按钮"}, "deleteDoctor": "删除医生", "@deleteDoctor": {"description": "删除医生按钮"}, "confirmDeleteDoctor": "确认删除医生", "@confirmDeleteDoctor": {"description": "确认删除医生对话框标题"}, "deleteDoctorConfirmMessage": "确定要删除医生 {doctor<PERSON><PERSON>} 吗？此操作不可撤销。", "@deleteDoctorConfirmMessage": {"description": "删除医生确认消息", "placeholders": {"doctorName": {"type": "String", "description": "医生姓名"}}}, "deleteDoctorSuccess": "删除医生成功", "@deleteDoctorSuccess": {"description": "删除医生成功消息"}, "deleteDoctorFailed": "删除医生失败", "@deleteDoctorFailed": {"description": "删除医生失败消息"}, "detailedInfo": "详细信息", "@detailedInfo": {"description": "详细信息部分"}, "aiSettings": "AI设置", "@aiSettings": {"description": "AI设置"}, "statusSettings": "状态设置", "@statusSettings": {"description": "状态设置"}, "enterDoctorName": "请输入医生姓名", "@enterDoctorName": {"description": "输入医生姓名提示"}, "specialty": "专科", "@specialty": {"description": "专科标签"}, "enterSpecialty": "请输入专科名称，如：心血管内科", "@enterSpecialty": {"description": "输入专科提示"}, "description": "简介", "@description": {"description": "简介标签"}, "enterDescription": "请输入医生简介", "@enterDescription": {"description": "输入医生简介提示"}, "enterDetailedDescription": "请输入产品的详细描述，包括成分、功效、使用方法等", "@enterDetailedDescription": {"description": "详细描述输入提示"}, "addSpecialty": "添加擅长领域", "@addSpecialty": {"description": "添加擅长领域按钮"}, "enterSpecialtyField": "请输入擅长领域，如：冠心病", "@enterSpecialtyField": {"description": "输入擅长领域提示"}, "deleteSpecialty": "删除此擅长领域", "@deleteSpecialty": {"description": "删除擅长领域提示"}, "systemPrompt": "系统提示词", "@systemPrompt": {"description": "系统提示词"}, "enterSystemPrompt": "请输入AI系统提示词，定义AI医生的行为和回答风格", "@enterSystemPrompt": {"description": "输入系统提示词提示"}, "avatarUrl": "头像URL", "@avatarUrl": {"description": "头像URL"}, "enterAvatarUrl": "请输入头像图片URL或点击上方上传头像", "@enterAvatarUrl": {"description": "输入头像URL提示"}, "llmModelName": "模型名称", "@llmModelName": {"description": "LLM模型名称"}, "enterLlmModelName": "请输入LLM模型名称，如：qwen-max-latest、claude-3-sonnet等", "@enterLlmModelName": {"description": "输入LLM模型名称提示"}, "enterYearsOfExperience": "请输入工作年限（年）", "@enterYearsOfExperience": {"description": "输入工作年限提示"}, "rating": "评分", "@rating": {"description": "评分标签"}, "enterRating": "请输入评分（0.0-5.0）", "@enterRating": {"description": "输入评分提示"}, "digitalHumanUrl": "数字人URL", "@digitalHumanUrl": {"description": "数字人URL"}, "enterDigitalHumanUrl": "请输入数字人URL（可选）", "@enterDigitalHumanUrl": {"description": "输入数字人URL提示"}, "phone": "联系电话", "@phone": {"description": "联系电话标签"}, "enterPhone": "请输入医生联系电话", "@enterPhone": {"description": "输入联系电话提示"}, "enterAddress": "请输入医生工作地址", "@enterAddress": {"description": "输入工作地址提示"}, "isActive": "启用状态", "@isActive": {"description": "启用状态标签"}, "saveDoctor": "保存医生", "@saveDoctor": {"description": "保存医生按钮"}, "saving": "保存中...", "@saving": {"description": "保存中状态"}, "createDoctorSuccess": "创建医生成功", "@createDoctorSuccess": {"description": "创建医生成功消息"}, "updateDoctorSuccess": "更新医生成功", "@updateDoctorSuccess": {"description": "更新医生成功消息"}, "createDoctorFailed": "创建医生失败", "@createDoctorFailed": {"description": "创建医生失败消息"}, "updateDoctorFailed": "更新医生失败", "@updateDoctorFailed": {"description": "更新医生失败消息"}, "enabled": "启用", "@enabled": {"description": "启用状态"}, "disabled": "禁用", "@disabled": {"description": "禁用状态"}, "enterValidPhone": "请输入有效的手机号码", "@enterValidPhone": {"description": "输入有效手机号码提示"}, "doctorAvatar": "医生头像", "@doctorAvatar": {"description": "医生头像"}, "uploadAvatar": "上传头像", "@uploadAvatar": {"description": "上传头像按钮"}, "enableStatus": "启用状态", "@enableStatus": {"description": "启用状态"}, "doctorEnabledDescription": "医生当前处于启用状态，用户可以与其对话", "@doctorEnabledDescription": {"description": "医生启用状态描述"}, "doctorDisabledDescription": "医生当前处于禁用状态，用户无法与其对话", "@doctorDisabledDescription": {"description": "医生禁用状态描述"}, "specialtyInputHint": "提示：每个输入框填写一个擅长领域，保存时将自动合并", "@specialtyInputHint": {"description": "擅长领域输入提示"}, "confirmExit": "确认退出", "@confirmExit": {"description": "确认退出对话框标题"}, "unsavedChangesWarning": "您有未保存的更改，确定要退出吗？", "@unsavedChangesWarning": {"description": "未保存更改警告"}, "exit": "退出", "@exit": {"description": "退出按钮"}, "uploadFailed": "上传失败", "@uploadFailed": {"description": "上传失败消息"}, "supportedImageFormats": "支持 JPG、PNG 格式，大小不超过 5MB", "@supportedImageFormats": {"description": "支持的图片格式说明"}, "collapse": "收起", "@collapse": {"description": "收起按钮"}, "multilingual": "多语言", "@multilingual": {"description": "多语言按钮"}, "all": "全部", "@all": {"description": "全部选项"}, "pending": "待审核", "@pending": {"description": "待审核状态"}, "approved": "已通过", "@approved": {"description": "已通过状态"}, "rejected": "已拒绝", "@rejected": {"description": "已拒绝状态"}, "offline": "已下架", "@offline": {"description": "已下架状态"}, "loadDataFailed": "加载数据失败: {error}", "@loadDataFailed": {"description": "加载数据失败消息", "placeholders": {"error": {"type": "String"}}}, "loadDoctorMultilingualDataFailed": "加载医生多语言数据失败", "@loadDoctorMultilingualDataFailed": {"description": "加载医生多语言数据失败消息"}, "doctorCreationCompleteCallback": "医生创建完成回调", "@doctorCreationCompleteCallback": {"description": "医生创建完成回调消息"}, "enterModelName": "请输入模型名称", "@enterModelName": {"description": "输入模型名称验证消息"}, "imageSizeExceedsLimit": "图片大小不能超过5MB", "@imageSizeExceedsLimit": {"description": "图片大小超出限制消息"}, "doctorManagementTitle": "医生管理", "@doctorManagementTitle": {"description": "医生管理页面标题"}, "productManagementTab": "产品管理", "@productManagementTab": {"description": "产品管理标签页"}, "dataOverview": "数据概览", "@dataOverview": {"description": "数据概览标题"}, "products": "产品", "@products": {"description": "产品标签"}, "pendingReview": "待审核", "@pendingReview": {"description": "待审核状态"}, "totalProducts": "总产品", "@totalProducts": {"description": "总产品数量"}, "totalSales": "总销售额", "@totalSales": {"description": "总销售额"}, "totalOrders": "总订单", "@totalOrders": {"description": "总订单数量"}, "approvedProducts": "已通过", "@approvedProducts": {"description": "已通过产品数量"}, "rejectedProducts": "已拒绝", "@rejectedProducts": {"description": "已拒绝产品数量"}, "reviewStatus": "审核状态", "@reviewStatus": {"description": "审核状态标签"}, "inventory": "库存", "@inventory": {"description": "库存标签"}, "salesVolume": "销量", "@salesVolume": {"description": "销量标签"}, "orderOverview": "订单概览", "@orderOverview": {"description": "订单概览标题"}, "shippingStatus": "物流状态", "@shippingStatus": {"description": "物流状态标签"}, "paymentStatus": "支付状态", "@paymentStatus": {"description": "支付状态标签"}, "totalOrderNumber": "总计订单号", "@totalOrderNumber": {"description": "总计订单号标签"}, "customer": "客户", "@customer": {"description": "客户标签"}, "pendingPayment": "待支付", "@pendingPayment": {"description": "待支付状态"}, "pendingShipment": "待发货", "@pendingShipment": {"description": "待发货状态"}, "shipped": "已发货", "@shipped": {"description": "已发货状态"}, "completed": "已完成", "@completed": {"description": "已完成状态"}, "cancelled": "已取消", "@cancelled": {"description": "已取消状态"}, "reviewApproved": "审核通过", "@reviewApproved": {"description": "审核通过备注"}, "reviewRejected": "审核拒绝", "@reviewRejected": {"description": "审核拒绝状态"}, "offShelf": "下架", "@offShelf": {"description": "下架状态"}, "addProduct": "添加产品", "@addProduct": {"description": "添加产品标题"}, "editProduct": "编辑产品", "@editProduct": {"description": "编辑产品标题"}, "enterProductName": "请输入产品名称", "@enterProductName": {"description": "产品名称输入提示"}, "productDescription": "产品描述", "@productDescription": {"description": "产品描述标题"}, "enterProductDescription": "请输入产品简介", "@enterProductDescription": {"description": "产品简介输入提示"}, "productCategory": "产品分类", "@productCategory": {"description": "产品分类字段"}, "enterProductCategory": "请输入产品分类，如：保健品、药物等", "@enterProductCategory": {"description": "产品分类输入提示"}, "enterManufacturer": "请输入制造商名称", "@enterManufacturer": {"description": "制造商输入提示"}, "productMainImage": "产品主图", "@productMainImage": {"description": "产品主图字段"}, "priceInfo": "价格信息", "@priceInfo": {"description": "价格信息部分"}, "currentPrice": "现价", "@currentPrice": {"description": "现价字段"}, "enterCurrentPrice": "请输入现价", "@enterCurrentPrice": {"description": "现价输入提示"}, "enterOriginalPrice": "请输入原价（可选）", "@enterOriginalPrice": {"description": "原价输入提示"}, "inventoryInfo": "库存信息", "@inventoryInfo": {"description": "库存信息部分"}, "inventoryCount": "库存数量", "@inventoryCount": {"description": "库存数量字段"}, "enterInventoryCount": "请输入库存数量", "@enterInventoryCount": {"description": "库存数量输入提示"}, "productDetailImages": "产品详情图", "@productDetailImages": {"description": "产品详情图标签"}, "productCreatedSuccess": "产品创建成功", "@productCreatedSuccess": {"description": "产品创建成功消息"}, "productUpdatedSuccess": "产品更新成功", "@productUpdatedSuccess": {"description": "产品更新成功消息"}, "loadProductMultilingualDataFailed": "加载产品多语言数据失败", "@loadProductMultilingualDataFailed": {"description": "加载产品多语言数据失败消息"}, "orderNumber": "订单号", "@orderNumber": {"description": "订单号标签"}, "orderTotal": "总计", "@orderTotal": {"description": "订单总计标签"}, "customerLabel": "客户: {customer}", "@customerLabel": {"description": "订单项中的客户标签", "placeholders": {"customer": {"type": "String"}}}, "orderNumberShort": "订单号", "@orderNumberShort": {"description": "简短订单号标签"}, "paidStatus": "已支付", "@paidStatus": {"description": "已支付状态"}, "unpaidStatus": "未支付", "@unpaidStatus": {"description": "未支付状态"}, "trackingNumber": "快递单号", "@trackingNumber": {"description": "快递单号标签"}, "networkImage": "网络图片", "@networkImage": {"description": "网络图片标签"}, "maxSixImages": "最多6张", "@maxSixImages": {"description": "最多6张图片提示"}, "addImage": "添加图片", "@addImage": {"description": "添加图片按钮"}, "expressInfo": "快递信息", "@expressInfo": {"description": "快递信息标题"}, "expressNumber": "快递单号", "@expressNumber": {"description": "快递单号标签"}, "expressCompany": "快递公司", "@expressCompany": {"description": "快递公司标签"}, "shippingNote": "发货备注", "@shippingNote": {"description": "发货备注标签"}, "noShippingInfo": "暂无物流信息", "@noShippingInfo": {"description": "暂无物流信息消息"}, "orderCount": "{count}单", "@orderCount": {"description": "订单数量带数字", "placeholders": {"count": {"type": "int"}}}, "imageSelected": "已选择图片", "@imageSelected": {"description": "已选择图片状态"}, "unsavedChangesMessage": "您有未保存的更改，确定要退出吗？", "@unsavedChangesMessage": {"description": "未保存更改确认消息"}, "confirmAction": "确定", "@confirmAction": {"description": "确定操作按钮"}, "noProductsMessage": "暂无产品", "@noProductsMessage": {"description": "暂无产品空状态消息"}, "addFirstProductHint": "点击右下角按钮添加您的第一个产品", "@addFirstProductHint": {"description": "添加第一个产品提示消息"}, "noOrdersMessage": "订单数据会显示在这里", "@noOrdersMessage": {"description": "暂无订单消息"}, "ordersWillShowHere": "客户购买您的产品后订单会显示在这里", "@ordersWillShowHere": {"description": "订单将显示在这里提示消息"}, "reviewSuccess": "审核成功", "@reviewSuccess": {"description": "审核成功消息"}, "reviewFailed": "审核失败", "@reviewFailed": {"description": "审核失败消息"}, "batchReviewSuccess": "批量审核成功", "@batchReviewSuccess": {"description": "批量审核成功消息"}, "batchReviewFailed": "批量审核失败", "@batchReviewFailed": {"description": "批量审核失败消息"}, "allDoctors": "全部医生", "@allDoctors": {"description": "全部医生选项"}, "pleaseSelectProducts": "请选择要审核的产品", "@pleaseSelectProducts": {"description": "请选择产品提示"}, "batchApproved": "批量审核通过", "@batchApproved": {"description": "批量审核通过备注"}, "batchRejected": "批量审核拒绝", "@batchRejected": {"description": "批量审核拒绝备注"}, "batchApprove": "批量通过", "@batchApprove": {"description": "批量通过按钮"}, "batchReject": "批量拒绝", "@batchReject": {"description": "批量拒绝按钮"}, "deselectAll": "取消全选", "@deselectAll": {"description": "取消全选按钮"}, "exitSelection": "退出选择", "@exitSelection": {"description": "退出选择按钮"}, "batchSelection": "批量选择", "@batchSelection": {"description": "批量选择按钮"}, "reviewStatistics": "审核统计", "@reviewStatistics": {"description": "审核统计标题"}, "total": "总数", "@total": {"description": "总数标签"}, "sales": "销量", "@sales": {"description": "销量标签"}, "approve": "通过", "@approve": {"description": "通过按钮"}, "reject": "拒绝", "@reject": {"description": "拒绝按钮"}, "rejectReview": "拒绝审核", "@rejectReview": {"description": "拒绝审核标题"}, "confirmRejectProduct": "确定要拒绝产品 \"{productName}\" 吗？", "@confirmRejectProduct": {"description": "确认拒绝产品提示", "placeholders": {"productName": {"type": "String"}}}, "rejectReason": "拒绝原因（可选）", "@rejectReason": {"description": "拒绝原因输入提示"}, "confirmReject": "确定拒绝", "@confirmReject": {"description": "确定拒绝按钮"}, "filterDoctors": "筛选医生", "@filterDoctors": {"description": "筛选医生标签"}, "loadProductDetailFailed": "加载产品详情失败", "@loadProductDetailFailed": {"description": "加载产品详情失败消息"}, "unknownDoctor": "未知医生", "@unknownDoctor": {"description": "未知医生姓名"}, "createdAt": "创建时间", "@createdAt": {"description": "创建时间标签"}, "updatedAt": "更新时间", "@updatedAt": {"description": "更新时间标签"}, "productImages": "产品图片", "@productImages": {"description": "产品图片标题"}, "productSpecifications": "产品规格", "@productSpecifications": {"description": "产品规格标题"}, "reviewInfo": "审核信息", "@reviewInfo": {"description": "审核信息标题"}, "productId": "产品ID", "@productId": {"description": "产品ID标签"}, "viewShipping": "查看物流", "@viewShipping": {"description": "查看物流按钮"}, "cancelOrder": "取消订单", "@cancelOrder": {"description": "取消订单按钮"}, "payNow": "立即支付", "@payNow": {"description": "立即支付按钮"}, "confirmCancel": "确认取消", "@confirmCancel": {"description": "确认取消对话框标题"}, "confirmCancelOrder": "确定要取消这个订单吗？", "@confirmCancelOrder": {"description": "确认取消订单消息"}, "orderCancelled": "订单已取消", "@orderCancelled": {"description": "订单已取消消息"}, "cancelOrderFailed": "取消订单失败：{error}", "@cancelOrderFailed": {"description": "取消订单失败消息", "placeholders": {"error": {"type": "String"}}}, "getPaymentParamsFailed": "获取支付参数失败", "@getPaymentParamsFailed": {"description": "获取支付参数失败消息"}, "paymentCancelled": "支付已取消", "@paymentCancelled": {"description": "支付已取消消息"}, "confirmPayment": "确认支付", "@confirmPayment": {"description": "确认支付对话框标题"}, "paymentAmount": "支付金额", "@paymentAmount": {"description": "支付金额标签"}, "confirmPaymentButton": "确认支付", "@confirmPaymentButton": {"description": "确认支付按钮"}, "orderPaidSuccessfully": "您的订单已支付成功", "@orderPaidSuccessfully": {"description": "订单支付成功消息"}, "currentConversationDeleted": "当前对话已被删除", "@currentConversationDeleted": {"description": "当前对话已删除消息"}, "newConversation": "新对话", "@newConversation": {"description": "新对话标题"}, "refreshSuccess": "刷新成功", "@refreshSuccess": {"description": "刷新成功消息"}, "refreshFailed": "刷新失败: {error}", "@refreshFailed": {"description": "刷新失败消息", "placeholders": {"error": {"type": "String"}}}, "titleUpdateSuccess": "标题更新成功", "@titleUpdateSuccess": {"description": "标题更新成功提示"}, "titleUpdateFailed": "标题更新失败: {error}", "@titleUpdateFailed": {"description": "标题更新失败提示", "placeholders": {"error": {"type": "String", "description": "错误信息"}}}, "conversationNotFound": "对话不存在", "@conversationNotFound": {"description": "对话不存在错误"}, "chatHistoryPageTitle": "聊天历史", "@chatHistoryPageTitle": {"description": "聊天历史页面标题"}, "noChatRecordsForDate": "该日期暂无聊天记录", "@noChatRecordsForDate": {"description": "该日期无聊天记录提示"}, "enterNewTitle": "请输入新的标题", "@enterNewTitle": {"description": "输入新标题提示"}, "year": "年", "@year": {"description": "年份单位"}, "month": "月", "@month": {"description": "月份单位"}, "monthLabel": "月份：", "@monthLabel": {"description": "月份标签"}, "yearLabel": "年份：", "@yearLabel": {"description": "年份标签"}, "weekdayMon": "一", "@weekdayMon": {"description": "星期一"}, "weekdayTue": "二", "@weekdayTue": {"description": "星期二"}, "weekdayWed": "三", "@weekdayWed": {"description": "星期三"}, "weekdayThu": "四", "@weekdayThu": {"description": "星期四"}, "weekdayFri": "五", "@weekdayFri": {"description": "星期五"}, "weekdaySat": "六", "@weekdaySat": {"description": "星期六"}, "weekdaySun": "日", "@weekdaySun": {"description": "星期日"}, "selectYearMonth": "选择年月", "@selectYearMonth": {"description": "选择年月对话框标题"}, "ok": "确定", "@ok": {"description": "确定按钮"}, "digitalHumanChatInDevelopment": "数字人AI聊天页面开发中...", "@digitalHumanChatInDevelopment": {"description": "数字人聊天开发中消息"}, "voiceTranslationFeature": "语音翻译", "@voiceTranslationFeature": {"description": "语音翻译功能名称"}, "chatFeature": "聊天功能", "@chatFeature": {"description": "聊天功能名称"}, "voiceFeature": "语音功能", "@voiceFeature": {"description": "语音功能名称"}, "chatHistoryFeature": "聊天历史", "@chatHistoryFeature": {"description": "聊天历史功能名称"}, "voiceRecognitionSuccess": "语音识别成功", "@voiceRecognitionSuccess": {"description": "语音识别成功消息"}, "voiceRecognitionFailed": "语音识别失败，请重试", "@voiceRecognitionFailed": {"description": "语音识别失败消息"}, "addTextDescriptionOrSendImage": "添加文字描述或直接发送图片", "@addTextDescriptionOrSendImage": {"description": "图片预览提示文字"}, "refresh": "刷新", "@refresh": {"description": "刷新提示"}, "noChatRecords": "暂无聊天记录", "@noChatRecords": {"description": "暂无聊天记录消息"}, "filterDoctorsLabel": "筛选医生", "@filterDoctorsLabel": {"description": "筛选医生下拉框标签"}, "allDoctorsOption": "全部医生", "@allDoctorsOption": {"description": "全部医生筛选选项"}, "unknownDoctorLabel": "未知医生", "@unknownDoctorLabel": {"description": "订单列表中的未知医生标签"}, "quantityLabel": "数量: {quantity}", "@quantityLabel": {"description": "订单项中的数量标签", "placeholders": {"quantity": {"type": "int"}}}, "doctorLabel": "医生: {doctor}", "@doctorLabel": {"description": "订单项中的医生标签", "placeholders": {"doctor": {"type": "String"}}}, "unitPriceLabel": "单价: ¥{price}", "@unitPriceLabel": {"description": "订单项中的单价标签", "placeholders": {"price": {"type": "String"}}}, "totalAmountLabel": "总计: ¥{amount}", "@totalAmountLabel": {"description": "订单项中的总金额标签", "placeholders": {"amount": {"type": "String"}}}, "orderNumberLabel": "订单号: {orderNumber}", "@orderNumberLabel": {"description": "订单项中的订单号标签", "placeholders": {"orderNumber": {"type": "String"}}}, "adminShipAction": "管理员发货", "@adminShipAction": {"description": "管理员发货操作"}, "markCompleteAction": "标记完成", "@markCompleteAction": {"description": "订单菜单中的标记完成操作"}, "markCancelAction": "标记取消", "@markCancelAction": {"description": "订单菜单中的标记取消操作"}, "deleteOrderAction": "删除订单", "@deleteOrderAction": {"description": "删除订单操作"}, "totalOrdersLabel": "总订单", "@totalOrdersLabel": {"description": "统计信息中的总订单标签"}, "totalSalesLabel": "总销售额", "@totalSalesLabel": {"description": "统计信息中的总销售额标签"}, "completedOrdersLabel": "已完成", "@completedOrdersLabel": {"description": "统计信息中的已完成订单标签"}, "pendingPaymentLabel": "待支付", "@pendingPaymentLabel": {"description": "统计信息中的待支付标签"}, "pendingShipmentLabel": "待发货", "@pendingShipmentLabel": {"description": "统计信息中的待发货标签"}, "cancelledOrdersLabel": "已取消", "@cancelledOrdersLabel": {"description": "统计信息中的已取消订单标签"}, "ordersUnit": "单", "@ordersUnit": {"description": "订单单位后缀"}, "orderStatusUpdateSuccess": "订单状态更新成功", "@orderStatusUpdateSuccess": {"description": "订单状态更新成功消息"}, "updateFailed": "更新失败: {error}", "@updateFailed": {"description": "更新失败消息", "placeholders": {"error": {"type": "String"}}}, "noOrdersTitle": "暂无订单", "@noOrdersTitle": {"description": "暂无订单标题"}, "batchOperationSuccess": "批量操作成功", "@batchOperationSuccess": {"description": "批量操作成功消息"}, "batchOperationFailed": "批量操作失败: {error}", "@batchOperationFailed": {"description": "批量操作失败消息", "placeholders": {"error": {"type": "String"}}}, "confirmDeleteTitle": "确认删除", "@confirmDeleteTitle": {"description": "确认删除对话框标题"}, "confirmDeleteMessage": "确定要删除这个订单吗？此操作不可撤销。", "@confirmDeleteMessage": {"description": "确认删除对话框消息"}, "cancelAction": "取消", "@cancelAction": {"description": "取消操作按钮"}, "deleteAction": "删除", "@deleteAction": {"description": "删除操作按钮"}, "batchOperationsTitle": "批量操作 ({count}个订单)", "@batchOperationsTitle": {"description": "批量操作对话框标题", "placeholders": {"count": {"type": "int"}}}, "markAsCompletedAction": "标记为已完成", "@markAsCompletedAction": {"description": "标记为已完成操作"}, "markAsCancelledAction": "标记为已取消", "@markAsCancelledAction": {"description": "标记为已取消操作"}, "markAsShippedAction": "标记为已发货", "@markAsShippedAction": {"description": "标记为已发货操作"}, "ordersUnitSuffix": "单", "@ordersUnitSuffix": {"description": "统计信息中的订单单位后缀"}, "orderStatusUpdateSuccessMessage": "订单状态更新成功", "@orderStatusUpdateSuccessMessage": {"description": "订单状态更新成功消息"}, "paymentStatusUpdateSuccessMessage": "支付状态更新成功", "@paymentStatusUpdateSuccessMessage": {"description": "支付状态更新成功消息"}, "orderDeleteSuccessMessage": "订单删除成功", "@orderDeleteSuccessMessage": {"description": "订单删除成功消息"}, "pleaseSelectOrdersMessage": "请先选择要操作的订单", "@pleaseSelectOrdersMessage": {"description": "请先选择订单消息"}, "markAsPaidAction": "标记已支付", "@markAsPaidAction": {"description": "标记已支付操作"}, "orderStatusPendingPayment": "待支付", "@orderStatusPendingPayment": {"description": "待支付订单状态"}, "orderStatusPaid": "已支付", "@orderStatusPaid": {"description": "已支付订单状态"}, "orderDetailTitle": "订单详情", "@orderDetailTitle": {"description": "订单详情页面标题"}, "markAsRefundAction": "标记退款", "@markAsRefundAction": {"description": "标记退款操作"}, "markAsCompleteAction": "标记完成", "@markAsCompleteAction": {"description": "标记完成操作"}, "markAsCancelAction": "标记取消", "@markAsCancelAction": {"description": "标记取消操作"}, "waitingForPaymentDescription": "等待客户完成支付", "@waitingForPaymentDescription": {"description": "等待支付描述"}, "waitingForShipmentDescription": "等待医生发货", "@waitingForShipmentDescription": {"description": "等待发货描述"}, "shippedDescription": "商品已发货，等待客户收货", "@shippedDescription": {"description": "已发货描述"}, "orderCompletedDescription": "订单已完成", "@orderCompletedDescription": {"description": "订单完成描述"}, "orderCancelledDescription": "订单已取消", "@orderCancelledDescription": {"description": "订单取消描述"}, "orderStatusAbnormalDescription": "订单状态异常", "@orderStatusAbnormalDescription": {"description": "订单状态异常描述"}, "orderStatusPendingDescription": "请尽快完成支付，超时订单将自动取消", "@orderStatusPendingDescription": {"description": "待支付订单状态描述"}, "orderStatusPreparingDescription": "您的订单正在准备中，请耐心等待", "@orderStatusPreparingDescription": {"description": "订单准备中状态描述"}, "orderStatusShippedUserDescription": "商品已发货，请注意查收", "@orderStatusShippedUserDescription": {"description": "用户端已发货状态描述"}, "orderStatusCompletedUserDescription": "订单已完成，感谢您的购买", "@orderStatusCompletedUserDescription": {"description": "用户端订单完成状态描述"}, "orderStatusCancelledUserDescription": "订单已取消", "@orderStatusCancelledUserDescription": {"description": "用户端订单取消状态描述"}, "shippingStatusWaitingReceive": "已发货，等待收货", "@shippingStatusWaitingReceive": {"description": "等待收货状态"}, "shippingStatusCompleted": "已完成", "@shippingStatusCompleted": {"description": "物流完成状态"}, "shippingStatusShipped": "已发货", "@shippingStatusShipped": {"description": "已发货状态"}, "shippingStatusPending": "待支付", "@shippingStatusPending": {"description": "物流待支付状态"}, "shippingStatusWaitingShip": "待发货", "@shippingStatusWaitingShip": {"description": "物流待发货状态"}, "shippingStatusCancelled": "已取消", "@shippingStatusCancelled": {"description": "物流已取消状态"}, "shippingStatusUnknown": "未知状态", "@shippingStatusUnknown": {"description": "物流未知状态"}, "insufficientPermissionDoctorRequired": "权限不足，需要医生权限", "@insufficientPermissionDoctorRequired": {"description": "权限不足需要医生权限错误消息"}, "getPendingShipmentOrdersFailed": "获取待发货订单失败", "@getPendingShipmentOrdersFailed": {"description": "获取待发货订单失败错误消息"}, "trackingNumberCannotBeEmpty": "快递单号不能为空", "@trackingNumberCannotBeEmpty": {"description": "快递单号不能为空错误消息"}, "shipmentFailed": "发货失败", "@shipmentFailed": {"description": "发货失败错误消息"}, "orderNotExistOrNoAccess": "订单不存在或无权访问", "@orderNotExistOrNoAccess": {"description": "订单不存在或无权访问错误消息"}, "shipmentFailedCheckOrderStatus": "发货失败，请检查订单状态", "@shipmentFailedCheckOrderStatus": {"description": "发货失败请检查订单状态错误消息"}, "getShippingStatusFailed": "获取物流状态失败", "@getShippingStatusFailed": {"description": "获取物流状态失败错误消息"}, "getShippedOrdersFailed": "获取已发货订单失败", "@getShippedOrdersFailed": {"description": "获取已发货订单失败错误消息"}, "productInfoTitle": "商品信息", "@productInfoTitle": {"description": "商品信息标题"}, "orderInfoTitle": "订单信息", "@orderInfoTitle": {"description": "订单信息标题"}, "orderNumberFieldLabel": "订单号", "@orderNumberFieldLabel": {"description": "订单号字段标签"}, "orderTimeLabel": "下单时间", "@orderTimeLabel": {"description": "下单时间标签"}, "paymentTimeLabel": "支付时间", "@paymentTimeLabel": {"description": "支付时间标签"}, "shipmentTimeLabel": "发货时间", "@shipmentTimeLabel": {"description": "发货时间标签"}, "completionTimeLabel": "完成时间", "@completionTimeLabel": {"description": "完成时间标签"}, "customerInfoTitle": "客户信息", "@customerInfoTitle": {"description": "客户信息标题"}, "customerNicknameLabel": "客户昵称", "@customerNicknameLabel": {"description": "客户昵称标签"}, "userIdLabel": "用户ID", "@userIdLabel": {"description": "用户ID标签"}, "shippingInfoTitle": "收货信息", "@shippingInfoTitle": {"description": "收货信息标题"}, "recipientLabel": "收货人", "@recipientLabel": {"description": "收货人标签"}, "contactPhoneLabel": "联系电话", "@contactPhoneLabel": {"description": "联系电话标签"}, "shippingAddressLabel": "收货地址", "@shippingAddressLabel": {"description": "收货地址标签"}, "trackingInfoTitle": "物流信息", "@trackingInfoTitle": {"description": "物流信息标题"}, "viewDetailsAction": "查看详情", "@viewDetailsAction": {"description": "查看详情操作"}, "trackingNumberLabel": "快递单号", "@trackingNumberLabel": {"description": "快递单号标签"}, "shippingCompanyLabel": "快递公司", "@shippingCompanyLabel": {"description": "快递公司标签"}, "shippingNoteLabel": "发货备注", "@shippingNoteLabel": {"description": "发货备注标签"}, "priceDetailsTitle": "费用明细", "@priceDetailsTitle": {"description": "费用明细标题"}, "productAmountLabel": "商品金额", "@productAmountLabel": {"description": "商品金额标签"}, "shippingFeeLabel": "运费", "@shippingFeeLabel": {"description": "运费标签"}, "totalPaidLabel": "实付金额", "@totalPaidLabel": {"description": "实付金额标签"}, "cancelOrderAction": "取消订单", "@cancelOrderAction": {"description": "取消订单操作"}, "viewTrackingAction": "查看物流", "@viewTrackingAction": {"description": "查看物流操作"}, "copiedToClipboardMessage": "已复制到剪贴板", "@copiedToClipboardMessage": {"description": "已复制到剪贴板消息"}, "confirmDeleteOrderTitle": "确认删除", "@confirmDeleteOrderTitle": {"description": "确认删除订单标题"}, "confirmDeleteOrderMessage": "确定要删除这个订单吗？此操作不可撤销。", "@confirmDeleteOrderMessage": {"description": "确认删除订单消息"}, "orderDetailLoadFailedMessage": "加载订单详情失败", "@orderDetailLoadFailedMessage": {"description": "订单详情加载失败消息"}, "orderInfoLoadFailedMessage": "订单信息加载失败", "@orderInfoLoadFailedMessage": {"description": "订单信息加载失败消息"}, "updateFailedMessage": "更新失败: {error}", "@updateFailedMessage": {"description": "更新失败消息", "placeholders": {"error": {"type": "String"}}}, "deleteFailedMessage": "删除失败: {error}", "@deleteFailedMessage": {"description": "删除失败消息", "placeholders": {"error": {"type": "String"}}}, "editConversationTitle": "编辑对话标题", "@editConversationTitle": {"description": "编辑对话标题对话框标题"}, "replying": "回复中...", "@replying": {"description": "回复中状态文字"}, "imageLoadFailed": "图片加载失败", "@imageLoadFailed": {"description": "图片加载失败消息"}, "imageNotAvailable": "图片不可用", "@imageNotAvailable": {"description": "图片不可用消息"}, "releaseToCancel": "松开取消录音", "@releaseToCancel": {"description": "松开取消录音文字"}, "recording": "正在录音", "@recording": {"description": "录音状态文字"}, "slideUpToCancel": "上滑取消", "@slideUpToCancel": {"description": "上滑取消提示"}, "continueSlideUpToCancel": "继续向上滑动取消录音", "@continueSlideUpToCancel": {"description": "继续上滑取消提示"}, "takePhotoAndSend": "拍照发送", "@takePhotoAndSend": {"description": "拍照发送页面标题"}, "selectSendArea": "选择发送区域", "@selectSendArea": {"description": "选择发送区域页面标题"}, "send": "发送", "@send": {"description": "发送按钮"}, "orderTimeline": "订单时间轴", "@orderTimeline": {"description": "订单时间轴标题"}, "orderCreated": "订单创建", "@orderCreated": {"description": "订单创建状态"}, "paymentCompleted": "支付完成", "@paymentCompleted": {"description": "支付完成状态"}, "goodsShipped": "商品发货", "@goodsShipped": {"description": "商品发货状态"}, "orderCompleted": "订单完成", "@orderCompleted": {"description": "订单完成状态"}, "shipOrder": "订单发货", "@shipOrder": {"description": "订单发货对话框标题"}, "confirmShip": "确认发货", "@confirmShip": {"description": "确认发货按钮"}, "ship": "发货", "@ship": {"description": "发货按钮"}, "enterTrackingNumber": "请输入快递单号", "@enterTrackingNumber": {"description": "输入快递单号提示"}, "trackingNumberRequired": "请输入快递单号", "@trackingNumberRequired": {"description": "快递单号必填验证"}, "selectShippingCompany": "请选择快递公司（可选）", "@selectShippingCompany": {"description": "选择快递公司提示"}, "enterShippingNote": "请输入发货备注（可选）", "@enterShippingNote": {"description": "输入发货备注提示"}, "shipSuccess": "发货成功", "@shipSuccess": {"description": "发货成功消息"}, "shipFailed": "发货失败: {error}", "@shipFailed": {"description": "发货失败消息", "placeholders": {"error": {"type": "String"}}}, "productLabel": "产品", "@productLabel": {"description": "产品标签"}, "adminShipment": "管理员发货", "@adminShipment": {"description": "管理员发货标题"}, "trackingNumberRequiredField": "快递单号 *", "@trackingNumberRequiredField": {"description": "快递单号必填字段标签"}, "shippingCompanyHint": "如：顺丰速运、圆通快递等", "@shippingCompanyHint": {"description": "快递公司输入提示"}, "shippingNoteHint": "可填写发货说明或注意事项", "@shippingNoteHint": {"description": "发货备注输入提示"}, "shipmentSuccess": "发货成功", "@shipmentSuccess": {"description": "发货成功消息"}, "shipmentFailedWithError": "发货失败: {error}", "@shipmentFailedWithError": {"description": "发货失败错误消息", "placeholders": {"error": {"type": "String", "description": "错误信息"}}}, "copiedToClipboardWithTitle": "{title}已复制到剪贴板", "@copiedToClipboardWithTitle": {"description": "带标题的复制成功提示", "placeholders": {"title": {"type": "String", "description": "复制内容的标题"}}}, "voiceRecognizing": "正在识别语音...", "@voiceRecognizing": {"description": "语音识别中提示"}, "voiceRecognitionRetry": "语音识别失败，请重试", "@voiceRecognitionRetry": {"description": "语音识别失败重试提示"}, "cannotOpenPhoneAppCopied": "无法打开电话应用，号码已复制到剪贴板", "@cannotOpenPhoneAppCopied": {"description": "无法打开电话应用时的提示"}, "operationFailedManualDialWithNumber": "操作失败，请手动拨打：{phoneNumber}", "@operationFailedManualDialWithNumber": {"description": "操作失败手动拨打提示", "placeholders": {"phoneNumber": {"type": "String", "description": "电话号码"}}}, "healthAssistantVoiceRecognition": "健康助手语音识别: {text}", "@healthAssistantVoiceRecognition": {"description": "健康助手语音识别结果", "placeholders": {"text": {"type": "String", "description": "识别的文本"}}}, "healthAssistantVoiceProcessingFailed": "健康助手语音处理失败: {error}", "@healthAssistantVoiceProcessingFailed": {"description": "健康助手语音处理失败", "placeholders": {"error": {"type": "String", "description": "错误信息"}}}, "loginFailedWithMessage": "登录失败: {message}", "@loginFailedWithMessage": {"description": "登录失败消息", "placeholders": {"message": {"type": "String", "description": "错误消息"}}}, "verificationCodeIncorrectOrExpired": "验证码错误或已过期", "@verificationCodeIncorrectOrExpired": {"description": "验证码错误或已过期"}, "usernameAlreadyExists": "用户名已被注册，请更换用户名", "@usernameAlreadyExists": {"description": "用户名已存在"}, "dpiAdaptationSettings": "DPI适配设置", "@dpiAdaptationSettings": {"description": "DPI适配设置页面标题"}, "dpiAdaptationDescription": "调整应用的显示缩放比例以适应不同的屏幕密度", "@dpiAdaptationDescription": {"description": "DPI适配设置页面描述"}, "currentDpiScale": "当前缩放比例", "@currentDpiScale": {"description": "当前DPI缩放比例"}, "systemDefault": "系统默认", "@systemDefault": {"description": "系统默认选项"}, "small": "小", "@small": {"description": "小尺寸选项"}, "normal": "正常", "@normal": {"description": "正常尺寸选项"}, "large": "大", "@large": {"description": "大尺寸选项"}, "extraLarge": "超大", "@extraLarge": {"description": "超大尺寸选项"}, "previewText": "预览文本", "@previewText": {"description": "预览文本标签"}, "sampleText": "这是一段示例文本，用于预览当前的缩放效果。", "@sampleText": {"description": "示例预览文本"}, "applyChanges": "应用更改", "@applyChanges": {"description": "应用更改按钮"}, "resetToDefault": "重置为默认", "@resetToDefault": {"description": "重置为默认按钮"}, "dpiSettingsApplied": "DPI设置已应用", "@dpiSettingsApplied": {"description": "DPI设置应用成功提示"}, "dpiSettingsReset": "DPI设置已重置为默认", "@dpiSettingsReset": {"description": "DPI设置重置成功提示"}, "dpiModeAuto": "自动适配", "@dpiModeAuto": {"description": "DPI自动适配模式"}, "dpiModeAutoDesc": "根据设备DPI自动调整界面大小（推荐）", "@dpiModeAutoDesc": {"description": "DPI自动适配模式描述"}, "dpiModeSmall": "紧凑模式", "@dpiModeSmall": {"description": "DPI紧凑模式"}, "dpiModeSmallDesc": "较小的界面元素，适合高DPI设备", "@dpiModeSmallDesc": {"description": "DPI紧凑模式描述"}, "dpiModeStandard": "标准模式", "@dpiModeStandard": {"description": "DPI标准模式"}, "dpiModeStandardDesc": "默认大小的界面元素", "@dpiModeStandardDesc": {"description": "DPI标准模式描述"}, "dpiModeLarge": "宽松模式", "@dpiModeLarge": {"description": "DPI宽松模式"}, "dpiModeLargeDesc": "较大的界面元素，适合低DPI设备", "@dpiModeLargeDesc": {"description": "DPI宽松模式描述"}, "currentStatus": "当前状态", "@currentStatus": {"description": "当前状态标题"}, "adaptationMode": "适配模式", "@adaptationMode": {"description": "适配模式标签"}, "scaleFactor": "缩放因子", "@scaleFactor": {"description": "缩放因子标签"}, "deviceInfo": "设备信息", "@deviceInfo": {"description": "设备信息标题"}, "screenSize": "屏幕尺寸", "@screenSize": {"description": "屏幕尺寸标签"}, "devicePixelRatio": "设备像素比", "@devicePixelRatio": {"description": "设备像素比标签"}, "screenDiagonal": "屏幕对角线", "@screenDiagonal": {"description": "屏幕对角线标签"}, "autoScaleFactor": "自动缩放因子", "@autoScaleFactor": {"description": "自动缩放因子标签"}, "effectPreview": "效果预览", "@effectPreview": {"description": "效果预览标题"}, "sampleButton": "示例按钮", "@sampleButton": {"description": "示例按钮文本"}, "titleText": "标题文本", "@titleText": {"description": "标题文本示例"}, "sampleDescription": "这是一段示例文本，用于预览当前DPI适配设置的效果。", "@sampleDescription": {"description": "示例描述文本"}, "inches": "英寸", "@inches": {"description": "英寸单位"}, "dpiAdaptation": "DPI适配", "@dpiAdaptation": {"description": "DPI适配设置选项标题"}, "dpiAdaptationSubtitle": "调整界面元素大小以适应不同DPI的设备", "@dpiAdaptationSubtitle": {"description": "DPI适配设置选项描述"}}