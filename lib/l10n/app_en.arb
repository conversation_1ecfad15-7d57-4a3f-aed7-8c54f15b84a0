{"@@locale": "en", "settingsTitle": "Settings", "@settingsTitle": {"description": "Settings page title"}, "languageSettings": "Language", "@languageSettings": {"description": "Language settings option"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Select language"}, "selectSourceLanguage": "Select Source Language", "@selectSourceLanguage": {"description": "Select source language title"}, "selectTargetLanguage": "Select Target Language", "@selectTargetLanguage": {"description": "Select target language title"}, "languageChinese": "Chinese", "@languageChinese": {"description": "Chinese language name"}, "languageEnglish": "English", "@languageEnglish": {"description": "English language name"}, "languageUyghur": "Uyghur", "@languageUyghur": {"description": "Uyghur language name"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button"}, "displaySettings": "Display", "@displaySettings": {"description": "Display settings group title"}, "darkMode": "Dark Mode", "@darkMode": {"description": "Dark mode setting"}, "darkModeDescription": "Switch between light and dark themes", "@darkModeDescription": {"description": "Dark mode setting description"}, "followSystemTheme": "Follow System", "@followSystemTheme": {"description": "Follow system theme setting"}, "followSystemThemeDescription": "Automatically adapt to system light/dark settings", "@followSystemThemeDescription": {"description": "Follow system theme setting description"}, "fontSize": "Font Size", "@fontSize": {"description": "Font size setting"}, "fontSizeDescription": "Adjust app font size", "@fontSizeDescription": {"description": "Font size setting description"}, "languageSettingsDescription": "Choose app display language", "@languageSettingsDescription": {"description": "Language settings description"}, "other": "Other", "@other": {"description": "Other settings group"}, "helpAndFeedback": "Help & Feedback", "@helpAndFeedback": {"description": "Help and feedback"}, "helpAndFeedbackDescription": "FAQ and feedback submission", "@helpAndFeedbackDescription": {"description": "Help and feedback description"}, "aboutUs": "About", "@aboutUs": {"description": "About us"}, "aboutUsDescription": "Version info and company introduction", "@aboutUsDescription": {"description": "About us description"}, "logout": "Sign Out", "@logout": {"description": "Logout"}, "logoutDescription": "Clear login status and return to login page", "@logoutDescription": {"description": "Logout description"}, "aboutDialogTitle": "About Us", "@aboutDialogTitle": {"description": "About dialog title"}, "aboutDialogContent": "Health Assistant v1.0.0\n\nA professional AI health guide app", "@aboutDialogContent": {"description": "About dialog content"}, "homeTitle": "Health Assistant", "@homeTitle": {"description": "Health Assistant page title"}, "historyTitle": "History", "@historyTitle": {"description": "History page title"}, "aiTourGuideTitle": "Health Assistant", "@aiTourGuideTitle": {"description": "Health Assistant page title"}, "smartGuide": "Smart Guide", "@smartGuide": {"description": "Smart guide tab"}, "welcomeTo": "Welcome to", "@welcomeTo": {"description": "Welcome to prefix"}, "shareApp": "Share App", "@shareApp": {"description": "Share app menu item"}, "distributionManagement": "Affiliate Management", "@distributionManagement": {"description": "Distribution management menu item"}, "myLanguage": "My Language", "@myLanguage": {"description": "My language"}, "theirLanguage": "Their Language", "@theirLanguage": {"description": "Their language"}, "sourceLanguage": "From", "@sourceLanguage": {"description": "Source language label"}, "targetLanguage": "To", "@targetLanguage": {"description": "Target language label"}, "bottomNavHome": "Health Assistant", "@bottomNavHome": {"description": "Bottom navigation Health Assistant"}, "bottomNavHistory": "History", "@bottomNavHistory": {"description": "Bottom navigation history"}, "bottomNavAiGuide": "Health Assistant", "@bottomNavAiGuide": {"description": "Bottom navigation Health Assistant"}, "bottomNavSearch": "List", "@bottomNavSearch": {"description": "Bottom navigation list"}, "bottomNavProfile": "Profile", "@bottomNavProfile": {"description": "Bottom navigation profile"}, "bottomNavSettings": "Settings", "@bottomNavSettings": {"description": "Bottom navigation settings"}, "searchPageTitle": "List", "@searchPageTitle": {"description": "List page title"}, "searchHint": "Search doctors or products...", "@searchHint": {"description": "Search box hint text"}, "doctorTab": "Doctors", "@doctorTab": {"description": "Doctor tab"}, "productTab": "Products", "@productTab": {"description": "Product tab"}, "noDoctorsAvailable": "No doctors available", "@noDoctorsAvailable": {"description": "No doctors available message"}, "noProductsAvailable": "No products available", "@noProductsAvailable": {"description": "No products available message"}, "noSearchResults": "No search results found", "@noSearchResults": {"description": "No search results message"}, "inputHint": "Please enter your question...", "@inputHint": {"description": "Input field hint text"}, "tapToSpeak": "Tap to speak", "@tapToSpeak": {"description": "Voice input prompt"}, "listening": "Listening...", "@listening": {"description": "Voice recognition in progress"}, "processing": "Processing...", "@processing": {"description": "Processing status text"}, "clearHistory": "Clear", "@clearHistory": {"description": "Clear button"}, "copy": "Copy", "@copy": {"description": "Copy button"}, "share": "Share", "@share": {"description": "Share button"}, "play": "Play", "@play": {"description": "Play button"}, "pause": "Pause", "@pause": {"description": "Pause button"}, "retry": "Retry", "@retry": {"description": "Retry button"}, "error": "Error", "@error": {"description": "Error dialog title"}, "networkError": "Network connection failed. Please check your network settings", "@networkError": {"description": "Network error message"}, "permissionDenied": "Permission denied", "@permissionDenied": {"description": "Permission denied message"}, "cameraPermissionRequired": "Camera permission required", "@cameraPermissionRequired": {"description": "Camera permission message"}, "microphonePermissionRequired": "Microphone permission required for recording", "@microphonePermissionRequired": {"description": "Microphone permission required for recording"}, "appTitle": "MinHan Translator", "@appTitle": {"description": "App title"}, "welcomeMessage": "Welcome to <PERSON><PERSON><PERSON> Translator", "@welcomeMessage": {"description": "Welcome message"}, "welcomeDescription": "Your personal AI health guide, providing health consultation and guide services anytime", "@welcomeDescription": {"description": "Welcome page description"}, "exitAppConfirm": "Press back again to exit", "@exitAppConfirm": {"description": "Exit app confirmation prompt"}, "themeSwitch": "Theme Switch", "@themeSwitch": {"description": "Theme switch dialog title"}, "themeSwitchMessage": "Switching theme mode requires restarting the app to take full effect. Restart now?", "@themeSwitchMessage": {"description": "Theme switch confirmation message"}, "languageSwitchMessage": "Switching language requires restarting the app to take full effect. Restart now?", "@languageSwitchMessage": {"description": "Language switch confirmation message"}, "restart": "<PERSON><PERSON>", "@restart": {"description": "Restart button"}, "userManagementTab": "User Management", "@userManagementTab": {"description": "User management tab"}, "userManagement": "User Management", "@userManagement": {"description": "User management"}, "userList": "User List", "@userList": {"description": "User list"}, "userStatistics": "User Statistics", "@userStatistics": {"description": "User statistics"}, "totalUsers": "Total Users", "@totalUsers": {"description": "Total users"}, "activeUsers": "Active Users", "@activeUsers": {"description": "Active users"}, "adminUsers": "Admins", "@adminUsers": {"description": "Admin users"}, "doctorUsers": "Doctors", "@doctorUsers": {"description": "Doctor users"}, "searchUsers": "Search Users", "@searchUsers": {"description": "Search users"}, "searchByNicknamePhoneId": "Search by nickname, phone or ID", "@searchByNicknamePhoneId": {"description": "Search users hint text"}, "userStatus": "User Status", "@userStatus": {"description": "User status"}, "allStatus": "All", "@allStatus": {"description": "All status filter"}, "enabledStatus": "Enabled", "@enabledStatus": {"description": "Enabled status"}, "disabledStatus": "Disabled", "@disabledStatus": {"description": "Disabled status"}, "userRole": "User Role", "@userRole": {"description": "User role"}, "allRoles": "All Roles", "@allRoles": {"description": "All roles"}, "normalUser": "Normal User", "@normalUser": {"description": "Normal user status"}, "adminUser": "Admin", "@adminUser": {"description": "Admin user"}, "doctorUser": "Doctor", "@doctorUser": {"description": "Doctor user"}, "userGender": "Gender", "@userGender": {"description": "User gender"}, "male": "Male", "@male": {"description": "Male gender option"}, "female": "Female", "@female": {"description": "Female gender option"}, "unknown": "Unknown", "@unknown": {"description": "Unknown label"}, "registerSource": "Register Source", "@registerSource": {"description": "Register source"}, "appSource": "APP", "@appSource": {"description": "APP register source"}, "miniProgramSource": "Mini Program", "@miniProgramSource": {"description": "Mini program register source"}, "userBalance": "User Balance", "@userBalance": {"description": "User balance"}, "userIntegral": "User Points", "@userIntegral": {"description": "User integral"}, "adjustBalance": "Adjust Balance", "@adjustBalance": {"description": "Adjust balance"}, "adjustIntegral": "Adjust Points", "@adjustIntegral": {"description": "Adjust integral"}, "adjustAmount": "Adjust Amount", "@adjustAmount": {"description": "Adjust amount"}, "adjustReason": "Adjust Reason", "@adjustReason": {"description": "Adjust reason"}, "pleaseEnterAmount": "Please enter adjust amount", "@pleaseEnterAmount": {"description": "Please enter adjust amount"}, "pleaseEnterReason": "Please enter adjust reason", "@pleaseEnterReason": {"description": "Please enter adjust reason"}, "positiveForIncrease": "Positive for increase, negative for decrease", "@positiveForIncrease": {"description": "Positive for increase, negative for decrease"}, "userDetail": "User Detail", "@userDetail": {"description": "User detail"}, "editUser": "Edit User", "@editUser": {"description": "Edit user"}, "enableUser": "Enable User", "@enableUser": {"description": "Enable user"}, "disableUser": "Disable User", "@disableUser": {"description": "Disable user"}, "resetPassword": "Reset Password", "@resetPassword": {"description": "Reset password"}, "newPassword": "New Password", "@newPassword": {"description": "New password field label"}, "pleaseEnterNewPassword": "Please enter new password", "@pleaseEnterNewPassword": {"description": "Please enter new password"}, "passwordLength": "Password length 6-20 characters", "@passwordLength": {"description": "Password length 6-20 characters"}, "userNickname": "User Nickname", "@userNickname": {"description": "User nickname"}, "userPhone": "User Phone", "@userPhone": {"description": "User phone"}, "userBirthday": "User Birthday", "@userBirthday": {"description": "User birthday"}, "registrationTime": "Registration Time", "@registrationTime": {"description": "Registration time"}, "lastLoginTime": "Last Login", "@lastLoginTime": {"description": "Last login time"}, "userTokens": "<PERSON><PERSON>", "@userTokens": {"description": "User login tokens"}, "clearAllTokens": "Clear All Tokens", "@clearAllTokens": {"description": "Clear all tokens"}, "confirmClearTokens": "Confirm to clear all login tokens?", "@confirmClearTokens": {"description": "Confirm to clear all login tokens?"}, "clearTokensWarning": "Users will be forced to log out on all devices after clearing", "@clearTokensWarning": {"description": "Clear tokens warning"}, "deviceType": "Device Type", "@deviceType": {"description": "Device type"}, "expiryTime": "Expiry Time", "@expiryTime": {"description": "Expiry time"}, "createTime": "Create Time", "@createTime": {"description": "Create time"}, "expired": "Expired", "@expired": {"description": "Expired"}, "valid": "<PERSON><PERSON>", "@valid": {"description": "<PERSON><PERSON>"}, "loadUserListFailed": "Failed to load user list", "@loadUserListFailed": {"description": "Failed to load user list"}, "loadUserDetailFailed": "Failed to load user detail", "@loadUserDetailFailed": {"description": "Failed to load user detail"}, "updateUserSuccess": "User information updated successfully", "@updateUserSuccess": {"description": "User information updated successfully"}, "updateUserFailed": "Failed to update user information", "@updateUserFailed": {"description": "Failed to update user information"}, "adjustBalanceSuccess": "Balance adjusted successfully", "@adjustBalanceSuccess": {"description": "Balance adjusted successfully"}, "adjustBalanceFailed": "Failed to adjust balance", "@adjustBalanceFailed": {"description": "Failed to adjust balance"}, "adjustIntegralSuccess": "Points adjusted successfully", "@adjustIntegralSuccess": {"description": "Points adjusted successfully"}, "adjustIntegralFailed": "Failed to adjust points", "@adjustIntegralFailed": {"description": "Failed to adjust points"}, "resetPasswordSuccess": "Password reset successfully", "@resetPasswordSuccess": {"description": "Password reset successfully"}, "resetPasswordFailed": "Failed to reset password", "@resetPasswordFailed": {"description": "Failed to reset password"}, "clearTokensSuccess": "Tokens cleared successfully", "@clearTokensSuccess": {"description": "Tokens cleared successfully"}, "clearTokensFailed": "Failed to clear tokens", "@clearTokensFailed": {"description": "Failed to clear tokens"}, "noUsersFound": "No users found", "@noUsersFound": {"description": "No users found"}, "enableUserSuccess": "User enabled successfully", "@enableUserSuccess": {"description": "User enabled successfully"}, "disableUserSuccess": "User disabled successfully", "@disableUserSuccess": {"description": "User disabled successfully"}, "reset": "Reset", "@reset": {"description": "Reset"}, "apply": "Apply", "@apply": {"description": "Apply"}, "todayNewUsers": "Today New", "@todayNewUsers": {"description": "Today new users"}, "thisWeekNewUsers": "This Week New", "@thisWeekNewUsers": {"description": "This week new users"}, "totalBalance": "Total Balance", "@totalBalance": {"description": "Total balance"}, "totalIntegral": "Total Points", "@totalIntegral": {"description": "Total integral"}, "expandFilters": "Expand Filters", "@expandFilters": {"description": "Expand filters"}, "collapseFilters": "Collapse Filters", "@collapseFilters": {"description": "Collapse filters"}, "userDevelopmentInProgress": "User detail page under development...", "@userDevelopmentInProgress": {"description": "User detail page under development"}, "roleAdmin": "Admin", "@roleAdmin": {"description": "Admin role"}, "roleDoctor": "Doctor", "@roleDoctor": {"description": "Doctor role"}, "roleReferrer": "<PERSON><PERSON><PERSON>", "@roleReferrer": {"description": "Referrer role"}, "roleNormalUser": "Normal User", "@roleNormalUser": {"description": "Normal user role"}, "editUserInfo": "Edit User Info", "@editUserInfo": {"description": "Edit user info"}, "editUserRole": "Edit User Role", "@editUserRole": {"description": "Edit user role"}, "userNicknameLabel": "User Nickname", "@userNicknameLabel": {"description": "User nickname label"}, "userPhoneLabel": "User Phone", "@userPhoneLabel": {"description": "User phone label"}, "userBirthdayLabel": "User Birthday", "@userBirthdayLabel": {"description": "User birthday label"}, "userGenderLabel": "User Gender", "@userGenderLabel": {"description": "User gender label"}, "pleaseEnterNickname": "Please enter user nickname", "@pleaseEnterNickname": {"description": "Please enter user nickname"}, "pleaseEnterPhone": "Please enter user phone", "@pleaseEnterPhone": {"description": "Please enter user phone"}, "selectBirthday": "Select Birthday", "@selectBirthday": {"description": "Select birthday"}, "selectGender": "Select Gender", "@selectGender": {"description": "Select gender"}, "associatedDoctor": "Associated Doctor", "@associatedDoctor": {"description": "Associated doctor"}, "selectDoctor": "Select Doctor", "@selectDoctor": {"description": "Select doctor"}, "referrerLevel": "Referrer Level", "@referrerLevel": {"description": "Referrer level"}, "level": "Level", "@level": {"description": "Level"}, "ipAddress": "IP Address", "@ipAddress": {"description": "IP address"}, "roleManagement": "Role Management", "@roleManagement": {"description": "Role management"}, "currentRoles": "Current Roles", "@currentRoles": {"description": "Current roles"}, "roleDetails": "Role Details", "@roleDetails": {"description": "Role details"}, "editRole": "Edit Role", "@editRole": {"description": "Edit role"}, "balanceIntegralManagement": "Balance & Points Management", "@balanceIntegralManagement": {"description": "Balance & points management"}, "tokenManagement": "Token Management", "@tokenManagement": {"description": "Token management"}, "totalTokens": "Total Tokens", "@totalTokens": {"description": "Total tokens"}, "noTokensFound": "No login tokens found", "@noTokensFound": {"description": "No login tokens found"}, "tokenClearSuccess": "<PERSON><PERSON> cleared successfully", "@tokenClearSuccess": {"description": "<PERSON><PERSON> cleared successfully"}, "tokenClearFailed": "Failed to clear token", "@tokenClearFailed": {"description": "Failed to clear token"}, "loadTokensFailed": "Failed to load token list", "@loadTokensFailed": {"description": "Failed to load token list"}, "pleaseCompleteInfo": "Please complete all information", "@pleaseCompleteInfo": {"description": "Please complete all information"}, "pleaseEnterValidAmount": "Please enter valid amount", "@pleaseEnterValidAmount": {"description": "Please enter valid amount"}, "pleaseEnterValidPoints": "Please enter valid points", "@pleaseEnterValidPoints": {"description": "Please enter valid points"}, "userEditInProgress": "User edit feature under development...", "@userEditInProgress": {"description": "User edit feature under development"}, "roleEditInProgress": "Role edit feature under development...", "@roleEditInProgress": {"description": "Role edit feature under development"}, "genderMale": "Male", "@genderMale": {"description": "Male gender"}, "genderFemale": "Female", "@genderFemale": {"description": "Female gender"}, "genderUnknown": "Unknown", "@genderUnknown": {"description": "Unknown gender"}, "referrerText": "<PERSON><PERSON><PERSON>", "@referrerText": {"description": "Referrer text"}, "updateRoleSuccess": "Role updated successfully", "@updateRoleSuccess": {"description": "Role updated successfully"}, "updateRoleFailed": "Failed to update role", "@updateRoleFailed": {"description": "Failed to update role"}, "adminRoleDescription": "Has system administration privileges", "@adminRoleDescription": {"description": "Admin role description"}, "doctorRoleDescription": "Can manage products and orders", "@doctorRoleDescription": {"description": "Doctor role description"}, "referrerRoleDescription": "Can promote and earn commissions", "@referrerRoleDescription": {"description": "Referrer role description"}, "pleaseEnterReferrerLevel": "Please enter referrer level", "@pleaseEnterReferrerLevel": {"description": "Please enter referrer level"}, "registerSourceApp": "APP", "@registerSourceApp": {"description": "APP registration source"}, "registerSourceMiniProgram": "Mini Program", "@registerSourceMiniProgram": {"description": "Mini program registration source"}, "statusEnabled": "Enabled", "@statusEnabled": {"description": "Enabled status"}, "statusDisabled": "Disabled", "@statusDisabled": {"description": "Disabled status"}, "healthProfile": "Health Profile", "@healthProfile": {"description": "Health profile"}, "viewFullHealthProfile": "View Full Profile", "@viewFullHealthProfile": {"description": "View full health profile"}, "heartRate": "Heart Rate", "@heartRate": {"description": "Heart rate"}, "bodyTemperature": "Body Temperature", "@bodyTemperature": {"description": "Body temperature"}, "weight": "Weight", "@weight": {"description": "Weight label"}, "height": "Height", "@height": {"description": "Height label"}, "healthProfileFeatureComingSoon": "Health profile feature coming soon", "@healthProfileFeatureComingSoon": {"description": "Health profile feature coming soon"}, "bloodType": "Blood Type", "@bloodType": {"description": "Blood type label"}, "exerciseFrequency": "Exercise Frequency", "@exerciseFrequency": {"description": "Exercise frequency label"}, "sedentary": "Sedentary", "@sedentary": {"description": "Sedentary lifestyle"}, "lightExercise": "Light Exercise", "@lightExercise": {"description": "Light exercise"}, "moderateExercise": "Moderate Exercise", "@moderateExercise": {"description": "Moderate exercise"}, "activeExercise": "Active Exercise", "@activeExercise": {"description": "Active exercise"}, "noHealthProfileYet": "No health profile yet", "@noHealthProfileYet": {"description": "No health profile yet"}, "createHealthProfile": "Create Health Profile", "@createHealthProfile": {"description": "Create health profile"}, "healthProfileEditFeatureComingSoon": "Health profile editing feature coming soon", "@healthProfileEditFeatureComingSoon": {"description": "Health profile editing feature coming soon"}, "basicInfo": "Basic Information", "@basicInfo": {"description": "Basic information title"}, "allergyHistory": "Allergy History", "@allergyHistory": {"description": "Allergy history title"}, "chronicDiseaseHistory": "Chronic Disease History", "@chronicDiseaseHistory": {"description": "Chronic disease history title"}, "currentMedication": "Current Medication", "@currentMedication": {"description": "Current medication title"}, "lifestyle": "Lifestyle", "@lifestyle": {"description": "Lifestyle title"}, "hasAllergies": "Do you have any drug, food or other substance allergies", "@hasAllergies": {"description": "Has allergies question"}, "drugAllergies": "Drug Allergies", "@drugAllergies": {"description": "Drug allergies"}, "foodAllergies": "Food Allergies", "@foodAllergies": {"description": "Food allergies"}, "otherAllergies": "Other Allergies", "@otherAllergies": {"description": "Other allergies"}, "hasChronicDiseases": "Has Chronic Diseases", "@hasChronicDiseases": {"description": "Has chronic diseases"}, "chronicDiseasesList": "Chronic Diseases List", "@chronicDiseasesList": {"description": "Chronic diseases list"}, "bloodPressureRange": "Usual blood pressure range", "@bloodPressureRange": {"description": "Blood pressure range label"}, "bloodSugarRange": "Usual fasting blood sugar range", "@bloodSugarRange": {"description": "Blood sugar range label"}, "hasCurrentMedication": "Has Current Medication", "@hasCurrentMedication": {"description": "Has current medication"}, "medicationDetails": "Medication Details", "@medicationDetails": {"description": "Medication details"}, "smokingStatus": "Smoking Status", "@smokingStatus": {"description": "Smoking status label"}, "drinkingStatus": "Drinking Status", "@drinkingStatus": {"description": "Drinking status label"}, "sleepDuration": "Average nightly sleep duration", "@sleepDuration": {"description": "Sleep duration label"}, "sleepQuality": "Sleep Quality", "@sleepQuality": {"description": "Sleep quality label"}, "stressLevel": "Recent stress level", "@stressLevel": {"description": "Stress level label"}, "yes": "Yes", "@yes": {"description": "Yes option"}, "no": "No", "@no": {"description": "No option"}, "never": "Never", "@never": {"description": "Never"}, "quit": "Quit", "@quit": {"description": "Quit"}, "occasional": "Occasional", "@occasional": {"description": "Occasional"}, "daily": "Daily", "@daily": {"description": "Daily"}, "social": "Social", "@social": {"description": "Social"}, "weekly": "Weekly", "@weekly": {"description": "Weekly"}, "lessThan6Hours": "Less than 6 hours", "@lessThan6Hours": {"description": "Less than 6 hours"}, "sixToSevenHours": "6-7 hours", "@sixToSevenHours": {"description": "6-7 hours"}, "sevenToEightHours": "7-8 hours", "@sevenToEightHours": {"description": "7-8 hours"}, "moreThan8Hours": "More than 8 hours", "@moreThan8Hours": {"description": "More than 8 hours"}, "good": "Good", "@good": {"description": "Good"}, "fair": "Fair", "@fair": {"description": "Fair"}, "poor": "Poor", "@poor": {"description": "Poor"}, "veryLow": "Very Low", "@veryLow": {"description": "Very low"}, "low": "Low", "@low": {"description": "Low"}, "moderate": "Moderate", "@moderate": {"description": "Moderate"}, "high": "High", "@high": {"description": "High"}, "veryHigh": "Very High", "@veryHigh": {"description": "Very high"}, "pleaseEnterPhoneNumber": "Please enter phone number", "@pleaseEnterPhoneNumber": {"description": "Please enter phone number validation hint"}, "pleaseEnterCorrectPhoneNumber": "Please enter a valid phone number", "@pleaseEnterCorrectPhoneNumber": {"description": "Please enter correct phone number prompt"}, "pleaseEnterPassword": "Please enter password", "@pleaseEnterPassword": {"description": "Please enter password prompt"}, "passwordMinLength": "Password must be at least 6 characters", "@passwordMinLength": {"description": "Password minimum length prompt"}, "loggingIn": "Logging in...", "@loggingIn": {"description": "Logging in prompt"}, "loginSuccessful": "Login successful", "@loginSuccessful": {"description": "Login successful prompt"}, "loginSuccessButNoData": "Login successful but user data is empty", "@loginSuccessButNoData": {"description": "Login successful but no data"}, "dataProcessingError": "Error processing user data: {error}", "@dataProcessingError": {"description": "Data processing error", "placeholders": {"error": {"type": "String"}}}, "loginProcessError": "Error during login process: {error}", "@loginProcessError": {"description": "Login process error", "placeholders": {"error": {"type": "String"}}}, "passwordIncorrect": "Incorrect password", "@passwordIncorrect": {"description": "Password incorrect prompt"}, "phoneNotRegistered": "This phone number is not registered, please register first", "@phoneNotRegistered": {"description": "Phone not registered prompt"}, "passwordLogin": "Password Login", "@passwordLogin": {"description": "Password login title"}, "passwordLoginSubtitle": "Please use your phone number and password to login", "@passwordLoginSubtitle": {"description": "Password login subtitle"}, "phoneNumberHint": "Please enter phone number", "@phoneNumberHint": {"description": "Phone number input hint"}, "passwordHint": "Please enter password", "@passwordHint": {"description": "Password input hint"}, "forgotPassword": "Forgot password?", "@forgotPassword": {"description": "Forgot password button"}, "smsLogin": "SMS Login", "@smsLogin": {"description": "SMS login button"}, "registerAccount": "Register Account", "@registerAccount": {"description": "Register account button"}, "orOtherLoginMethods": "Or choose other login methods", "@orOtherLoginMethods": {"description": "Other login methods hint"}, "loginAgreement": "By logging in, you agree to the User Agreement and Privacy Policy", "@loginAgreement": {"description": "Login agreement text"}, "verificationCodeSent": "Verification code sent", "@verificationCodeSent": {"description": "Verification code sent"}, "sendFailed": "Send failed", "@sendFailed": {"description": "Send failed"}, "pleaseEnterVerificationCode": "Please enter verification code", "@pleaseEnterVerificationCode": {"description": "Enter verification code prompt"}, "verificationCodeShouldBe6Digits": "Verification code should be 6 digits", "@verificationCodeShouldBe6Digits": {"description": "Verification code digits prompt"}, "login": "Sign In", "@login": {"description": "Login button"}, "welcomeBack": "Welcome Back", "@welcomeBack": {"description": "Welcome back"}, "pleaseLoginWithPhoneNumber": "Please sign in with your phone number", "@pleaseLoginWithPhoneNumber": {"description": "Please login with phone number"}, "passwordLoginDesc": "Please sign in with your phone number and password", "@passwordLoginDesc": {"description": "Password login description"}, "agreeToTerms": "By signing in, you agree to our Terms of Service and Privacy Policy", "@agreeToTerms": {"description": "Agree to terms"}, "verificationCodeHint": "Enter verification code", "@verificationCodeHint": {"description": "Verification code input hint"}, "newPasswordHint": "Enter new password", "@newPasswordHint": {"description": "New password input hint"}, "confirmPasswordHint": "Enter new password again", "@confirmPasswordHint": {"description": "Confirm password input hint"}, "getVerificationCode": "Get Code", "@getVerificationCode": {"description": "Get verification code"}, "resendVerificationCode": "Resend", "@resendVerificationCode": {"description": "Resend verification code"}, "resetPasswordDescription": "Enter your phone number to get verification code, then set a new password", "@resetPasswordDescription": {"description": "Reset password description"}, "confirmReset": "Confirm Reset", "@confirmReset": {"description": "Confirm reset"}, "passwordsDoNotMatch": "The two passwords do not match", "@passwordsDoNotMatch": {"description": "Passwords do not match validation message"}, "passwordResetSuccess": "Password reset successfully. Please sign in with your new password", "@passwordResetSuccess": {"description": "Password reset success"}, "resetPasswordTitle": "Reset Password", "@resetPasswordTitle": {"description": "Reset password page title"}, "enterCorrectPhoneNumber": "Please enter a valid phone number", "@enterCorrectPhoneNumber": {"description": "Enter valid phone number"}, "enterVerificationCode": "Please enter verification code", "@enterVerificationCode": {"description": "Enter verification code"}, "verificationCodeSixDigits": "Verification code should be 6 digits", "@verificationCodeSixDigits": {"description": "Verification code 6 digits hint"}, "enterNewPassword": "Please enter new password", "@enterNewPassword": {"description": "Enter new password"}, "passwordMinSixCharacters": "Password must be at least 6 characters", "@passwordMinSixCharacters": {"description": "Password minimum 6 characters hint"}, "enterNewPasswordAgain": "Please enter new password again", "@enterNewPasswordAgain": {"description": "Enter new password again hint"}, "getVerificationCodeButton": "Get Code", "@getVerificationCodeButton": {"description": "Get verification code button"}, "resendCountdown": "Resend ({seconds}s)", "@resendCountdown": {"description": "Resend countdown", "placeholders": {"seconds": {"type": "int"}}}, "sendVerificationCodeFailed": "Failed to send verification code", "@sendVerificationCodeFailed": {"description": "Send verification code failed"}, "resettingPasswordLoading": "Resetting password...", "@resettingPasswordLoading": {"description": "Resetting password loading hint"}, "passwordResetFailed": "Password reset failed", "@passwordResetFailed": {"description": "Password reset failed"}, "networkConnectionFailedRetry": "Network connection failed, please try again later", "@networkConnectionFailedRetry": {"description": "Network connection failed retry hint"}, "contactCustomerService": "If you have any issues, please contact customer service", "@contactCustomerService": {"description": "Contact customer service"}, "clearHistoryConfirm": "Are you sure you want to clear all chat history? This action cannot be undone.", "@clearHistoryConfirm": {"description": "Clear chat history confirmation"}, "clearHistoryTitle": "Clear Chat History", "@clearHistoryTitle": {"description": "Clear chat history title"}, "historyCleared": "History cleared", "@historyCleared": {"description": "History cleared message"}, "clearHistoryFailed": "Failed to clear history", "@clearHistoryFailed": {"description": "Clear history failed"}, "translationHistory": "Translation History", "@translationHistory": {"description": "Translation history menu item"}, "noTranslationHistoryDesc": "Translation history will appear here after you translate", "@noTranslationHistoryDesc": {"description": "No translation history description"}, "clearAllHistory": "Clear All History", "@clearAllHistory": {"description": "Clear all history"}, "daysAgo": "days ago", "@daysAgo": {"description": "Days ago"}, "hoursAgo": "{hours} hours ago", "@hoursAgo": {"description": "Hours ago time indicator", "placeholders": {"hours": {"type": "int"}}}, "minutesAgo": "{minutes} minutes ago", "@minutesAgo": {"description": "Minutes ago time indicator", "placeholders": {"minutes": {"type": "int"}}}, "justNow": "Just now", "@justNow": {"description": "Just now time indicator"}, "selectTranslationArea": "Select Translation Area", "@selectTranslationArea": {"description": "Select translation area"}, "retakePhoto": "Retake Photo", "@retakePhoto": {"description": "Retake photo"}, "startTranslation": "Start Translation", "@startTranslation": {"description": "Start translation"}, "imageTranslationFailed": "Translation failed", "@imageTranslationFailed": {"description": "Image translation failed"}, "imageProcessingError": "Error processing image", "@imageProcessingError": {"description": "Image processing error"}, "exitAppHint": "Press back again to exit", "@exitAppHint": {"description": "Exit app hint"}, "chinese": "Chinese", "@chinese": {"description": "Chinese language"}, "uyghur": "Uyghur", "@uyghur": {"description": "Uyghur language"}, "kazakh": "Kazakh", "@kazakh": {"description": "Kazakh language"}, "russian": "Russian", "@russian": {"description": "Russian language"}, "french": "French", "@french": {"description": "French language"}, "spanish": "Spanish", "@spanish": {"description": "Spanish language"}, "cantonese": "Cantonese", "@cantonese": {"description": "Cantonese language"}, "languageNotSupportedForVoice": "(Voice translation not supported)", "@languageNotSupportedForVoice": {"description": "Language not supported for voice"}, "selectSourceLanguageFirst": "Please select source language first", "@selectSourceLanguageFirst": {"description": "Select source language first"}, "targetLanguageWillUpdate": "Target language will be updated based on source language", "@targetLanguageWillUpdate": {"description": "Target language will update"}, "faceToFaceConversation": "Face-to-Face Conversation", "@faceToFaceConversation": {"description": "Face-to-face conversation"}, "conversation": "Cha<PERSON>", "@conversation": {"description": "Conversation button text"}, "newChat": "New", "@newChat": {"description": "New chat button text"}, "aiChatHistory": "Chat History", "@aiChatHistory": {"description": "AI chat history title"}, "noChatHistory": "No chat history", "@noChatHistory": {"description": "No chat history message"}, "startNewChat": "Tap the New button to start chatting", "@startNewChat": {"description": "Start new chat hint"}, "startChatting": "Start Chatting", "@startChatting": {"description": "Start chatting hint"}, "sendFirstMessage": "Send your first message to start the conversation", "@sendFirstMessage": {"description": "Send first message hint"}, "thinking": "Thinking...", "@thinking": {"description": "AI thinking message"}, "sending": "Sending...", "@sending": {"description": "Message sending status"}, "audioMessage": "Audio Message", "@audioMessage": {"description": "Audio message label"}, "typeMessage": "Type a message...", "@typeMessage": {"description": "Input field placeholder"}, "editTitle": "Edit Title", "@editTitle": {"description": "Edit title button"}, "enterTitle": "Enter title", "@enterTitle": {"description": "Enter title hint"}, "deleteConversation": "Delete Conversation", "@deleteConversation": {"description": "Delete conversation"}, "deleteConversationConfirm": "Are you sure you want to delete this conversation? This action cannot be undone.", "@deleteConversationConfirm": {"description": "Delete conversation confirmation"}, "edit": "Edit", "@edit": {"description": "Edit button"}, "delete": "Delete", "@delete": {"description": "Delete button"}, "save": "Save", "@save": {"description": "Save button"}, "loadFailed": "Failed to load data", "@loadFailed": {"description": "Load failed"}, "microphonePermissionDenied": "Microphone permission denied", "@microphonePermissionDenied": {"description": "Microphone permission denied"}, "recordingTooShort": "Recording too short", "@recordingTooShort": {"description": "Recording too short message"}, "recordingCancelled": "Recording cancelled", "@recordingCancelled": {"description": "Recording cancelled"}, "aiChatHistorySubtitle": "View your conversation history with Health Assistant", "@aiChatHistorySubtitle": {"description": "AI chat history subtitle"}, "clearHistoryButton": "Clear History", "@clearHistoryButton": {"description": "Clear history button text"}, "clearConversation": "Clear Conversation", "@clearConversation": {"description": "Clear conversation"}, "holdToSpeak": "Hold to speak", "@holdToSpeak": {"description": "Hold to speak button text"}, "waitingForOther": "Waiting for the other person to speak", "@waitingForOther": {"description": "Waiting for other person"}, "microphonePermissionNeeded": "Microphone permission needed for recording", "@microphonePermissionNeeded": {"description": "Microphone permission needed"}, "recordingFailed": "Recording failed: {error}", "@recordingFailed": {"description": "Recording failed", "placeholders": {"error": {"type": "String"}}}, "invalidAudioFile": "Invalid audio file", "@invalidAudioFile": {"description": "Invalid audio file"}, "audioProcessingFailed": "Audio processing failed, please try again", "@audioProcessingFailed": {"description": "Audio processing failed message"}, "cannotRecognizeVoice": "Could not recognize voice content", "@cannotRecognizeVoice": {"description": "Cannot recognize voice"}, "voiceTranslationNotAvailable": "Voice translation temporarily unavailable, please try again later", "@voiceTranslationNotAvailable": {"description": "Voice translation not available"}, "confirmClearConversation": "Are you sure you want to clear all chat history? This action cannot be undone.", "@confirmClearConversation": {"description": "Confirm clear conversation"}, "conversationCleared": "Conversation cleared", "@conversationCleared": {"description": "Conversation cleared"}, "user": "User", "@user": {"description": "User"}, "clickToLogin": "Tap to sign in", "@clickToLogin": {"description": "Click to login"}, "loginToEnjoyMoreFeatures": "Sign in to enjoy more features", "@loginToEnjoyMoreFeatures": {"description": "Login to enjoy more features"}, "editProfile": "Edit Profile", "@editProfile": {"description": "Edit profile page title"}, "vipMember": "VIP Member", "@vipMember": {"description": "VIP member status"}, "distributorLevel": "Affiliate", "@distributorLevel": {"description": "Distributor level"}, "appName": "<PERSON><PERSON>an Health Assistant", "@appName": {"description": "App name"}, "shareSuccess": "Shared successfully! Your friends can get rewards by downloading through your link", "@shareSuccess": {"description": "Share success"}, "shareNotAvailable": "Share feature temporarily unavailable, please try again later", "@shareNotAvailable": {"description": "Share not available"}, "shareSubject": "Recommend App", "@shareSubject": {"description": "Share subject"}, "shareContentWithReferral": "I found an amazing translation tour guide app: {appName}! It supports real-time translation in multiple languages, plus voice translation, camera translation, Health Assistant and more. Download using my exclusive referral link for extra benefits! 🎁\n\nDownload now: {url}", "@shareContentWithReferral": {"description": "Share content with referral", "placeholders": {"appName": {"type": "String"}, "url": {"type": "String"}}}, "shareContentNormal": "I found an amazing translation tour guide app: {appName}! It supports real-time translation in multiple languages, plus voice translation, camera translation, Health Assistant and more. Give it a try!\n\nDownload now: {url}", "@shareContentNormal": {"description": "Normal share content", "placeholders": {"appName": {"type": "String"}, "url": {"type": "String"}}}, "logoutConfirmation": "Are you sure you want to sign out? You'll need to sign in again to use all features.", "@logoutConfirmation": {"description": "Logout confirmation"}, "logoutSuccess": "Signed out successfully", "@logoutSuccess": {"description": "Logout success"}, "logoutFailed": "Failed to sign out", "@logoutFailed": {"description": "Logout failed"}, "comingSoon": "(Coming Soon)", "@comingSoon": {"description": "Coming soon feature indicator"}, "yearsExperience": "{years} years exp.", "@yearsExperience": {"description": "Doctor work experience", "placeholders": {"years": {"type": "int"}}}, "ratingScore": "{rating} pts", "@ratingScore": {"description": "Doctor rating score", "placeholders": {"rating": {"type": "String"}}}, "aiAssistant": "AI", "@aiAssistant": {"description": "AI assistant badge"}, "professionalIntroduction": "Professional Introduction", "@professionalIntroduction": {"description": "Doctor professional introduction title"}, "doctorAiAssistantSelected": "Selected {<PERSON><PERSON><PERSON>}'s AI Assistant", "@doctorAiAssistantSelected": {"description": "Doctor AI assistant selection success message", "placeholders": {"doctorName": {"type": "String"}}}, "loading": "Loading...", "@loading": {"description": "Loading text"}, "noDoctorInfo": "No doctor information", "@noDoctorInfo": {"description": "No doctor information message"}, "doctorTitle": "Doctor", "@doctorTitle": {"description": "Doctor title"}, "specialtyField": "Specialty", "@specialtyField": {"description": "Specialty field"}, "startChatWithAiGuide": "Start chatting with AI guide", "@startChatWithAiGuide": {"description": "Chat history empty state hint"}, "updateTitleFailed": "Failed to update title", "@updateTitleFailed": {"description": "Update conversation title failed message"}, "selectAddress": "Select Address", "@selectAddress": {"description": "Select address button"}, "addressManagement": "Address Management", "@addressManagement": {"description": "Address management button"}, "noAddressesYet": "No addresses yet", "@noAddressesYet": {"description": "No addresses message"}, "clickToAddAddress": "Click the button below to add an address", "@clickToAddAddress": {"description": "Add address hint"}, "setAsDefault": "Set as <PERSON><PERSON><PERSON>", "@setAsDefault": {"description": "Set as default button"}, "selectedItemsCount": "Selected {count} items", "@selectedItemsCount": {"description": "Selected items count", "placeholders": {"count": {"type": "int"}}}, "totalAmount": "Total: ", "@totalAmount": {"description": "Total amount label"}, "myLikesTitle": "My Likes", "@myLikesTitle": {"description": "My likes page title"}, "myFavoritesTitle": "My Favorites", "@myFavoritesTitle": {"description": "My favorites page title"}, "noLikedDoctors": "No liked doctors yet", "@noLikedDoctors": {"description": "No liked doctors empty state message"}, "noFavoriteDoctors": "No favorite doctors yet", "@noFavoriteDoctors": {"description": "No favorite doctors empty state message"}, "goLikeDoctors": "Go to doctor details to like your favorite doctors", "@goLikeDoctors": {"description": "Go like doctors hint message"}, "goFavoriteDoctors": "Go to doctor details to favorite your preferred doctors", "@goFavoriteDoctors": {"description": "Go favorite doctors hint message"}, "selectAll": "Select All", "@selectAll": {"description": "Select all button"}, "deleteSelected": "Delete Selected", "@deleteSelected": {"description": "Delete selected button"}, "checkout": "Checkout", "@checkout": {"description": "Checkout button"}, "deleteWithCount": "Delete ({count})", "@deleteWithCount": {"description": "Delete button with count", "placeholders": {"count": {"type": "int"}}}, "checkoutWithCount": "Checkout ({count})", "@checkoutWithCount": {"description": "Checkout button with count", "placeholders": {"count": {"type": "int"}}}, "doctorInfo": "Doctor Information", "@doctorInfo": {"description": "Doctor information title"}, "doctorName": "Doctor Name", "@doctorName": {"description": "Doctor name label"}, "contactPhone": "Contact Phone", "@contactPhone": {"description": "Contact phone label"}, "workAddress": "Work Address", "@workAddress": {"description": "Work address label"}, "call": "Call", "@call": {"description": "Call button"}, "noProducts": "No products", "@noProducts": {"description": "No products hint"}, "productsCount": "{count} products", "@productsCount": {"description": "Products count", "placeholders": {"count": {"type": "int"}}}, "buyNow": "Buy Now", "@buyNow": {"description": "Buy now button"}, "addToCart": "Add to Cart", "@addToCart": {"description": "Add to cart button"}, "doctor": "Doctor", "@doctor": {"description": "Doctor label"}, "productQuantity": "Product Quantity", "@productQuantity": {"description": "Product quantity label"}, "productTotalPrice": "Product Total", "@productTotalPrice": {"description": "Product total price label"}, "shippingFee": "Shipping Fee", "@shippingFee": {"description": "Shipping fee label"}, "free": "Free", "@free": {"description": "Free label"}, "actualPayment": "Total Payment", "@actualPayment": {"description": "Actual payment label"}, "confirmOrder": "Confirm Order", "@confirmOrder": {"description": "Confirm order button"}, "orderCreatedSuccess": "Order created successfully", "@orderCreatedSuccess": {"description": "Order created success message"}, "createOrderFailed": "Create order failed: {error}", "@createOrderFailed": {"description": "Create order failed message", "placeholders": {"error": {"type": "String"}}}, "checkoutFailed": "Checkout failed: {error}", "@checkoutFailed": {"description": "Checkout failed message", "placeholders": {"error": {"type": "String"}}}, "quantityRange": "Please enter a valid quantity between 1-99", "@quantityRange": {"description": "Quantity range message"}, "items": "items", "@items": {"description": "Items unit"}, "consultation": "Consult", "@consultation": {"description": "Consultation button"}, "appointment": "Appoint", "@appointment": {"description": "Appointment button"}, "years": "years", "@years": {"description": "Years unit"}, "yearsOfExperience": "Years of Experience", "@yearsOfExperience": {"description": "Years of experience label"}, "productDetail": "Product Detail", "@productDetail": {"description": "Product detail page title"}, "price": "Price", "@price": {"description": "Price label"}, "appointmentTime": "Appointment Time", "@appointmentTime": {"description": "Appointment time label"}, "detailedDescription": "Detailed Description", "@detailedDescription": {"description": "Detailed description title"}, "selectQuantity": "Select Quantity", "@selectQuantity": {"description": "Select quantity title"}, "quantity": "Quantity", "@quantity": {"description": "Quantity label"}, "cartEmpty": "Your cart is empty", "@cartEmpty": {"description": "Empty cart message"}, "cartEmptyDescription": "Go pick some great products", "@cartEmptyDescription": {"description": "Empty cart description"}, "goShopping": "Go Shopping", "@goShopping": {"description": "Go shopping button"}, "purchaseConfirmation": "Purchase Confirmation", "@purchaseConfirmation": {"description": "Purchase confirmation dialog title"}, "manufacturer": "Manufacturer", "@manufacturer": {"description": "Manufacturer field"}, "wednesday": "Wednesday", "@wednesday": {"description": "Wednesday"}, "morning": "AM", "@morning": {"description": "Morning time"}, "afternoon": "PM", "@afternoon": {"description": "Afternoon time"}, "evening": "PM", "@evening": {"description": "Evening time"}, "confirmPurchase": "Confirm Purchase", "@confirmPurchase": {"description": "Confirm purchase button"}, "productName": "Product Name", "@productName": {"description": "Product name field"}, "unitPrice": "Unit Price", "@unitPrice": {"description": "Unit price label"}, "purchaseConfirmationMessage": "Please confirm your purchase information. <PERSON><PERSON> confirm to proceed to order confirmation page.", "@purchaseConfirmationMessage": {"description": "Purchase confirmation message"}, "orderConfirmation": "Order Confirmation", "@orderConfirmation": {"description": "Order confirmation page title"}, "productInfo": "Product Information", "@productInfo": {"description": "Product information title"}, "shippingInfo": "Shipping Info", "@shippingInfo": {"description": "Shipping information title"}, "orderAmount": "Order Amount", "@orderAmount": {"description": "Order amount title"}, "recipientName": "Recipient Name", "@recipientName": {"description": "Recipient name label"}, "recipientPhone": "Recipient Phone", "@recipientPhone": {"description": "Recipient phone label"}, "shippingAddress": "Shipping Address", "@shippingAddress": {"description": "Shipping address label"}, "getCurrentLocation": "Get Current Location", "@getCurrentLocation": {"description": "Get current location button"}, "gettingLocation": "Getting location...", "@gettingLocation": {"description": "Getting location message"}, "subtotal": "Subtotal", "@subtotal": {"description": "Subtotal label"}, "submitOrder": "Submit Order", "@submitOrder": {"description": "Submit order button"}, "submittingOrder": "Submitting order...", "@submittingOrder": {"description": "Submitting order message"}, "enterRecipientName": "Please enter recipient name", "@enterRecipientName": {"description": "Enter recipient name hint"}, "enterRecipientPhone": "Please enter recipient phone", "@enterRecipientPhone": {"description": "Enter recipient phone hint"}, "enterShippingAddress": "Please enter shipping address", "@enterShippingAddress": {"description": "Enter shipping address hint"}, "totalItems": "{count} items total", "@totalItems": {"description": "Total items count display", "placeholders": {"count": {"type": "int"}}}, "done": "Done", "@done": {"description": "Done button"}, "region": "Region", "@region": {"description": "Region label"}, "selectRegion": "Please select province/city/district", "@selectRegion": {"description": "Select region hint"}, "pleaseSelectRegion": "Please select a region", "@pleaseSelectRegion": {"description": "Region selection validation hint"}, "productAmount": "Product Amount", "@productAmount": {"description": "Product amount label"}, "freeShipping": "Free Shipping", "@freeShipping": {"description": "Free shipping label"}, "defaultAddress": "<PERSON><PERSON><PERSON>", "@defaultAddress": {"description": "Default address label"}, "editAddress": "Edit", "@editAddress": {"description": "Edit address button"}, "deleteAddress": "Delete", "@deleteAddress": {"description": "Delete address button"}, "loadAddressFailed": "Failed to load addresses", "@loadAddressFailed": {"description": "Load addresses failed message"}, "setDefaultSuccess": "Set successfully", "@setDefaultSuccess": {"description": "Set default address success message"}, "setDefaultFailed": "Failed to set", "@setDefaultFailed": {"description": "Set default address failed message"}, "addAddress": "Add Address", "@addAddress": {"description": "Add address title"}, "receiverName": "Receiver Name", "@receiverName": {"description": "Receiver name label"}, "enterReceiverName": "Please enter receiver name", "@enterReceiverName": {"description": "Receiver name input hint"}, "enterContactPhone": "Please enter contact phone", "@enterContactPhone": {"description": "Contact phone input hint"}, "detailedAddress": "Detailed Address", "@detailedAddress": {"description": "Detailed address label"}, "enterDetailedAddress": "Please enter detailed address (street, house number, etc.)", "@enterDetailedAddress": {"description": "Detailed address input hint"}, "postalCodeOptional": "Postal Code (Optional)", "@postalCodeOptional": {"description": "Postal code label"}, "enterPostalCode": "Please enter postal code", "@enterPostalCode": {"description": "Postal code input hint"}, "addressLabelOptional": "Address Label (Optional)", "@addressLabelOptional": {"description": "Address label label"}, "enterAddressLabel": "e.g.: Home, Office, School, etc.", "@enterAddressLabel": {"description": "Address label input hint"}, "addressTooShort": "Address must be at least 5 characters", "@addressTooShort": {"description": "Address length validation message"}, "addressTooLong": "Address cannot exceed 200 characters", "@addressTooLong": {"description": "Address length validation message"}, "saveChanges": "Save Changes", "@saveChanges": {"description": "Save changes button"}, "saveAddress": "Save Address", "@saveAddress": {"description": "Save address button"}, "setAsDefaultAddress": "Set as <PERSON><PERSON><PERSON><PERSON>", "@setAsDefaultAddress": {"description": "Set as default address switch"}, "likeSuccess": "Liked successfully", "@likeSuccess": {"description": "Like success message"}, "unlikeSuccess": "Unlike<PERSON> successfully", "@unlikeSuccess": {"description": "Unlike success message"}, "favoriteSuccess": "Added to favorites", "@favoriteSuccess": {"description": "Favorite success message"}, "unfavoriteSuccess": "Removed from favorites", "@unfavoriteSuccess": {"description": "Unfavorite success message"}, "operationFailed": "Operation failed", "@operationFailed": {"description": "Operation failed message"}, "consultDoctor": "Consult Doctor", "@consultDoctor": {"description": "Consult doctor button"}, "doctorDetails": "Details", "@doctorDetails": {"description": "Doctor details title"}, "specialties": "Specialties", "@specialties": {"description": "Specialties label"}, "doctorRecommendations": "Doctor Recommendations", "@doctorRecommendations": {"description": "Doctor recommendations title"}, "workingHours": "Mon-Fri 9:00-17:00", "@workingHours": {"description": "Working hours"}, "cannotOpenPhoneApp": "Cannot open phone app, number copied to clipboard", "@cannotOpenPhoneApp": {"description": "Cannot open phone app message"}, "operationFailedManualDial": "Operation failed, please dial manually", "@operationFailedManualDial": {"description": "Operation failed manual dial message"}, "physician": "Physician", "@physician": {"description": "Physician title"}, "noDescription": "No description available", "@noDescription": {"description": "No description message"}, "viewDetails": "View Details", "@viewDetails": {"description": "View details button"}, "copiedToClipboard": "{content} copied to clipboard", "@copiedToClipboard": {"description": "Copy success message", "placeholders": {"content": {"type": "String"}}}, "copyFailed": "Co<PERSON> failed", "@copyFailed": {"description": "<PERSON><PERSON> failed message"}, "cameraPermissionNeeded": "Cannot open camera, please check permission settings", "@cameraPermissionNeeded": {"description": "Camera permission needed"}, "aiTourGuideRecognition": "Health Assistant Recognition", "@aiTourGuideRecognition": {"description": "Health Assistant recognition"}, "aiTourGuideVoiceRecognition": "Health Assistant Voice Recognition", "@aiTourGuideVoiceRecognition": {"description": "Health Assistant voice recognition"}, "userAvatarFeatureInDevelopment": "User avatar feature in development", "@userAvatarFeatureInDevelopment": {"description": "User avatar feature in development"}, "inDevelopment": "Feature in development", "@inDevelopment": {"description": "In development"}, "close": "Close", "@close": {"description": "Close tooltip"}, "overview": "Overview", "@overview": {"description": "Overview tab"}, "records": "Records", "@records": {"description": "Records tab"}, "team": "Team", "@team": {"description": "Team tab"}, "promotion": "Promotion", "@promotion": {"description": "Promotion tab"}, "loadMoreFailed": "Failed to load more", "@loadMoreFailed": {"description": "Load more failed"}, "getUserListFailed": "Failed to get user list", "@getUserListFailed": {"description": "Get user list failed"}, "levelUpdateSuccess": "User level updated successfully", "@levelUpdateSuccess": {"description": "Level update success"}, "levelUpdateFailed": "Failed to update level", "@levelUpdateFailed": {"description": "Level update failed"}, "certifiedDistributor": "Certified Affiliate", "@certifiedDistributor": {"description": "Certified distributor"}, "fundsDetail": "Funds Detail", "@fundsDetail": {"description": "Funds detail"}, "withdrawing": "Withdrawing", "@withdrawing": {"description": "Withdrawing"}, "withdrawn": "Withdrawn", "@withdrawn": {"description": "Withdrawn"}, "totalCommissionIncome": "Total Commission Income", "@totalCommissionIncome": {"description": "Total commission income"}, "noPromotionPosters": "No promotion posters", "@noPromotionPosters": {"description": "No promotion posters"}, "languageShort": "Chinese", "@languageShort": {"description": "Chinese short"}, "testDescription": "Test Description:", "@testDescription": {"description": "Test description"}, "longPressToSelect": "<PERSON> press the text below to select, copied content only includes Chinese characters.", "@longPressToSelect": {"description": "Long press to select description"}, "smartCopyVersion": "Smart Copy Version:", "@smartCopyVersion": {"description": "Smart copy version"}, "plainTextVersion": "Plain Text Version:", "@plainTextVersion": {"description": "Plain text version"}, "smartCopyTest": "Smart Copy Feature Test", "@smartCopyTest": {"description": "Smart copy feature test"}, "loadHistoryFailed": "Failed to load history: {error}", "@loadHistoryFailed": {"description": "Failed to load history", "placeholders": {"error": {"type": "String"}}}, "clearHistoryFailure": "Failed to clear history: {error}", "@clearHistoryFailure": {"description": "Clear history failure", "placeholders": {"error": {"type": "String"}}}, "voiceTranslationFailure": "Voice translation failed: {error}", "@voiceTranslationFailure": {"description": "Voice translation failure", "placeholders": {"error": {"type": "String"}}}, "cameraAccessFailure": "Cannot open camera, please check permission settings", "@cameraAccessFailure": {"description": "Camera access failure"}, "instructions": "Tap the icons above or double-tap the screen to start translating\nSupports voice input, camera translation, and more", "@instructions": {"description": "Usage instructions"}, "enterPhoneNumber": "Please enter 11-digit phone number", "@enterPhoneNumber": {"description": "Enter phone number hint"}, "enterCorrectVerificationCode": "Please enter correct verification code", "@enterCorrectVerificationCode": {"description": "Enter correct verification code"}, "sendingVerificationCode": "Sending verification code...", "@sendingVerificationCode": {"description": "Sending verification code"}, "verificationCodeSentToPhone": "Verification code sent to your phone", "@verificationCodeSentToPhone": {"description": "Verification code sent to phone"}, "networkConnectionFailed": "Network connection failed, please try again later", "@networkConnectionFailed": {"description": "Network connection failed"}, "networkRequestFailed": "Network request failed, status code: {statusCode}", "@networkRequestFailed": {"description": "Network request failed", "placeholders": {"statusCode": {"type": "String"}}}, "sendVerificationCodeError": "Error sending verification code: {error}", "@sendVerificationCodeError": {"description": "Send verification code error", "placeholders": {"error": {"type": "String"}}}, "resettingPassword": "Resetting password...", "@resettingPassword": {"description": "Resetting password"}, "processingUserDataError": "Error processing user data: {error}", "@processingUserDataError": {"description": "Processing user data error", "placeholders": {"error": {"type": "String"}}}, "loginSuccessButNoUserData": "Login successful but no user data", "@loginSuccessButNoUserData": {"description": "Login success but no user data"}, "userNotRegisteredRedirecting": "User not registered, redirecting to registration page", "@userNotRegisteredRedirecting": {"description": "User not registered redirecting"}, "historyDeleted": "History deleted", "@historyDeleted": {"description": "History deleted"}, "deleteHistoryFailed": "Failed to delete history: {error}", "@deleteHistoryFailed": {"description": "Delete history failed", "placeholders": {"error": {"type": "String"}}}, "noCameraDetected": "No camera detected", "@noCameraDetected": {"description": "No camera detected"}, "cameraInitializationFailed": "Camera initialization failed: {error}", "@cameraInitializationFailed": {"description": "Camera initialization failed", "placeholders": {"error": {"type": "String"}}}, "cameraNotReady": "Camera not ready", "@cameraNotReady": {"description": "Camera not ready"}, "capturePhotoFailed": "Failed to capture photo: {error}", "@capturePhotoFailed": {"description": "Capture photo failed", "placeholders": {"error": {"type": "String"}}}, "galleryPermissionRequired": "Gallery permission required to select images", "@galleryPermissionRequired": {"description": "Gallery permission required"}, "selectImageFailed": "Select image failed: {error}", "@selectImageFailed": {"description": "Select image failed message", "placeholders": {"error": {"type": "String"}}}, "flashlightOperationFailed": "Flashlight operation failed", "@flashlightOperationFailed": {"description": "Flashlight operation failed"}, "switchCameraFailed": "Failed to switch camera", "@switchCameraFailed": {"description": "Switch camera failed"}, "languageNotSupportedAsSource": "{language} is not supported as source language, cannot switch", "@languageNotSupportedAsSource": {"description": "Language not supported as source", "placeholders": {"language": {"type": "String"}}}, "enterUsername": "Please enter username", "@enterUsername": {"description": "Enter username"}, "passwordMinLength6": "Password must be at least 6 characters", "@passwordMinLength6": {"description": "Password minimum length 6"}, "passwordsNotMatch": "Passwords do not match", "@passwordsNotMatch": {"description": "Passwords not match"}, "agreeToUserAgreement": "Please agree to the User Agreement and Privacy Policy", "@agreeToUserAgreement": {"description": "Agree to user agreement"}, "registering": "Registering...", "@registering": {"description": "Registering"}, "registerSuccess": "Registration successful", "@registerSuccess": {"description": "Register success"}, "phoneFormatIncorrect": "Phone number format is incorrect", "@phoneFormatIncorrect": {"description": "Phone format incorrect"}, "verificationCodeExpired": "Verification code is incorrect or expired", "@verificationCodeExpired": {"description": "Verification code expired"}, "usernameAlreadyRegistered": "Username is already registered, please choose another", "@usernameAlreadyRegistered": {"description": "Username already registered"}, "phoneAlreadyRegistered": "This phone number is already registered, you can login directly", "@phoneAlreadyRegistered": {"description": "Phone already registered"}, "registerFailed": "Registration failed: {message}", "@registerFailed": {"description": "Register failed", "placeholders": {"message": {"type": "String"}}}, "registerProcessError": "Error during registration process: {error}", "@registerProcessError": {"description": "Register process error", "placeholders": {"error": {"type": "String"}}}, "openUserAgreement": "Open User Agreement", "@openUserAgreement": {"description": "Open user agreement"}, "openPrivacyPolicy": "Open Privacy Policy", "@openPrivacyPolicy": {"description": "Open privacy policy"}, "priceInfoLoadingWait": "Price information loading, please wait...", "@priceInfoLoadingWait": {"description": "Price info loading wait"}, "priceInfoLoadFailed": "Failed to get price information, showing default prices", "@priceInfoLoadFailed": {"description": "Price info load failed"}, "recordingStartFailed": "Recording failed to start, please check microphone permission", "@recordingStartFailed": {"description": "Recording start failed"}, "recordingStartError": "Recording start error, please try again", "@recordingStartError": {"description": "Recording start error"}, "recordingFailedRetry": "Recording failed, please try again", "@recordingFailedRetry": {"description": "Recording failed retry"}, "audioProcessingFailedRetry": "Audio processing failed, please try again", "@audioProcessingFailedRetry": {"description": "Audio processing failed retry"}, "voiceProcessingError": "Error processing voice: {error}", "@voiceProcessingError": {"description": "Voice processing error", "placeholders": {"error": {"type": "String"}}}, "translationFailedError": "Translation failed: {error}", "@translationFailedError": {"description": "Translation failed", "placeholders": {"error": {"type": "String"}}}, "playbackFailed": "Playback failed: {error}", "@playbackFailed": {"description": "Playback failed", "placeholders": {"error": {"type": "String"}}}, "microphoneRecordingPermissionRequired": "Cannot record: microphone permission required for voice translation", "@microphoneRecordingPermissionRequired": {"description": "Recording microphone permission required"}, "permissionGrantedRetryRecording": "Permission granted, please long press the recording button again to start recording", "@permissionGrantedRetryRecording": {"description": "Permission granted retry recording"}, "logoutFailedError": "<PERSON><PERSON><PERSON> failed: {error}", "@logoutFailedError": {"description": "<PERSON><PERSON><PERSON> failed error", "placeholders": {"error": {"type": "String"}}}, "aiTourGuideRecognitionResult": "Health Assistant recognition: {text}", "@aiTourGuideRecognitionResult": {"description": "Health Assistant recognition result", "placeholders": {"text": {"type": "String"}}}, "aiTourGuideVoiceRecognitionResult": "Health Assistant voice recognition: {text}", "@aiTourGuideVoiceRecognitionResult": {"description": "Health Assistant voice recognition result", "placeholders": {"text": {"type": "String"}}}, "aiGuideVoiceRecognitionFailure": "Health Assistant voice recognition failed", "@aiGuideVoiceRecognitionFailure": {"description": "Health Assistant voice recognition failed message"}, "mapPreparingPleaseWait": "Map is being prepared, please try again later", "@mapPreparingPleaseWait": {"description": "Map preparing please wait"}, "mapTitle": "Map", "@mapTitle": {"description": "Map title"}, "searchingCategory": "Searching {category}...", "@searchingCategory": {"description": "Searching specific category", "placeholders": {"category": {"type": "String"}}}, "tryOtherCategoriesOrCheckNetwork": "Please try other categories or check network connection", "@tryOtherCategoriesOrCheckNetwork": {"description": "Try other categories or check network"}, "noResultsFoundFor": "No {category} found in {city}", "@noResultsFoundFor": {"description": "No results found for category in city", "placeholders": {"category": {"type": "String"}, "city": {"type": "String"}}}, "noRelatedCategoryFound": "No related {category} found", "@noRelatedCategoryFound": {"description": "No related category found", "placeholders": {"category": {"type": "String"}}}, "address": "Address", "@address": {"description": "Address label"}, "addressLabel": "Address: {address}", "@addressLabel": {"description": "Address label", "placeholders": {"address": {"type": "String"}}}, "scenicSpotType": "Scenic Spot", "@scenicSpotType": {"description": "Scenic spot type"}, "navigation": "Navigate", "@navigation": {"description": "Navigation"}, "openNavigationFailed": "Failed to open navigation: {error}", "@openNavigationFailed": {"description": "Open navigation failed", "placeholders": {"error": {"type": "String"}}}, "parksAndSquares": "Parks & Squares", "@parksAndSquares": {"description": "Parks and squares category"}, "parks": "Parks", "@parks": {"description": "Parks category"}, "zoos": "Zoos", "@zoos": {"description": "Zoos category"}, "botanicalGardens": "Botanical Gardens", "@botanicalGardens": {"description": "Botanical gardens category"}, "aquariums": "Aquariums", "@aquariums": {"description": "Aquariums category"}, "citySquares": "City Squares", "@citySquares": {"description": "City squares category"}, "memorialHalls": "Memorial Halls", "@memorialHalls": {"description": "Memorial halls category"}, "templesAndTaoistTemples": "Temples & Taoist Temples", "@templesAndTaoistTemples": {"description": "Temples and Taoist temples category"}, "churches": "Churches", "@churches": {"description": "Churches category"}, "beaches": "Beaches", "@beaches": {"description": "Beaches category"}, "loadDetailsFailed": "Failed to load details: {error}", "@loadDetailsFailed": {"description": "Load details failed", "placeholders": {"error": {"type": "String"}}}, "specialtyFood": "Specialty Food", "@specialtyFood": {"description": "Specialty food"}, "contactInfo": "Contact Info", "@contactInfo": {"description": "Contact information"}, "website": "Website", "@website": {"description": "Website"}, "generatingDetailInfo": "Generating detail information...", "@generatingDetailInfo": {"description": "Generating detail information"}, "viewAiGeneratedDetailedIntroduction": "View AI-generated detailed introduction", "@viewAiGeneratedDetailedIntroduction": {"description": "View AI generated detailed introduction"}, "clickToGetAiGeneratedDetailedIntroduction": "Click to get AI-generated detailed introduction", "@clickToGetAiGeneratedDetailedIntroduction": {"description": "Click to get AI generated detailed introduction"}, "aiGenerating": "AI is generating...", "@aiGenerating": {"description": "AI is generating"}, "generateDetailsFailed": "Failed to generate details, please try again later", "@generateDetailsFailed": {"description": "Generate details failed"}, "openNavigationFailedError": "Failed to open navigation: {error}", "@openNavigationFailedError": {"description": "Open navigation failed error", "placeholders": {"error": {"type": "String"}}}, "addressCopiedToClipboard": "Address copied to clipboard", "@addressCopiedToClipboard": {"description": "Address copied to clipboard"}, "openNavigationFailedWithError": "Failed to open navigation: {error}", "@openNavigationFailedWithError": {"description": "Open navigation failed with error", "placeholders": {"error": {"type": "String"}}}, "mapLoadFailed": "Map Load Failed", "@mapLoadFailed": {"description": "Map load failed"}, "unableToLoadMapPleaseRetry": "Unable to load map, please go back and try again", "@unableToLoadMapPleaseRetry": {"description": "Unable to load map, please retry"}, "back": "Back", "@back": {"description": "Back"}, "locateToCurrentPosition": "Locate to Current Position", "@locateToCurrentPosition": {"description": "Locate to current position"}, "searchLocation": "Search location...", "@searchLocation": {"description": "Search location..."}, "foundLocation": "Found: {name}", "@foundLocation": {"description": "Found location", "placeholders": {"name": {"type": "String"}}}, "mapControllerNotInitialized": "Map controller not initialized, please go back and try again", "@mapControllerNotInitialized": {"description": "Map controller not initialized"}, "locationServiceException": "Location service exception: {error}", "@locationServiceException": {"description": "Location service exception", "placeholders": {"error": {"type": "String"}}}, "mapNotFullyLoaded": "Map not fully loaded, please try again later", "@mapNotFullyLoaded": {"description": "Map not fully loaded"}, "locationFailed": "Location failed: {error}", "@locationFailed": {"description": "Location failed", "placeholders": {"error": {"type": "String"}}}, "profileTitle": "Profile", "@profileTitle": {"description": "Profile page title"}, "vipMemberBadge": "VIP Member", "@vipMemberBadge": {"description": "VIP member badge"}, "normalUserBadge": "Regular User", "@normalUserBadge": {"description": "Normal user badge"}, "distributorLevelBadge": "Affiliate Lv.{level}", "@distributorLevelBadge": {"description": "Distributor level badge", "placeholders": {"level": {"type": "String"}}}, "distributorBadge": "Affiliate", "@distributorBadge": {"description": "Distributor badge"}, "arabic": "Arabic", "@arabic": {"description": "Arabic language"}, "english": "English", "@english": {"description": "English language"}, "languageSwitchNotSupported": "{language} is not supported as source language, cannot switch", "@languageSwitchNotSupported": {"description": "Language switch not supported message", "placeholders": {"language": {"type": "String"}}}, "sourceLanguageLabel": "From", "@sourceLanguageLabel": {"description": "Source language label"}, "targetLanguageLabel": "To", "@targetLanguageLabel": {"description": "Target language label"}, "currentBalance": "Current Balance", "@currentBalance": {"description": "Current balance"}, "todayIncome": "Today's Income", "@todayIncome": {"description": "Today's income"}, "totalIncome": "Total Income", "@totalIncome": {"description": "Total income"}, "holdMicrophoneToSpeak": "Hold microphone to speak", "@holdMicrophoneToSpeak": {"description": "Hold microphone to speak"}, "waitingForOtherParty": "Waiting for other party to speak", "@waitingForOtherParty": {"description": "Waiting for other party to speak"}, "translationFailed": "Translation failed: {error}", "@translationFailed": {"description": "Translation failed", "placeholders": {"error": {"type": "String"}}}, "voiceTranslationFailed": "Voice translation failed", "@voiceTranslationFailed": {"description": "Voice translation failed"}, "confirmClear": "Confirm Clear", "@confirmClear": {"description": "Confirm clear conversation title"}, "confirmClearAllChatRecords": "Are you sure you want to clear all chat records? This action cannot be undone.", "@confirmClearAllChatRecords": {"description": "Confirm clear all chat records"}, "confirmClearAction": "Confirm Clear", "@confirmClearAction": {"description": "Confirm clear action"}, "registerTitle": "Create Account", "@registerTitle": {"description": "Register page title"}, "usernameHint": "Please enter username", "@usernameHint": {"description": "Username input hint"}, "setPassword": "Set password", "@setPassword": {"description": "Set password hint"}, "confirmPassword": "Confirm password", "@confirmPassword": {"description": "Confirm password hint"}, "getCodeButton": "Get Code", "@getCodeButton": {"description": "Get verification code button"}, "countdownSeconds": "{count}s", "@countdownSeconds": {"description": "Countdown seconds", "placeholders": {"count": {"type": "int"}}}, "register": "Sign Up", "@register": {"description": "Register button"}, "phoneNumberLogin": "Phone Login", "@phoneNumberLogin": {"description": "Phone number login button"}, "userAgreementAndPrivacyPolicy": "Terms of Service and Privacy Policy", "@userAgreementAndPrivacyPolicy": {"description": "User agreement and privacy policy"}, "iAgreeToThe": "I have read and agree to the", "@iAgreeToThe": {"description": "I agree to the prefix"}, "helpFeedbackTitle": "Help & Feedback", "@helpFeedbackTitle": {"description": "Help and feedback page title"}, "yourNameOptional": "Your Name (Optional)", "@yourNameOptional": {"description": "Name input field label"}, "yourPhoneNumber": "Your Phone Number", "@yourPhoneNumber": {"description": "Phone number input field label"}, "describeProblemDetail": "Please describe your problem or suggestion in detail", "@describeProblemDetail": {"description": "Problem description input field label"}, "submitFeedback": "Submit <PERSON>", "@submitFeedback": {"description": "Submit feedback button"}, "pleaseEnterCorrectPhoneFormat": "Please enter correct phone number format", "@pleaseEnterCorrectPhoneFormat": {"description": "Phone number format error hint"}, "pleaseDescribeProblem": "Please describe the problem you encountered", "@pleaseDescribeProblem": {"description": "Please describe problem validation hint"}, "descriptionMinLength": "Problem description must be at least 10 characters", "@descriptionMinLength": {"description": "Description minimum length hint"}, "descriptionMaxLength": "Problem description cannot exceed 1000 characters", "@descriptionMaxLength": {"description": "Description maximum length hint"}, "submittingFeedback": "Submitting feedback...", "@submittingFeedback": {"description": "Submitting feedback hint"}, "feedbackSubmittedSuccess": "<PERSON><PERSON><PERSON> submitted successfully, thank you for your suggestion!", "@feedbackSubmittedSuccess": {"description": "<PERSON><PERSON><PERSON> submitted success hint"}, "feedbackSubmissionFailed": "Feedback submission failed: {error}", "@feedbackSubmissionFailed": {"description": "Fe<PERSON><PERSON> submission failed hint", "placeholders": {"error": {"type": "String"}}}, "feedbackInstructions": "Feedback Instructions", "@feedbackInstructions": {"description": "Feedback instructions title"}, "feedbackInstructionsText": "We will collect your problem description and related app data (excluding sensitive information) to better solve your problem. After submission, we will contact you through the phone number you provided.", "@feedbackInstructionsText": {"description": "Feedback instructions text"}, "enterYourName": "Please enter your name", "@enterYourName": {"description": "Enter your name hint"}, "problemDescriptionHint": "Please describe your problem or suggestion in detail\nIncluding:\n• Specific operation steps\n• Expected results\n• What actually happened\n• Other relevant information", "@problemDescriptionHint": {"description": "Problem description hint text"}, "submitting": "Submitting...", "@submitting": {"description": "Submitting status text"}, "testLogGeneration": "Test Log Generation", "@testLogGeneration": {"description": "Test log generation button"}, "viewErrorLogs": "View Error Logs", "@viewErrorLogs": {"description": "View error logs button"}, "generateTestErrors": "Generate Test Errors", "@generateTestErrors": {"description": "Generate test errors button"}, "privacyNotice": "Note: Your privacy is important to us. We do not collect sensitive information such as passwords, only necessary app configuration and log data to help solve problems.", "@privacyNotice": {"description": "Privacy notice text"}, "logGenerationSuccess": "Log Generation Successful", "@logGenerationSuccess": {"description": "Log generation success title"}, "logSize": "Log size: {size}KB", "@logSize": {"description": "Log size text", "placeholders": {"size": {"type": "String"}}}, "testErrorLogsGenerated": "Test error logs generated, you can view them in the error log viewer", "@testErrorLogsGenerated": {"description": "Test error logs generated message"}, "feedbackSubmittedSuccessfully": "Feedback submitted successfully, we will process your issue as soon as possible", "@feedbackSubmittedSuccessfully": {"description": "<PERSON><PERSON><PERSON> submitted successfully message"}, "submissionFailed": "Submission failed", "@submissionFailed": {"description": "Submission failed message"}, "submissionFailedCheckNetwork": "Submission failed, please check network connection", "@submissionFailedCheckNetwork": {"description": "Submission failed check network message"}, "logGenerationFailed": "Log generation failed: {error}", "@logGenerationFailed": {"description": "Log generation failed message", "placeholders": {"error": {"type": "String"}}}, "phoneNumberForContact": "(for problem follow-up)", "@phoneNumberForContact": {"description": "Phone number for contact hint"}, "nickname": "Nickname", "@nickname": {"description": "Nickname field label"}, "nicknameRequired": "Nickname cannot be empty", "@nicknameRequired": {"description": "Nickname required validation message"}, "nicknameMinLength": "Nickname must be at least 2 characters", "@nicknameMinLength": {"description": "Nickname minimum length validation message"}, "changePassword": "Change Password", "@changePassword": {"description": "Change password section title"}, "changePasswordDescription": "Please set your new password to ensure account security.", "@changePasswordDescription": {"description": "Change password page description text"}, "passwordRequirements": "Password Requirements", "@passwordRequirements": {"description": "Password requirements title"}, "passwordLengthRequirement": "Password must be 6-20 characters long", "@passwordLengthRequirement": {"description": "Password length requirement description"}, "accountSettings": "Account <PERSON><PERSON>", "@accountSettings": {"description": "Account settings section title"}, "changePasswordSubtitle": "Change your login password", "@changePasswordSubtitle": {"description": "Change password option subtitle"}, "newPasswordRequired": "Please enter new password", "@newPasswordRequired": {"description": "New password required validation message"}, "confirmNewPassword": "Confirm New Password", "@confirmNewPassword": {"description": "Confirm new password field label"}, "confirmNewPasswordRequired": "Please confirm new password", "@confirmNewPasswordRequired": {"description": "Confirm new password required validation message"}, "changeAvatar": "Change Avatar", "@changeAvatar": {"description": "Change avatar button text"}, "uploading": "Uploading...", "@uploading": {"description": "Uploading status"}, "selectAvatar": "Select Avatar", "@selectAvatar": {"description": "Select avatar dialog title"}, "takePhoto": "Take Photo", "@takePhoto": {"description": "Take photo option"}, "selectFromGallery": "Select from Gallery", "@selectFromGallery": {"description": "Select from gallery option"}, "avatarUploadSuccess": "Avatar uploaded successfully", "@avatarUploadSuccess": {"description": "Avatar upload success message"}, "avatarUploadFailed": "Avatar upload failed", "@avatarUploadFailed": {"description": "Avatar upload failed message"}, "gender": "Gender", "@gender": {"description": "Gender field label"}, "notSet": "Not Set", "@notSet": {"description": "Not set option"}, "birthday": "Birthday", "@birthday": {"description": "Birthday field label"}, "profileSaveSuccess": "Profile saved successfully", "@profileSaveSuccess": {"description": "Profile save success message"}, "saveFailed": "Save failed", "@saveFailed": {"description": "Save failed message"}, "passwordChangeSuccess": "Password changed successfully", "@passwordChangeSuccess": {"description": "Password change success message"}, "cropAvatar": "Crop Avatar", "@cropAvatar": {"description": "Crop avatar dialog title"}, "reselectImage": "Reselect Image", "@reselectImage": {"description": "Reselect image button"}, "confirmCrop": "Confirm", "@confirmCrop": {"description": "Confirm crop button"}, "cannotParseImageFile": "Cannot parse image file", "@cannotParseImageFile": {"description": "Cannot parse image file error message"}, "loadUserProfileFailed": "Failed to load user profile", "@loadUserProfileFailed": {"description": "Load user profile failed message"}, "takePhotoFailed": "Take photo failed: {error}", "@takePhotoFailed": {"description": "Take photo failed message", "placeholders": {"error": {"type": "String"}}}, "avatarUploadFailedButProfileWillSave": "Avatar upload failed, but other profile data will continue to save", "@avatarUploadFailedButProfileWillSave": {"description": "Avatar upload failed but profile will save message"}, "loginExpiredPleaseRelogin": "<PERSON>gin expired, please login again", "@loginExpiredPleaseRelogin": {"description": "Login expired message"}, "processImageFailed": "Process image failed: {error}", "@processImageFailed": {"description": "Process image failed message", "placeholders": {"error": {"type": "String"}}}, "newPasswordMaxLength": "New password cannot exceed 20 characters", "@newPasswordMaxLength": {"description": "New password maximum length validation message"}, "userCardMemberStatusLabel": "Membership:", "@userCardMemberStatusLabel": {"description": "Membership status label in user card"}, "userCardExpiryDateLabel": "Expires on:", "@userCardExpiryDateLabel": {"description": "Expiry date label in user card"}, "userCardUidLabel": "UID:", "@userCardUidLabel": {"description": "UID label in user card"}, "languageOptionChineseSimplified": "Chinese (Simplified)", "@languageOptionChineseSimplified": {"description": "Chinese simplified language option"}, "languageOptionUyghur": "Uyghur", "@languageOptionUyghur": {"description": "Uyghur language option"}, "languageOptionEnglish": "English", "@languageOptionEnglish": {"description": "English language option"}, "languageOptionKazakh": "Kazakh", "@languageOptionKazakh": {"description": "Kazakh language option"}, "languageOptionRussian": "Russian", "@languageOptionRussian": {"description": "Russian language option"}, "languageOptionFrench": "French", "@languageOptionFrench": {"description": "French language option"}, "languageOptionSpanish": "Spanish", "@languageOptionSpanish": {"description": "Spanish language option"}, "languageOptionCantonese": "Cantonese", "@languageOptionCantonese": {"description": "Cantonese language option"}, "languageOptionArabic": "Arabic", "@languageOptionArabic": {"description": "Arabic language option"}, "translationHistoryPageTitle": "Translation History", "@translationHistoryPageTitle": {"description": "Translation history page title"}, "historyListEmpty": "No history yet", "@historyListEmpty": {"description": "Empty history list message"}, "aiGuideVoiceQueryButton": "Voice Query", "@aiGuideVoiceQueryButton": {"description": "Health Assistant voice query button"}, "aiGuidePhotoQueryButton": "Photo Query", "@aiGuidePhotoQueryButton": {"description": "Health Assistant photo query button"}, "aiGuideQueryHint": "Welcome to <PERSON><PERSON><PERSON> Translate. Press and hold the button below to ask by photo.", "@aiGuideQueryHint": {"description": "Health Assistant query hint text"}, "faceToFaceSelectLanguagesHint": "Please select languages for both sides.", "@faceToFaceSelectLanguagesHint": {"description": "Face to face conversation language selection hint"}, "resendCodeTimerLabel": "Resend in {seconds}s", "@resendCodeTimerLabel": {"description": "Resend code timer label", "placeholders": {"seconds": {"type": "String"}}}, "guestUser": "Guest User", "@guestUser": {"description": "Guest user label"}, "pleaseLogin": "Please Login", "@pleaseLogin": {"description": "Please login prompt"}, "membershipStatus": "Membership:", "@membershipStatus": {"description": "Membership status label"}, "expiresOn": "Expires on:", "@expiresOn": {"description": "Expires on label"}, "editProfileButton": "Edit", "@editProfileButton": {"description": "Edit profile button text"}, "notLoggedInUser": "Not Logged In", "@notLoggedInUser": {"description": "Not logged in user status"}, "verificationCodeLoginTitle": "Login with Code", "@verificationCodeLoginTitle": {"description": "Verification code login page title"}, "phoneInputLabel": "Phone Number", "@phoneInputLabel": {"description": "Phone number input field label"}, "phoneInputHint": "Enter phone number", "@phoneInputHint": {"description": "Phone number input field hint"}, "codeInputLabel": "Verification Code", "@codeInputLabel": {"description": "Verification code input field label"}, "codeInputHint": "Enter verification code", "@codeInputHint": {"description": "Verification code input field hint"}, "resendCodeTimerButton": "Resend in {seconds}s", "@resendCodeTimerButton": {"description": "Resend verification code timer button", "placeholders": {"seconds": {"type": "String"}}}, "loginButton": "<PERSON><PERSON>", "@loginButton": {"description": "Login button"}, "autoRegisterHint": "Unregistered numbers will be automatically registered", "@autoRegisterHint": {"description": "Auto registration hint"}, "reminderTitle": "Reminder", "@reminderTitle": {"description": "Reminder dialog title"}, "loginRequiredForDistributionMessage": "Please log in to use the Distribution Management feature", "@loginRequiredForDistributionMessage": {"description": "Distribution management login required message"}, "distributionAccessDeniedMessage": "Your account does not have distributor permissions. You can apply to become a distributor", "@distributionAccessDeniedMessage": {"description": "Distribution management access denied message"}, "goToLoginButton": "Log In", "@goToLoginButton": {"description": "Go to login button"}, "applyButton": "Apply", "@applyButton": {"description": "Apply button"}, "typeMessageHint": "Type a message", "@typeMessageHint": {"description": "Translation page input field hint"}, "verificationCodeSentSeconds": "{seconds}s", "@verificationCodeSentSeconds": {"description": "Verification code countdown display", "placeholders": {"seconds": {"type": "String"}}}, "welcomeBackTitle": "Welcome Back", "@welcomeBackTitle": {"description": "Welcome back title"}, "loginWithPhoneSubtitle": "Please login with your phone number", "@loginWithPhoneSubtitle": {"description": "Login with phone subtitle"}, "registerAccountButton": "Register", "@registerAccountButton": {"description": "Register account button"}, "passwordLoginButton": "Login with Password", "@passwordLoginButton": {"description": "Password login button"}, "loginAgreementText": "By logging in, you agree to the Terms of Service and Privacy Policy", "@loginAgreementText": {"description": "Login agreement text"}, "loginFailed": "<PERSON><PERSON> failed", "@loginFailed": {"description": "<PERSON><PERSON> failed message"}, "loginRequiredForFeature": "Please log in to use {featureName} feature", "@loginRequiredForFeature": {"description": "Feature requires login message", "placeholders": {"featureName": {"type": "String"}}}, "loginRequiredGeneral": "This feature requires login. Please log in first", "@loginRequiredGeneral": {"description": "General login required message"}, "loginButtonText": "<PERSON><PERSON>", "@loginButtonText": {"description": "Login button text"}, "applicationSubmitted": "Application submitted, please wait for review", "@applicationSubmitted": {"description": "Application submitted message"}, "applicationFailed": "Application failed", "@applicationFailed": {"description": "Application failed message"}, "distributorBenefitDescription": "As a distributor, you can promote products and earn commission income", "@distributorBenefitDescription": {"description": "Distributor benefit description"}, "clickRecordToViewFullTranslation": "Tap a record to view full translation on main screen", "@clickRecordToViewFullTranslation": {"description": "History record tap hint"}, "aiGuidePhotoQuestion": "Health Assistant Photo Question", "@aiGuidePhotoQuestion": {"description": "Health Assistant photo question feature name"}, "aiGuideVoiceQuestion": "Health Assistant Voice Question", "@aiGuideVoiceQuestion": {"description": "Health Assistant voice question feature name"}, "recordingStartFailedCheckPermission": "Recording failed to start, please check microphone permission", "@recordingStartFailedCheckPermission": {"description": "Recording start failed permission message"}, "aiGuideName": "Dr. <PERSON>", "@aiGuideName": {"description": "Health Assistant name"}, "aiGuideDescription": "Chief Physician specializing in internal medicine and health management with 15 years of clinical experience", "@aiGuideDescription": {"description": "Health Assistant service description"}, "adjustFontSize": "Adjust Font Size", "@adjustFontSize": {"description": "Adjust font size dialog title"}, "fontPreviewText": "Font Preview 字体预览", "@fontPreviewText": {"description": "Font preview text"}, "smallSize": "Small", "@smallSize": {"description": "Small font size label"}, "largeSize": "Large", "@largeSize": {"description": "Large font size label"}, "currentSizeLabel": "Current Size: {size}", "@currentSizeLabel": {"description": "Current font size label", "placeholders": {"size": {"type": "String"}}}, "smallSizeLabel": "S", "@smallSizeLabel": {"description": "Small font size preset label"}, "mediumSizeLabel": "M", "@mediumSizeLabel": {"description": "Medium font size preset label"}, "largeSizeLabel": "L", "@largeSizeLabel": {"description": "Large font size preset label"}, "vipMembershipTitle": "VIP Membership Benefits", "@vipMembershipTitle": {"description": "VIP membership dialog title"}, "multiLanguageTranslation": "Multi-language Translation", "@multiLanguageTranslation": {"description": "Multi-language translation feature"}, "higherAccuracy": "Higher Accuracy", "@higherAccuracy": {"description": "Higher accuracy feature"}, "adFree": "Ad-free Experience", "@adFree": {"description": "Ad-free feature"}, "unlimitedUsage": "Unlimited Usage", "@unlimitedUsage": {"description": "Unlimited usage feature"}, "monthlyMembership": "Monthly", "@monthlyMembership": {"description": "Monthly membership option"}, "annualMembership": "Annual", "@annualMembership": {"description": "Annual membership option"}, "savePercentage": "Save {percentage}%", "@savePercentage": {"description": "Discount percentage", "placeholders": {"percentage": {"type": "String"}}}, "gettingPriceInfo": "Getting price information...", "@gettingPriceInfo": {"description": "Getting price information message"}, "originalPrice": "Original Price", "@originalPrice": {"description": "Original price field"}, "approximately": "Approx.", "@approximately": {"description": "Approximately"}, "monthlyUnit": "month", "@monthlyUnit": {"description": "Monthly unit"}, "yearlyUnit": "year", "@yearlyUnit": {"description": "Yearly unit"}, "perMonth": "/month", "@perMonth": {"description": "Per month"}, "perYear": "/year", "@perYear": {"description": "Per year"}, "activateVipNow": "Activate VIP Now", "@activateVipNow": {"description": "Activate VIP button"}, "serviceTermsAgreement": "By activating, you agree to the Terms of Service and Privacy Policy", "@serviceTermsAgreement": {"description": "Service terms agreement text"}, "monthlyMemberPackage": "Monthly Plan", "@monthlyMemberPackage": {"description": "Monthly membership package name"}, "annualMemberPackage": "Annual Plan", "@annualMemberPackage": {"description": "Annual membership package name"}, "oneMonthVipPrivileges": "One month VIP privileges", "@oneMonthVipPrivileges": {"description": "One month VIP privileges description"}, "oneYearVipPrivileges": "One year VIP privileges with auto-renewal", "@oneYearVipPrivileges": {"description": "One year VIP privileges description"}, "priceLoadFailed": "Failed to get price information, showing default prices", "@priceLoadFailed": {"description": "Price load failed message"}, "priceInfoLoading": "Price information loading, please wait...", "@priceInfoLoading": {"description": "Price information loading message"}, "aboutToActivate": "About to activate {packageName}, please wait...", "@aboutToActivate": {"description": "About to activate package message", "placeholders": {"packageName": {"type": "String"}}}, "annualVipMember": "Annual VIP", "@annualVipMember": {"description": "Annual VIP member badge"}, "monthlyVipMember": "Monthly VIP", "@monthlyVipMember": {"description": "Monthly VIP member badge"}, "lifetimeVipMember": "Lifetime VIP", "@lifetimeVipMember": {"description": "Lifetime VIP member badge"}, "fontSizeSmall": "Small", "@fontSizeSmall": {"description": "Small font size level"}, "fontSizeMedium": "Medium", "@fontSizeMedium": {"description": "Medium font size level"}, "fontSizeLarge": "Large", "@fontSizeLarge": {"description": "Large font size level"}, "myOrders": "My Orders", "@myOrders": {"description": "My orders menu item"}, "chatHistory": "Chat History", "@chatHistory": {"description": "Chat history tooltip"}, "doctorManagement": "Doctor Management", "@doctorManagement": {"description": "Doctor management menu item"}, "adminManagement": "Admin", "@adminManagement": {"description": "Admin management menu item"}, "myLikes": "My Likes", "@myLikes": {"description": "My likes button"}, "myFavorites": "My Favorites", "@myFavorites": {"description": "My favorites button"}, "shoppingCart": "Shopping Cart", "@shoppingCart": {"description": "Shopping cart button"}, "distributionManagementFeature": "Affiliate Management", "@distributionManagementFeature": {"description": "Distribution management feature name"}, "adminManagementFeature": "Admin Management", "@adminManagementFeature": {"description": "Admin management feature name"}, "doctorManagementFeature": "Doctor Management", "@doctorManagementFeature": {"description": "Doctor management feature name"}, "onlyDoctorUsersCanAccess": "Only doctor users can access doctor management features", "@onlyDoctorUsersCanAccess": {"description": "Doctor access restriction message"}, "viewShoppingCartFeature": "View Shopping Cart", "@viewShoppingCartFeature": {"description": "View shopping cart feature name"}, "pleaseSelectItemsToDelete": "Please select items to delete", "@pleaseSelectItemsToDelete": {"description": "Select items to delete prompt"}, "confirmDelete": "Confirm Delete", "@confirmDelete": {"description": "Confirm delete dialog title"}, "confirmDeleteItems": "Are you sure you want to delete the selected {count} items?", "@confirmDeleteItems": {"description": "Confirm delete items message", "placeholders": {"count": {"type": "int"}}}, "deleteSuccess": "Deleted successfully", "@deleteSuccess": {"description": "Delete success message"}, "deleteFailed": "Delete failed: {error}", "@deleteFailed": {"description": "Delete failed message", "placeholders": {"error": {"type": "String"}}}, "pleaseSelectItemsToCheckout": "Please select items to checkout", "@pleaseSelectItemsToCheckout": {"description": "Select items to checkout prompt"}, "cartTitle": "Shopping Cart", "@cartTitle": {"description": "Shopping cart page title"}, "myOrdersTitle": "My Orders", "@myOrdersTitle": {"description": "My orders page title"}, "myOrdersFeature": "My Orders", "@myOrdersFeature": {"description": "My orders feature name"}, "orderStatusAll": "All", "@orderStatusAll": {"description": "All orders status tab"}, "orderStatusPending": "Pending Payment", "@orderStatusPending": {"description": "Pending payment status"}, "orderStatusPendingShipment": "Pending Shipment", "@orderStatusPendingShipment": {"description": "Pending shipment order status"}, "orderStatusShipped": "Shipped", "@orderStatusShipped": {"description": "Shipped order status"}, "orderStatusCompleted": "Completed", "@orderStatusCompleted": {"description": "Completed order status"}, "orderStatusCancelled": "Cancelled", "@orderStatusCancelled": {"description": "Cancelled order status"}, "orderStatusUnknown": "Unknown Status", "@orderStatusUnknown": {"description": "Unknown order status"}, "payStatusUnpaid": "Unpaid", "@payStatusUnpaid": {"description": "Unpaid status"}, "payStatusPaid": "Paid", "@payStatusPaid": {"description": "Paid status"}, "payStatusRefunded": "Refunded", "@payStatusRefunded": {"description": "Refunded status"}, "product": "Product", "@product": {"description": "Product label"}, "noOrders": "No orders", "@noOrders": {"description": "No orders message"}, "adminManagementTitle": "Admin Management", "@adminManagementTitle": {"description": "Admin management page title"}, "doctorManagementTab": "Doctor Management", "@doctorManagementTab": {"description": "Doctor management tab"}, "productReviewTab": "Product Review", "@productReviewTab": {"description": "Product review tab"}, "orderManagementTab": "Order Management", "@orderManagementTab": {"description": "Order management tab"}, "noDoctorData": "No doctor data", "@noDoctorData": {"description": "No doctor data message"}, "loadDoctorListFailed": "Failed to load doctor list", "@loadDoctorListFailed": {"description": "Failed to load doctor list message"}, "addDoctor": "Add Doctor", "@addDoctor": {"description": "Add doctor button"}, "editDoctor": "Edit Doctor", "@editDoctor": {"description": "Edit doctor button"}, "deleteDoctor": "Delete Doctor", "@deleteDoctor": {"description": "Delete doctor button"}, "confirmDeleteDoctor": "Confirm Delete Doctor", "@confirmDeleteDoctor": {"description": "Confirm delete doctor dialog title"}, "deleteDoctorConfirmMessage": "Are you sure you want to delete doctor {doctor<PERSON><PERSON>}? This action cannot be undone.", "@deleteDoctorConfirmMessage": {"description": "Delete doctor confirmation message", "placeholders": {"doctorName": {"type": "String", "description": "Doctor name"}}}, "deleteDoctorSuccess": "Doctor deleted successfully", "@deleteDoctorSuccess": {"description": "Doctor deleted successfully message"}, "deleteDoctorFailed": "Failed to delete doctor", "@deleteDoctorFailed": {"description": "Failed to delete doctor message"}, "detailedInfo": "Detailed Information", "@detailedInfo": {"description": "Detailed information section"}, "aiSettings": "AI Settings", "@aiSettings": {"description": "AI settings"}, "statusSettings": "Status Settings", "@statusSettings": {"description": "Status settings"}, "enterDoctorName": "Please enter doctor name", "@enterDoctorName": {"description": "Enter doctor name hint"}, "specialty": "Specialty", "@specialty": {"description": "Specialty label"}, "enterSpecialty": "Please enter specialty, e.g.: Cardiology", "@enterSpecialty": {"description": "Enter specialty hint"}, "description": "Description", "@description": {"description": "Description label"}, "enterDescription": "Please enter doctor description", "@enterDescription": {"description": "Enter doctor description hint"}, "enterDetailedDescription": "Please enter detailed description, including ingredients, effects, usage, etc.", "@enterDetailedDescription": {"description": "Detailed description input hint"}, "addSpecialty": "Add Specialty", "@addSpecialty": {"description": "Add specialty button"}, "enterSpecialtyField": "Please enter specialty, e.g.: Coronary Heart Disease", "@enterSpecialtyField": {"description": "Enter specialty field hint"}, "deleteSpecialty": "Delete this specialty", "@deleteSpecialty": {"description": "Delete specialty tooltip"}, "systemPrompt": "System Prompt", "@systemPrompt": {"description": "System prompt"}, "enterSystemPrompt": "Enter AI system prompt to define AI doctor's behavior and response style", "@enterSystemPrompt": {"description": "Enter system prompt hint"}, "avatarUrl": "Avatar URL", "@avatarUrl": {"description": "Avatar URL"}, "enterAvatarUrl": "Enter avatar image URL or click above to upload avatar", "@enterAvatarUrl": {"description": "Enter avatar URL hint"}, "llmModelName": "Model Name", "@llmModelName": {"description": "LLM model name"}, "enterLlmModelName": "Enter LLM model name, e.g.: qwen-max-latest, claude-3-sonnet, etc.", "@enterLlmModelName": {"description": "Enter LLM model name hint"}, "enterYearsOfExperience": "Enter years of experience", "@enterYearsOfExperience": {"description": "Enter years of experience hint"}, "rating": "Rating", "@rating": {"description": "Rating label"}, "enterRating": "Enter rating (0.0-5.0)", "@enterRating": {"description": "Enter rating hint"}, "digitalHumanUrl": "Digital Human URL", "@digitalHumanUrl": {"description": "Digital human URL"}, "enterDigitalHumanUrl": "Enter digital human URL (optional)", "@enterDigitalHumanUrl": {"description": "Enter digital human URL hint"}, "phone": "Phone", "@phone": {"description": "Phone label"}, "enterPhone": "Enter doctor's contact phone", "@enterPhone": {"description": "Enter contact phone hint"}, "enterAddress": "Enter doctor's work address", "@enterAddress": {"description": "Enter work address hint"}, "isActive": "Active Status", "@isActive": {"description": "Active status label"}, "saveDoctor": "Save Doctor", "@saveDoctor": {"description": "Save doctor button"}, "saving": "Saving...", "@saving": {"description": "Saving status"}, "createDoctorSuccess": "Doctor created successfully", "@createDoctorSuccess": {"description": "Create doctor success message"}, "updateDoctorSuccess": "Doctor updated successfully", "@updateDoctorSuccess": {"description": "Update doctor success message"}, "createDoctorFailed": "Failed to create doctor", "@createDoctorFailed": {"description": "Create doctor failed message"}, "updateDoctorFailed": "Failed to update doctor", "@updateDoctorFailed": {"description": "Update doctor failed message"}, "enabled": "Enabled", "@enabled": {"description": "Enabled status"}, "disabled": "Disabled", "@disabled": {"description": "Disabled status"}, "enterValidPhone": "Enter a valid phone number", "@enterValidPhone": {"description": "Enter valid phone number hint"}, "doctorAvatar": "Doctor <PERSON><PERSON>", "@doctorAvatar": {"description": "Doctor avatar"}, "uploadAvatar": "Upload Avatar", "@uploadAvatar": {"description": "Upload avatar button"}, "enableStatus": "Enable Status", "@enableStatus": {"description": "Enable status"}, "doctorEnabledDescription": "Doctor is currently enabled, users can chat with them", "@doctorEnabledDescription": {"description": "Doctor enabled status description"}, "doctorDisabledDescription": "Doctor is currently disabled, users cannot chat with them", "@doctorDisabledDescription": {"description": "Doctor disabled status description"}, "specialtyInputHint": "Tip: Fill in one specialty per input field, they will be automatically merged when saved", "@specialtyInputHint": {"description": "Specialty input hint"}, "confirmExit": "Confirm Exit", "@confirmExit": {"description": "Confirm exit dialog title"}, "unsavedChangesWarning": "You have unsaved changes, are you sure you want to exit?", "@unsavedChangesWarning": {"description": "Unsaved changes warning"}, "exit": "Exit", "@exit": {"description": "Exit button"}, "uploadFailed": "Upload failed", "@uploadFailed": {"description": "Upload failed message"}, "supportedImageFormats": "Supports JPG, PNG formats, max size 5MB", "@supportedImageFormats": {"description": "Supported image formats description"}, "collapse": "Collapse", "@collapse": {"description": "Collapse button"}, "multilingual": "Multilingual", "@multilingual": {"description": "Multilingual button"}, "all": "All", "@all": {"description": "All option"}, "pending": "Pending", "@pending": {"description": "Pending status"}, "approved": "Approved", "@approved": {"description": "Approved status"}, "rejected": "Rejected", "@rejected": {"description": "Rejected status"}, "offline": "Offline", "@offline": {"description": "Offline status"}, "loadDataFailed": "Failed to load data: {error}", "@loadDataFailed": {"description": "Load data failed message", "placeholders": {"error": {"type": "String"}}}, "loadDoctorMultilingualDataFailed": "Failed to load doctor multilingual data", "@loadDoctorMultilingualDataFailed": {"description": "Failed to load doctor multilingual data message"}, "doctorCreationCompleteCallback": "Doctor creation complete callback", "@doctorCreationCompleteCallback": {"description": "Doctor creation complete callback message"}, "enterModelName": "Please enter model name", "@enterModelName": {"description": "Enter model name validation message"}, "imageSizeExceedsLimit": "Image size cannot exceed 5MB", "@imageSizeExceedsLimit": {"description": "Image size exceeds limit message"}, "doctorManagementTitle": "Doctor Management", "@doctorManagementTitle": {"description": "Doctor management page title"}, "productManagementTab": "Product Management", "@productManagementTab": {"description": "Product management tab"}, "dataOverview": "Data Overview", "@dataOverview": {"description": "Data overview title"}, "products": "Products", "@products": {"description": "Products label"}, "pendingReview": "Pending Review", "@pendingReview": {"description": "Pending review status"}, "totalProducts": "Total Products", "@totalProducts": {"description": "Total products count"}, "totalSales": "Total Sales", "@totalSales": {"description": "Total sales amount"}, "totalOrders": "Total Orders", "@totalOrders": {"description": "Total orders count"}, "approvedProducts": "Approved", "@approvedProducts": {"description": "Approved products count"}, "rejectedProducts": "Rejected", "@rejectedProducts": {"description": "Rejected products count"}, "reviewStatus": "Review Status", "@reviewStatus": {"description": "Review status label"}, "inventory": "Inventory", "@inventory": {"description": "Inventory label"}, "salesVolume": "Sales Volume", "@salesVolume": {"description": "Sales volume label"}, "orderOverview": "Order Overview", "@orderOverview": {"description": "Order overview title"}, "shippingStatus": "Shipping Status", "@shippingStatus": {"description": "Shipping status label"}, "paymentStatus": "Payment Status", "@paymentStatus": {"description": "Payment status label"}, "totalOrderNumber": "Total Order Number", "@totalOrderNumber": {"description": "Total order number label"}, "customer": "Customer", "@customer": {"description": "Customer label"}, "pendingPayment": "Pending Payment", "@pendingPayment": {"description": "Pending payment status"}, "pendingShipment": "Pending Shipment", "@pendingShipment": {"description": "Pending shipment status"}, "shipped": "Shipped", "@shipped": {"description": "Shipped status"}, "completed": "Completed", "@completed": {"description": "Completed status"}, "cancelled": "Cancelled", "@cancelled": {"description": "Cancelled status"}, "reviewApproved": "Review approved", "@reviewApproved": {"description": "Review approved note"}, "reviewRejected": "Rejected", "@reviewRejected": {"description": "Review rejected status"}, "offShelf": "Off Shelf", "@offShelf": {"description": "Off shelf status"}, "addProduct": "Add Product", "@addProduct": {"description": "Add product title"}, "editProduct": "Edit Product", "@editProduct": {"description": "Edit product title"}, "enterProductName": "Please enter product name", "@enterProductName": {"description": "Product name input hint"}, "productDescription": "Product Description", "@productDescription": {"description": "Product description title"}, "enterProductDescription": "Please enter product description", "@enterProductDescription": {"description": "Product description input hint"}, "productCategory": "Product Category", "@productCategory": {"description": "Product category field"}, "enterProductCategory": "Please enter product category, e.g.: Health supplements, Medicine, etc.", "@enterProductCategory": {"description": "Product category input hint"}, "enterManufacturer": "Please enter manufacturer name", "@enterManufacturer": {"description": "Manufacturer input hint"}, "productMainImage": "Product Main Image", "@productMainImage": {"description": "Product main image field"}, "priceInfo": "Price Information", "@priceInfo": {"description": "Price information section"}, "currentPrice": "Current Price", "@currentPrice": {"description": "Current price field"}, "enterCurrentPrice": "Please enter current price", "@enterCurrentPrice": {"description": "Current price input hint"}, "enterOriginalPrice": "Please enter original price (optional)", "@enterOriginalPrice": {"description": "Original price input hint"}, "inventoryInfo": "Inventory Information", "@inventoryInfo": {"description": "Inventory information section"}, "inventoryCount": "Inventory Count", "@inventoryCount": {"description": "Inventory count field"}, "enterInventoryCount": "Please enter inventory count", "@enterInventoryCount": {"description": "Inventory count input hint"}, "productDetailImages": "Product Detail Images", "@productDetailImages": {"description": "Product detail images label"}, "productCreatedSuccess": "Product created successfully", "@productCreatedSuccess": {"description": "Product creation success message"}, "productUpdatedSuccess": "Product updated successfully", "@productUpdatedSuccess": {"description": "Product update success message"}, "loadProductMultilingualDataFailed": "Failed to load product multilingual data", "@loadProductMultilingualDataFailed": {"description": "Load product multilingual data failed message"}, "orderNumber": "Order Number", "@orderNumber": {"description": "Order number label"}, "orderTotal": "Total", "@orderTotal": {"description": "Order total label"}, "customerLabel": "Customer: {customer}", "@customerLabel": {"description": "Customer label in order item", "placeholders": {"customer": {"type": "String"}}}, "orderNumberShort": "Order No.", "@orderNumberShort": {"description": "Short order number label"}, "paidStatus": "Paid", "@paidStatus": {"description": "Paid status"}, "unpaidStatus": "Unpaid", "@unpaidStatus": {"description": "Unpaid status"}, "trackingNumber": "Tracking No.", "@trackingNumber": {"description": "Tracking number label"}, "networkImage": "Network Image", "@networkImage": {"description": "Network image label"}, "maxSixImages": "Max 6 images", "@maxSixImages": {"description": "Maximum 6 images hint"}, "addImage": "Add Image", "@addImage": {"description": "Add image button"}, "expressInfo": "Express Info", "@expressInfo": {"description": "Express information title"}, "expressNumber": "Express Number", "@expressNumber": {"description": "Express number label"}, "expressCompany": "Express Company", "@expressCompany": {"description": "Express company label"}, "shippingNote": "Shipping Note", "@shippingNote": {"description": "Shipping note label"}, "noShippingInfo": "No shipping information", "@noShippingInfo": {"description": "No shipping information message"}, "orderCount": "{count} orders", "@orderCount": {"description": "Order count with number", "placeholders": {"count": {"type": "int"}}}, "imageSelected": "Image Selected", "@imageSelected": {"description": "Image selected status"}, "unsavedChangesMessage": "You have unsaved changes, are you sure you want to exit?", "@unsavedChangesMessage": {"description": "Unsaved changes confirmation message"}, "confirmAction": "Confirm", "@confirmAction": {"description": "Confirm action button"}, "noProductsMessage": "No products yet", "@noProductsMessage": {"description": "No products empty state message"}, "addFirstProductHint": "Click the button below to add your first product", "@addFirstProductHint": {"description": "Add first product hint message"}, "noOrdersMessage": "Order data will be displayed here", "@noOrdersMessage": {"description": "No orders message"}, "ordersWillShowHere": "Orders will appear here when customers purchase your products", "@ordersWillShowHere": {"description": "Orders will show here hint message"}, "reviewSuccess": "Review successful", "@reviewSuccess": {"description": "Review success message"}, "reviewFailed": "Review failed", "@reviewFailed": {"description": "Review failed message"}, "batchReviewSuccess": "Batch review successful", "@batchReviewSuccess": {"description": "Batch review success message"}, "batchReviewFailed": "Batch review failed", "@batchReviewFailed": {"description": "Batch review failed message"}, "allDoctors": "All Doctors", "@allDoctors": {"description": "All doctors option"}, "pleaseSelectProducts": "Please select products to review", "@pleaseSelectProducts": {"description": "Please select products hint"}, "batchApproved": "Batch approved", "@batchApproved": {"description": "Batch approved note"}, "batchRejected": "<PERSON><PERSON> rejected", "@batchRejected": {"description": "<PERSON><PERSON> rejected note"}, "batchApprove": "<PERSON><PERSON> Approve", "@batchApprove": {"description": "Batch approve button"}, "batchReject": "<PERSON>ch Reject", "@batchReject": {"description": "Batch reject button"}, "deselectAll": "Deselect All", "@deselectAll": {"description": "Deselect all button"}, "exitSelection": "Exit Selection", "@exitSelection": {"description": "Exit selection button"}, "batchSelection": "Batch Selection", "@batchSelection": {"description": "Batch selection button"}, "reviewStatistics": "Review Statistics", "@reviewStatistics": {"description": "Review statistics title"}, "total": "Total", "@total": {"description": "Total label"}, "sales": "Sales", "@sales": {"description": "Sales label"}, "approve": "Approve", "@approve": {"description": "Approve button"}, "reject": "Reject", "@reject": {"description": "Reject button"}, "rejectReview": "Reject Review", "@rejectReview": {"description": "Reject review title"}, "confirmRejectProduct": "Are you sure you want to reject product \"{productName}\"?", "@confirmRejectProduct": {"description": "Confirm reject product prompt", "placeholders": {"productName": {"type": "String"}}}, "rejectReason": "Reject reason (optional)", "@rejectReason": {"description": "Reject reason input hint"}, "confirmReject": "Confirm Reject", "@confirmReject": {"description": "Confirm reject button"}, "filterDoctors": "Filter <PERSON>", "@filterDoctors": {"description": "Filter doctors label"}, "loadProductDetailFailed": "Failed to load product detail", "@loadProductDetailFailed": {"description": "Load product detail failed message"}, "unknownDoctor": "Unknown Doctor", "@unknownDoctor": {"description": "Unknown doctor name"}, "createdAt": "Created At", "@createdAt": {"description": "Created at label"}, "updatedAt": "Updated At", "@updatedAt": {"description": "Updated at label"}, "productImages": "Product Images", "@productImages": {"description": "Product images title"}, "productSpecifications": "Product Specifications", "@productSpecifications": {"description": "Product specifications title"}, "reviewInfo": "Review Information", "@reviewInfo": {"description": "Review information title"}, "productId": "Product ID", "@productId": {"description": "Product ID label"}, "viewShipping": "View Shipping", "@viewShipping": {"description": "View shipping button"}, "cancelOrder": "Cancel Order", "@cancelOrder": {"description": "Cancel order button"}, "payNow": "Pay Now", "@payNow": {"description": "Pay now button"}, "confirmCancel": "Confirm Cancel", "@confirmCancel": {"description": "Confirm cancel dialog title"}, "confirmCancelOrder": "Are you sure you want to cancel this order?", "@confirmCancelOrder": {"description": "Confirm cancel order message"}, "orderCancelled": "Order cancelled", "@orderCancelled": {"description": "Order cancelled message"}, "cancelOrderFailed": "Cancel order failed: {error}", "@cancelOrderFailed": {"description": "Cancel order failed message", "placeholders": {"error": {"type": "String"}}}, "getPaymentParamsFailed": "Failed to get payment parameters", "@getPaymentParamsFailed": {"description": "Get payment parameters failed message"}, "paymentCancelled": "Payment cancelled", "@paymentCancelled": {"description": "Payment cancelled message"}, "confirmPayment": "Confirm Payment", "@confirmPayment": {"description": "Confirm payment dialog title"}, "paymentAmount": "Payment Amount", "@paymentAmount": {"description": "Payment amount label"}, "confirmPaymentButton": "Confirm Payment", "@confirmPaymentButton": {"description": "Confirm payment button"}, "orderPaidSuccessfully": "Your order has been paid successfully", "@orderPaidSuccessfully": {"description": "Order paid successfully message"}, "currentConversationDeleted": "Current conversation has been deleted", "@currentConversationDeleted": {"description": "Current conversation deleted message"}, "newConversation": "New Conversation", "@newConversation": {"description": "New conversation title"}, "refreshSuccess": "Refresh successful", "@refreshSuccess": {"description": "Refresh success message"}, "refreshFailed": "Refresh failed: {error}", "@refreshFailed": {"description": "Refresh failed message", "placeholders": {"error": {"type": "String"}}}, "titleUpdateSuccess": "Title updated successfully", "@titleUpdateSuccess": {"description": "Title update success message"}, "titleUpdateFailed": "Title update failed: {error}", "@titleUpdateFailed": {"description": "Title update failed message", "placeholders": {"error": {"type": "String", "description": "Error message"}}}, "conversationNotFound": "Conversation not found", "@conversationNotFound": {"description": "Conversation not found error"}, "chatHistoryPageTitle": "Chat History", "@chatHistoryPageTitle": {"description": "Chat history page title"}, "noChatRecordsForDate": "No chat records for this date", "@noChatRecordsForDate": {"description": "No chat records for date message"}, "enterNewTitle": "Please enter a new title", "@enterNewTitle": {"description": "Enter new title hint"}, "year": "", "@year": {"description": "Year unit (empty for English)"}, "month": "", "@month": {"description": "Month unit (empty for English)"}, "monthLabel": "Month:", "@monthLabel": {"description": "Month label"}, "yearLabel": "Year:", "@yearLabel": {"description": "Year label"}, "weekdayMon": "Mon", "@weekdayMon": {"description": "Monday"}, "weekdayTue": "<PERSON><PERSON>", "@weekdayTue": {"description": "Tuesday"}, "weekdayWed": "Wed", "@weekdayWed": {"description": "Wednesday"}, "weekdayThu": "<PERSON>hu", "@weekdayThu": {"description": "Thursday"}, "weekdayFri": "<PERSON><PERSON>", "@weekdayFri": {"description": "Friday"}, "weekdaySat": "Sat", "@weekdaySat": {"description": "Saturday"}, "weekdaySun": "Sun", "@weekdaySun": {"description": "Sunday"}, "selectYearMonth": "Select Year and Month", "@selectYearMonth": {"description": "Select year and month dialog title"}, "ok": "OK", "@ok": {"description": "OK button"}, "digitalHumanChatInDevelopment": "Digital human AI chat page is under development...", "@digitalHumanChatInDevelopment": {"description": "Digital human chat development message"}, "voiceTranslationFeature": "Voice Translation", "@voiceTranslationFeature": {"description": "Voice translation feature name"}, "chatFeature": "Chat Feature", "@chatFeature": {"description": "Chat feature name"}, "voiceFeature": "Voice Feature", "@voiceFeature": {"description": "Voice feature name"}, "chatHistoryFeature": "Chat History", "@chatHistoryFeature": {"description": "Chat history feature name"}, "voiceRecognitionSuccess": "Voice recognition successful", "@voiceRecognitionSuccess": {"description": "Voice recognition success message"}, "voiceRecognitionFailed": "Voice recognition failed, please try again", "@voiceRecognitionFailed": {"description": "Voice recognition failed message"}, "addTextDescriptionOrSendImage": "Add text description or send image directly", "@addTextDescriptionOrSendImage": {"description": "Image preview hint text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh tooltip"}, "noChatRecords": "No chat records", "@noChatRecords": {"description": "No chat records message"}, "filterDoctorsLabel": "Filter <PERSON>", "@filterDoctorsLabel": {"description": "Filter doctors dropdown label"}, "allDoctorsOption": "All Doctors", "@allDoctorsOption": {"description": "All doctors filter option"}, "unknownDoctorLabel": "Unknown Doctor", "@unknownDoctorLabel": {"description": "Unknown doctor label in order list"}, "quantityLabel": "Quantity: {quantity}", "@quantityLabel": {"description": "Quantity label in order item", "placeholders": {"quantity": {"type": "int"}}}, "doctorLabel": "Doctor: {doctor}", "@doctorLabel": {"description": "Doctor label in order item", "placeholders": {"doctor": {"type": "String"}}}, "unitPriceLabel": "Unit Price: ¥{price}", "@unitPriceLabel": {"description": "Unit price label in order item", "placeholders": {"price": {"type": "String"}}}, "totalAmountLabel": "Total: ¥{amount}", "@totalAmountLabel": {"description": "Total amount label in order item", "placeholders": {"amount": {"type": "String"}}}, "orderNumberLabel": "Order No: {orderNumber}", "@orderNumberLabel": {"description": "Order number label in order item", "placeholders": {"orderNumber": {"type": "String"}}}, "adminShipAction": "Admin Ship", "@adminShipAction": {"description": "Admin ship action"}, "markCompleteAction": "Mark Complete", "@markCompleteAction": {"description": "Mark complete action in order menu"}, "markCancelAction": "<PERSON>", "@markCancelAction": {"description": "<PERSON> cancel action in order menu"}, "deleteOrderAction": "Delete Order", "@deleteOrderAction": {"description": "Delete order action"}, "totalOrdersLabel": "Total Orders", "@totalOrdersLabel": {"description": "Total orders label in statistics"}, "totalSalesLabel": "Total Sales", "@totalSalesLabel": {"description": "Total sales label in statistics"}, "completedOrdersLabel": "Completed", "@completedOrdersLabel": {"description": "Completed orders label in statistics"}, "pendingPaymentLabel": "Pending Payment", "@pendingPaymentLabel": {"description": "Pending payment label in statistics"}, "pendingShipmentLabel": "Pending Shipment", "@pendingShipmentLabel": {"description": "Pending shipment label in statistics"}, "cancelledOrdersLabel": "Cancelled", "@cancelledOrdersLabel": {"description": "Cancelled orders label in statistics"}, "ordersUnit": " orders", "@ordersUnit": {"description": "Orders unit suffix"}, "orderStatusUpdateSuccess": "Order status updated successfully", "@orderStatusUpdateSuccess": {"description": "Order status update success message"}, "updateFailed": "Update failed: {error}", "@updateFailed": {"description": "Update failed message", "placeholders": {"error": {"type": "String"}}}, "noOrdersTitle": "No Orders", "@noOrdersTitle": {"description": "No orders title"}, "batchOperationSuccess": "Batch operation successful", "@batchOperationSuccess": {"description": "Batch operation success message"}, "batchOperationFailed": "Batch operation failed: {error}", "@batchOperationFailed": {"description": "Batch operation failed message", "placeholders": {"error": {"type": "String"}}}, "confirmDeleteTitle": "Confirm Delete", "@confirmDeleteTitle": {"description": "Confirm delete dialog title"}, "confirmDeleteMessage": "Are you sure you want to delete this order? This action cannot be undone.", "@confirmDeleteMessage": {"description": "Confirm delete dialog message"}, "cancelAction": "Cancel", "@cancelAction": {"description": "Cancel action button"}, "deleteAction": "Delete", "@deleteAction": {"description": "Delete action button"}, "batchOperationsTitle": "Batch Operations ({count} orders)", "@batchOperationsTitle": {"description": "Batch operations dialog title", "placeholders": {"count": {"type": "int"}}}, "markAsCompletedAction": "<PERSON> as Completed", "@markAsCompletedAction": {"description": "Mark as completed action"}, "markAsCancelledAction": "<PERSON> as Cancelled", "@markAsCancelledAction": {"description": "Mark as cancelled action"}, "markAsShippedAction": "<PERSON> as Shipped", "@markAsShippedAction": {"description": "Mark as shipped action"}, "ordersUnitSuffix": " orders", "@ordersUnitSuffix": {"description": "Orders unit suffix for statistics"}, "orderStatusUpdateSuccessMessage": "Order status updated successfully", "@orderStatusUpdateSuccessMessage": {"description": "Order status update success message"}, "paymentStatusUpdateSuccessMessage": "Payment status updated successfully", "@paymentStatusUpdateSuccessMessage": {"description": "Payment status update success message"}, "orderDeleteSuccessMessage": "Order deleted successfully", "@orderDeleteSuccessMessage": {"description": "Order delete success message"}, "pleaseSelectOrdersMessage": "Please select orders to operate", "@pleaseSelectOrdersMessage": {"description": "Please select orders message"}, "markAsPaidAction": "<PERSON> as <PERSON><PERSON>", "@markAsPaidAction": {"description": "Mark as paid action"}, "orderStatusPendingPayment": "Pending Payment", "@orderStatusPendingPayment": {"description": "Pending payment order status"}, "orderStatusPaid": "Paid", "@orderStatusPaid": {"description": "Paid order status"}, "orderDetailTitle": "Order Details", "@orderDetailTitle": {"description": "Order detail page title"}, "markAsRefundAction": "<PERSON> as Refund", "@markAsRefundAction": {"description": "Mark as refund action"}, "markAsCompleteAction": "Mark as Complete", "@markAsCompleteAction": {"description": "<PERSON> as complete action"}, "markAsCancelAction": "<PERSON> as <PERSON><PERSON>", "@markAsCancelAction": {"description": "<PERSON> as cancel action"}, "waitingForPaymentDescription": "Waiting for customer to complete payment", "@waitingForPaymentDescription": {"description": "Waiting for payment description"}, "waitingForShipmentDescription": "Waiting for doctor to ship", "@waitingForShipmentDescription": {"description": "Waiting for shipment description"}, "shippedDescription": "Product has been shipped, waiting for customer to receive", "@shippedDescription": {"description": "Shipped description"}, "orderCompletedDescription": "Order has been completed", "@orderCompletedDescription": {"description": "Order completed description"}, "orderCancelledDescription": "Order has been cancelled", "@orderCancelledDescription": {"description": "Order cancelled description"}, "orderStatusAbnormalDescription": "Order status is abnormal", "@orderStatusAbnormalDescription": {"description": "Order status abnormal description"}, "orderStatusPendingDescription": "Please complete payment as soon as possible, overdue orders will be automatically cancelled", "@orderStatusPendingDescription": {"description": "Pending payment order status description"}, "orderStatusPreparingDescription": "Your order is being prepared, please wait patiently", "@orderStatusPreparingDescription": {"description": "Order preparing status description"}, "orderStatusShippedUserDescription": "The product has been shipped, please pay attention to receive it", "@orderStatusShippedUserDescription": {"description": "User side shipped status description"}, "orderStatusCompletedUserDescription": "Order completed, thank you for your purchase", "@orderStatusCompletedUserDescription": {"description": "User side order completed status description"}, "orderStatusCancelledUserDescription": "Order has been cancelled", "@orderStatusCancelledUserDescription": {"description": "User side order cancelled status description"}, "shippingStatusWaitingReceive": "Shipped, waiting for delivery", "@shippingStatusWaitingReceive": {"description": "Waiting for delivery status"}, "shippingStatusCompleted": "Completed", "@shippingStatusCompleted": {"description": "Shipping completed status"}, "shippingStatusShipped": "Shipped", "@shippingStatusShipped": {"description": "Shipped status"}, "shippingStatusPending": "Pending Payment", "@shippingStatusPending": {"description": "Shipping pending payment status"}, "shippingStatusWaitingShip": "Pending Shipment", "@shippingStatusWaitingShip": {"description": "Shipping waiting for shipment status"}, "shippingStatusCancelled": "Cancelled", "@shippingStatusCancelled": {"description": "Shipping cancelled status"}, "shippingStatusUnknown": "Unknown Status", "@shippingStatusUnknown": {"description": "Shipping unknown status"}, "insufficientPermissionDoctorRequired": "Insufficient permission, doctor permission required", "@insufficientPermissionDoctorRequired": {"description": "Insufficient permission doctor required error message"}, "getPendingShipmentOrdersFailed": "Failed to get pending shipment orders", "@getPendingShipmentOrdersFailed": {"description": "Get pending shipment orders failed error message"}, "trackingNumberCannotBeEmpty": "Tracking number cannot be empty", "@trackingNumberCannotBeEmpty": {"description": "Tracking number cannot be empty error message"}, "shipmentFailed": "Shipment failed", "@shipmentFailed": {"description": "Shipment failed error message"}, "orderNotExistOrNoAccess": "Order does not exist or no access", "@orderNotExistOrNoAccess": {"description": "Order not exist or no access error message"}, "shipmentFailedCheckOrderStatus": "Shipment failed, please check order status", "@shipmentFailedCheckOrderStatus": {"description": "Shipment failed check order status error message"}, "getShippingStatusFailed": "Failed to get shipping status", "@getShippingStatusFailed": {"description": "Get shipping status failed error message"}, "getShippedOrdersFailed": "Failed to get shipped orders", "@getShippedOrdersFailed": {"description": "Get shipped orders failed error message"}, "productInfoTitle": "Product Information", "@productInfoTitle": {"description": "Product information title"}, "orderInfoTitle": "Order Information", "@orderInfoTitle": {"description": "Order information title"}, "orderNumberFieldLabel": "Order No.", "@orderNumberFieldLabel": {"description": "Order number field label"}, "orderTimeLabel": "Order Time", "@orderTimeLabel": {"description": "Order time label"}, "paymentTimeLabel": "Payment Time", "@paymentTimeLabel": {"description": "Payment time label"}, "shipmentTimeLabel": "Shipment Time", "@shipmentTimeLabel": {"description": "Shipment time label"}, "completionTimeLabel": "Completion Time", "@completionTimeLabel": {"description": "Completion time label"}, "customerInfoTitle": "Customer Information", "@customerInfoTitle": {"description": "Customer information title"}, "customerNicknameLabel": "Customer Nickname", "@customerNicknameLabel": {"description": "Customer nickname label"}, "userIdLabel": "User ID", "@userIdLabel": {"description": "User ID label"}, "shippingInfoTitle": "Shipping Information", "@shippingInfoTitle": {"description": "Shipping information title"}, "recipientLabel": "Recipient", "@recipientLabel": {"description": "Recipient label"}, "contactPhoneLabel": "Contact Phone", "@contactPhoneLabel": {"description": "Contact phone label"}, "shippingAddressLabel": "Shipping Address", "@shippingAddressLabel": {"description": "Shipping address label"}, "trackingInfoTitle": "Tracking Information", "@trackingInfoTitle": {"description": "Tracking information title"}, "viewDetailsAction": "View Details", "@viewDetailsAction": {"description": "View details action"}, "trackingNumberLabel": "Tracking Number", "@trackingNumberLabel": {"description": "Tracking number label"}, "shippingCompanyLabel": "Shipping Company", "@shippingCompanyLabel": {"description": "Shipping company label"}, "shippingNoteLabel": "Shipping Note", "@shippingNoteLabel": {"description": "Shipping note label"}, "priceDetailsTitle": "Price Details", "@priceDetailsTitle": {"description": "Price details title"}, "productAmountLabel": "Product Amount", "@productAmountLabel": {"description": "Product amount label"}, "shippingFeeLabel": "Shipping Fee", "@shippingFeeLabel": {"description": "Shipping fee label"}, "totalPaidLabel": "Total Paid", "@totalPaidLabel": {"description": "Total paid label"}, "cancelOrderAction": "Cancel Order", "@cancelOrderAction": {"description": "Cancel order action"}, "viewTrackingAction": "View Tracking", "@viewTrackingAction": {"description": "View tracking action"}, "copiedToClipboardMessage": "Copied to clipboard", "@copiedToClipboardMessage": {"description": "Copied to clipboard message"}, "confirmDeleteOrderTitle": "Confirm Delete", "@confirmDeleteOrderTitle": {"description": "Confirm delete order title"}, "confirmDeleteOrderMessage": "Are you sure you want to delete this order? This action cannot be undone.", "@confirmDeleteOrderMessage": {"description": "Confirm delete order message"}, "orderDetailLoadFailedMessage": "Failed to load order details", "@orderDetailLoadFailedMessage": {"description": "Order detail load failed message"}, "orderInfoLoadFailedMessage": "Order information failed to load", "@orderInfoLoadFailedMessage": {"description": "Order info load failed message"}, "updateFailedMessage": "Update failed: {error}", "@updateFailedMessage": {"description": "Update failed message", "placeholders": {"error": {"type": "String"}}}, "deleteFailedMessage": "Delete failed: {error}", "@deleteFailedMessage": {"description": "Delete failed message", "placeholders": {"error": {"type": "String"}}}, "editConversationTitle": "Edit Conversation Title", "@editConversationTitle": {"description": "Edit conversation title dialog title"}, "replying": "Replying...", "@replying": {"description": "Replying status text"}, "imageLoadFailed": "Image load failed", "@imageLoadFailed": {"description": "Image load failed message"}, "imageNotAvailable": "Image not available", "@imageNotAvailable": {"description": "Image not available message"}, "releaseToCancel": "Release to cancel recording", "@releaseToCancel": {"description": "Release to cancel recording text"}, "recording": "Recording", "@recording": {"description": "Recording status text"}, "slideUpToCancel": "Slide up to cancel", "@slideUpToCancel": {"description": "Slide up to cancel hint"}, "continueSlideUpToCancel": "Continue sliding up to cancel recording", "@continueSlideUpToCancel": {"description": "Continue slide up to cancel hint"}, "takePhotoAndSend": "Take Photo & Send", "@takePhotoAndSend": {"description": "Take photo and send page title"}, "selectSendArea": "Select Send Area", "@selectSendArea": {"description": "Select send area page title"}, "send": "Send", "@send": {"description": "Send button"}, "orderTimeline": "Order Timeline", "@orderTimeline": {"description": "Order timeline title"}, "orderCreated": "Order Created", "@orderCreated": {"description": "Order created status"}, "paymentCompleted": "Payment Completed", "@paymentCompleted": {"description": "Payment completed status"}, "goodsShipped": "Goods Shipped", "@goodsShipped": {"description": "Goods shipped status"}, "orderCompleted": "Order Completed", "@orderCompleted": {"description": "Order completed status"}, "shipOrder": "Ship Order", "@shipOrder": {"description": "Ship order dialog title"}, "confirmShip": "Confirm Ship", "@confirmShip": {"description": "Confirm ship button"}, "ship": "Ship", "@ship": {"description": "Ship button"}, "enterTrackingNumber": "Please enter tracking number", "@enterTrackingNumber": {"description": "Enter tracking number hint"}, "trackingNumberRequired": "Please enter tracking number", "@trackingNumberRequired": {"description": "Tracking number required validation"}, "selectShippingCompany": "Please select shipping company (optional)", "@selectShippingCompany": {"description": "Select shipping company hint"}, "enterShippingNote": "Please enter shipping note (optional)", "@enterShippingNote": {"description": "Enter shipping note hint"}, "shipSuccess": "Shipped successfully", "@shipSuccess": {"description": "Ship success message"}, "shipFailed": "Ship failed: {error}", "@shipFailed": {"description": "Ship failed message", "placeholders": {"error": {"type": "String"}}}, "productLabel": "Product", "@productLabel": {"description": "Product label"}, "adminShipment": "Admin Shipment", "@adminShipment": {"description": "Admin shipment title"}, "trackingNumberRequiredField": "Tracking Number *", "@trackingNumberRequiredField": {"description": "Tracking number required field label"}, "shippingCompanyHint": "e.g.: SF Express, YTO Express, etc.", "@shippingCompanyHint": {"description": "Shipping company input hint"}, "shippingNoteHint": "Optional shipping instructions or notes", "@shippingNoteHint": {"description": "Shipping note input hint"}, "shipmentSuccess": "Shipment successful", "@shipmentSuccess": {"description": "Shipment success message"}, "shipmentFailedWithError": "Shipment failed: {error}", "@shipmentFailedWithError": {"description": "Shipment failed error message", "placeholders": {"error": {"type": "String", "description": "Error information"}}}, "copiedToClipboardWithTitle": "{title} copied to clipboard", "@copiedToClipboardWithTitle": {"description": "Copy success message with title", "placeholders": {"title": {"type": "String", "description": "Title of copied content"}}}, "voiceRecognizing": "Recognizing voice...", "@voiceRecognizing": {"description": "Voice recognition in progress"}, "voiceRecognitionRetry": "Voice recognition failed, please try again", "@voiceRecognitionRetry": {"description": "Voice recognition failed retry message"}, "cannotOpenPhoneAppCopied": "Cannot open phone app, number copied to clipboard", "@cannotOpenPhoneAppCopied": {"description": "Cannot open phone app message"}, "operationFailedManualDialWithNumber": "Operation failed, please dial manually: {phoneNumber}", "@operationFailedManualDialWithNumber": {"description": "Operation failed manual dial message", "placeholders": {"phoneNumber": {"type": "String", "description": "Phone number"}}}, "healthAssistantVoiceRecognition": "Health Assistant voice recognition: {text}", "@healthAssistantVoiceRecognition": {"description": "Health Assistant voice recognition result", "placeholders": {"text": {"type": "String", "description": "Recognized text"}}}, "healthAssistantVoiceProcessingFailed": "Health Assistant voice processing failed: {error}", "@healthAssistantVoiceProcessingFailed": {"description": "Health Assistant voice processing failed", "placeholders": {"error": {"type": "String", "description": "Error information"}}}, "loginFailedWithMessage": "<PERSON><PERSON> failed: {message}", "@loginFailedWithMessage": {"description": "<PERSON><PERSON> failed message", "placeholders": {"message": {"type": "String", "description": "Error message"}}}, "verificationCodeIncorrectOrExpired": "Verification code is incorrect or expired", "@verificationCodeIncorrectOrExpired": {"description": "Verification code incorrect or expired"}, "usernameAlreadyExists": "Username already exists, please choose another", "@usernameAlreadyExists": {"description": "Username already exists"}, "editProfileTitle": "Edit Profile", "@editProfileTitle": {"description": "Edit profile page title"}, "healthInfo": "Health Information", "@healthInfo": {"description": "Health information title"}, "heightHint": "Enter height (cm)", "@heightHint": {"description": "Height input hint"}, "heightValidation": "Please enter a valid height (50-250cm)", "@heightValidation": {"description": "Height validation message"}, "weightHint": "Enter weight (kg)", "@weightHint": {"description": "Weight input hint"}, "weightValidation": "Please enter a valid weight (20-300kg)", "@weightValidation": {"description": "Weight validation message"}, "selectBloodType": "Select blood type", "@selectBloodType": {"description": "Blood type selection hint"}, "bloodTypeA": "Type A", "@bloodTypeA": {"description": "Blood type A"}, "bloodTypeB": "Type B", "@bloodTypeB": {"description": "Blood type B"}, "bloodTypeAB": "Type AB", "@bloodTypeAB": {"description": "Blood type AB"}, "bloodTypeO": "Type O", "@bloodTypeO": {"description": "Blood type O"}, "bloodTypeUnknown": "Unknown", "@bloodTypeUnknown": {"description": "Blood type unknown"}, "residentialAddress": "Residential Address", "@residentialAddress": {"description": "Residential address label"}, "locate": "Locate", "@locate": {"description": "Locate button"}, "selectResidentialAddress": "Select residential address", "@selectResidentialAddress": {"description": "Select residential address hint"}, "regionSelectionFailed": "Region selection failed, please try again", "@regionSelectionFailed": {"description": "Region selection failed message"}, "commonAllergens": "Common Allergens", "@commonAllergens": {"description": "Common allergens title"}, "penicillinAllergy": "Penicillin drugs", "@penicillinAllergy": {"description": "Penicillin allergy"}, "cephalosporinAllergy": "Cephalosporin drugs", "@cephalosporinAllergy": {"description": "Cephalosporin allergy"}, "aspirinAllergy": "<PERSON><PERSON><PERSON>", "@aspirinAllergy": {"description": "Aspirin allergy"}, "peanutAllergy": "Peanuts", "@peanutAllergy": {"description": "Peanut allergy"}, "seafoodAllergy": "Seafood", "@seafoodAllergy": {"description": "Seafood allergy"}, "milkAllergy": "Milk", "@milkAllergy": {"description": "Milk allergy"}, "eggAllergy": "Eggs", "@eggAllergy": {"description": "Egg allergy"}, "pollenDustMiteAllergy": "Pollen/Dust mites", "@pollenDustMiteAllergy": {"description": "Pollen dust mite allergy"}, "otherAllergens": "Other Allergens", "@otherAllergens": {"description": "Other allergens label"}, "otherAllergensHint": "Please specify other allergens", "@otherAllergensHint": {"description": "Other allergens input hint"}, "takingMedication": "Are you currently taking any medications", "@takingMedication": {"description": "Taking medication question"}, "medicationList": "Medication List", "@medicationList": {"description": "Medication list label"}, "medicationListHint": "Please list the names, dosages and frequencies of medications you are taking", "@medicationListHint": {"description": "Medication list input hint"}, "hasChronicDisease": "Do you have any chronic diseases", "@hasChronicDisease": {"description": "Has chronic disease question"}, "specificSymptoms": "Specific Conditions", "@specificSymptoms": {"description": "Specific symptoms title"}, "hypertension": "Hypertension", "@hypertension": {"description": "Hypertension"}, "bloodPressureHint": "e.g. 130/85 mmHg", "@bloodPressureHint": {"description": "Blood pressure input hint"}, "diabetes": "Diabetes", "@diabetes": {"description": "Diabetes"}, "bloodSugarHint": "e.g. 5.8 mmol/L", "@bloodSugarHint": {"description": "Blood sugar input hint"}, "otherChronicDiseases": "Other Chronic Diseases", "@otherChronicDiseases": {"description": "Other chronic diseases label"}, "otherChronicDiseasesHint": "Please specify other chronic diseases", "@otherChronicDiseasesHint": {"description": "Other chronic diseases input hint"}, "surgeryHistory": "Surgery & Hospitalization History", "@surgeryHistory": {"description": "Surgery history title"}, "hasSurgeryHistory": "Have you had any surgeries or hospitalizations in the past", "@hasSurgeryHistory": {"description": "Has surgery history question"}, "surgeryDetails": "Details", "@surgeryDetails": {"description": "Surgery details label"}, "surgeryDetailsHint": "Please describe the details of surgeries or hospitalizations", "@surgeryDetailsHint": {"description": "Surgery details input hint"}, "familyHistory": "Family History", "@familyHistory": {"description": "Family history title"}, "familyDiseaseHistory": "Do any immediate family members (parents, siblings, children) have the following diseases", "@familyDiseaseHistory": {"description": "Family disease history question"}, "familyHypertension": "Hypertension", "@familyHypertension": {"description": "Family hypertension history"}, "familyDiabetes": "Diabetes", "@familyDiabetes": {"description": "Family diabetes history"}, "familyHeartDisease": "Heart Disease", "@familyHeartDisease": {"description": "Family heart disease history"}, "familyStroke": "Stroke", "@familyStroke": {"description": "Family stroke history"}, "familyCancer": "Cancer", "@familyCancer": {"description": "Family cancer history"}, "familyMentalHealth": "Mental Health Disorders", "@familyMentalHealth": {"description": "Family mental health history"}, "otherFamilyHistory": "Other Family History", "@otherFamilyHistory": {"description": "Other family history label"}, "otherFamilyHistoryHint": "Please specify other family medical history", "@otherFamilyHistoryHint": {"description": "Other family history input hint"}, "exerciseSedentary": "Sedentary (little to no exercise)", "@exerciseSedentary": {"description": "Sedentary exercise frequency"}, "exerciseLight": "Lightly active (1-2 times per week)", "@exerciseLight": {"description": "Light exercise frequency"}, "exerciseModerate": "Moderately active (3-5 times per week)", "@exerciseModerate": {"description": "Moderate exercise frequency"}, "exerciseActive": "Very active (6+ times per week)", "@exerciseActive": {"description": "High exercise frequency"}, "dietaryPreferences": "Dietary Preferences", "@dietaryPreferences": {"description": "Dietary preferences label"}, "balancedDiet": "Balanced diet", "@balancedDiet": {"description": "Balanced diet"}, "vegetarianDiet": "Prefer vegetarian", "@vegetarianDiet": {"description": "Vegetarian diet preference"}, "meatDiet": "Prefer meat", "@meatDiet": {"description": "Meat diet preference"}, "oilyFood": "Prefer oily food", "@oilyFood": {"description": "Oily food preference"}, "saltyFood": "Prefer salty food", "@saltyFood": {"description": "Salty food preference"}, "sweetFood": "Prefer sweet food", "@sweetFood": {"description": "Sweet food preference"}, "neverSmoke": "Never smoke", "@neverSmoke": {"description": "Never smoke"}, "quitSmoking": "Quit smoking", "@quitSmoking": {"description": "Quit smoking"}, "occasionalSmoking": "Occasional smoking (not daily)", "@occasionalSmoking": {"description": "Occasional smoking"}, "dailySmoking": "Regular smoking (daily)", "@dailySmoking": {"description": "Daily smoking"}, "neverDrink": "Never drink", "@neverDrink": {"description": "Never drink"}, "quitDrinking": "Quit drinking", "@quitDrinking": {"description": "Quit drinking"}, "socialDrinking": "Occasional social drinking", "@socialDrinking": {"description": "Social drinking"}, "weeklyDrinking": "1-3 times per week", "@weeklyDrinking": {"description": "Weekly drinking"}, "dailyDrinking": "Almost daily", "@dailyDrinking": {"description": "Daily drinking"}, "sleepLessThan6": "Less than 6 hours", "@sleepLessThan6": {"description": "Less than 6 hours sleep"}, "sleep6To7": "6-7 hours", "@sleep6To7": {"description": "6-7 hours sleep"}, "sleep7To8": "7-8 hours", "@sleep7To8": {"description": "7-8 hours sleep"}, "sleepMoreThan8": "More than 8 hours", "@sleepMoreThan8": {"description": "More than 8 hours sleep"}, "sleepGood": "Good (easy to fall asleep, rarely wake up)", "@sleepGood": {"description": "Good sleep quality"}, "sleepFair": "Fair (occasional difficulty falling asleep or early waking)", "@sleepFair": {"description": "Fair sleep quality"}, "sleepPoor": "Poor (chronic difficulty falling asleep, frequent dreams, early waking)", "@sleepPoor": {"description": "Poor sleep quality"}, "stressLow": "Very low", "@stressLow": {"description": "Low stress"}, "stressMild": "Mild stress", "@stressMild": {"description": "Mild stress"}, "stressModerate": "Moderate stress", "@stressModerate": {"description": "Moderate stress"}, "stressHigh": "High stress", "@stressHigh": {"description": "High stress"}, "stressExtreme": "Extreme stress", "@stressExtreme": {"description": "Extreme stress"}, "womenHealth": "Women's Health", "@womenHealth": {"description": "Women's health title"}, "isMenopause": "Have you reached menopause", "@isMenopause": {"description": "Menopause question"}, "menstrualCycleRegular": "Is your menstrual cycle regular", "@menstrualCycleRegular": {"description": "Menstrual cycle regularity question"}, "menstrualRegular": "Regular", "@menstrualRegular": {"description": "Regular menstrual cycle"}, "menstrualIrregular": "Irregular", "@menstrualIrregular": {"description": "Irregular menstrual cycle"}, "menstrualUncertain": "Uncertain", "@menstrualUncertain": {"description": "Uncertain menstrual cycle"}, "hasPregnancy": "Have you ever been pregnant", "@hasPregnancy": {"description": "Pregnancy history question"}, "birthCount": "Number of births", "@birthCount": {"description": "Birth count label"}, "birthCount0": "0 times", "@birthCount0": {"description": "0 births"}, "birthCount1": "1 time", "@birthCount1": {"description": "1 birth"}, "birthCount2": "2 times", "@birthCount2": {"description": "2 births"}, "birthCount3": "3 times", "@birthCount3": {"description": "3 births"}, "birthCount4": "4 times", "@birthCount4": {"description": "4 births"}, "birthCount5Plus": "5 or more times", "@birthCount5Plus": {"description": "5 or more births"}, "cannotParseImage": "Cannot parse image file", "@cannotParseImage": {"description": "Cannot parse image file error"}, "dpiAdaptationSettings": "DPI Adaptation Settings", "@dpiAdaptationSettings": {"description": "DPI adaptation settings page title"}, "dpiAdaptationDescription": "Adjust the app's display scaling ratio to adapt to different screen densities", "@dpiAdaptationDescription": {"description": "DPI adaptation settings page description"}, "currentDpiScale": "Current Scale Ratio", "@currentDpiScale": {"description": "Current DPI scale ratio"}, "systemDefault": "System Default", "@systemDefault": {"description": "System default option"}, "small": "Small", "@small": {"description": "Small size option"}, "normal": "Normal", "@normal": {"description": "Normal size option"}, "large": "Large", "@large": {"description": "Large size option"}, "extraLarge": "Extra Large", "@extraLarge": {"description": "Extra large size option"}, "previewText": "Preview Text", "@previewText": {"description": "Preview text label"}, "sampleText": "This is a sample text to preview the current scaling effect.", "@sampleText": {"description": "Sample preview text"}, "applyChanges": "Apply Changes", "@applyChanges": {"description": "Apply changes button"}, "resetToDefault": "Reset to De<PERSON>ult", "@resetToDefault": {"description": "Reset to default button"}, "dpiSettingsApplied": "DPI settings applied", "@dpiSettingsApplied": {"description": "DPI settings applied success message"}, "dpiSettingsReset": "DPI settings reset to default", "@dpiSettingsReset": {"description": "DPI settings reset success message"}, "dpiModeAuto": "Auto Adaptation", "@dpiModeAuto": {"description": "DPI auto adaptation mode"}, "dpiModeAutoDesc": "Automatically adjust interface size based on device DPI (Recommended)", "@dpiModeAutoDesc": {"description": "DPI auto adaptation mode description"}, "dpiModeSmall": "Compact Mode", "@dpiModeSmall": {"description": "DPI compact mode"}, "dpiModeSmallDesc": "Smaller interface elements, suitable for high DPI devices", "@dpiModeSmallDesc": {"description": "DPI compact mode description"}, "dpiModeStandard": "Standard Mode", "@dpiModeStandard": {"description": "DPI standard mode"}, "dpiModeStandardDesc": "Default size interface elements", "@dpiModeStandardDesc": {"description": "DPI standard mode description"}, "dpiModeLarge": "Relaxed Mode", "@dpiModeLarge": {"description": "DPI relaxed mode"}, "dpiModeLargeDesc": "Larger interface elements, suitable for low DPI devices", "@dpiModeLargeDesc": {"description": "DPI relaxed mode description"}, "currentStatus": "Current Status", "@currentStatus": {"description": "Current status title"}, "adaptationMode": "Adaptation Mode", "@adaptationMode": {"description": "Adaptation mode label"}, "scaleFactor": "Scale Factor", "@scaleFactor": {"description": "Scale factor label"}, "deviceInfo": "Device Information", "@deviceInfo": {"description": "Device information title"}, "screenSize": "Screen Size", "@screenSize": {"description": "Screen size label"}, "devicePixelRatio": "<PERSON><PERSON> Pixel Ratio", "@devicePixelRatio": {"description": "Device pixel ratio label"}, "screenDiagonal": "Screen Diagonal", "@screenDiagonal": {"description": "Screen diagonal label"}, "autoScaleFactor": "Auto Scale Factor", "@autoScaleFactor": {"description": "Auto scale factor label"}, "effectPreview": "Effect Preview", "@effectPreview": {"description": "Effect preview title"}, "sampleButton": "<PERSON><PERSON>", "@sampleButton": {"description": "Sample button text"}, "titleText": "Title Text", "@titleText": {"description": "Title text example"}, "sampleDescription": "This is a sample text to preview the current DPI adaptation settings effect.", "@sampleDescription": {"description": "Sample description text"}, "inches": "inches", "@inches": {"description": "Inches unit"}, "dpiAdaptation": "DPI Adaptation", "@dpiAdaptation": {"description": "DPI adaptation setting option title"}, "dpiAdaptationSubtitle": "Adjust interface element size to adapt to different DPI devices", "@dpiAdaptationSubtitle": {"description": "DPI adaptation setting option description"}}