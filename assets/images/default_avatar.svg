<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A80F0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5E92F3;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="64" fill="url(#avatarGradient)"/>
  
  <!-- 用户图标 -->
  <g transform="translate(32, 28)">
    <!-- 头部 -->
    <circle cx="32" cy="24" r="16" fill="white" fill-opacity="0.9"/>
    
    <!-- 身体 -->
    <path d="M12 56 C12 48, 20 42, 32 42 C44 42, 52 48, 52 56 L52 72 L12 72 Z" fill="white" fill-opacity="0.9"/>
  </g>
</svg> 