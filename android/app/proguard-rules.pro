# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.

# Flutter相关混淆规则
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# 百度地图混淆规则已移除，现使用flutter_map + Azure Maps

# 权限处理相关
-keep class com.baseflow.permissionhandler.** { *; }

# 音频录制相关
-keep class com.llfbandit.record.** { *; }

# 相机相关
-keep class io.flutter.plugins.camera.** { *; }

# HTTP相关
-keepattributes Signature
-keepattributes *Annotation*
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.** 