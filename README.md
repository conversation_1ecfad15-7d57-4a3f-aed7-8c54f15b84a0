# 健康助手 (Health Assistant)

<div align="center">
  <img src="assets/images/app_logo.png" alt="Health Assistant Logo" width="120" height="120">

  <h3>智能健康管理与AI医生咨询应用</h3>

  [![Flutter](https://img.shields.io/badge/Flutter-3.8.1+-02569B?style=flat&logo=flutter)](https://flutter.dev)
  [![Dart](https://img.shields.io/badge/Dart-3.8.1+-0175C2?style=flat&logo=dart)](https://dart.dev)
  [![License](https://img.shields.io/badge/License-Private-red?style=flat)](LICENSE)
  [![Platform](https://img.shields.io/badge/Platform-Android%20%7C%20iOS-lightgrey?style=flat)](https://flutter.dev/docs/deployment)
  [![Version](https://img.shields.io/badge/Version-1.0.3-green?style=flat)](pubspec.yaml)
</div>

## 📖 项目简介

健康助手是一款专为个人健康管理设计的智能健康应用，集成了先进的AI技术，提供AI医生咨询、健康档案管理、健康产品购买、在线支付等全方位健康服务。应用致力于为用户提供专业、便捷的健康管理体验，通过AI医生助手帮助用户获得个性化的健康建议和医疗指导。

### 🌟 核心特性

- **�‍⚕️ AI医生咨询**: 与多位专业AI医生进行智能对话，获得个性化健康建议
- **📋 健康档案管理**: 完整的个人健康信息记录和管理系统
- **� 健康产品商城**: 医生推荐的健康产品购买和订单管理
- **� 在线支付**: 集成微信支付，支持安全便捷的在线支付
- **📱 现代化UI**: Material Design 3设计语言，支持亮暗主题切换
- **🌐 多语言支持**: 完整的中文、英文、维吾尔语本地化支持
- **🔒 数据安全**: 采用加密存储和安全传输保护用户隐私
- **📊 智能缓存**: 优化的数据缓存机制，提升应用性能

## 🏗️ 技术架构

### 架构设计模式

本项目采用**分层架构**和**模块化设计**，遵循**关注点分离**原则：

```
lib/
├── main.dart                 # 应用入口点
├── app.dart                  # 应用根组件
├── generated/                # 自动生成的国际化文件
├── l10n/                     # 本地化资源文件 (中文/英文/维吾尔语)
└── src/
    ├── config/               # 配置层
    │   ├── api/             # API配置和端点管理
    │   ├── routes/          # 路由配置和页面导航
    │   ├── themes/          # 主题配置和样式
    │   └── extensions/      # 扩展方法和工具
    ├── data/                # 数据层
    │   └── services/        # 数据服务和存储
    ├── exceptions/          # 异常处理
    ├── models/              # 数据模型
    │   ├── doctor_model.dart      # 医生信息模型
    │   ├── health_profile_model.dart # 健康档案模型
    │   ├── user_product_model.dart   # 产品模型
    │   ├── cart_model.dart           # 购物车模型
    │   ├── payment_model.dart        # 支付模型
    │   └── shipping_model.dart       # 物流模型
    ├── services/            # 业务服务层
    │   ├── auth_service.dart         # 用户认证服务
    │   ├── doctor_service.dart       # 医生管理服务
    │   ├── health_profile_service.dart # 健康档案服务
    │   ├── cart_service.dart         # 购物车服务
    │   ├── payment_service.dart      # 支付服务
    │   ├── wechat_pay_service.dart   # 微信支付服务
    │   └── shipping_service.dart     # 物流服务
    ├── presentation/        # 表示层
    │   ├── splash/          # 启动页模块
    │   ├── auth/            # 用户认证模块
    │   ├── core/            # 核心页面模块
    │   ├── health_assistant/ # AI医生咨询模块
    │   ├── health/          # 健康档案模块
    │   ├── product_list/    # 产品浏览模块
    │   ├── cart/            # 购物车模块
    │   ├── orders/          # 订单管理模块
    │   ├── shipping/        # 物流跟踪模块
    │   ├── address/         # 地址管理模块
    │   ├── admin/           # 管理员模块
    │   ├── doctor_management/ # 医生管理模块
    │   ├── doctor_product/  # 医生产品管理模块
    │   ├── distribution_management/ # 分销管理模块
    │   └── settings/        # 设置模块
    ├── common/              # 公共组件
    │   └── widgets/         # 通用UI组件
    └── utils/               # 工具类
        ├── app_toast.dart        # 消息提示工具
        ├── auth_helper.dart      # 认证辅助工具
        ├── chat_navigation_helper.dart # 聊天导航工具
        └── order_status_helper.dart    # 订单状态工具
```

### 核心技术栈

#### 🎯 前端框架
- **Flutter 3.8.1+**: 跨平台移动应用开发框架
- **Dart 3.8.1+**: 现代化编程语言

#### 🎨 UI/UX
- **Material Design 3**: 现代化设计语言
- **flutter_svg**: SVG图标支持
- **shimmer**: 骨架屏加载动画
- **flutter_markdown**: Markdown文本渲染
- **自定义主题系统**: 支持亮暗主题切换和字体大小调节
- **DPI适配系统**: 智能屏幕适配和字体缩放

#### 🌐 网络与API
- **http**: HTTP客户端
- **http_parser**: HTTP响应解析
- **RESTful API**: 后端服务通信
- **流式响应**: 支持AI对话的实时流式响应

#### 🗄️ 数据存储与缓存
- **sqflite**: 本地SQLite数据库
- **shared_preferences**: 轻量级键值存储
- **flutter_secure_storage**: 安全加密存储
- **cached_network_image**: 网络图片缓存
- **flutter_cache_manager**: 缓存管理
- **智能缓存系统**: 多层缓存架构优化性能

#### 🎵 多媒体处理
- **audioplayers**: 音频播放
- **record**: 音频录制
- **camera**: 相机功能
- **image_picker**: 图片选择
- **crop_your_image**: 图片裁剪
- **image**: 图片处理

#### � 位置与地理服务
- **geolocator**: GPS定位服务
- **geocoding**: 地理编码服务
- **city_pickers**: 城市选择器

#### 💳 支付与商务
- **fluwx**: 微信支付SDK
- **微信支付集成**: 完整的支付流程支持
- **订单管理**: 完整的电商订单系统
- **物流跟踪**: 订单物流状态管理

#### 🔧 状态管理与架构
- **Provider**: 状态管理
- **单例模式**: 服务层管理
- **观察者模式**: 状态通知
- **flutter_phoenix**: 应用重启管理

#### 🌍 国际化与本地化
- **flutter_localizations**: 官方国际化支持
- **intl**: 国际化工具
- **多语言支持**: 中文、英文、维吾尔语
- **RTL支持**: 右到左文本方向支持
- **自动代码生成**: ARB文件自动生成Dart代码

#### 🔒 安全与权限
- **permission_handler**: 系统权限管理
- **数据加密**: 敏感信息安全存储
- **安全传输**: HTTPS加密通信

#### 🛠️ 开发工具
- **flutter_lints**: 代码规范检查
- **flutter_launcher_icons**: 应用图标生成
- **package_info_plus**: 应用信息获取
- **share_plus**: 系统分享功能
- **url_launcher**: URL启动器

## 🚀 快速开始

### 环境要求

- **Flutter SDK**: 3.8.1 或更高版本
- **Dart SDK**: 3.8.1 或更高版本
- **Android Studio** / **VS Code** (推荐安装Flutter插件)
- **Android SDK**: API Level 21+ (Android 5.0+)
- **iOS**: iOS 12.0+ (如需iOS支持)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd minhan_app
   ```

2. **安装依赖**
   ```bash
   flutter pub get
   ```

3. **生成国际化文件**
   ```bash
   flutter gen-l10n
   ```

4. **配置API地址**

   编辑 `lib/src/config/api/api_config.dart` 文件，配置后端API地址：
   ```dart
   class ApiConfig {
     /// API基础URL - 需要替换为实际的后端服务地址
     static String baseUrl = "https://your-backend-domain.com";
   }
   ```

5. **配置微信支付**

   编辑 `lib/src/services/wechat_pay_service.dart` 文件，配置微信支付参数：
   ```dart
   class WeChatPayConfig {
     /// 微信开放平台AppID - 需要替换为实际的AppID
     static const String appId = 'wx1234567890abcdef';

     /// iOS Universal Link - 需要替换为实际的Universal Link
     static const String? universalLink = 'https://your-domain.com/';
   }
   ```

6. **运行应用**
   ```bash
   # 调试模式
   flutter run

   # 发布模式
   flutter run --release
   ```

### 构建发布版本

```bash
# Android APK
flutter build apk --release

# Android App Bundle
flutter build appbundle --release

# iOS (需要macOS环境)
flutter build ios --release
```

## 📱 功能模块详解

### �‍⚕️ AI医生咨询模块

#### 智能医生对话
- **多位专业AI医生**: 不同专科的AI医生助手
- **个性化咨询**: 基于用户健康档案的个性化建议
- **实时对话**: 流式响应，自然对话体验
- **多语言支持**: 支持中文、英文、维吾尔语咨询
- **语音交互**: 支持语音输入和语音播放
- **图片识别**: 上传医疗相关图片进行分析

#### 医生管理功能
- **医生列表**: 浏览所有可用的AI医生
- **医生详情**: 查看医生专业信息和擅长领域
- **收藏功能**: 收藏常用的医生
- **点赞功能**: 对医生服务进行评价
- **预约咨询**: 快速开始与医生的对话

### 📋 健康档案管理

#### 个人健康信息
- **基础信息**: 身高、体重、血型等基本健康数据
- **过敏史管理**: 药物过敏、食物过敏记录
- **用药记录**: 当前用药情况管理
- **慢性病史**: 慢性疾病记录和管理
- **手术史**: 手术历史记录
- **家族病史**: 家族遗传病史记录

#### 生活方式记录
- **运动习惯**: 运动频率和强度记录
- **饮食偏好**: 饮食习惯和偏好管理
- **生活习惯**: 吸烟、饮酒、睡眠等生活方式
- **压力水平**: 心理健康状态记录
- **女性健康**: 专门的女性健康信息管理

#### 地址信息管理
- **居住地址**: 详细的居住地址信息
- **地区选择**: 省市区三级地址选择
- **多地址管理**: 支持多个地址的管理
- **默认地址**: 设置默认收货地址

### � 健康产品商城

#### 产品浏览
- **产品列表**: 浏览所有健康产品
- **分类筛选**: 按产品分类进行筛选
- **医生推荐**: 查看特定医生推荐的产品
- **产品详情**: 详细的产品信息和图片
- **多图展示**: 支持多张产品图片展示

#### 购物车功能
- **加入购物车**: 将产品添加到购物车
- **数量调整**: 调整购物车中产品数量
- **批量操作**: 批量选择和删除商品
- **价格计算**: 自动计算总价和优惠
- **快速结算**: 一键结算购买

### � 订单与支付管理

#### 订单管理
- **订单创建**: 从购物车或直接购买创建订单
- **订单列表**: 查看所有历史订单
- **订单详情**: 详细的订单信息和状态
- **订单状态**: 实时跟踪订单处理状态
- **订单取消**: 支持取消未支付订单

#### 支付功能
- **微信支付**: 集成微信支付SDK
- **安全支付**: 加密传输保障支付安全
- **支付状态**: 实时查询支付状态
- **支付同步**: 自动同步支付结果
- **退款处理**: 支持订单退款流程

#### 物流跟踪
- **发货管理**: 医生端订单发货功能
- **物流状态**: 实时跟踪物流信息
- **配送进度**: 详细的配送进度显示
- **收货确认**: 用户收货确认功能

### 🏥 管理员功能模块

#### 医生管理
- **医生信息管理**: 创建、编辑、删除医生信息
- **多语言支持**: 医生信息的多语言管理
- **头像上传**: 医生头像图片管理
- **专业信息**: 医生专业领域和擅长管理

#### 产品审核
- **产品列表**: 查看所有医生发布的产品
- **待审核产品**: 专门的待审核产品列表
- **批量审核**: 支持批量审核产品
- **产品统计**: 产品数据统计和分析

#### 订单管理
- **订单监控**: 查看所有系统订单
- **状态管理**: 更新订单和支付状态
- **代发货**: 管理员代替医生发货
- **订单统计**: 订单数据分析和报表

#### 用户管理
- **用户列表**: 查看所有注册用户
- **用户详情**: 详细的用户信息管理
- **权限管理**: 用户角色和权限设置
- **余额管理**: 用户余额和积分调整

### 📊 分销管理系统

#### 分销员功能
- **申请分销**: 用户申请成为分销员
- **收入统计**: 分销收入和佣金统计
- **下级管理**: 管理下级分销员
- **等级管理**: 分销等级和权限管理

#### 推广工具
- **分销海报**: 个性化推广海报生成
- **邀请链接**: 专属邀请链接管理
- **佣金计算**: 自动计算分销佣金
- **提现功能**: 分销收入提现管理

### ⚙️ 设置与个性化

#### 主题设置
- **亮暗主题**: 支持亮色和暗色主题
- **自动切换**: 跟随系统主题设置
- **主题色**: 健康绿色主题配色

#### 语言设置
- **界面语言**: 中文/英文/维吾尔语界面切换
- **RTL支持**: 维吾尔语右到左文本支持
- **字体优化**: 针对不同语言的字体优化

#### 字体设置
- **字体大小**: 多级字体大小调节
- **DPI适配**: 智能屏幕密度适配
- **阅读优化**: 针对不同语言优化显示

## 🔧 开发指南

### 项目结构说明

#### 配置层 (config/)
- **api_config.dart**: API端点配置和URL管理
- **app_routes.dart**: 路由配置和页面导航
- **app_theme.dart**: 主题样式定义
- **app_colors.dart**: 颜色常量定义
- **app_assets.dart**: 资源文件路径管理

#### 核心服务层 (services/)
- **认证服务**:
  - `auth_service.dart`: 用户认证和登录管理
  - `user_info_manager_service.dart`: 用户信息管理
  - `avatar_manager_service.dart`: 头像管理
- **健康服务**:
  - `doctor_service.dart`: 医生信息管理
  - `health_profile_service.dart`: 健康档案管理
  - `doctor_interaction_service.dart`: 医生互动服务
- **商务服务**:
  - `user_product_service.dart`: 产品管理
  - `cart_service.dart`: 购物车管理
  - `payment_service.dart`: 支付处理
  - `wechat_pay_service.dart`: 微信支付集成
  - `shipping_service.dart`: 物流管理
  - `address_service.dart`: 地址管理
- **AI对话服务**:
  - `chat_repository.dart`: 聊天数据管理
  - `speech_to_text_service.dart`: 语音识别
  - `text_to_speech_service.dart`: 语音合成
- **缓存服务**:
  - `health_profile_cache_service.dart`: 健康档案缓存
  - `doctor_product_cache_service.dart`: 产品缓存
  - `cart_cache_service.dart`: 购物车缓存
  - `order_cache_service.dart`: 订单缓存
  - `image_cache_service.dart`: 图片缓存
- **系统服务**:
  - `theme_service.dart`: 主题管理
  - `language_service.dart`: 语言管理
  - `font_size_service.dart`: 字体管理
  - `dpi_adaptation_service.dart`: DPI适配
  - `text_direction_service.dart`: 文本方向管理
  - `location_service.dart`: 位置服务
  - `error_logger_service.dart`: 错误日志

#### 数据模型层 (models/)
- **用户模型**: `user_model.dart`, `user_profile_model.dart`
- **医生模型**: `doctor_model.dart`
- **健康模型**: `health_profile_model.dart`
- **产品模型**: `user_product_model.dart`, `doctor_product_model.dart`
- **订单模型**: `cart_model.dart`, `payment_model.dart`, `shipping_model.dart`
- **聊天模型**: `chat_message_model.dart`, `conversation_model.dart`
- **地址模型**: `address_model.dart`

#### 表示层 (presentation/)
- **模块化设计**: 每个功能模块独立组织
- **组件复用**: 公共组件统一管理
- **状态管理**: Provider模式状态管理
- **页面路由**: 统一的路由管理和页面跳转

### 开发规范

#### 代码风格
- 遵循 Dart 官方代码规范
- 使用 `flutter_lints` 进行代码检查
- 统一的命名约定和注释规范

#### 文件组织
- 按功能模块组织文件结构
- 使用 barrel exports 简化导入
- 分离业务逻辑和UI逻辑

#### 状态管理
- 使用 Provider 进行状态管理
- 服务层采用单例模式
- 合理使用 ChangeNotifier

### 测试策略

#### 单元测试
```bash
# 运行所有测试
flutter test

# 运行特定测试文件
flutter test test/widget_test.dart

# 生成测试覆盖率报告
flutter test --coverage
```

#### 集成测试
```bash
# 运行集成测试
flutter drive --target=test_driver/app.dart
```

#### 性能测试
```bash
# 性能分析
flutter run --profile
```

## 🔌 API接口文档

### 认证接口

#### 短信登录
```http
POST /applet/v1/app/sms_login
Content-Type: application/x-www-form-urlencoded

phone=13800138000&code=123456
```

#### 密码登录
```http
POST /applet/v1/app/login
Content-Type: application/x-www-form-urlencoded

phone=13800138000&password=123456
```

#### 用户注册
```http
POST /applet/v1/app/register
Content-Type: application/x-www-form-urlencoded

phone=13800138000&password=123456&code=123456
```

### 医生管理接口

#### 获取医生列表
```http
GET /applet/v1/doctors?lang=zh
Authorization: Bearer <token>
```

#### 获取医生详情
```http
GET /applet/v1/doctors/{doctorId}?lang=zh
Authorization: Bearer <token>
```

#### 点赞/取消点赞医生
```http
POST /applet/v1/doctors/{doctorId}/like
Authorization: Bearer <token>

DELETE /applet/v1/doctors/{doctorId}/like
Authorization: Bearer <token>
```

#### 收藏/取消收藏医生
```http
POST /applet/v1/doctors/{doctorId}/favorite
Authorization: Bearer <token>

DELETE /applet/v1/doctors/{doctorId}/favorite
Authorization: Bearer <token>
```

### AI聊天接口

#### 获取对话列表
```http
GET /applet/v1/chat/conversations
Authorization: Bearer <token>
```

#### 发送消息
```http
POST /applet/v1/chat/conversations/{conversationId}/messages
Authorization: Bearer <token>
Content-Type: application/json

{
  "content": "我最近感觉头痛，应该怎么办？",
  "doctor_id": 1
}
```

#### 语音转文字
```http
POST /applet/v1/chat/speech_to_text
Authorization: Bearer <token>
Content-Type: multipart/form-data

audio=<audio_file>
```

#### 文字转语音
```http
POST /applet/v1/trans/tts
Authorization: Bearer <token>
Content-Type: application/x-www-form-urlencoded

text=您好，请问有什么可以帮助您的？&lang=zh&speed=5
```

### 产品管理接口

#### 获取产品列表
```http
GET /applet/v1/products?lang=zh&page=1&limit=20
```

#### 获取产品详情
```http
GET /applet/v1/products/{productId}?lang=zh
```

#### 获取医生的产品
```http
GET /applet/v1/products/doctor/{doctorId}?lang=zh
```

### 购物车接口

#### 加入购物车
```http
POST /applet/v1/cart/add
Authorization: Bearer <token>
Content-Type: application/json

{
  "product_id": 1,
  "quantity": 2
}
```

#### 获取购物车列表
```http
GET /applet/v1/cart
Authorization: Bearer <token>
```

#### 购物车结算
```http
POST /applet/v1/cart/checkout
Authorization: Bearer <token>
Content-Type: application/json

{
  "address_id": 1,
  "cart_ids": [1, 2, 3]
}
```

### 订单管理接口

#### 创建订单
```http
POST /applet/v1/products/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "product_id": 1,
  "quantity": 2,
  "address_id": 1
}
```

#### 获取我的订单
```http
GET /applet/v1/products/orders/my?status=all&page=1&limit=20
Authorization: Bearer <token>
```

#### 订单支付
```http
POST /applet/v1/products/orders/{orderId}/payment
Authorization: Bearer <token>
Content-Type: application/json

{
  "payment_method": "wechat"
}
```

### 地址管理接口

#### 创建地址
```http
POST /applet/v1/addresses
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "张三",
  "phone": "13800138000",
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "detail": "某某街道123号",
  "is_default": true
}
```

#### 获取地址列表
```http
GET /applet/v1/addresses
Authorization: Bearer <token>
```

### 健康档案接口

#### 获取健康档案
```http
GET /applet/v1/app/profile
Authorization: Bearer <token>
```

#### 更新健康档案
```http
POST /applet/v1/app/update_profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "height": 175.5,
  "weight": 70.0,
  "blood_type": "A",
  "has_allergies": true,
  "allergy_drugs": ["青霉素", "阿司匹林"]
}
```

## 📦 依赖库详解

### 核心依赖

| 依赖库 | 版本 | 用途 | 说明 |
|--------|------|------|------|
| `flutter` | SDK | 核心框架 | Flutter SDK |
| `flutter_localizations` | SDK | 国际化 | 官方国际化支持 |
| `cupertino_icons` | ^1.0.8 | 图标 | iOS风格图标 |
| `intl` | ^0.20.2 | 国际化 | 国际化工具库 |

### UI/UX依赖

| 依赖库 | 版本 | 用途 | 说明 |
|--------|------|------|------|
| `flutter_svg` | ^2.0.10+1 | SVG支持 | SVG图标和图片显示 |
| `shimmer` | ^3.0.0 | 加载动画 | 骨架屏加载效果 |
| `flutter_markdown` | ^0.7.4+1 | Markdown | Markdown文本渲染 |

### 网络与数据

| 依赖库 | 版本 | 用途 | 说明 |
|--------|------|------|------|
| `http` | ^1.4.0 | HTTP客户端 | 网络请求 |
| `http_parser` | ^4.0.2 | HTTP解析 | HTTP响应解析 |
| `shared_preferences` | ^2.5.3 | 轻量存储 | 键值对存储 |
| `sqflite` | ^2.4.2 | 数据库 | SQLite本地数据库 |
| `flutter_secure_storage` | ^10.0.0-beta.4 | 安全存储 | 加密数据存储 |
| `path` | ^1.9.1 | 路径处理 | 文件路径操作 |

### 状态管理与架构

| 依赖库 | 版本 | 用途 | 说明 |
|--------|------|------|------|
| `provider` | ^6.1.2 | 状态管理 | Provider状态管理 |
| `flutter_phoenix` | ^1.1.1 | 应用重启 | 应用重启管理 |

### 多媒体处理

| 依赖库 | 版本 | 用途 | 说明 |
|--------|------|------|------|
| `audioplayers` | ^6.1.0 | 音频播放 | 音频文件播放 |
| `record` | ^6.0.0 | 音频录制 | 语音录制功能 |
| `camera` | ^0.11.0 | 相机功能 | 拍照和录像 |
| `image_picker` | ^1.1.2 | 图片选择 | 从相册选择图片 |
| `crop_your_image` | ^2.0.0 | 图片裁剪 | 图片裁剪编辑 |
| `image` | ^4.3.0 | 图片处理 | 图片格式转换和处理 |

### 缓存与图片

| 依赖库 | 版本 | 用途 | 说明 |
|--------|------|------|------|
| `cached_network_image` | ^3.4.1 | 图片缓存 | 网络图片缓存显示 |
| `flutter_cache_manager` | ^3.4.1 | 缓存管理 | 文件缓存管理 |

### 支付与商务

| 依赖库 | 版本 | 用途 | 说明 |
|--------|------|------|------|
| `fluwx` | ^5.6.0 | 微信支付 | 微信支付SDK |

### 位置与地理

| 依赖库 | 版本 | 用途 | 说明 |
|--------|------|------|------|
| `geolocator` | ^10.1.0 | GPS定位 | 获取设备位置 |
| `geocoding` | ^2.1.1 | 地理编码 | 地址和坐标转换 |
| `city_pickers` | ^1.3.0 | 城市选择 | 城市选择器组件 |

### 权限与系统

| 依赖库 | 版本 | 用途 | 说明 |
|--------|------|------|------|
| `permission_handler` | ^12.0.0+1 | 权限管理 | 系统权限申请 |
| `path_provider` | ^2.1.4 | 路径获取 | 系统目录路径 |
| `package_info_plus` | ^8.0.2 | 应用信息 | 获取应用版本信息 |
| `share_plus` | ^11.0.0 | 分享功能 | 系统分享接口 |
| `url_launcher` | ^6.2.4 | URL启动 | 打开外部应用和拨打电话 |

### 开发工具

| 依赖库 | 版本 | 用途 | 说明 |
|--------|------|------|------|
| `flutter_test` | SDK | 测试框架 | 单元测试和Widget测试 |
| `flutter_lints` | ^6.0.0 | 代码检查 | Dart代码规范检查 |
| `flutter_launcher_icons` | ^0.14.3 | 图标生成 | 应用图标自动生成 |

## 🌍 国际化支持

### 支持语言

- **中文 (zh)**: 简体中文界面，完整的中文本地化
- **English (en)**: 英文界面，完整的英文本地化
- **维吾尔语 (ug)**: 维吾尔语界面，支持RTL文本方向

### 本地化特性

#### 多语言界面
- **完整翻译**: 所有界面文本都有对应的多语言版本
- **动态切换**: 支持运行时动态切换语言
- **RTL支持**: 维吾尔语支持从右到左的文本方向
- **字体优化**: 针对不同语言使用优化的字体

#### 本地化文件结构
```
lib/l10n/
├── app_zh.arb    # 中文资源文件 (8660+ 条翻译)
├── app_en.arb    # 英文资源文件
└── app_ug.arb    # 维吾尔语资源文件
```

#### 主要本地化内容
- **界面标题**: 所有页面标题和导航文本
- **按钮文本**: 所有操作按钮的文本
- **表单标签**: 表单字段和验证消息
- **错误消息**: 错误提示和警告信息
- **医疗术语**: 医疗相关的专业术语
- **产品信息**: 产品名称和描述
- **订单状态**: 订单和支付状态文本

### 使用方法

#### 在代码中使用本地化
```dart
// 在Widget中使用本地化字符串
Text(AppLocalizations.of(context)!.settingsTitle)

// 获取当前语言代码
String languageCode = Localizations.localeOf(context).languageCode;

// 切换语言
LanguageService().setLocale(Locale('en'));

// 检查是否为RTL语言
bool isRTL = TextDirectionService.rtlLanguages.contains(languageCode);
```

#### 多语言数据处理
```dart
// 后端API支持多语言数据
{
  "name": {
    "zh": "心血管内科",
    "en": "Cardiology",
    "ug": "يۈرەك قان تومۇر ئىچكى دوختۇرلۇقى"
  }
}

// 获取当前语言的文本
String getLocalizedText(Map<String, String> multiLangText) {
  String currentLang = LanguageService().currentLocale.languageCode;
  return multiLangText[currentLang] ?? multiLangText['zh'] ?? '';
}
```

### 字体配置

#### 维吾尔语字体
```dart
// 使用UKIJTor字体支持维吾尔语显示
TextStyle(
  fontFamily: 'UKIJTor',
  fontSize: 16.0,
  letterSpacing: 1.2, // 增加字符间距提高可读性
)
```

#### 字体文件
```
assets/fonts/
├── UKIJTor.ttf           # 维吾尔语专用字体
├── DancingScript-Regular.ttf  # 装饰字体
└── DancingScript-Bold.ttf     # 粗体装饰字体
```

## 🎨 主题系统

### 主题配置

应用支持完整的主题定制系统，采用健康绿色作为主题色，包括亮色主题、暗色主题和自定义颜色配置。

#### 健康主题色彩
```dart
class AppColors {
  // 健康主题色 - 绿色系
  static const Color primary = Color(0xFF109D58);      // 主绿色
  static const Color primarySolid = Color(0xFF12B768); // 实心绿色
  static const Color secondary = Color(0xFF4CAF50);    // 辅助绿色

  // 功能色彩
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
}
```

#### 主题切换
```dart
// 切换到暗色主题
ThemeService().setThemeMode(ThemeMode.dark);

// 切换到亮色主题
ThemeService().setThemeMode(ThemeMode.light);

// 跟随系统主题
ThemeService().setThemeMode(ThemeMode.system);
```

#### Material Design 3 支持
- **动态颜色**: 支持Android 12+的动态颜色系统
- **自适应设计**: 根据系统主题自动调整
- **无障碍支持**: 符合无障碍设计标准
- **高对比度**: 支持高对比度模式

### 字体与显示系统

#### 智能字体缩放
```dart
// 设置字体缩放比例
FontSizeService().setTextScaleFactor(1.2);

// 获取缩放后的字体大小
double scaledSize = FontSizeService().getScaledFontSize(16.0);

// DPI适配
MediaQueryData adaptedData = DpiAdaptationService().getModifiedMediaQueryData(
  context,
  originalData
);
```

#### 多语言字体优化
- **UKIJTor**: 维吾尔语专用字体，支持RTL显示
- **系统字体**: 中英文使用系统默认字体
- **字符间距**: 维吾尔语文本增加字符间距提高可读性
- **字体权重**: 针对不同语言优化字体粗细

#### DPI适配系统
```dart
class DpiAdaptationService {
  // 智能屏幕适配
  static const double baseWidth = 375.0;  // 基准宽度
  static const double baseHeight = 812.0; // 基准高度

  // 获取适配后的尺寸
  double getAdaptedSize(double size, BuildContext context);

  // 获取适配后的MediaQueryData
  MediaQueryData getModifiedMediaQueryData(BuildContext context, MediaQueryData data);
}
```

### UI组件系统

#### 统一设计语言
- **卡片设计**: 圆角卡片，阴影效果
- **按钮样式**: 统一的按钮设计规范
- **输入框**: 一致的表单输入样式
- **导航栏**: 统一的导航栏设计

#### 动画效果
- **页面转场**: 流畅的页面切换动画
- **加载动画**: Shimmer骨架屏效果
- **交互反馈**: 按钮点击和状态变化动画
- **列表动画**: 列表项的进入和退出动画

## 🔒 安全与隐私

### 数据安全

#### 加密存储
- 使用 `flutter_secure_storage` 加密存储敏感数据
- 用户认证令牌安全存储
- 个人信息本地加密

#### 网络安全
- HTTPS加密传输
- API请求签名验证
- 防止中间人攻击

#### 权限管理
```dart
// 位置权限
await Permission.location.request();

// 相机权限
await Permission.camera.request();

// 麦克风权限
await Permission.microphone.request();

// 存储权限
await Permission.storage.request();
```

### 隐私保护

- **最小权限原则**: 仅申请必要权限
- **数据本地化**: 敏感数据本地存储
- **用户控制**: 用户可控制数据使用
- **透明度**: 明确的隐私政策

## 🚀 性能优化

### 应用性能

#### 启动优化
- 延迟初始化非关键服务
- 预加载关键资源
- 优化启动页面加载

#### 内存管理
- 及时释放不用的资源
- 图片缓存管理
- 避免内存泄漏

#### 网络优化
- 请求缓存机制
- 图片压缩传输
- 断点续传支持

### 地图性能

#### 地图优化
- WebView地图预加载
- 坐标系转换缓存
- POI搜索结果缓存

#### 定位优化
- GPS定位缓存
- 位置更新频率控制
- 低功耗定位模式

### UI性能

#### 渲染优化
- Widget复用
- 避免不必要的重建
- 使用const构造函数

#### 动画优化
- 硬件加速动画
- 60fps流畅动画
- 动画资源预加载

## 📱 平台适配

### Android适配

#### 版本支持
- **最低版本**: Android 5.0 (API Level 21)
- **目标版本**: Android 14 (API Level 34)
- **编译版本**: 最新稳定版

#### 特性适配
- Material Design 3
- 动态颜色支持
- 边缘到边缘显示
- 自适应图标

#### 权限配置 (android/app/src/main/AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### iOS适配

#### 版本支持
- **最低版本**: iOS 12.0
- **目标版本**: iOS 17.0
- **架构支持**: arm64

#### 特性适配
- iOS设计规范
- 深色模式支持
- 安全区域适配
- 动态类型支持

#### 权限配置 (ios/Runner/Info.plist)
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>此应用需要位置权限来提供基于位置的翻译和导游服务</string>
<key>NSCameraUsageDescription</key>
<string>此应用需要相机权限来进行图像翻译</string>
<key>NSMicrophoneUsageDescription</key>
<string>此应用需要麦克风权限来进行语音翻译</string>
```

## 🔧 故障排除

### 常见问题

#### 1. 编译错误
```bash
# 清理构建缓存
flutter clean
flutter pub get

# 重新生成国际化文件
flutter gen-l10n
```

#### 2. 地图不显示
- 检查高德地图API密钥配置
- 确认网络连接正常
- 检查WebView权限设置

#### 3. 定位失败
- 确认位置权限已授予
- 检查GPS是否开启
- 确认网络连接正常

#### 4. 翻译失败
- 检查API服务器连接
- 确认用户登录状态
- 检查网络权限设置

### 调试技巧

#### 日志输出
```dart
// 开发模式日志
if (kDebugMode) {
  print('调试信息: $message');
}

// 错误日志
ErrorLoggerService().logError('错误信息', error, stackTrace);
```

#### 性能分析
```bash
# 性能分析模式
flutter run --profile

# 内存分析
flutter run --profile --trace-startup
```

## � 微信支付集成

### 支付功能特性

#### 完整支付流程
- **订单创建**: 自动生成订单并计算金额
- **支付调起**: 调用微信支付SDK进行支付
- **状态同步**: 实时同步支付状态
- **结果处理**: 自动处理支付成功/失败结果

#### 安全保障
- **加密传输**: 所有支付数据加密传输
- **签名验证**: 支付请求签名验证
- **状态校验**: 多重支付状态校验机制
- **异常处理**: 完善的支付异常处理

#### 配置要求
```dart
class WeChatPayConfig {
  /// 微信开放平台AppID
  static const String appId = 'wx1234567890abcdef';

  /// iOS Universal Link
  static const String? universalLink = 'https://your-domain.com/';

  /// 调试模式开关
  static const bool isDebug = false;
}
```

#### 支付流程
1. **创建订单**: 用户选择商品并创建订单
2. **发起支付**: 调用微信支付接口
3. **用户支付**: 跳转微信完成支付
4. **结果回调**: 接收支付结果并更新订单状态
5. **状态同步**: 后台验证支付状态确保一致性

### 配置指南

详细的微信支付配置请参考 [WECHAT_PAY_SETUP.md](WECHAT_PAY_SETUP.md) 文档，包括：

- 微信开放平台账号注册和配置
- 微信商户平台设置
- Android和iOS平台配置
- 证书和签名配置
- 测试和调试指南

## �📈 版本历史

### v1.0.3 (当前版本)
- ✅ AI医生咨询系统 (多位专科医生)
- ✅ 健康档案管理 (完整的健康信息记录)
- ✅ 健康产品商城 (产品浏览和购买)
- ✅ 购物车和订单管理
- ✅ 微信支付集成
- ✅ 物流跟踪系统
- ✅ 地址管理系统
- ✅ 用户认证和权限管理
- ✅ 管理员后台功能
- ✅ 分销管理系统
- ✅ 多语言支持 (中文/英文/维吾尔语)
- ✅ RTL文本方向支持
- ✅ 智能缓存系统
- ✅ DPI适配和字体缩放
- ✅ 语音识别和语音合成
- ✅ 图片上传和处理
- ✅ 实时流式AI对话

### 计划功能 (v1.1.0)
- 🔄 AI医生对话优化和更多专科医生
- 🔄 健康数据分析和趋势图表
- 🔄 健康提醒和用药提醒功能
- 🔄 社区功能和用户互动
- 🔄 更多支付方式集成
- 🔄 离线模式支持
- 🔄 数据云端同步功能

## 🤝 贡献指南

### 开发流程

1. **Fork项目**
2. **创建功能分支**
   ```bash
   git checkout -b feature/new-feature
   ```
3. **提交更改**
   ```bash
   git commit -m "Add new feature"
   ```
4. **推送分支**
   ```bash
   git push origin feature/new-feature
   ```
5. **创建Pull Request**

### 代码规范

- 遵循 Dart 官方代码规范
- 使用有意义的变量和函数名
- 添加必要的注释和文档
- 编写单元测试

### 提交规范

```
type(scope): description

[optional body]

[optional footer]
```

类型说明:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 📄 许可证

本项目为私有项目，版权所有。未经授权不得复制、分发或修改。

## 📞 联系方式

- **项目维护者**: [维护者姓名]
- **邮箱**: [联系邮箱]
- **问题反馈**: [GitHub Issues链接]

## 🙏 致谢

感谢以下开源项目和服务提供商：

- **Flutter团队** - 提供优秀的跨平台开发框架
- **微信开放平台** - 提供微信支付SDK和服务
- **所有依赖库的开发者** - 提供优质的开源组件
- **AI技术提供商** - 提供智能对话和语音服务
- **开源社区** - 提供技术支持和解决方案

## 📋 项目特色

### 🏥 专业医疗背景
- 基于真实医疗场景设计
- 专业的医疗术语和流程
- 符合医疗行业标准的数据结构

### 🛡️ 企业级架构
- 完整的用户权限管理系统
- 多角色支持 (用户/医生/管理员)
- 完善的数据缓存和性能优化
- 企业级的错误处理和日志系统

### 🌏 国际化设计
- 真正的多语言支持，不仅仅是界面翻译
- RTL语言支持，适配不同文化背景
- 多语言数据存储和管理
- 智能的语言检测和切换

### 💼 商业化功能
- 完整的电商系统 (产品/订单/支付/物流)
- 分销管理系统
- 多种支付方式集成
- 数据统计和分析功能

---

<div align="center">
  <p>© 2024 Health Assistant. All rights reserved.</p>
  <p>Made with ❤️ using Flutter</p>
  <p>Version 1.0.3 - 智能健康管理与AI医生咨询应用</p>
</div>
