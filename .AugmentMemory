# Project Overview
- User is transforming a project into a health assistant app (健康助手) from an AI tour guide app, focusing on health assistant and profile pages.
- The app's theme color should change from blue to #109D58 (green), with solid color #12B768 for splash screen.
- The app is for users to chat with AI agents representing different doctors, not for live consultations.

# UI/UX Preferences
- User prefers cleaner UI without black divider lines, consistent dotted line separators, and fixed bottom action bars.
- User prefers higher quality skeleton loading animations and optimized, smooth navigation logic.
- User prefers floating toast notifications with black background, white text, auto-dismiss functionality, and lower screen positioning.
- User prefers delete buttons in top-right corner as red text buttons, and quantity input fields to be directly editable.
- User prefers phone icon buttons over text-based contact buttons in doctor detail pages.
- User prefers Uyghur language text in AI chat bubbles to have slightly larger font size and increased character spacing for better readability.

# Health Assistant & Doctor Features
- Doctor cards should use horizontal carousel with infinite scrolling, be responsive to screen sizes, and include consultation/appointment buttons.
- Doctor detail page should include: back button, photo with experience banner, name/hospital info, rating/schedule/consult section, bio, specialties, and recommendations.
- Doctor appointment button should open popup with doctor info, where phone jumps to system dialer and name/address copy to clipboard.
- All doctor information must be fetched from backend APIs with no hardcoded defaults.
- User wants like/favorite functionality for doctors with dedicated API endpoints and UI buttons.

# Doctor Data Management
- Backend doctor table includes comprehensive fields including multilingual content for name, specialty, specialties, description, detailed_info, and system_prompt.
- Backend supports specialties field with comma-separated values, admin interface should use individual input boxes.
- Backend expects multilingual doctor data in nested JSON format with language codes (zh/en/ug) when creating/updating doctors.
- User prefers collapsible multilingual input fields in admin interfaces for multiple languages rather than separate fields.
- Admin doctors API returns multilingual data in nested JSON format (e.g., name: {zh: '', en: '', ug: ''}) without requiring language parameter specification.
- Admin doctors API endpoint returns multilingual data in all languages regardless of lang parameter, so admin edit pages should display all language fields not just Chinese.

# Product & Order Management
- Product cards should have capsule-shaped design with light mint green background and dotted divider lines.
- Product management should use image upload APIs instead of URL input fields, supporting multi-image upload and editing.
- The app needs a complete logistics system with doctor-side shipping management and user-side tracking.
- Order items should navigate to detailed order information page showing all order-related details.
- User wants product creation and editing to use the same multilingual support pattern as implemented for doctor management.

# Chat & User Features
- Chat API requires doctor_id parameter in all conversation endpoints, with history accessible via drawer and profile page.
- Login API returns is_doctor and doctor_id fields to control product management access.
- Profile page should include "My Orders" option with shopping cart accessible only from profile page.

# Admin & Management Features
- Admin management page should include doctor management, product review with batch selection, and order management tabs.
- Admin pages should use consistent UI design patterns across all management functions.

# API & Technical Requirements
- All API endpoints should be centrally managed in api_config files rather than hardcoded in service files.
- Use fluwx 5.6.0 for WeChat payment integration and city_pickers 1.3.0 for region selection with current location option.
- User wants address management system with CRUD operations, default address selection, following e-commerce patterns.

# Internationalization
- Backend supports multilingual APIs (zh/en/ug) with lang parameter for doctors and products endpoints.
- Content is stored in JSON format with language codes as keys for multilingual fields.
- User prefers multilingual interfaces without flag icons, only showing language codes and names.
- Backend expects MultiLangText object format for multilingual editing, not string data format.