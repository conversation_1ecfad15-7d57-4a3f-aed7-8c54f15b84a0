<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AudioRecordingSettings</key>
	<dict>
		<key>AVFormatIDKey</key>
		<integer>1633772320</integer>
		<key>AVSampleRateKey</key>
		<real>44100</real>
		<key>AVNumberOfChannelsKey</key>
		<integer>1</integer>
		<key>AVEncoderBitRateKey</key>
		<integer>128000</integer>
		<key>AVEncoderAudioQualityKey</key>
		<integer>4</integer>
		<key>AVLinearPCMBitDepthKey</key>
		<integer>16</integer>
		<key>AVLinearPCMIsFloatKey</key>
		<false/>
		<key>AVLinearPCMIsBigEndianKey</key>
		<false/>
	</dict>
	<key>MinimumRecordingDuration</key>
	<real>2.0</real>
	<key>AudioSessionCategory</key>
	<string>AVAudioSessionCategoryPlayAndRecord</string>
	<key>AudioSessionOptions</key>
	<array>
		<string>AVAudioSessionCategoryOptionDefaultToSpeaker</string>
		<string>AVAudioSessionCategoryOptionAllowBluetooth</string>
	</array>
</dict>
</plist>