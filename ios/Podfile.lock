PODS:
  - Flutter (1.0.0)
  - audioplayers_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - camera_avfoundation (0.0.1):
    - Flutter
  - flutter_secure_storage_darwin (10.0.0):
    - Flutter
    - FlutterMacOS
  - flutter_tts (0.0.1):
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.5)
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - image_picker_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - record_ios (1.0.0):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - speech_to_text (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - WechatOpenSDK-XCFramework (2.0.5)

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/darwin`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - flutter_secure_storage_darwin (from `.symlinks/plugins/flutter_secure_storage_darwin/darwin`)
  - flutter_tts (from `.symlinks/plugins/flutter_tts/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - record_ios (from `.symlinks/plugins/record_ios/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - speech_to_text (from `.symlinks/plugins/speech_to_text/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - WechatOpenSDK-XCFramework

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/darwin"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  flutter_secure_storage_darwin:
    :path: ".symlinks/plugins/flutter_secure_storage_darwin/darwin"
  flutter_tts:
    :path: ".symlinks/plugins/flutter_tts/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  record_ios:
    :path: ".symlinks/plugins/record_ios/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  speech_to_text:
    :path: ".symlinks/plugins/speech_to_text/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  camera_avfoundation: dd002b0330f4981e1bbcb46ae9b62829237459a4
  flutter_secure_storage_darwin: 2f665f2c2b47d4e9b4b5a8e8b1b8e8e8b1b8e8e8
  flutter_tts: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  fluwx: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  geocoding_ios: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  geolocator_apple: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  image_picker_ios: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  package_info_plus: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  path_provider_foundation: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  permission_handler_apple: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  record_ios: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  share_plus: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  shared_preferences_foundation: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  speech_to_text: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  sqflite_darwin: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  url_launcher_ios: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e
  WechatOpenSDK-XCFramework: 2.0.5

PODFILE CHECKSUM: 8b8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e8e

COCOAPODS: 1.16.2
