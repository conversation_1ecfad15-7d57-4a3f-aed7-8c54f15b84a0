PODS:
  - audioplayers_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - camera_avfoundation (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_secure_storage_darwin (10.0.0):
    - Flutter
    - FlutterMacOS
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.5)
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - image_picker_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - record_ios (1.0.0):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - WechatOpenSDK-XCFramework (2.0.5)

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/darwin`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - Flutter (from `Flutter`)
  - flutter_secure_storage_darwin (from `.symlinks/plugins/flutter_secure_storage_darwin/darwin`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - record_ios (from `.symlinks/plugins/record_ios/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - WechatOpenSDK-XCFramework

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/darwin"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  Flutter:
    :path: Flutter
  flutter_secure_storage_darwin:
    :path: ".symlinks/plugins/flutter_secure_storage_darwin/darwin"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  record_ios:
    :path: ".symlinks/plugins/record_ios/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  audioplayers_darwin: 4f9ca89d92d3d21cec7ec580e78ca888e5fb68bd
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_secure_storage_darwin: ce237a8775b39723566dc72571190a3769d70468
  fluwx: 2ef787502fccb3f3596b380509001a8ea71cbbff
  geocoding_ios: 86f1c479822f07a2f79f7094f2b753d1e9e20215
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  record_ios: fee1c924aa4879b882ebca2b4bce6011bcfc3d8b
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  WechatOpenSDK-XCFramework: b072030c9eeee91dfff1856a7846f70f7b9a88ed

PODFILE CHECKSUM: 37d34e036dafdec20a1b05161faa914ebbf37abc

COCOAPODS: 1.16.2
