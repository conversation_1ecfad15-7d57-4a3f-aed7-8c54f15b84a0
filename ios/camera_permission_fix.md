# iOS 相机权限修复指南

## 当前问题
相机权限在 iOS 上没有弹出请求窗口，设置中也找不到权限选项。

## 诊断步骤

### 1. 检查 Info.plist 权限声明
确保以下权限已正确声明：
- NSCameraUsageDescription ✅
- NSPhotoLibraryUsageDescription ✅  
- NSPhotoLibraryAddUsageDescription ✅

### 2. 检查项目配置
- iOS 部署目标：12.0 ✅
- Podfile 平台版本：12.0 ✅

### 3. 权限请求时机检查
权限应该在用户首次尝试使用相机时请求，而不是应用启动时。

## 修复方案

### 方案1：添加权限处理增强
在相机初始化前添加更详细的权限检查和处理。

### 方案2：检查应用签名和配置
确保应用有正确的开发者签名和配置。

### 方案3：重置权限状态
如果之前拒绝过权限，需要完全卸载应用重新安装。